@php use App\Enums\AttendanceStatus; @endphp
@extends('reports.layout')

@section('content')
    @foreach($data as $class)
        <table>
            <thead>
            <tr>
                <th>{{__('general.student_no')}}</th>
                <th>{{__('general.student_class')}} ({{$class['class_name']}})</th>
                <th>{{__('attendance.time_in')}}</th>
                <th>{{__('attendance.time_out')}}</th>
                <th>{{__('attendance.attendance_status')}}</th>
                <th>{{__('general.reason')}}</th>
                <th>{{__('general.remarks')}}</th>
            </tr>
            </thead>

            <tbody>
            @foreach($class['dates'] as $date)
                <tr>
                    <td colspan="6">{{$date['date']}}</td>
                </tr>

                @foreach($date['students'] as $student)
                    <tr @if($student['attendance_status'] == AttendanceStatus::ABSENT) style="background-color: red; color: white;" @endif>
                        <td>{{$student['student_number']}}</td>
                        <td>{{$student['student_name']}}</td>
                        <td>{{$student['attendance_time_in']}}</td>
                        <td>{{$student['attendance_time_out']}}</td>
                        <td>{{$student['attendance_status']}}</td>
                        <td>{{$student['attendance_reason']}}</td>
                        <td></td>
                    </tr>
                @endforeach
                <tr>
                    <td colspan="2">{{__('general.total')}} : {{$date['total']}}</td>
                    <td colspan="2">{{__('attendance.attend')}} : {{$date['total_present']}}</td>
                    <td>{{__('attendance.absent')}} : {{$date['total_absent']}}</td>
                    <td>{{__('attendance.late')}} : {{$date['total_late']}}</td>
                    <td></td>
                </tr>
            @endforeach

            </tbody>
        </table>

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection
