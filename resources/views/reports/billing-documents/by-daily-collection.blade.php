@extends('reports.layout')

@section('content')
    <table>
        <thead>
        <tr>
            <th>{{ __('general.no') }}</th>
            <th>{{ __('general.payment_date') }}</th>
            <th>{{ __('general.invoice_date') }}</th>
            <th>{{ __('general.invoice_no') }}</th>
            <th>{{ __('general.bill_to_name') }}</th>
            <th>{{ __('general.bill_to_reference_no') }}</th>
            <th>{{ __('general.class') }}</th>
            <th>{{ __('general.description') }}</th>
            <th>{{ __('general.amount') }} ({{ config('school.currency_code')}})</th>
            <th>{{ __('general.payment') }}</th>
            <th>{{ __('general.bank') }}</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($data as $invoice)
            @php
                $filtered_line_items = $invoice->lineItems->toArray();

                $line_items_count = count($filtered_line_items);
                $payments_count = count($invoice->payments);
                // Get the maximum row count between line items and payments
                $row_count = max($line_items_count, $payments_count, 1);
            @endphp

            @for ($i = 0; $i < $row_count; $i++)
                <tr>
                    @if ($i === 0)
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            {{ $loop->iteration }}
                        </td>
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            {{ \Carbon\Carbon::parse($invoice->paid_at)->tz(config('school.timezone'))->format('Y-m-d') }}
                        </td>
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            {{ $invoice->document_date }}
                        </td>
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            {{ $invoice->reference_no }}
                        </td>
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            {{ $invoice->billTo->getTranslation('name', 'zh') ?? '-' }}
                        </td>
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            {{ $invoice->bill_to_reference_number }}
                        </td>
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            {{ $invoice->class_name ?? '-' }}
                        </td>
                    @endif

                    <td style="text-align: left; vertical-align: center;">
                        @php
                            $valid_line_items = array_values($filtered_line_items);
                        @endphp
                        @if (isset($valid_line_items[$i]))
                            {{ $valid_line_items[$i]['description'] }}
                        @endif
                    </td>

                    @if ($i === 0)
                        <td rowspan="{{ $row_count }}" style="text-align: left; vertical-align: center;">
                            @php
                                $amount = array_sum(array_column($filtered_line_items, 'amount_before_tax'));
                            @endphp
                            {{ number_format($amount, 2, '.', '') }}
                        </td>
                    @endif

                    @if (isset($invoice->payments[$i]))
                        <td style="text-align: left; vertical-align: center;">
                            {{ $invoice->payments[$i]?->paymentMethod->code }} ({{ $invoice->payments[$i]->payment_reference_no }})
                        </td>
                        
                        {{-- get bank info --}}
                        @if (isset($invoice->payments[$i]->paymentSource?->bank))
                            <td style="text-align: left; vertical-align: center;">
                                {{ $invoice->payments[$i]->paymentSource?->bank->name }} ({{ $invoice->payments[$i]->paymentSource?->bank->swift_code }})
                            </td>
                        @endif
                    @endif
                </tr>
            @endfor
        @endforeach
        <tr>
            <td colspan="8" style="text-align: right;">{{__('general.total_amount')}} :</td>
            <td style="text-align: left;"> {{ number_format($total_amount, 2, '.', '') }} </td>
        </tr>
        @if ($net_amount > 0)
            <tr>
                <td colspan="8" style="text-align: right;">{{__('general.net_amount')}} (After Payment Gateway Charges) :</td>
                <td style="text-align: left;"> {{ number_format($net_amount, 2, '.', '') }} </td>
            </tr>
        @endif
        </tbody>
    </table>
@endsection
