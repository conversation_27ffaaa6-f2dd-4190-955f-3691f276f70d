@extends('reports.layout')
@section('content')
    <table>
        <thead>
        <tr>
            <th>{{__('general.number')}}</th>
            <th>{{__('general.member_no')}}</th>
            <th>{{__('general.name')}}</th>
            <th>{{__('general.class')}}</th>
            <th>{{__('library.call_no')}}</th>
            <th>{{__('library.book_title')}}</th>
            <th>{{__('library.loan_date')}}</th>
            <th>{{__('general.due_date')}}</th>
            <th>{{__('general.return_date')}}</th>
            <th>{{__('general.balance')}}</th>
            <th>{{__('library.book_loan_status')}}</th>
        </tr>
        </thead>
        <tbody>
        @foreach($data as $book_loan)
            <tr>
                <td>{{$loop->iteration}}</td>
                <td>{{$book_loan['member_number']}}</td>
                <td>{{$book_loan['name']}}</td>
                <td>{{$book_loan['class_name']}}</td>
                <td>{{$book_loan['call_no']}}</td>
                <td>{{$book_loan['title']}}</td>
                <td>{{$book_loan['loan_date']}}</td>
                <td>{{$book_loan['due_date']}}</td>
                <td>{{$book_loan['return_date'] ?: '-'}}</td>
                <td>{{$book_loan['balance']}}</td>
                <td>{{$book_loan['loan_status']}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection



