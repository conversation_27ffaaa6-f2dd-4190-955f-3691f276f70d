@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h3>{{ __('general.outstanding_balance_report_title') }}</h3>
        <h3>
            {{ __('general.print_date') }} : {{ App\Helpers\ReportHelper::getPrintDate() }}
        </h3>
    </div>
    @php
        $available_locales = App\Helpers\ConfigHelper::getAvailableLocales();
        $colspan = count($available_locales) + 5;
        $header_class_colspan = count($available_locales) + 2;
        $header_teacher_guardian_colspan = 3;
    @endphp

    @if( isset($data['students_group_by_semester_class']) && count($data['students_group_by_semester_class']) > 0 )
        @foreach($data['students_group_by_semester_class'] as $class)
            <table style="margin-bottom: 30px;">
                <thead>
                <tr>
                    <td colspan="{{ $header_class_colspan }}" style="border-style: none;">{{ __('general.class') }} : {{ $class['class_details']['class_name'] }}</td>
                    <td colspan="{{ $header_teacher_guardian_colspan }}" style="text-align: right; border-style: none;">{{ __('general.teacher_guardian') }} : {{ $class['class_details']['homeroom_teacher'] }}</td>
                </tr>
                <tr>
                    <th style="text-align: center;">{{ __('general.number') }}</th>
                    <th style="text-align: center;">{{ __('general.student_no') }}</th>
                    @foreach($available_locales as $locale)
                        <th style="text-align: center;">{{ __('general.name') }}</th>
                    @endforeach
                    <th style="text-align: center;">{{ __('general.fee') }}</th>
                    <th style="text-align: center;">{{ __('general.month') }}</th>
                    <th style="text-align: center;">{{ __('general.amount_with_currency', ['currency' => config('school.currency_code')]) }}</th>
                </tr>
                </thead>

                <tbody>
                @if( isset($class['students']) )
                    @foreach($class['students'] as $student)
                        <tr>
                            <td style="width: 5%;text-align: center;">{{ $student['no'] }}</td>
                            <td style="width: 10%;text-align: center;">{{ $student['student_no'] }}</td>
                            @foreach($available_locales as $locale)
                                <td style="width: 10%;text-align: center;">{{ $student['name'][$locale] }}</td>
                            @endforeach
                            <td style="width: 55%;text-align: left;">{{ $student['description'] }}</td>
                            <td style="width: 10%;text-align: center;">{{ $student['month_concat_string'] }}</td>
                            <td style="width: 10%;text-align: right;">{{ $student['student_total_amount'] }}</td>
                        </tr>
                    @endforeach
                    <tr>
                        <td colspan="{{$colspan - 1}}" style="text-align: right;">{{ __('general.total') }}</td>
                        <td style="text-align: right;">{{ $class['class_details']['class_total_amount'] }}</td>
                    </tr>
                @else
                    <tr>
                        <td colspan="{{$colspan}}" style="text-align: center;">{{ __('general.no_data_available') }}</td>
                    </tr>
                @endif
                </tbody>
            </table>
        @endforeach
    @endif

    <table style="margin-bottom: 30px; width: 40%;">
        <thead>
        <tr>
            <th style="text-align: center;">{{ __('general.month') }}</th>
            <th style="text-align: center;">{{ __('general.no_of_debtors') }}</th>
            <th style="text-align: center;">{{ __('general.total') }}</th>
        </tr>
        </thead>
        <tbody>
        @if( count($data['total_debtors_and_arrears_group_by_year_month']) > 0 )
            @foreach($data['total_debtors_and_arrears_group_by_year_month'] as $month => $total_debtors_and_arrears)
                <tr>
                    <td style="width: 10%;text-align: center;">{{ $month }}</td>
                    <td style="width: 10%;text-align: center;">{{ $total_debtors_and_arrears['total_debtors'] }}</td>
                    <td style="width: 10%;text-align: right;">{{ $total_debtors_and_arrears['total_arrears'] }}</td>
                </tr>
            @endforeach
            <tr>
                <td colspan="2" style="text-align: right;">{{ __('general.total') }}</td>
                <td style="text-align: right;">{{ $data['all_classes_total_arrears'] }}</td>
            </tr>
        @else
            <tr>
                <td colspan="3">{{ __('general.no_data_available') }}</td>
            </tr>
        @endif
        </tbody>
    </table>

    <table style="width: 80%;">
        <thead>
        <tr>
            <th style="text-align: center;">{{ __('general.consecutive_months_in_arrears') }}</th>
            <th style="text-align: center;">{{ __('general.no_of_debtors') }}</th>
            <th style="text-align: center;">{{ __('general.total_arrears') }}</th>
        </tr>
        </thead>
        <tbody>
        @if( count($data['total_debtors_and_arrears_group_by_consecutive_year_month']) > 0 )
            @foreach($data['total_debtors_and_arrears_group_by_consecutive_year_month'] as $consecutive_month => $total_debtors_and_arrears)
                <tr>
                    <td style="width: 10%;text-align: center;">{{ $consecutive_month }}</td>
                    <td style="width: 10%;text-align: center;">{{ $total_debtors_and_arrears['total_debtors'] }}</td>
                    <td style="width: 10%;text-align: center;">{{ $total_debtors_and_arrears['total_arrears'] }}</td>
                </tr>
            @endforeach
            <tr>
                <td style="text-align: center;">{{ __('general.total') }}</td>
                <td style="text-align: center;">{{ $data['all_classes_total_debtors'] }}</td>
                <td style="text-align: center;">{{ $data['all_classes_total_arrears'] }}</td>
            </tr>
        @else
            <tr>
                <td colspan="3">{{ __('general.no_data_available') }}</td>
            </tr>
        @endif
        </tbody>
    </table>
@endsection
