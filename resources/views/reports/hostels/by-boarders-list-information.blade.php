@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h2 style="margin: 0;">{{ __('hostel.title.boarders_list_info', ['semester_setting' => $semester_setting->name]) }}</h2>
        <div style="margin: 0; margin-bottom: 5px;">{{ __('general.print_date') }} : {{ now()->format('d M Y') }}</div>
    </div>

    <table>
        <thead>
        <tr>
            <th>{{ __('general.room') }}</th>
            <th>{{ __('general.bed_number') }}</th>
            <th>{{ __('general.student_no') }}</th>
            <th>{{ __('general.name') }}</th>
            <th>{{ __('general.ic_no') }}</th>
            <th>{{ __('general.address') }}</th>
            <th>{{ __('general.phone_no') }}</th>
            <th>{{ __('general.guardian') }}</th>
            <th>{{ __('general.class') }}</th>
            <th>{{ __('general.check_in_date') }}</th>
        </tr>
        </thead>

        <tbody>
        @foreach($data as $record)
            @php
                $guardian_name = '';

                if (!empty($record['guardians'])) {
                    $guardian = $record['guardians'][0];

                    $zh_name = $guardian['name']['zh'] ?? '';
                    $en_name = $guardian['name']['en'] ?? '';

                    $guardian_name = trim("$zh_name $en_name");
                }
            @endphp
            <tr>
                <td>{{ $record['room_name'] }}</td>
                <td>{{ $record['bed_name'] }}</td>
                <td>{{ $record['student_number'] }}</td>
                <td>{{ $record['student_name']['zh'] ?? '' }} {{ $record['student_name']['en'] ?? '' }}</td>
                <td>{{ $record['student_nric'] }}</td>
                <td>{{ $record['student_address'] }}</td>
                <td>{{ $record['student_phone_number'] }}</td>
                <td>{{ $guardian_name }}</td>
                <td>{{ $record['class_name']['en'] ?? '' }}</td>
                <td>{{ $record['start_date'] }}</td>
            </tr>
        @endforeach
        </tbody>

    </table>
@endsection
