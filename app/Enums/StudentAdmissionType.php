<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum StudentAdmissionType: string implements IEnum
{
    use EnumOption;

    case NEW = 'NEW';
    case TRANSFERRED = 'TRANSFERRED';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::NEW => 'New',
            self::TRANSFERRED => 'Transferred',
        };
    }
}
