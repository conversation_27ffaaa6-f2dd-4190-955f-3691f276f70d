<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum PushNotificationPlatform: string implements IEnum
{
    use EnumOption;
    case PLATFORM_ANDROID = 'ANDROID';
    case PLATFORM_IOS = 'IOS';
    case PLATFORM_WEB = 'WEB';
    case PLATFORM_HUAWEI = 'HUAWEI';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::PLATFORM_ANDROID => 'Android',
            self::PLATFORM_IOS => 'iOS',
            self::PLATFORM_HUAWEI => 'Huawei',
            self::PLATFORM_WEB => 'Web',
        };
    }
}
