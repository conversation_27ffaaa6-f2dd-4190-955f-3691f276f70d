<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum ExportType: string implements IEnum
{
    use EnumOption;

    case PDF = 'PDF';
    case EXCEL = 'EXCEL';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::PDF => 'Pdf',
            self::EXCEL => 'Excel',
        };
    }

    public static function getExtension($value): string
    {
        return match ($value) {
            self::PDF => '.pdf',
            self::EXCEL => '.xlsx',
        };
    }
}
