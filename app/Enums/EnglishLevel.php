<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum EnglishLevel: string implements IEnum
{
    use EnumOption;

    case STARTER = 'STARTER';
    case ELEMENTARY = 'ELEMENTARY';
    case PRE_INTERMEDIATE = 'PRE_INTERMEDIATE';
    case INTERMEDIATE = 'INTERMEDIATE';
    case UPPER_INTERMEDIATE = 'UPPER_INTERMEDIATE';
    case ADVANCED_1 = 'ADVANCED_1';
    case ADVANCED_2 = 'ADVANCED_2';
    case ADVANCED_3 = 'ADVANCED_3';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::STARTER => 'Starter',
            self::ELEMENTARY => 'Elementary',
            self::PRE_INTERMEDIATE => 'Pre-Intermediate',
            self::INTERMEDIATE => 'Intermediate',
            self::UPPER_INTERMEDIATE => 'Upper Intermediate',
            self::ADVANCED_1 => 'Advanced 1',
            self::ADVANCED_2 => 'Advanced 2',
            self::ADVANCED_3 => 'Advanced 3',
        };
    }
}
