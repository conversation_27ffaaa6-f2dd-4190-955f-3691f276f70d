<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum AttendanceCheckOutStatus: string implements IEnum
{
    use EnumOption;

    case ON_TIME = 'ON_TIME';
    case LEFT_EARLY = 'LEFT_EARLY';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::ON_TIME => 'On Time',
            self::LEFT_EARLY => 'Left Early'
        };
    }
}
