<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum Module: string implements IEnum
{
    use EnumOption;
    case HOSTEL = 'HOSTEL';
    case HOSTEL_BLOCK = 'HOSTEL_BLOCK';
    case OTHERS = 'OTHERS';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::HOSTEL => 'Hostel',
            self::HOSTEL_BLOCK => 'Hostel Block',
            self::OTHERS => 'Others',
        };
    }

}
