<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum ContractorDepartment: string implements IEnum
{
    use EnumOption;

    case COCURRICULUM = 'COCURRICULUM';
    case SPORTS = 'SPORTS';
    case ALL = 'ALL';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::COCURRICULUM => 'Cocurriculum',
            self::SPORTS => 'Sports',
            self::ALL => 'All',
        };
    }
}
