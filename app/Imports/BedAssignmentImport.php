<?php

namespace App\Imports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStartRow;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class BedAssignmentImport implements WithMapping, WithStartRow, SkipsEmptyRows
{
    public function map($row): array
    {
        // trim whitespaces from all cells
        $row = array_map('trim', $row);

        Validator::make($row, [
            '0' => ['required'],
            '1' => ['required'],
            '2' => ['required'],
            '3' => ['required'],
            '4' => ['required', function ($attribute, $value, $fail) {
                try {
                    Date::excelToTimestamp($value);
                } catch (\Throwable $e) {
                    return $fail('invalid date format in excel file');
                }
                return true;
            }],
            '5' => ['nullable'],
        ], [], [
            '0' => 'employee/student number',
            '1' => 'block',
            '2' => 'room',
            '3' => 'bed',
            '4' => 'start date',
            '5' => 'remarks',
        ])->validate();

        return [
            'number' => (string) $row[0],
            'block_code' => (string) $row[1],
            'room' => (string) $row[2],
            'bed' => (string) $row[3],
            'start_date' => Carbon::parse(Date::excelToTimestamp($row[4]))->toDateString(),
            'remarks' => (string) $row[5],
        ];
    }

    public function startRow(): int
    {
        return 2;
    }
}
