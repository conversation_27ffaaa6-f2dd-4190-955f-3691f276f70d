<?php

namespace App\Rules;

use App\Enums\ContractorDepartment;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class ClassSubjectTrainerValidation implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $contractor_ids = collect($value);

        $contractors = DB::table('contractors')
            ->select(['id', 'department'])
            ->whereIn('id', $contractor_ids->all())
            ->get();

        // Check if all contractors exist
        if ($contractor_ids->count() !== $contractors->count()) {
            $fail(__('validation.in'));
            return;
        }

        // Check if all contractors are from COCURRICULUM department
        $invalid_contractors = $contractors->where('department', '!=', ContractorDepartment::COCURRICULUM->value);

        if ($invalid_contractors->isNotEmpty()) {
            $fail(__('validation.custom.class_subject.only_cocu_trainer'));
        }
    }
}
