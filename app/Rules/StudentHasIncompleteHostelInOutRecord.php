<?php

namespace App\Rules;

use App\Repositories\StudentRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class StudentHasIncompleteHostelInOutRecord implements ValidationRule
{
    private StudentRepository $studentRepository;

    public function __construct()
    {
        $this->studentRepository = app(StudentRepository::class);
    }
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if($this->studentRepository->find($value)->getLatestIncompleteHostelInOutRecord()) {
            $fail(__('validation.has_incomplete_hostel_in_out_record'));
        }
    }
}
