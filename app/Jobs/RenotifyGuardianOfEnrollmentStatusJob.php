<?php

namespace App\Jobs;

use App\Mail\EnrollmentStatusMail;
use App\Models\Enrollment;
use App\Services\ISmsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class RenotifyGuardianOfEnrollmentStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public mixed $enrollmentId;

    public function __construct($enrollment_id)
    {
        $this->onQueue('enrollment')->onConnection('enrollment');
        $this->enrollmentId = $enrollment_id;
    }

    public function handle(): void
    {
        $enrollment = Enrollment::with('enrollmentUser')
            ->where('id', $this->enrollmentId)
            ->first();

        if (!$enrollment) {
            return;
        }

        $email = $enrollment->enrollmentUser?->email;

        Mail::to($email)->send(new EnrollmentStatusMail($enrollment));

        $phone_number = $enrollment->enrollmentUser?->phone_number;

        if ($phone_number) {
            $sms_message = "滨华中学：您好，恭喜贵子女已被录取为本校2026年初一新生。请留意电邮，我们将与您联系安排注册事宜。\n
Congratulations! Your child has been admitted to Pin Hwa High School for 2026 Junior 1. We will send you an email with registration details.";
            app()->make(ISmsService::class)->sendSms($phone_number, $sms_message);
        }
    }
}
