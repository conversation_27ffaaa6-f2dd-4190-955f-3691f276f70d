<?php

namespace App\Providers;

use App\Events\ClassSubjectStudentUpdatedEvent;
use App\Events\ExamSubjectExemptionEvent;
use App\Events\InvoicePaidEvent;
use App\Events\InvoiceVoidedEvent;
use App\Events\PaymentCompletedEvent;
use App\Listeners\CreateAdvanceTransactions;
use App\Listeners\GenerateAndSendReceipt;
use App\Listeners\MarkBillableItemAsPaid;
use App\Listeners\InvoiceVoidedCallback;
use App\Listeners\SyncStudentGradingFramework;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        InvoicePaidEvent::class => [
            MarkBillableItemAsPaid::class,
            CreateAdvanceTransactions::class,
        ],
        InvoiceVoidedEvent::class => [
            InvoiceVoidedCallback::class,
        ],
        PaymentCompletedEvent::class => [
            GenerateAndSendReceipt::class,
        ],
        ClassSubjectStudentUpdatedEvent::class => [
            SyncStudentGradingFramework::class
        ],
        ExamSubjectExemptionEvent::class => [
            SyncStudentGradingFramework::class
        ]
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
