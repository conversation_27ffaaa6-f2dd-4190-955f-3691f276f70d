<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EcommerceProductAvailableDate extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'available_date',
        'product_group_id'
    ];

    protected $casts = [
        'available_date' => 'date'
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(EcommerceProduct::class);
    }

    public function productGroup(): BelongsTo
    {
        return $this->belongsTo(EcommerceProductGroup::class);
    }
}
