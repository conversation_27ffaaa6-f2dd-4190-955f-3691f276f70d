<?php

namespace App\Models;

use App\Enums\HostelMeritDemeritType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class HostelMeritDemeritSetting extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'type' => HostelMeritDemeritType::class
    ];

    public function hostelRewardPunishmentSettings(): HasMany
    {
        return $this->hasMany(HostelRewardPunishmentSetting::class, 'hostel_merit_demerit_setting_id');
    }

    public function hasHostelRewardPunishmentSettings(): bool
    {
        return $this->hostelRewardPunishmentSettings()->exists();
    }
}
