<?php

namespace App\Models;

use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ComprehensiveAssessmentQuestion extends Model
{
    use HasFactory, HasTranslations;

    protected $guarded = ['id'];

    public $translatable = ['question'];

    public function comprehensiveAssessmentCategory(): BelongsTo
    {
        return $this->belongsTo(ComprehensiveAssessmentCategory::class);
    }
}
