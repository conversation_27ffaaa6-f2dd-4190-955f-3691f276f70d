<?php

namespace App\Models;

use App\Enums\Gender;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Translatable\HasTranslations;

class LibraryMemberDetail extends Model
{
    use HasFactory, HasTranslations;

    public $translatable = ['name'];

    protected $fillable = [
        'library_member_id',
        'register_date',
        'name',
        'gender',
        'nric',
        'date_of_birth',
        'race_id',
        'religion_id',
        'address',
        'postcode',
        'city',
        'state_id',
        'country_id',
        'phone_no',
        'email',
    ];

    protected $casts = [
        'register_date' => 'date',
        'gender' => Gender::class,
        'date_of_birth' => 'date',
    ];

    public function race(): BelongsTo
    {
        return $this->belongsTo(Race::class);
    }

    public function religion(): BelongsTo
    {
        return $this->belongsTo(Religion::class);
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}
