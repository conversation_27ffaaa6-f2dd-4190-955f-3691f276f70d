<?php

namespace App\Models;

use App\Traits\Models\HasTranslations;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResultSourceExam extends Model
{
    use HasFactory;

    protected $table = 'result_source_exams';

    protected $guarded = ['id'];

    public function resultSource() {
        return $this->belongsTo(ResultSource::class);
    }
}
