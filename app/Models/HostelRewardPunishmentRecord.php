<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use OwenIt\Auditing\Contracts\Auditable;

class HostelRewardPunishmentRecord extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    protected $casts = [
        'date' => 'date',
    ];

    public function personInCharge(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'person_in_charge_id');
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function hostelRewardPunishmentSetting(): BelongsTo
    {
        return $this->belongsTo(HostelRewardPunishmentSetting::class, 'hostel_reward_punishment_setting_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function hostelBedByYear(): MorphMany
    {
        return $this->morphMany(HostelBedByYearView::class, 'assignable');
    }
}
