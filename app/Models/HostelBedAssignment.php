<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use OwenIt\Auditing\Contracts\Auditable;

class HostelBedAssignment extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    protected $table = 'hostel_bed_assignments';

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function assignable(): MorphTo
    {
        return $this->morphTo();
    }

    public function bed(): BelongsTo
    {
        return $this->belongsTo(HostelRoomBed::class, 'hostel_room_bed_id');
    }

    public function previousBed(): BelongsTo
    {
        return $this->belongsTo(HostelRoomBed::class, 'previous_hostel_room_bed_id');
    }

    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }
}
