<?php

namespace App\Models;

use App\Enums\GuardianType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphPivot;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Table to link guardian to Enrollment | Student
 */
class GuardianStudent extends MorphPivot
{
    use HasFactory;

    protected $table = 'guardian_student';

    public $timestamps = false;
    protected $casts = [
        'type' => GuardianType::class
    ];

    /**
     * Get the parent studentGuardianable model (Enrollment | Student).
     */
    public function studentGuardianable(): MorphTo
    {
        return $this->morphTo();
    }
}
