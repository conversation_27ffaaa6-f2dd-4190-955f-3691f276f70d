<?php

namespace App\Models;

use App\Interfaces\Printable;
use App\Interfaces\TransactionLoggable;
use App\Interfaces\Userable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class BillingDocument extends Model implements Printable, TransactionLoggable, Auditable
{
    /**
     * LX 2024-09-23: One thing to note is our Billing Document is unable to cater for multiple tax codes in 1 document due to less advance payment calculation.
     * We have insufficient knowledge to know how to correctly deduct the advance amount from each group of taxes to properly calculate the final tax amount.
     * So for now the Billing Document module will only support 1 tax code at a time, since in Malaysia schools will not be taxed and this is not a valid concern at this point in time.
     */
    use HasFactory, \OwenIt\Auditing\Auditable;

    const STATUS_DRAFT = 'DRAFT';
    const STATUS_CONFIRMED = 'CONFIRMED';
    const STATUS_VOIDED = 'VOIDED';
    const STATUS_POSTED = 'POSTED';

    const PAYMENT_STATUS_UNPAID = 'UNPAID';
    const PAYMENT_STATUS_PAID = 'PAID';
    const PAYMENT_STATUS_PARTIAL = 'PARTIAL';

    const TYPE_INVOICE = 'INVOICE';
    const TYPE_ADVANCE_INVOICE = 'ADVANCE_INVOICE';
    const TYPE_CREDIT_NOTE = 'CREDIT_NOTE';
    const TYPE_DEBIT_NOTE = 'DEBIT_NOTE';

    const SUB_TYPE_FEES = 'FEES';
    const SUB_TYPE_WALLET_TOPUP = 'WALLET';
    const SUB_TYPE_ENROLLMENT_EXAM_FEES = 'ENROLLMENT_EXAM_FEES';
    const SUB_TYPE_OTHERS = 'OTHERS';
    const SUB_TYPE_ECOMMERCE = 'ECOMMERCE';
    const SUB_TYPE_HOSTEL_SAVINGS_ACCOUNT = 'HOSTEL_SAVINGS_ACCOUNT';
    const SUB_TYPE_ENROLLMENT_FEES = 'ENROLLMENT_FEES';

    const CLASSIFICATION_AR = 'AR';
    const CLASSIFICATION_AP = 'AP';

    const CLASSIFICATION_MAPPING = [
        self::TYPE_INVOICE => self::CLASSIFICATION_AR,
        self::TYPE_ADVANCE_INVOICE => self::CLASSIFICATION_AR,
        self::TYPE_CREDIT_NOTE => self::CLASSIFICATION_AR,
        self::TYPE_DEBIT_NOTE => self::CLASSIFICATION_AR,
    ];

    const DOCUMENT_NUMBER_PREFIX = [
        self::TYPE_INVOICE => 'INV',
        self::TYPE_ADVANCE_INVOICE => 'AINV',
        self::TYPE_CREDIT_NOTE => 'CN',
        self::TYPE_DEBIT_NOTE => 'DN',
    ];

    const ALL_STATUSES = [
        self::STATUS_POSTED,
        self::STATUS_CONFIRMED,
        self::STATUS_VOIDED,
        self::STATUS_DRAFT,
    ];

    const ALL_PAYMENT_STATUSES = [
        self::PAYMENT_STATUS_UNPAID,
        self::PAYMENT_STATUS_PAID,
        self::PAYMENT_STATUS_PARTIAL,
    ];

    const ALL_TYPES = [
        self::TYPE_INVOICE,
        self::TYPE_ADVANCE_INVOICE,
        self::TYPE_CREDIT_NOTE,
        self::TYPE_DEBIT_NOTE,
    ];

    const ALL_SUB_TYPES = [
        self::SUB_TYPE_FEES,
        self::SUB_TYPE_WALLET_TOPUP,
        self::SUB_TYPE_ENROLLMENT_EXAM_FEES,
        self::SUB_TYPE_ENROLLMENT_FEES,
        self::SUB_TYPE_OTHERS,
        self::SUB_TYPE_ECOMMERCE,
        self::SUB_TYPE_HOSTEL_SAVINGS_ACCOUNT,
    ];

    protected $table = 'billing_documents';

    protected $guarded = ['id'];

    protected $casts = [
        'posting_date' => 'datetime',
        'paid_at' => 'datetime',
    ];

    public function isFinalized()
    {
        return $this->status === self::STATUS_CONFIRMED || $this->status === self::STATUS_POSTED;
    }

    public function billTo()
    {
        return $this->morphTo();
    }

    public function legalEntity()
    {
        return $this->belongsTo(LegalEntity::class);
    }

    public function lineItems()
    {
        return $this->hasMany(BillingDocumentLineItem::class);
    }

    // Even though code-wise we support multiple payments per billing document, realistically there will only be 1 payment for every billing document
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function hasPayment()
    {
        return $this->payments()->exists();
    }

    public function paymentRequests() {
        return $this->hasMany(PaymentRequest::class);
    }

    public function paymentGatewayLogs(): HasMany
    {
        return $this->hasMany(PaymentGatewayLog::class);
    }

    public function getTotalPaidAmount() {
        return $this->payments()->sum('amount_received');
    }

    public function tax()
    {
        return $this->belongsTo(Tax::class, 'tax_code', 'code');
    }

    public function paymentTerm()
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function remitTo()
    {
        return $this->belongsTo(BankAccount::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'code');
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function getPrintableHtml(): string
    {
        return view('pdf.invoice', [
            'bill_to_name' => $this->bill_to_name,
            'bill_to_type' => Userable::USER_LABELS[$this->bill_to_type],
            'bill_to_reference_no' => $this->bill_to_reference_number,
            'line_items' => $this->lineItems,
            'invoice' => $this,
        ]);
    }

    public function getPrintableFileName(string $file_extension): string
    {
        return 'invoice-' . $this->reference_no . '-' . now()->format('YmdHisv') . '.' . $file_extension;
    }

    public function isRequirePayment()
    {
        return in_array($this->type, [self::TYPE_INVOICE, self::TYPE_ADVANCE_INVOICE]);
    }

    public function isValidStatusForPayment(): bool
    {
        return $this->status == self::STATUS_CONFIRMED && $this->payment_status == self::PAYMENT_STATUS_UNPAID;
    }
}
