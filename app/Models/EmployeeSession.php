<?php

namespace App\Models;

use App\Interfaces\Deletable;
use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmployeeSession extends Model implements Deletable
{
    use HasFactory, HasTranslations;

    protected $table = 'master_employee_sessions';

    protected $guarded = ['id'];

    public $translatable = ['name'];

    public function employeeSessionSettings(): HasMany
    {
        return $this->hasMany(EmployeeSessionSetting::class, 'employee_session_id');
    }

    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class, 'employee_session_id');
    }

    public function canBeDeleted(): bool
    {
        if ($this->employees()->count()) {
            return false;
        }

        return true;
    }
}
