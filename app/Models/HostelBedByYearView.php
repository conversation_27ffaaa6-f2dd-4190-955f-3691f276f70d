<?php

namespace App\Models;

use App\Interfaces\MaterializedViewModel;
use App\Jobs\RefreshMaterializedViewJob;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class HostelBedByYearView extends Model implements MaterializedViewModel
{
    // This is a view table
    protected $table = 'hostel_bed_by_year_views';

    public $timestamps = false;

    public function assignable(): MorphTo
    {
        return $this->morphTo();
    }

    public function bed(): BelongsTo
    {
        return $this->belongsTo(HostelRoomBed::class, 'hostel_room_bed_id');
    }

    public static function refreshViewTable($queue = true): void
    {
        if ($queue) {
            RefreshMaterializedViewJob::dispatch('hostel_bed_by_year_views');
        } else {
            RefreshMaterializedViewJob::dispatchSync('hostel_bed_by_year_views');
        }
    }
}
