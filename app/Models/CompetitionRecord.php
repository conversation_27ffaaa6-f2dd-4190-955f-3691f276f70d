<?php

namespace App\Models;

use App\Enums\CompetitionBonusType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class CompetitionRecord extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    protected $casts = [
        'type_of_bonus' => CompetitionBonusType::class,
    ];

    public function competition(): BelongsTo
    {
        return $this->belongsTo(Competition::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function award(): BelongsTo
    {
        return $this->belongsTo(Award::class, 'award_id');
    }

    public function semesterClass(): BelongsTo
    {
        return $this->belongsTo(SemesterClass::class);
    }
}
