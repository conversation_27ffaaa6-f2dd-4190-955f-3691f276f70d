<?php

namespace App\Models;

use App\Enums\RewardPunishmentRecordStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class RewardPunishmentRecord extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    protected $casts = [
        'notification_sent_at' => 'datetime',
        'status' => RewardPunishmentRecordStatus::class,
    ];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    // !!Important: This function must be used with orderBy()->first() to get the correct class
    // Refer to RewardPunishmentRecordResource
    public function studentLatestPrimaryClasses(): HasMany
    {
        return $this->hasMany(LatestPrimaryClassBySemesterSettingView::class, 'student_id', 'student_id');
    }

    public function rewardPunishment(): BelongsTo
    {
        return $this->belongsTo(RewardPunishment::class, 'reward_punishment_id');
    }
}
