<?php

namespace App\Models;

use App\Enums\CardStatus;
use App\Enums\ClassType;
use App\Enums\Gender;
use App\Enums\StudentAdmissionType;
use App\Enums\StudentLeaveStatus;
use App\Helpers\ConfigHelper;
use App\Interfaces\AttendanceRecordable;
use App\Interfaces\Billable;
use App\Interfaces\HostelRoomBedAssignable;
use App\Interfaces\LeaveApplicable;
use App\Interfaces\StatusChangeable;
use App\Interfaces\Userable;
use App\Traits\Models\HasMediaInteractions;
use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;

class Student extends Model implements Userable, HasMedia, Billable, HostelRoomBedAssignable, StatusChangeable, LeaveApplicable, Auditable, AttendanceRecordable
{
    use HasFactory, HasMediaInteractions, HasTranslations, \OwenIt\Auditing\Auditable;

    const LEAVE = 'LEAVE';
    const GRADUATE = 'GRADUATE';
    const RETURN = 'RETURN';

    public $translatable = ['name'];
    protected $guarded = ['id'];
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'custom_field' => 'json',
        'leave_status' => StudentLeaveStatus::class,
        'gender' => Gender::class,
        'admission_type' => StudentAdmissionType::class,
    ];

    public function userable(): BelongsTo
    {
        return $this->belongsTo(UserableView::class, 'user_id', 'user_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function cards(): MorphMany
    {
        return $this->morphMany(Card::class, 'userable');
    }

    public function firstActiveCard(): MorphOne
    {
        return $this->morphOne(Card::class, 'userable')->latest('id')->where('status', CardStatus::ACTIVE->value);
    }

    public function orders(): MorphMany
    {
        return $this->morphMany(EcommerceOrder::class, 'buyer_userable');
    }

    public function calendarTarget(): MorphMany
    {
        return $this->morphMany(CalendarTarget::class, 'calendarTargetable');
    }

    public function libraryMember(): MorphOne
    {
        return $this->morphOne(LibraryMember::class, 'userable');
    }

    public function admissionGrade(): BelongsTo
    {
        return $this->belongsTo(Grade::class, 'admission_grade_id');
    }

    public function race(): BelongsTo
    {
        return $this->belongsTo(Race::class, 'race_id');
    }

    public function religion(): BelongsTo
    {
        return $this->belongsTo(Religion::class, 'religion_id');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

//    public function birthplace(): BelongsTo
//    {
//        return $this->belongsTo(Country::class, 'birthplace_id');
//    }

    public function nationality(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'nationality_id');
    }

    public function classes(): HasMany
    {
        return $this->hasMany(StudentClass::class, 'student_id');
    }

    public function societyPositions(): HasMany
    {
        return $this->hasMany(StudentSocietyPosition::class, 'student_id');
    }

    public function inactivePrimaryClasses(): HasMany
    {
        return $this->hasMany(StudentClass::class, 'student_id')
            ->where('class_type', ClassType::PRIMARY)
            ->where('is_active', false);
    }

    public function latestPrimaryClass(): HasOne
    {
        return $this->hasOne(StudentClass::class, 'student_id')
            ->where('class_type', ClassType::PRIMARY)
            ->orderBy('class_enter_date', 'desc');
    }

    public function currentSemesterPrimaryClass(): HasOne
    {
        return $this->hasOne(StudentClass::class, 'student_id')
            ->where('class_type', ClassType::PRIMARY)
            ->whereHas('semesterSetting', function ($query) {
                $query->where('is_current_semester', true);
            })
            ->where('is_active', true);
            //->orderBy('class_enter_date', 'desc');
    }

    public function latestPrimaryClassInSemester($semester_setting_id)
    {
        return $this->hasMany(StudentClass::class, 'student_id')
            ->where([
                'class_type' => ClassType::PRIMARY,
                'is_latest_class_in_semester' => true,
                'semester_setting_id' => $semester_setting_id
            ])
            ->first();
    }

    public function latestEnglishClassInSemester($semester_setting_id)
    {
        return $this->hasMany(StudentClass::class, 'student_id')
            ->where([
                'class_type' => ClassType::ENGLISH,
                'is_latest_class_in_semester' => true,
                'semester_setting_id' => $semester_setting_id
            ])
            ->first();
    }

    public function getPrimaryClassOfSemester($semester_setting_id)
    {

        // todo: refactor this when MAT view is ready
        $student_classes = $this->classes()
            ->where('class_type', ClassType::PRIMARY)
            ->where('semester_setting_id', $semester_setting_id)
            ->orderBy('class_enter_date', 'desc')
            ->get();

        // if got active, take active as current student class
        $student_class = $student_classes->where('is_active', true)->first();

        if ($student_class === null) {
            // if no active, take the latest class_enter_date as latest student class
            $student_class = $student_classes->first();
        }

        return $student_class;
    }

    public function primaryClass(): HasOne
    {
        return $this->hasOne(StudentClass::class, 'student_id')
            ->where('class_type', ClassType::PRIMARY)
            ->where('is_active', true);
    }

    public function latestPrimaryClassBySemesterSettings(): HasMany
    {
        return $this->hasMany(LatestPrimaryClassBySemesterSettingView::class, 'student_id');
    }

    public function classAndGradeBySemesterSetting(): HasMany
    {
        return $this->hasMany(HistoricalStudentClassAndGrade::class, 'student_id');
    }

    public function primaryClassAndGradeBySemesterSetting(): HasMany
    {
        return $this->classAndGradeBySemesterSetting()->where('class_type', ClassType::PRIMARY);
    }

    public function latestPrimaryClassAndGrade(): HasOne
    {
        return $this->hasOne(CurrentStudentClassAndGrade::class, 'student_id')->where('class_type', ClassType::PRIMARY);
    }

    public function guardians(): MorphToMany
    {
        return $this->morphToMany(Guardian::class, 'studenable', GuardianStudent::class)
            ->withPivot(['type', 'relation_to_student', 'is_primary', 'is_direct_dependant']);
    }

    public function directGuardians(): MorphToMany
    {
        return $this->guardians()->wherePivot('is_direct_dependant', true);
    }

    public function leadershipPositionRecord(): HasOne
    {
        return $this->hasOne(LeadershipPositionRecord::class, 'student_id');
    }

    public function hasExistingBedAssignment(): bool
    {
        return $this->activeHostelBedAssignments()->exists();
    }

    public function activeHostelBedAssignments(): MorphMany
    {
        return $this->morphMany(HostelBedAssignment::class, 'assignable')->whereNull('end_date');
    }

    public function hostelBedByYear(): MorphMany
    {
        return $this->morphMany(HostelBedByYearView::class, 'assignable');
    }

    /**
     * Only get latest first active bed assignment
     */
    public function firstActiveHostelBedAssignment(): MorphOne
    {
        return $this->morphOne(HostelBedAssignment::class, 'assignable')->latest('id')->whereNull('end_date');
    }

    public function getHostelRewardPunishmentRecordsPoints(): array
    {
        $current_year = now()->year;

        $hostel_join_year = $this->beds()->orderBy('start_date', 'desc')->first()?->start_date?->year ?: $current_year;

        $hostel_reward_punishment_deductable_points = ConfigHelper::get('HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS');

        $hostel_reward_punishment_records = $this->hostelRewardPunishmentRecords()
            ->join('hostel_reward_punishment_settings',
                'hostel_reward_punishment_records.hostel_reward_punishment_setting_id', '=',
                'hostel_reward_punishment_settings.id')
            ->whereYear('date', '>=', $hostel_join_year)
            ->selectRaw("EXTRACT('Year' FROM hostel_reward_punishment_records.date) as year, SUM(hostel_reward_punishment_settings.points) as total_points")
            ->groupByRaw("EXTRACT('Year' FROM hostel_reward_punishment_records.date)")
            ->get()
            ->keyBy('year')
            ->all();

        $hostel_reward_punishment_balance = [];
        for ($year = $hostel_join_year; $year <= $current_year; $year++) {
            $points = $hostel_reward_punishment_deductable_points;
            if (isset($hostel_reward_punishment_records[$year])) {
                $points = $hostel_reward_punishment_deductable_points + $hostel_reward_punishment_records[$year]['total_points'];
            }

            $hostel_reward_punishment_balance[] = [
                'year' => $year,
                'points' => (float) $points
            ];
        }

        return $hostel_reward_punishment_balance;
    }

    public function beds(): MorphMany
    {
        return $this->morphMany(HostelBedAssignment::class, 'assignable');
    }

    public function hostelRewardPunishmentRecords(): HasMany
    {
        return $this->hasMany(HostelRewardPunishmentRecord::class, 'student_id');
    }

    public function getPhotoAttribute()
    {
        $media = $this->getMedia('photo')->first();

        if ($media) {
            return $media->getUrl();
//            return $media->getTemporaryUrl(now()->addSeconds(config('filesystems.s3_expires_seconds')));
        }

        return null;
    }

    public function getUserNumberColumnName(): ?string
    {
        return 'student_number';
    }

    public function getUserNumber(): ?string
    {
        return $this->student_number;
    }

    public function getProfilePicture(): ?string
    {
        return $this->photo;
    }

    public function getUserTypeDescription(): string
    {
        return Userable::USER_LABELS[Student::class];
    }

    public function getBillToType(): string
    {
        return get_class($this);
    }

    public function getBillToId(): int
    {
        return $this->id;
    }

    public function getBillToReferenceNumber(): ?string
    {
        return $this->student_number;
    }

    public function getBillToName(): string
    {
        return $this->getUserName() ?? '';
    }

    public function getUserName(): ?string
    {
        return $this->name;
    }

    public function getBillToAddress(): string
    {
        return $this->address ?? '';
    }

    public function getBillToEmail(): ?string
    {
        return $this->email;
    }

    public function getLatestIncompleteHostelInOutRecord(): ?HostelInOutRecord
    {
        return $this->hostelInOutRecords()
            ->whereNull('check_in_datetime')
            ->first();
    }

    public function incompleteHostelInOutRecords(): HasMany
    {
        return $this->hasMany(HostelInOutRecord::class, 'student_id')->whereNull('check_in_datetime');
    }

    // Incomplete record is a record that has not null check_out_datetime and check_in_datetime as null

    public function hostelInOutRecords(): HasMany
    {
        return $this->hasMany(HostelInOutRecord::class, 'student_id');
    }

    public function healthConcern(): BelongsTo
    {
        return $this->belongsTo(HealthConcern::class);
    }

    public function primarySchool(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function pendingStatusChanges(): MorphMany
    {
        return $this->morphMany(PendingStudentEmployeeStatusChange::class, name: 'status_changeable');
    }

    public function gradingFrameworks()
    {
        return $this->hasMany(StudentGradingFramework::class);
    }

    public function activeGradingFramework()
    {
        return $this->hasOne(StudentGradingFramework::class, 'student_id')
            ->where('is_active', true);
    }

    public function reportCards()
    {
        return $this->hasMany(StudentReportCard::class)->where('is_active', true)->orderBy('file_generated_at', 'desc');
    }

    public function guardiansUserNotifiable()
    {
        $guardians = $this->guardians()->wherePivot('is_direct_dependant', true)->with('user')->get();

        return $guardians->pluck('user')->unique()->values();
    }

    public function leaves(): MorphMany
    {
        return $this->morphMany(LeaveApplication::class, 'leave_applicable');
    }

    public function getLeaveApplicableClass(): string
    {
        return self::class;
    }

    public function getLeaveApplicableId()
    {
        return $this->id;
    }

    public function attendances(): MorphMany
    {
        return $this->morphMany(Attendance::class, 'attendance_recordable');
    }

    public function attendancePeriodOverrides(): MorphMany
    {
        return $this->morphMany(AttendancePeriodOverride::class, 'attendance_recordable');
    }

    public function getClass(): string
    {
        return self::class;
    }

    public function getId()
    {
        return $this->id;
    }

    public function periodAttendances(): HasMany
    {
        return $this->hasMany(PeriodAttendance::class, 'student_id');
    }

    public static function getActiveStudents($only_student_ids = [], $excluded_student_ids = [], $select = ['*'])
    {
        $query = self::query()
            ->select($select)
            ->where('is_active', true);

        if (count($only_student_ids) > 0) {
            $query->whereIn('id', $only_student_ids);
        }

        if (count($excluded_student_ids) > 0) {
            $query->whereNotIn('id', $excluded_student_ids);
        }

        return $query;
    }

    public function unpaidItems(): MorphMany
    {
        return $this->morphMany(UnpaidItem::class, 'bill_to');
    }
}
