<?php

namespace App\Models;

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use OwenIt\Auditing\Contracts\Auditable;

class PaymentGatewayLog extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    protected $casts = [
        'type' => PaymentType::class,
        'status' => PaymentStatus::class,
        'provider' => PaymentProvider::class,
        'request_data' => 'array',
        'response_data' => 'array',
        'callback_data' => 'array',
    ];

    public function transactionLoggable(): MorphTo
    {
        return $this->morphTo();
    }

    public function billingDocument()
    {
        return $this->belongsTo(BillingDocument::class);
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function bank()
    {
        return $this->belongsTo(Bank::class);
    }
}
