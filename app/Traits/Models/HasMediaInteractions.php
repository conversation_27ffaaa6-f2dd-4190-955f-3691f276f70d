<?php

namespace App\Traits\Models;

use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;

trait HasMediaInteractions
{
    use InteractsWithMedia;

    public function replaceMedia($collection_name, $media)
    {
        $this->clearMediaCollection($collection_name);
        $this->addMedia($media)->toMediaCollection($collection_name);
    }

    public function replaceMultipleMedia($collection_name, array $medias)
    {
        $this->clearMediaCollection($collection_name);

        foreach ($medias as $media) {
            $this->addMedia($media)->toMediaCollection($collection_name);
        }
    }

    public function registerMediaConversions(?\Spatie\MediaLibrary\MediaCollections\Models\Media $media = null): void
    {
        if (app()->environment('testing')) {
            return;
        }

        $this->addMediaConversion('thumb')
            ->width(200)
            ->nonQueued();
    }
}
