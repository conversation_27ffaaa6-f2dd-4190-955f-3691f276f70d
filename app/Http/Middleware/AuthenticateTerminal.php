<?php

namespace App\Http\Middleware;

use App\Helpers\ErrorCodeHelper;
use App\Models\PosTerminalKey;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateTerminal
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $key = $request->header('X-Terminal-Auth-Key');

        $check = PosTerminalKey::checkTerminalKey($key);

        if (!$check) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::TERMINAL_ERROR, 21001);
        }

        return $next($request);
    }
}
