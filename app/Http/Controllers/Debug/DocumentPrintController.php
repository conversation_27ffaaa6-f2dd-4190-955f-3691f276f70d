<?php

namespace App\Http\Controllers\Debug;

use App\Http\Controllers\Controller;
use App\Interfaces\Userable;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Student;
use App\Models\UnpaidItem;
use App\Models\User;

class DocumentPrintController extends Controller
{
    public function invoice() {

        $invoice = BillingDocument::orderBy('id', 'desc')->first();
        $line_items = $invoice->lineItems;

        return view('pdf.invoice', [
            'bill_to_name' => $invoice->bill_to_name,
            'bill_to_type' => Userable::USER_LABELS[$invoice->bill_to_type],
            'bill_to_reference_no' => $invoice->bill_to_reference_number,
            'line_items' => $line_items,
            'invoice' => $invoice,
        ]);
    }

    public function report_card(){
        return view('pdf.report-cards.pinhwa-template-test');
    }
}
