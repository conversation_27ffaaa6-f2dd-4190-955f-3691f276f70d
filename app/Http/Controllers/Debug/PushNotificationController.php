<?php

namespace App\Http\Controllers\Debug;

use App\Enums\PushNotificationClickAction;
use App\Factories\PushNotificationFactory;
use App\Http\Controllers\Controller;
use App\Http\Resources\ApiResponse;
use App\Models\User;
use Illuminate\Http\Request;

class PushNotificationController extends Controller
{
    public function testSend(Request $request)
    {

        $input = $request->all();

        // test send push notification to a user
        $user = User::where('id', $input['user_id'])->firstOrFail();

        if (!$user->canReceivePushNotification()) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError('User cannot receive push notification. Bad configuration')
                ->setData([
                    'push_notification_token' => $user->push_notification_token,
                    'push_notification_platform' => $user->push_notification_platform,
                ])
                ->setHttpCode(400)
                ->setCode(400)
                ->getResponse();
        }

        $service = PushNotificationFactory::getInstance($user->push_notification_platform);

        $service
            ->setTitle($input['title'] ?? 'Default Title')
            ->setBody($input['body'] ?? 'Default Body')
            ->setClickAction($input['click_action'] ?? PushNotificationClickAction::FLUTTER_NOTIFICATION_CLICK)
            ->setUser($user)
            ->setToken($user->push_notification_token)
            ->setData(["id" => $input['user_inbox_id'] ?? 0])
            ->queuedSend();

        return (new ApiResponse())
            ->setStatus(ApiResponse::STATUS_OK)
            ->setMessage('Push notification sending queued.')
            ->setData([
                'push_notification_token' => $user->push_notification_token,
                'push_notification_platform' => $user->push_notification_platform,
            ])
            ->setHttpCode(200)
            ->getResponse();

    }
}
