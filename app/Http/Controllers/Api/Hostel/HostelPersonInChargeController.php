<?php

namespace App\Http\Controllers\Api\Hostel;

use App\Enums\UserSpecialSettingModule;
use App\Enums\UserSpecialSettingSubModule;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Hostel\HostelPersonInChargeAddRequest;
use App\Http\Requests\Api\Hostel\HostelPersonInChargeIndexRequest;
use App\Http\Requests\Api\Hostel\HostelPersonInChargeRemoveRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\HostelPersonInChargeResource;
use App\Repositories\EmployeeRepository;
use App\Repositories\UserSpecialSettingRepository;
use App\Services\UserSpecialSettingService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class HostelPersonInChargeController extends Controller
{
    use HandlesPagination;

    public function __construct(
        protected UserSpecialSettingService $userSpecialSettingService,
        protected UserSpecialSettingRepository $userSpecialSettingRepository,
    )
    {
    }

    public function index(HostelPersonInChargeIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $input['module'] = UserSpecialSettingModule::HOSTEL->value;
        $input['submodule'] = UserSpecialSettingSubModule::HOSTEL_PIC->value;
        $input['includes'] = ['user.employee'];

        if (!isset($input['order_by'])) {
            // default sorting by employee name
            $input['order_by'] = [
                'employee' => [
                    'name' => 'asc',
                ],
            ];
        }

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->userSpecialSettingService, 'getAllUserSpecialSettings', 'getAllPaginatedUserSpecialSettings');
        $this->determinePagination($api_response, $input, $data);

        return $api_response
            ->setData(HostelPersonInChargeResource::collection($data))
            ->getResponse();
    }

    public function add(HostelPersonInChargeAddRequest $request): JsonResponse
    {
        $input = $request->validated();

        $input['user_id'] = resolve(EmployeeRepository::class)->find($input['employee_id'])->user_id;
        $input['module'] = UserSpecialSettingModule::HOSTEL->value;
        $input['submodule'] = UserSpecialSettingSubModule::HOSTEL_PIC->value;

        $this->userSpecialSettingService->createUserSpecialSetting($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function remove(HostelPersonInChargeRemoveRequest $request): JsonResponse
    {
        $input = $request->validated();

        $user_special_setting = $this->userSpecialSettingRepository->first([
            'module' => UserSpecialSettingModule::HOSTEL->value,
            'submodule' => UserSpecialSettingSubModule::HOSTEL_PIC->value,
            'user_id' => (new EmployeeRepository())->find($input['employee_id'])->user_id,
        ], false);

        $this->userSpecialSettingService->deleteUserSpecialSetting($user_special_setting);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
