<?php

namespace App\Http\Controllers\Api\Hostel;

use App\Enums\BedAssignmentTemplateType;
use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Hostel\HostelBedAssignDownloadTemplateRequest;
use App\Http\Requests\Api\Hostel\HostelBedAssignmentImportRequest;
use App\Http\Requests\Api\Hostel\HostelBedAssignmentSubmitBulkRequest;
use App\Http\Requests\Api\Hostel\HostelBedAssignRequest;
use App\Http\Requests\Api\Hostel\HostelBedChangeRequest;
use App\Http\Requests\Api\Hostel\HostelBedUnassignRequest;
use App\Http\Resources\ApiResponse;
use App\Services\HostelBedAssignmentService;
use App\Services\ReportPrintService;
use Illuminate\Http\JsonResponse;

class HostelBedAssignmentController extends Controller
{
    public function __construct(
        protected HostelBedAssignmentService $hostelBedAssignmentService,
        protected ReportPrintService $reportPrintService,
    )
    {
    }

    /**
     * Download excel template to assign bed
     */
    public function downloadBedAssignmentTemplate(HostelBedAssignDownloadTemplateRequest $request): mixed
    {
        $input = $request->validated();

        $data = $this->hostelBedAssignmentService->getBedAssignmentTemplateData($input);

        $report_data = ['data' => $data];
        $report_view_name = 'templates.bed-assignment-template';
        $file_name = strtolower($data['type']) . '-bed-assignment-template';

        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor(ExportType::EXCEL);

        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    /**
     * Import excel to assign bed
     */
    public function import(HostelBedAssignmentImportRequest $request)
    {
        $input = $request->validated();

        $response = $this->hostelBedAssignmentService
            ->setImportFile($input['file'])
            ->setImportType($input['type'])
            ->transformExcelToCollection()
            ->validateBedAssignmentUniquenessForImport()
            ->validateUserNumberForImport()
            ->validateBlockForImport()
            ->validateRoomForImport()
            ->validateBedForImport();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData([
                'success' => $response->getValidImport(),
                'errors' => $response->getInvalidImport(),
            ])
            ->getResponse();
    }

    /**
     * Submit excel payload to bulk save bed assignment
     */
    public function bulkAssignment(HostelBedAssignmentSubmitBulkRequest $request)
    {
        $input = $request->validated();

        if (isset($input['employees'])) {
            $payload = $input['employees'];
            $type = BedAssignmentTemplateType::EMPLOYEE->value;
        } elseif (isset($input['students'])) {
            $payload = $input['students'];
            $type = BedAssignmentTemplateType::STUDENT->value;
        }

        $assigned_by = auth()->user();

        try {
            $response = $this->hostelBedAssignmentService
                ->setValidImport($payload)
                ->setImportType($type)
                ->validateBedAssignmentUniquenessForImport()
                ->validateUserNumberForImport()
                ->validateBlockForImport()
                ->validateRoomForImport()
                ->validateBedForImport();

            if ($response->hasValidationErrors()) {
                return (new ApiResponse())
                    ->setError([
                        'success' => $response->getValidImport(),
                        'errors' => $response->getInvalidImport(),
                    ])
                    ->getResponse();
            }

            $response->setActor($assigned_by)->insert();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $e) {
            return (new ApiResponse())
                ->setError($e->getMessage())
                ->setCode($e->getCode())
                ->getResponse();
        }
    }

    /**
     * Only for student WITHOUT bed
     */
    public function assign(HostelBedAssignRequest $request): JsonResponse
    {
        $user = auth()->user();

        $input = $request->validated();

        $this->hostelBedAssignmentService->assignBeds($user, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    /**
     * Only student WITH bed
     */
    public function unassign(HostelBedUnassignRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->hostelBedAssignmentService->unassignBeds($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    /**
     * Only student WITH bed
     */
    public function change(HostelBedChangeRequest $request): JsonResponse
    {
        $user = auth()->user();

        $input = $request->validated();

        $this->hostelBedAssignmentService->changeBeds($user, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
