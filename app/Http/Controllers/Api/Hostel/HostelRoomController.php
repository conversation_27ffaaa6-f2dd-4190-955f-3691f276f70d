<?php

namespace App\Http\Controllers\Api\Hostel;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Hostel\HostelRoomCreateRequest;
use App\Http\Requests\Api\Hostel\HostelRoomIndexRequest;
use App\Http\Requests\Api\Hostel\HostelRoomUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\HostelRoomResource;
use App\Models\HostelRoom;
use App\Services\HostelRoomService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class HostelRoomController extends Controller
{
    use HandlesPagination;

    protected HostelRoomService $hostelRoomService;

    public function __construct(HostelRoomService $hostel_room_service)
    {
        $this->hostelRoomService = $hostel_room_service;
    }

    public function index(HostelRoomIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->hostelRoomService, 'getAllHostelRooms', 'getAllPaginatedHostelRooms');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(HostelRoomResource::collection($data))->getResponse();
    }

    public function show(HostelRoom $hostel_room): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new HostelRoomResource($hostel_room))
            ->getResponse();
    }

    public function create(HostelRoomCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $hostel_room = $this->hostelRoomService->createHostelRoom($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new HostelRoomResource($hostel_room))
            ->getResponse();
    }

    public function update(HostelRoom $hostel_room, HostelRoomUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $hostel_room = $this->hostelRoomService->updateHostelRoom($hostel_room, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new HostelRoomResource($hostel_room))
            ->getResponse();
    }

    public function destroy(HostelRoom $hostel_room): JsonResponse
    {
        $this->hostelRoomService->deleteHostelRoom($hostel_room);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
