<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\EnrollmentSession\EnrollmentSessionCreateRequest;
use App\Http\Requests\Api\EnrollmentSession\EnrollmentSessionIndexRequest;
use App\Http\Requests\Api\EnrollmentSession\EnrollmentSessionUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\EnrollmentSessionResource;
use App\Models\EnrollmentSession;
use App\Services\EnrollmentSessionService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class EnrollmentSessionController extends Controller
{
    use HandlesPagination;

    public function __construct(
        protected EnrollmentSessionService $enrollmentSessionService
    ) {
    }

    public function index(EnrollmentSessionIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->enrollmentSessionService, 'getAllEnrollmentSessions', 'getAllPaginatedEnrollmentSessions');
        $this->determinePagination($api_response, $input, $data);

        return $api_response
            ->setData(EnrollmentSessionResource::collection($data))
            ->getResponse();
    }

    public function getFeeSettingConditions(): JsonResponse
    {
        $conditions = $this->enrollmentSessionService->getFeeSettingConditions();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($conditions)
            ->getResponse();
    }

    public function show(EnrollmentSession $enrollment_session): JsonResponse
    {
        $enrollment_session->load(['examSubjects', 'course']);

        $summary = $this->enrollmentSessionService->getSummary($enrollment_session);

        $enrollment_session->setAttribute('summary', $summary);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentSessionResource($enrollment_session))
            ->getResponse();
    }

    public function create(EnrollmentSessionCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment_session = $this->enrollmentSessionService->createEnrollmentSession($input);

        $enrollment_session->load(['examSubjects', 'course']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentSessionResource($enrollment_session))
            ->getResponse();
    }

    public function update(EnrollmentSession $enrollment_session, EnrollmentSessionUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment_session = $this->enrollmentSessionService->updateEnrollmentSession($enrollment_session, $input);

        $enrollment_session->loadMissing(['examSubjects', 'course']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentSessionResource($enrollment_session))
            ->getResponse();
    }

    public function destroy(EnrollmentSession $enrollment_session): JsonResponse
    {
        $this->enrollmentSessionService->deleteEnrollmentSession($enrollment_session);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
