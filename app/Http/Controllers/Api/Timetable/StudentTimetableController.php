<?php

namespace App\Http\Controllers\Api\Timetable;

use App\Enums\Day;
use App\Helpers\ErrorCodeHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Timetable\GetStudentTimetableRequest;
use App\Http\Requests\Api\Timetable\StudentTimetableAttendancePeriodRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Student;
use App\Services\Timetable\StudentTimetableService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class StudentTimetableController extends Controller
{
    protected StudentTimetableService $studentTimetableService;

    public function __construct(StudentTimetableService $studentTimetableService)
    {
        $this->studentTimetableService = $studentTimetableService;
    }

    public function attendancePeriods(StudentTimetableAttendancePeriodRequest $request): JsonResponse
    {
        $input = $request->validated();

        if (isset($input['student_id'])) {
            $this->studentTimetableService->setStudent(Student::findOrFail($input['student_id']));
        }

        if ( !isset($input['student_id']) ) {

            // cache for 15 mins
            // when this is updated, please update CacheStudentAttendancePeriods as well
            $cache_key = "cache-attendance-periods-{$input['period_from']}-{$input['period_to']}";
            $data = Cache::remember($cache_key, 15 * 60, function () use ($input) {
                return $this->studentTimetableService
                        ->setSimpleOutput(true)
                        ->getAttendancePeriods($input['period_from'], $input['period_to']);
            });

        }else{

            $data = $this->studentTimetableService
                ->setSimpleOutput(true)
                ->getAttendancePeriods($input['period_from'], $input['period_to']);

        }

        if (isset($input['with_contractors']) && $input['with_contractors'] == true) {
            $data = $this->studentTimetableService->appendContractorsData($data);
        }

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }

    public function studentTimetable(GetStudentTimetableRequest $request): JsonResponse
    {

        $input = $request->validated();
        $student = \Auth::user()->student;

        if ($student === null) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::TIMETABLE_ERROR, 40007);
        }

        $date = Carbon::parse($input['period']);
        $day = Day::carbonWeekdayToDay($date->dayOfWeek);

        $timetable = $this->studentTimetableService->setStudent($student)
            ->setDayFilter($day)
            ->getDefaultTimetable();

        $attendance_period = $this->studentTimetableService
            ->setStudent($student)
            ->getAttendancePeriods($input['period'], $input['period']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData([
                'timetable' => $timetable,
                'attendance_period' => $attendance_period,
            ])
            ->getResponse();

    }

}
