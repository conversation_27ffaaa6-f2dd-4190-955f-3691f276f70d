<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ConductRecord\ConductRecordCreateOrUpdateRequest;
use App\Http\Requests\Api\ConductRecord\ConductRecordIndexRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\ConductRecordResource;
use App\Models\ConductRecord;
use App\Models\ConductSettingTeacher;
use App\Services\ConductRecordService;
use Illuminate\Http\JsonResponse;

class ConductRecordController extends Controller
{
    protected ConductRecordService $conductRecordService;

    public function __construct(ConductRecordService $conduct_record_service)
    {
        $this->conductRecordService = $conduct_record_service;
    }

    public function index(ConductRecordIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->conductRecordService->getAllConductRecords($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(ConductRecordResource::collection($data))
            ->getResponse();
    }

    public function createOrUpdate(ConductSettingTeacher $conduct_setting_teacher, ConductRecordCreateOrUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->conductRecordService
            ->setConductSettingTeacher($conduct_setting_teacher)
            ->createOrUpdateConductRecords($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function destroy(ConductRecord $conduct_record): JsonResponse
    {
        $this->conductRecordService->deleteConductRecord($conduct_record);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
