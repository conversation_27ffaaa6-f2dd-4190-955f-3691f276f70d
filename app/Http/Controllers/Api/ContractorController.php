<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Contractor\ContractorCreateRequest;
use App\Http\Requests\Api\Contractor\ContractorIndexRequest;
use App\Http\Requests\Api\Contractor\ContractorUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\ContractorResource;
use App\Models\Contractor;
use App\Services\ContractorService;
use Illuminate\Http\JsonResponse;

class ContractorController extends Controller
{
    public function __construct(protected ContractorService $contractorService)
    {
    }

    public function index(ContractorIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->contractorService->getAllPaginatedContractors($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(ContractorResource::collection($data))
            ->setPagination($data)
            ->getResponse();
    }

    public function show(Contractor $contractor): JsonResponse
    {
        $contractor->loadMissing('user');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ContractorResource($contractor))
            ->getResponse();
    }

    public function create(ContractorCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $contractor = $this->contractorService->createContractor($input);

        $contractor->loadMissing('user');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ContractorResource($contractor))
            ->getResponse();
    }

    public function update(Contractor $contractor, ContractorUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $contractor = $this->contractorService->updateContractor($contractor, $input);

        $contractor->loadMissing('user');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ContractorResource($contractor))
            ->getResponse();
    }

    public function destroy(Contractor $contractor): JsonResponse
    {
        $this->contractorService->deleteContractor($contractor);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
