<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\ExportType;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Enrollment\EnrollmentStudentRegistrationReportRequest;
use App\Http\Requests\Api\Reports\Enrollment\ReportByDailyCollectionRequest;
use App\Http\Resources\ApiResponse;
use App\Services\Reports\EnrollmentReportService;
use Illuminate\Http\JsonResponse;


class EnrollmentReportController extends Controller
{
    public function __construct(
        protected EnrollmentReportService $enrollmentReportService,
    ) {}

    public function reportByStudentRegistration(EnrollmentStudentRegistrationReportRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('enrollment-report-by-student-registration');

        $response = $this->enrollmentReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.enrollment.student-registration-report')
            ->setFileName($file_name)
            ->getRegisteredStudentReport($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByDailyCollection(ReportByDailyCollectionRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('enrollment-report-by-daily-collection');

        $response = $this->enrollmentReportService
            ->setExportType(ExportType::EXCEL->value)
            ->setFileName($file_name)
            ->getDailyCollectionReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }
}
