<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Reports\Attendance\ClassAttendanceTakingStatusReportRequest;
use App\Http\Requests\Api\Reports\Attendance\ReportByAttendanceSummaryRequest;
use App\Http\Requests\Api\Reports\Attendance\ReportByClassAttendanceTakingRequest;
use App\Http\Requests\Api\Reports\Attendance\ReportByStudentAttendanceMarkDeductionRequest;
use App\Http\Requests\Api\Reports\Attendance\StudentAbsentReportRequest;
use App\Http\Requests\Api\Reports\Attendance\StudentAttendanceReportRequest;
use App\Http\Resources\ApiResponse;
use App\Services\Report\AttendanceReportService;
use App\Services\ReportPrintService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class AttendanceReportController extends Controller
{
    public function __construct(
        protected AttendanceReportService $attendanceReportService,
        protected ReportPrintService $reportPrintService,
    ) {
    }

    public function reportByAttendanceSummary(ReportByAttendanceSummaryRequest $request)
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('attendances-report-summary');

        $response = $this->attendanceReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.attendances.by-attendances-summary')
            ->setFileName($file_name)
            ->getReportByAttendanceSummaryData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function classAttendanceReport(ReportByClassAttendanceTakingRequest $request)
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('class-attendance-taking');

        $response = $this->attendanceReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.attendances.class-attendance-report')
            ->setFileName($file_name)
            ->getReportByClassAttendanceTakingData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function studentAttendanceReport(StudentAttendanceReportRequest $request): JsonResponse
    {
        $input = $request->validated();

        app()->setLocale($input['report_language']);

        $data = $this->attendanceReportService->getStudentAttendance($input);

        $export_type = Arr::get($input, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
            'title' => __('attendance.title.student_attendance'),
            'subtitle' => Carbon::parse($input['date_from'])->translatedFormat('F j, Y') . ' - ' . Carbon::parse($input['date_to'])->translatedFormat('F j, Y'),
        ];

        $report_view_name = 'reports.attendances.student-attendance-report';
        $file_name = 'student-attendance-report';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);
        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function studentAbsentReport(StudentAbsentReportRequest $request): JsonResponse
    {
        $input = $request->validated();

        app()->setLocale($input['report_language']);

        $data = $this->attendanceReportService->getStudentAbsent($input);

        $export_type = Arr::get($input, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
            'title' => __('attendance.title.student_absent'),
            'subtitle' => Carbon::parse($input['date_from'])->translatedFormat('F j, Y') . ' - ' . Carbon::parse($input['date_to'])->translatedFormat('F j, Y'),
        ];

        $report_view_name = 'reports.attendances.student-absent-report';
        $file_name = 'student-absent-report';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);
        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function reportByStudentAttendanceMarkDeduction(ReportByStudentAttendanceMarkDeductionRequest $request)
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('student-attendance-mark-deduction');

        $response = $this->attendanceReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.attendances.by-student-attendance-mark-deduction')
            ->setFileName($file_name)
            ->getReportByAttendanceMarkDeductionData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function classAttendanceTakingStatusReport(ClassAttendanceTakingStatusReportRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('teacher_attendance');

        $response = $this->attendanceReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.attendances.class-attendance-taking-status-report')
            ->setFileName($file_name)
            ->getClassAttendanceTakingStatus($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }
}
