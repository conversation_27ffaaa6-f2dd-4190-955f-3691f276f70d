<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\HostelInOutType;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Reports\Hostel\ReportByAvailableBedRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByBoardersContactInfoRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByBoardersDateOfBirthRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByBoardersGoHomeOrOutRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByBoardersListInfoRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByBoardersNameListRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByBoardersStaybackRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByChangeRoomRecordRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByCheckoutRecordRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByEmployeeLodgingRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByRewardPunishmentBlockRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByRewardPunishmentRoomRequest;
use App\Http\Requests\Api\Reports\Hostel\ReportByRewardPunishmentStudentRequest;
use App\Http\Resources\ApiResponse;
use App\Services\ReportPrintService;
use App\Services\Reports\HostelReportService;
use Illuminate\Http\JsonResponse;


class HostelReportController extends Controller
{
    public function __construct(
        protected HostelReportService $hostelReportService,
        protected ReportPrintService $reportPrintService
    ) {}

    public function reportByBoardersNameList(ReportByBoardersNameListRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-boarders-name-list');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-boarders-name-list')
            ->setFileName($file_name)
            ->getHostelBoarderListReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByAvailableBed(ReportByAvailableBedRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-available-bed');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-available-bed')
            ->setFileName($file_name)
            ->getAvailableBedReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByCheckoutRecord(ReportByCheckoutRecordRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-checkout-record');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-checkout-record')
            ->setFileName($file_name)
            ->getCheckoutRecordReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByBoardersListInfo(ReportByBoardersListInfoRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-boarders-list-information');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-boarders-list-information')
            ->setFileName($file_name)
            ->getBoardersListInfoReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByBoardersContactInfo(ReportByBoardersContactInfoRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-boarders-contact-information');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-boarders-contact-information')
            ->setFileName($file_name)
            ->getBoardersContactInfoReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByBoardersDateOfBirth(ReportByBoardersDateOfBirthRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-boarders-date-of-birth');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-boarders-date-of-birth')
            ->setFileName($file_name)
            ->getBoardersDateOfBirthReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByBoardersStayback(ReportByBoardersStaybackRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-boarders-stayback');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-boarders-stayback')
            ->setFileName($file_name)
            ->getBoardersStaybackReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByEmployeeLodging(ReportByEmployeeLodgingRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-employee-lodging');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-employee-lodging')
            ->setFileName($file_name)
            ->getEmployeeLodgingReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByBoardersGoHomeOrOut(ReportByBoardersGoHomeOrOutRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-boarders-go-home-or-out');

        $view_name = $filters['in_out_type'] == HostelInOutType::HOME->value ?
            'reports.hostels.by-boarders-go-home' :
            'reports.hostels.by-boarders-go-outing';

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName($view_name)
            ->setFileName($file_name)
            ->getGoHomeOrOutReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByChangeRoomRecord(ReportByChangeRoomRecordRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-change-room-record');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-change-room-record')
            ->setFileName($file_name)
            ->getChangeRoomReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByRewardPunishmentBlock(ReportByRewardPunishmentBlockRequest $request): JsonResponse
    {
        $filters = $request->validated();
        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-reward-punishment-block');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-reward-punishment-block')
            ->setFileName($file_name)
            ->getHostelRewardPunishmentReportByBlock($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    public function reportByRewardPunishmentStudent(ReportByRewardPunishmentStudentRequest $request): JsonResponse
    {
        $filters = $request->validated();
        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-reward-punishment-student');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-reward-punishment-student')
            ->setFileName($file_name)
            ->getHostelRewardPunishmentReportByStudent($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }    

    public function reportByRewardPunishmentRoom(ReportByRewardPunishmentRoomRequest $request): JsonResponse
    {
        $filters = $request->validated();
        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('hostels-report-by-reward-punishment-room');

        $response = $this->hostelReportService
            ->setExportType($filters['export_type'] ?? null)
            ->setReportViewName('reports.hostels.by-reward-punishment-room')
            ->setFileName($file_name)
            ->getHostelRewardPunishmentReportByRoom($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }        
}
