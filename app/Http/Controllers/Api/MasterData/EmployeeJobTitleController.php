<?php

namespace App\Http\Controllers\Api\MasterData;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\MasterData\EmployeeJobTitleCreateRequest;
use App\Http\Requests\Api\MasterData\EmployeeJobTitleIndexRequest;
use App\Http\Requests\Api\MasterData\EmployeeJobTitleUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\EmployeeJobTitleResource;
use App\Models\EmployeeJobTitle;
use App\Services\EmployeeJobTitleService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class EmployeeJobTitleController extends Controller
{
    use HandlesPagination;

    protected EmployeeJobTitleService $employeeJobTitleService;

    public function __construct(EmployeeJobTitleService $employee_job_title_service)
    {
        $this->employeeJobTitleService = $employee_job_title_service;
    }

    public function index(EmployeeJobTitleIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->employeeJobTitleService, 'getAllEmployeeJobTitles', 'getAllPaginatedEmployeeJobTitles');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(EmployeeJobTitleResource::collection($data))->getResponse();
    }

    public function show(EmployeeJobTitle $master_employee_job_title): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeJobTitleResource($master_employee_job_title))
            ->getResponse();
    }

    public function create(EmployeeJobTitleCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $master_employee_job_title = $this->employeeJobTitleService->createEmployeeJobTitle($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeJobTitleResource($master_employee_job_title))
            ->getResponse();
    }

    public function update(EmployeeJobTitle $master_employee_job_title, EmployeeJobTitleUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $master_employee_job_title = $this->employeeJobTitleService->updateEmployeeJobTitle($master_employee_job_title, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeJobTitleResource($master_employee_job_title))
            ->getResponse();
    }

    public function destroy(EmployeeJobTitle $master_employee_job_title): JsonResponse
    {
        $this->employeeJobTitleService->deleteEmployeeJobTitle($master_employee_job_title);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
