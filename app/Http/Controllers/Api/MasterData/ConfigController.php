<?php

namespace App\Http\Controllers\Api\MasterData;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\MasterData\ConfigIndexRequest;
use App\Http\Requests\Api\MasterData\ConfigUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Services\ConfigService;
use Illuminate\Http\JsonResponse;

class ConfigController extends Controller
{
    protected ConfigService $configService;

    public function __construct(ConfigService $config_service)
    {
        $this->configService = $config_service;
    }

    public function indexAdmin(ConfigIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->configService->getAllConfigs($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }

    public function index(): JsonResponse
    {
        $data = $this->configService->getUserConfigs();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }

    public function show($key): JsonResponse
    {
        $data = $this->configService->getConfig($key);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }

    public function store(ConfigUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->configService->updateOrCreateConfig($input['key'], $input['value'] ?? null, $input['category']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }
}
