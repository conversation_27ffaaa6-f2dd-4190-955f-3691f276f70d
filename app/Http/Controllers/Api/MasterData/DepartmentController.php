<?php

namespace App\Http\Controllers\Api\MasterData;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Department\DepartmentCreateRequest;
use App\Http\Requests\Api\Department\DepartmentIndexRequest;
use App\Http\Requests\Api\Department\DepartmentUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\DepartmentResource;
use App\Models\Department;
use App\Services\DepartmentService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class DepartmentController extends Controller
{
    use HandlesPagination;

    protected DepartmentService $departmentService;

    public function __construct(DepartmentService $departmentService)
    {
        $this->departmentService = $departmentService;
    }

    public function index(DepartmentIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->departmentService, 'getAllDepartments', 'getAllPaginatedDepartments');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(DepartmentResource::collection($data))->getResponse();
    }

    public function show(Department $department): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new DepartmentResource($department))
            ->getResponse();
    }

    public function create(DepartmentCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $counselling_case_record = $this->departmentService
            ->setData($input)
            ->createDepartment();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new DepartmentResource($counselling_case_record))
            ->getResponse();
    }

    public function update(Department $department, DepartmentUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $counselling_case_record = $this->departmentService
            ->setData($input)
            ->updateDepartment($department);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new DepartmentResource($counselling_case_record))
            ->getResponse();
    }

    public function destroy(Department $department): JsonResponse
    {
        $this->departmentService->deleteDepartment($department);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
