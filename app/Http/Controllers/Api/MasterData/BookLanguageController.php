<?php

namespace App\Http\Controllers\Api\MasterData;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\MasterData\Language\BookLanguageIndexRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\BookLanguageResource;
use App\Services\BookLanguageService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class BookLanguageController extends Controller
{
    use HandlesPagination;

    public function __construct(
        protected BookLanguageService $languageService
    ) {
    }

    public function index(BookLanguageIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->languageService, 'getAllBookLanguages', 'getAllPaginatedBookLanguages');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(BookLanguageResource::collection($data))->getResponse();
    }
}
