<?php

namespace App\Http\Controllers\Api\MasterData;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\MasterData\InternationalizationCreateRequest;
use App\Http\Requests\Api\MasterData\InternationalizationIndexRequest;
use App\Http\Requests\Api\MasterData\InternationalizationUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\InternationalizationResource;
use App\Models\Internationalization;
use App\Services\InternationalizationService;
use Illuminate\Http\JsonResponse;

class InternationalizationController extends Controller
{
    private InternationalizationService $internationalizationService;

    public function __construct(InternationalizationService $internationalizationService)
    {
        $this->internationalizationService = $internationalizationService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(InternationalizationIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->internationalizationService->getAllPaginatedInternationalization($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(InternationalizationResource::collection($data))
            ->setPagination($data, $input)
            ->getResponse();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function create(InternationalizationCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->internationalizationService->createInternationalization($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new InternationalizationResource($data))
            ->getResponse();
    }

    /**
     * Display the specified resource.
     */
    public function show(Internationalization $internationalization): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new InternationalizationResource($internationalization))
            ->getResponse();
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Internationalization $internationalization, InternationalizationUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->internationalizationService->updateInternationalization($internationalization, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new InternationalizationResource($data))
            ->getResponse();
    }
}
