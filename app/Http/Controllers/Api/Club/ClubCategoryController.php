<?php

namespace App\Http\Controllers\Api\Club;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Club\ClubCategoryCreateRequest;
use App\Http\Requests\Api\Club\ClubCategoryIndexRequest;
use App\Http\Requests\Api\Club\ClubCategoryUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\ClubCategoryResource;
use App\Models\ClubCategory;
use App\Services\ClubCategoryService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class ClubCategoryController extends Controller
{
    use HandlesPagination;

    protected ClubCategoryService $clubCategoryService;

    public function __construct(ClubCategoryService $club_category_service)
    {
        $this->clubCategoryService = $club_category_service;
    }

    public function index(ClubCategoryIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->clubCategoryService, 'getAllClubCategories', 'getAllPaginatedClubCategories');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(ClubCategoryResource::collection($data))->getResponse();
    }

    public function show(ClubCategory $club_category): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ClubCategoryResource($club_category))
            ->getResponse();
    }

    public function create(ClubCategoryCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $club_category = $this->clubCategoryService->createClubCategory($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ClubCategoryResource($club_category))
            ->getResponse();
    }

    public function update(ClubCategory $club_category, ClubCategoryUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $club_category = $this->clubCategoryService->updateClubCategory($club_category, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new ClubCategoryResource($club_category))
            ->getResponse();
    }

    public function destroy(ClubCategory $club_category): JsonResponse
    {
        $this->clubCategoryService->deleteClubCategory($club_category);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
