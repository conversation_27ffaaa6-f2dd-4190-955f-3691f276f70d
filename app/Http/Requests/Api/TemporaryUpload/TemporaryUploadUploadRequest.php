<?php

namespace App\Http\Requests\Api\TemporaryUpload;

use Illuminate\Foundation\Http\FormRequest;

class TemporaryUploadUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'files' => ['required', 'array'],
            'files.*.file' => ['required', 'file'],
            'files.*.name' => ['required', 'string'],
        ];
    }
}
