<?php

namespace App\Http\Requests\Api\EcommerceOrder;

use App\Enums\MerchantType;
use App\Interfaces\Userable;
use App\Rules\AuthorizeUserableAccess;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EcommerceOrderCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'userable_id' => ['required', new AuthorizeUserableAccess],
            'userable_type' => ['required', Rule::in(Userable::USERABLE_TYPES)],
            'merchant_type' => ['required', Rule::in(MerchantType::values())],
            'products' => 'required|array',
            'products.*.id' => [
                'required',
                'integer',
                Rule::exists('ecommerce_products', 'id')
                    ->where('is_active', true)
            ],
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.delivery_date' => 'nullable|date',
        ];
    }

    public function attributes(): array
    {
        return [
            'products.*.id' => 'product',
            'products.*.quantity' => 'product quantity',
            'products.*.delivery_date' => 'product delivery date',
        ];
    }

    public function messages(): array
    {
        return [
            'products.*.id.exists' => 'The selected :attribute is not available.'
        ];
    }
}
