<?php

namespace App\Http\Requests\Api\Employee;

use App\Enums\Gender;
use App\Enums\JobType;
use App\Enums\MarriedStatus;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'phone_number' => [
                'required', 'string', 'max:20', Rule::unique('employees', 'phone_number')->ignore($this->employee->id), // update employee wont update Users table
            ],
            'email' => [
                'required', 'email', 'max:100', Rule::unique('employees', 'email')->ignore($this->employee->id),    // update employee wont update Users table
            ],
            'personal_email' => [
                'nullable', 'email', 'max:100', Rule::unique('employees', 'personal_email')->ignore($this->employee->id)
            ],
            'nric' => [
                'nullable', 'numeric', 'digits:12', 'required_without:passport_number', Rule::unique('employees', 'nric')->ignore($this->employee->id)
            ],
            'passport_number' => ['nullable', 'string', 'max:50', 'required_without:nric', Rule::unique('employees', 'passport_number')->ignore($this->employee->id)],
            // Auto generated by system
//            'employee_number' => [
//                'nullable', 'string', Rule::unique('employees', 'employee_number')->ignore($this->employee->id)
//            ],
            'badge_no' => ['nullable', 'string'],
            'job_title_id' => ['required', 'exists:master_employee_job_titles,id'],
            'date_of_birth' => ['required', 'date'],
            'gender' => ['required', 'string', Rule::in(Gender::values())],
            'religion_id' => ['nullable', 'exists:master_religions,id'],
            'race_id' => ['nullable', 'exists:master_races,id'],
            'address' => ['required', 'string'],
            'address_2' => ['required', 'string'],
            'postal_code' => ['nullable', 'string'],
            'city' => ['nullable', 'string'],
            'state_id' => ['nullable', 'exists:master_states,id'],
            'country_id' => ['nullable', 'exists:master_countries,id'],
            'photo' => ['nullable', 'image', 'max:2048'],
            'is_hostel' => ['required', 'boolean'],
            'epf_number' => ['nullable', 'string', 'max:32'],
            'employment_start_date' => ['required', 'date'],
            'employment_end_date' => ['nullable', 'date'],
            'highest_education' => ['nullable', 'string'],
            'highest_education_country_id' => ['nullable', 'exists:master_countries,id'],
            'employment_type' => ['required', Rule::in(JobType::values())],
            'employee_category_id' => ['required', 'exists:master_employee_category,id'],
            'marriage_status' => ['nullable', Rule::in(MarriedStatus::values())],
            'employee_session_id' => ['nullable', 'exists:master_employee_sessions,id'],
        ];
    }

    public function messages()
    {
        return [
            'nric.required_without' => 'NRIC is needed if the Passport Number field is blank.',
            'passport_number.required_without' => 'Passport Number is needed if the NRIC field is blank.',
        ];
    }
}
