<?php

namespace App\Http\Requests\Api\Discount;

use App\Http\Requests\Api\CommonApiValidationRequest;
use App\Interfaces\Userable;
use App\Models\DiscountSetting;
use App\Models\GlAccount;
use App\Models\ScholarshipAward;
use Illuminate\Validation\Rule;

class DiscountIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'basis' => ['nullable', 'string', Rule::in(DiscountSetting::ALL_BASIS)],
            'effective_from' => ['nullable', 'required_with:effective_to', 'date'],
            'effective_to' => ['nullable', 'required_with:effective_from', 'date', 'after_or_equal:effective_from'],
            'gl_account_code' => ['nullable', 'string', Rule::exists(GlAccount::class, 'code')],
            'source_type' => ['nullable', Rule::in([ScholarshipAward::class])],
            'source_id' => ['nullable', 'integer', 'required_with:source_type'],
            'userable_type' => ['nullable', 'string', Rule::in(array_values(Userable::USER_TYPE_MAPPING))],
            'userable_id' => ['nullable', 'integer', 'required_with:userable_type'],
            'is_active' => ['nullable', 'boolean'],
        ];

        if ($this->input('userable_type')) {
            $model = Userable::USERABLE_MAPPING[$this->input('userable_type')] ?? null;

            if ($model) {
                $rules['userable_id'][] = Rule::exists($model, 'id');
            }
        }

        if ($this->input('source_type')) {
            $rules['source_id'][] = Rule::exists($this->input('source_type'), 'id');
        }

        return array_merge(parent::rules(), $rules);
    }
}
