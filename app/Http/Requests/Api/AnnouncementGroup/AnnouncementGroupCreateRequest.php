<?php

namespace App\Http\Requests\Api\AnnouncementGroup;

use App\Http\Requests\Api\CommonApiValidationRequest;
use App\Repositories\EmployeeRepository;
use App\Repositories\StudentRepository;
use App\Rules\CheckExistFromRepositoryRule;

class AnnouncementGroupCreateRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'is_active' => ['required', 'boolean'],
            'student_ids' => ['nullable', 'array', new CheckExistFromRepositoryRule(StudentRepository::class)],
            'employee_ids' => ['nullable', 'array', new CheckExistFromRepositoryRule(EmployeeRepository::class)],
        ];
    }
}
