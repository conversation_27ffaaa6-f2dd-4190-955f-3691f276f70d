<?php

namespace App\Http\Requests\Api\Student;

use App\Enums\DietaryRestriction;
use App\Enums\Gender;
use App\Enums\SchoolLevel;
use App\Enums\StudentAdmissionType;
use App\Helpers\ErrorCodeHelper;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use App\Traits\Requests\GuardianValidationRules;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Propaganistas\LaravelPhone\PhoneNumber;

class StudentUpdateRequest extends FormRequest
{
    use GuardianValidationRules;

    protected function prepareForValidation(): void
    {
        try {
            $guardians = $this->input('guardians') ?? [];

            foreach ($guardians as $index => $guardian) {
                $guardians[$index]['phone_number'] = $guardian['phone_number'] ? (new PhoneNumber($guardian['phone_number']))->formatE164() : null;
            }

            $this->merge([
                'phone_number' => $this->input('phone_number') ? (new PhoneNumber($this->input('phone_number')))->formatE164() : null,
                'guardians' => $guardians,
            ]);
        } catch (\Exception $e) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::VALIDATION_ERROR, 28001);
        }
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $guardians = $this->input('guardians') ?? [];

        $rules = [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'phone_number' => [
                'nullable',
                'phone',
                'max:20',
                Rule::unique('users', 'phone_number')->ignore($this->student->user->id),
                Rule::unique('students', 'phone_number')->ignore($this->student->id),
            ],
            'phone_number_2' => ['nullable', 'phone', 'max:20'],
            'admission_year' => ['required', 'numeric', 'digits:4'],
            'admission_grade_id' => ['required', 'exists:master_grades,id'],
            'join_date' => ['required', 'date'],
            'leave_date' => ['nullable', 'date'],
            // Auto generated by system
//            'student_number' => [
//                'required', 'string', 'max:20', Rule::unique('students', 'student_number')->ignore($this->student->id)
//            ],
            'birthplace' => ['nullable', 'string', 'max:100'],
            'nationality_id' => ['required', 'exists:master_countries,id'],
            'date_of_birth' => ['required', 'date', 'before:today'],
            'gender' => ['required', Rule::in(Gender::values())],
            'birth_cert_number' => [
                'required', 'string', 'max:50',
                Rule::unique('students', 'birth_cert_number')->ignore($this->student->id)
            ],
            'nric' => [
                'nullable', 'numeric', 'digits:12', 'required_without:passport_number', Rule::unique('students', 'nric')->ignore($this->student->id)
            ],
            'passport_number' => ['nullable', 'string', 'max:50', 'required_without:nric', Rule::unique('students', 'passport_number')->ignore($this->student->id)],
            'race_id' => ['required', 'exists:master_races,id'],
            'religion_id' => ['required', 'exists:master_religions,id'],
            'address' => ['required', 'string', 'max:255'],
            'address_2' => ['nullable', 'string', 'max:255'],
            'postal_code' => ['required', 'string', 'max:20'],
            'city' => ['required', 'string', 'max:100'],
            'state_id' => ['required', 'exists:master_states,id'],
            'country_id' => ['required', 'exists:master_countries,id'],
            'remarks' => ['nullable', 'string'],
            'custom_field' => ['nullable', 'json'],
            'is_hostel' => [
                'required',
                'boolean',
                function (string $attribute, mixed $value, Closure $fail) {
                    if (!$value && $this->student->is_hostel && $this->student->hasExistingBedAssignment()) {
                        $fail("Unable to change is hostel status to false because student has existing bed assignment.");
                    }
                },
            ],
            'photo' => ['nullable', 'image', 'max:2048'],
            'guardians' => ['present', 'array'],
            'dietary_restriction' => ['required', Rule::in(DietaryRestriction::values())],
            'health_concern_id' => ['required', 'exists:master_health_concerns,id'],
            'primary_school_id' => ['nullable', 'exists:master_schools,id,level,' . SchoolLevel::PRIMARY->value],
            'admission_type' => ['required', Rule::in(StudentAdmissionType::values())],
        ];

        $guardian_rules = $this->getGuardianValidationRules($guardians);

        return array_merge($rules, $guardian_rules);
    }

    public function attributes(): array
    {
        $guardians = $this->input('guardians') ?? [];
        return $this->getGuardianValidationAttributes($guardians);
    }

    public function messages()
    {
        return [
            'nric.required_without' => 'NRIC is needed if the Passport Number field is blank.',
            'passport_number.required_without' => 'Passport Number is needed if the NRIC field is blank.',
        ];
    }
}
