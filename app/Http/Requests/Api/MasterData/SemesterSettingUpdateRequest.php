<?php

namespace App\Http\Requests\Api\MasterData;

use Illuminate\Foundation\Http\FormRequest;

class SemesterSettingUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'course_id' => ['required', 'exists:master_courses,id'],
            'semester_year_setting_id' => ['required', 'exists:master_semester_year_settings,id'],
            'name' => ['required', 'string'],
            'from' => ['required', 'date'],
            'to' => ['required', 'date', 'after:from'],
            'is_current_semester' => ['required', 'boolean'],
        ];
    }
}
