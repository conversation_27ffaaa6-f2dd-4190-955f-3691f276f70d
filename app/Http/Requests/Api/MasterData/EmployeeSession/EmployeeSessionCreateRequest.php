<?php

namespace App\Http\Requests\Api\MasterData\EmployeeSession;

use App\Enums\Day;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeSessionCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string'],
            'is_active' => ['required', 'boolean'],
            'determine_attendance_status' => ['required', 'boolean'],
            'employee_session_settings' => ['required', 'array'],
            'employee_session_settings.*.day' => ['required', 'distinct', Rule::enum(Day::class)],
            'employee_session_settings.*.start_time' => ['required', 'date_format:H:i:s'],
            'employee_session_settings.*.end_time' => ['required', 'date_format:H:i:s', 'after:start_time']
        ];
    }
}
