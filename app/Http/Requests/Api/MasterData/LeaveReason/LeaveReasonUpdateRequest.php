<?php

namespace App\Http\Requests\Api\MasterData\LeaveReason;

use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;

class LeaveReasonUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'sequence' => ['required', 'integer'],
        ];
    }
}
