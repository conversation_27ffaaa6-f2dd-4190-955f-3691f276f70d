<?php

namespace App\Http\Requests\Api\MasterData\LeadershipPositionRecord;

use Illuminate\Foundation\Http\FormRequest;

class LeadershipPositionRecordBulkCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'semester_class_id' => ['required', 'exists:semester_classes,id'],
            'leadership_positions' => ['required', 'array'],
            'leadership_positions.*.id' => ['required', 'exists:master_leadership_positions,id'],
            'leadership_positions.*.student_id' => ['nullable', 'exists:students,id'],
        ];
    }

    public function attributes()
    {
        return [
            'leadership_positions.*.id' => 'leadership position',
            'leadership_positions.*.student_id' => 'student',
        ];
    }
}
