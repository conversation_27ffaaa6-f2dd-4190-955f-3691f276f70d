<?php

namespace App\Http\Requests\Api\BookLoanReport;

use App\Enums\ExportType;
use App\Enums\LibraryReportSchoolRateBorrowBookFilterBy;
use App\Models\SemesterSetting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReportSchoolRateBorrowBookRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'filter_type' => ['required', Rule::in(LibraryReportSchoolRateBorrowBookFilterBy::values())],
            'month' => [
                Rule::requiredIf($this->filter_type == LibraryReportSchoolRateBorrowBookFilterBy::MONTHLY->value),
                'integer',
                'digits_between:1,12'
            ],
            'semester_setting_id' => [
                'required',
                Rule::exists(SemesterSetting::class, 'id')
            ],
            'export_type' => ['nullable', Rule::in(ExportType::values())]
        ];
    }
}
