<?php

namespace App\Http\Requests\Api\LeaveApplication;

use App\Enums\LeaveApplicationStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LeaveApplicationUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'period_group_id' => ['required', 'integer', 'exists:period_groups,id'],
            'period_label_ids' => ['required', 'array', 'min:1'],
            'period_label_ids.*' => ['integer', 'exists:period_labels,id'],
            'leave_application_type_id' => ['required', 'integer', 'exists:leave_application_types,id'],
            'from_date' => ['required', 'date'],
            'to_date' => ['required', 'date', 'after_or_equal:from_date'],
            'reason' => ['required', 'string', 'max:250'],       // student input reason / reason to take leave
            'proof' => ['nullable', 'file', 'mimes:jpeg,jpg,png,pdf,docx,xlsx,pptx', 'max:10000'],
            'remarks' => ['nullable', 'string', 'max:250'],
            'is_present' => ['required', 'boolean'],
            'average_point_deduction' => ['required', 'numeric', 'min:0', 'max:100'],
            'conduct_point_deduction' => ['required', 'numeric', 'min:0', 'max:100'],
        ];
    }
}
