<?php

namespace App\Http\Requests\Api\WalletTransaction;

use App\Enums\WalletTransactionStatus;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class WalletTransactionShowRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            // To add in filters here if any
            'status' => ['nullable', Rule::in(WalletTransactionStatus::values())]
        ]);
    }
}
