<?php

namespace App\Http\Requests\Api\WalletTransaction;

use Illuminate\Foundation\Http\FormRequest;

class WalletTransactionRefundRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'remark' => ['required', 'string'],
        ];
    }
}
