<?php

namespace App\Http\Requests\Api\Terminal;

use App\Enums\TerminalType;
use App\Models\Terminal;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TerminalUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'code' => ['required', 'string', 'max:24', Rule::unique(Terminal::class, 'code')->ignore($this->terminal->id)],
            'type' => ['required', Rule::in(TerminalType::values())],
            'merchant_id' => ['nullable', 'exists:merchants,id']
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
