<?php

namespace App\Http\Requests\Api\EcommerceProduct;

use App\Enums\MerchantType;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class EcommerceProductGetByDeliveryDateRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'delivery_date_from' => ['required', 'date'],
            'delivery_date_to' => ['required', 'date', 'after:delivery_date_from'],
            'merchant_type' => ['required', Rule::in(MerchantType::values())],
        ]);
    }
}
