<?php

namespace App\Http\Requests\Api\EcommerceReport\Canteen;

use App\Enums\ExportType;
use App\Models\SemesterClass;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReportByClassWeeklyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'semester_class_ids' => ['present', 'array'],
            'semester_class_ids.*' => ['required', Rule::exists(SemesterClass::class, 'id')],
            'start_product_delivery_date' => ['required', 'date', 'date_format:Y-m-d'],
            'end_product_delivery_date' => ['required', 'date', 'date_format:Y-m-d'],
            'export_type' => ['nullable', Rule::in(ExportType::values())],
        ];
    }
}
