<?php

namespace App\Http\Requests\Api\RewardPunishmentRecord;

use Illuminate\Foundation\Http\FormRequest;

class RewardPunishmentRecordCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date' => ['required', 'date'],
            'student_ids' => ['required', 'array', 'min:1'],
            'student_ids.*' => ['required', 'exists:students,id'],
            'reward_punishment_id' => ['required', 'exists:reward_punishments,id'],
            'display_in_report_card' => ['required', 'boolean'],
        ];
    }
}
