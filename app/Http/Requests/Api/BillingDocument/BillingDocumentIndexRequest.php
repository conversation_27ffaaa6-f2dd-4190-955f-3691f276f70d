<?php

namespace App\Http\Requests\Api\BillingDocument;

use App\Http\Requests\Api\CommonApiValidationRequest;
use App\Interfaces\Userable;
use App\Models\BillingDocument;
use Illuminate\Validation\Rule;

/**
 * For FE use
 */
class BillingDocumentIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'bill_to_type' => ['required', Rule::in(array_values(Userable::USER_TYPE_MAPPING))],
            'bill_to_id' => ['required'],
            'type' => ['nullable', Rule::in(BillingDocument::ALL_TYPES)],
            'sub_type' => ['nullable', Rule::in(BillingDocument::ALL_SUB_TYPES)],
            'status' => ['nullable', Rule::in(BillingDocument::ALL_STATUSES)],
            'payment_status' => ['nullable', Rule::in(BillingDocument::ALL_PAYMENT_STATUSES)],
            'document_date_from' => ['nullable', 'required_with:document_date_to', 'date'],
            'document_date_to' => ['nullable', 'required_with:document_date_from', 'date', 'after_or_equal:document_date_from'],
            'reference_no' => ['nullable'],
        ];

        if ($this->input('bill_to_type')) {
            $model = Userable::USERABLE_MAPPING[$this->input('bill_to_type')] ?? null;

            if ($model) {
                $rules['bill_to_id'][] = Rule::exists($model, 'id');
            }
        }

        if ($this->input('reference_no')) {

            $rules['reference_no'][] = Rule::exists(BillingDocument::class, 'reference_no')->where(function ($query) use ($model) {
                $query->where('bill_to_type', $model)
                    ->where('bill_to_id', $this->input('bill_to_id'));
            });
        }

        return array_merge(parent::rules(), $rules);
    }
}
