<?php

namespace App\Http\Requests\Api\Reports\Hostel;

use App\Enums\ExportType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReportByChangeRoomRecordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'export_type' => ['nullable', Rule::in(ExportType::values())],
            'year' => ['required', 'integer', 'min:1900', 'max:2999'],
            'semester_setting_id' => ['required', 'exists:master_semester_settings,id'],
        ];
    }
}
