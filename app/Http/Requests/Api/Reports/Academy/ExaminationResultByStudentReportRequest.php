<?php

namespace App\Http\Requests\Api\Reports\Academy;

use App\Enums\ExamReportFilterBy;
use App\Enums\ExportType;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ExaminationResultByStudentReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'semester_setting_id' => ['required', Rule::exists(SemesterSetting::class, 'id')],
            'exam_id' => ['required', Rule::exists(Exam::class, 'id')],
            'filter_by' => ['required', Rule::in(ExamReportFilterBy::values())],
            'semester_class_ids' => [Rule::requiredIf($this->input('filter_by') == ExamReportFilterBy::SEMESTER_CLASS->value), 'array'],
            'semester_class_ids.*' => [
                Rule::exists(SemesterClass::class, 'id')
            ],
            'grade_ids' => [Rule::requiredIf($this->input('filter_by') == ExamReportFilterBy::GRADE->value), 'array'],
            'grade_ids.*' => [
                Rule::exists(Grade::class, 'id')
            ],
            'export_type' => ['nullable', Rule::in(ExportType::values())],
        ];
    }
}
