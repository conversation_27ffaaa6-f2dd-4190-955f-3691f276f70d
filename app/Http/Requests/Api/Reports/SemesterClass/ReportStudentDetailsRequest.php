<?php

namespace App\Http\Requests\Api\Reports\SemesterClass;

use App\Enums\ExportType;
use App\Models\SemesterClass;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReportStudentDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'semester_class_id' => ['required', 'integer', Rule::exists(SemesterClass::class, 'id')],
            'export_type' => ['nullable', Rule::in([ExportType::PDF->value])],
        ];
    }
}
