<?php

namespace App\Http\Requests\Api\Reports\Enrollment;

use Illuminate\Foundation\Http\FormRequest;

class ReportByDailyCollectionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'payment_date_from' => ['required', 'date', 'date_format:Y-m-d'],
            'payment_date_to' => ['required', 'after_or_equal:payment_date_from', 'date_format:Y-m-d'],
        ];
    }
}
