<?php

namespace App\Http\Requests\Api\Reports\Attendance;

use App\Enums\CardTemplateType;
use App\Enums\ExportType;
use App\Models\SemesterSetting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContractorDailyAttendanceReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'date' => ['required', 'date'],
            'type' => ['required', Rule::in(['all', 'attend_only'])],
            'semester_setting_id' => ['required', Rule::exists(SemesterSetting::class, 'id')],
            'export_type' => ['nullable', Rule::in(ExportType::values())],
        ];
    }
}
