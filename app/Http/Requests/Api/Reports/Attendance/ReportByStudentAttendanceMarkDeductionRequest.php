<?php

namespace App\Http\Requests\Api\Reports\Attendance;

use App\Enums\ExportType;
use App\Repositories\LeaveApplicationTypeRepository;
use App\Repositories\SemesterClassRepository;
use App\Rules\CheckExistFromRepositoryRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReportByStudentAttendanceMarkDeductionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'export_type' => ['nullable', Rule::in(ExportType::values())],
            'date_from' => ['required', 'date_format:Y-m-d', 'date'],
            'date_to' => ['required', 'after_or_equal:date_from', 'date_format:Y-m-d', 'date'],
            'semester_class_ids' => ['required', 'array', new CheckExistFromRepositoryRule(SemesterClassRepository::class)],
            'leave_application_type_ids' => ['nullable', 'array', new CheckExistFromRepositoryRule(LeaveApplicationTypeRepository::class)],
        ];
    }
}
