<?php

namespace App\Http\Requests\Api\Guest;

use App\Enums\GuestType;
use App\Helpers\ErrorCodeHelper;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Propaganistas\LaravelPhone\PhoneNumber;

class GuestUpdateRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        try {
            $this->merge([
                'phone_number' => $this->input('phone_number') ? (new PhoneNumber($this->input('phone_number')))->formatE164() : null,
            ]);
        } catch (\Exception $e) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::VALIDATION_ERROR, 28001);
        }
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'email' => [
                'nullable',
                'unique:guests,email',
                'email',
                'max:100'
            ],
            'phone_number' => [
                'required',
                'unique:guests,phone_number',
                'required',
                'phone',
                'max:20'
            ],
            'nric' => ['nullable', 'unique:guests,nric', 'numeric', 'digits:12',],
            'type' => ['required', 'string', Rule::in(GuestType::values())],
            'remarks' => ['nullable', 'string']
        ];
    }
}
