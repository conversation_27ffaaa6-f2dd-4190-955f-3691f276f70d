<?php

namespace App\Http\Requests\Api\ClassModel;

use App\Repositories\ClassRepository;
use App\Repositories\SemesterSettingRepository;
use App\Rules\CheckExistFromRepositoryRule;
use Illuminate\Foundation\Http\FormRequest;

class ClassesAssignToSemestersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'semester_setting_ids' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    if (count($value) !== collect($value)->uniqueStrict()->count()) {
                        $fail(__('validation.distinct', ['attribute' => 'semester setting']));
                    }
                },
                new CheckExistFromRepositoryRule(SemesterSettingRepository::class),
            ],
            'class_ids' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    if (count($value) !== collect($value)->uniqueStrict()->count()) {
                        $fail(__('validation.distinct', ['attribute' => 'class']));
                    }
                },
                new CheckExistFromRepositoryRule(ClassRepository::class),
            ],
        ];
    }
}
