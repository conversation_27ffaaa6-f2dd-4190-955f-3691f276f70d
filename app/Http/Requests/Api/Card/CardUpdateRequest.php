<?php

namespace App\Http\Requests\Api\Card;

use App\Enums\CardStatus;
use App\Enums\CardType;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Student;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CardUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'userable_id' => ['required', Rule::exists('userable_views', 'userable_id')],
            'userable_type' => ['required', Rule::in([Student::class, Employee::class, Contractor::class])],
            'name' => ['required'],
            'card_number' => ['required', Rule::unique('cards', 'card_number')->ignore($this->card->id)],
            'card_number2' => ['nullable'],
            'card_number3' => ['nullable'],
            'card_type' => ['required', Rule::in(CardType::values())],
            'status' => ['required', Rule::in(CardStatus::values())],
            'remarks' => ['required_if:status,' . CardStatus::INACTIVE->value, 'string'],
            'update_library_card_number' => ['required', 'boolean']
        ];

        if ($this->card_type == CardType::PROXIMITY->value) {
            $rules['card_number'][] = 'digits:10';
            $rules['card_number2'][] = 'digits:3';
            $rules['card_number3'][] = 'digits:5';
        }

        return $rules;
    }
}
