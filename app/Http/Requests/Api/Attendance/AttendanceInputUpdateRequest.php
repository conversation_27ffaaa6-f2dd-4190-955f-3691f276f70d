<?php

namespace App\Http\Requests\Api\Attendance;

use Illuminate\Foundation\Http\FormRequest;

class AttendanceInputUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'card_id' => ['nullable', 'exists:cards,id'],
            'remarks' => ['nullable', 'string', 'max:250'],
            'date' => ['required', 'date'],
            'record_datetime' => ['required', 'date_format:Y-m-d H:i:s'],
        ];
    }
}
