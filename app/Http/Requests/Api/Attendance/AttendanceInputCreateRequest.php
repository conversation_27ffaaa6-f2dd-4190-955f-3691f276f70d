<?php

namespace App\Http\Requests\Api\Attendance;

use App\Interfaces\AttendanceRecordable;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AttendanceInputCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'userable_id' => ['required', Rule::exists('userable_views', 'userable_id')],
            'userable_type' => ['required', Rule::in(AttendanceRecordable::ATTENDANCE_RECORDABLE_TYPES)],
            'card_id' => ['nullable', 'exists:cards,id'],
            'remarks' => ['nullable', 'string', 'max:250'],
            'date' => ['required', 'date'],
            'record_datetime' => ['required', 'date_format:Y-m-d H:i:s'],
            'repost_school_attendance' => ['boolean'],
        ];
    }
}
