<?php

namespace App\Http\Requests\Api;

use App\Repositories\EmployeeRepository;
use App\Repositories\StudentRepository;
use App\Rules\CheckExistFromRepositoryRule;

class AnnouncementCreateRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string'],
            'message' => ['required', 'string'],
            'send_now' => ['required', 'boolean'],
            'scheduled_time' => ['nullable', 'required_if:send_now,false', 'date_format:Y-m-d H:i:s', 'after:' . now()->toDateTimeString()],
            'student_ids' => ['required_without_all:employee_ids,announcement_group_ids', 'array', new CheckExistFromRepositoryRule(StudentRepository::class)],
            'employee_ids' => ['required_without_all:student_ids,announcement_group_ids', 'array', new CheckExistFromRepositoryRule(EmployeeRepository::class)],
            'student_as_recipient' => ['nullable', 'boolean'],
            'guardian_as_recipient' => ['nullable', 'boolean'],
            'announcement_group_ids' => ['required_without_all:student_ids,employee_ids', 'array', 'exists:announcement_groups,id'],
            'attachments' => ['nullable', 'array'],
            'attachments.*' => ['file', 'mimes:jpeg,jpg,png,pdf,docx', 'max:10000'],
        ];
    }
}
