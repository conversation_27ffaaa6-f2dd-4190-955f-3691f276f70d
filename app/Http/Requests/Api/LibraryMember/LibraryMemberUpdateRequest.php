<?php

namespace App\Http\Requests\Api\LibraryMember;

use App\Enums\Gender;
use App\Enums\LibraryMemberType;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Student;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LibraryMemberUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', Rule::in(LibraryMemberType::values())],
            'student_id' => [
                'required_if:type,'.LibraryMemberType::STUDENT->value,
                'exists:students,id',
                Rule::unique('library_members', 'userable_id')
                    ->where(function ($query) {
                        $query->where('type', LibraryMemberType::STUDENT->value)
                            ->where('userable_type', Student::class);
                    })->ignore($this->route('library_member'))
            ],
            'employee_id' => [
                'required_if:type,'.LibraryMemberType::EMPLOYEE->value,
                'exists:employees,id',
                Rule::unique('library_members', 'userable_id')
                    ->where(function ($query) {
                        $query->where('type', LibraryMemberType::EMPLOYEE->value)
                            ->where('userable_type', Employee::class);
                    })->ignore($this->route('library_member'))
            ],
            'guardian_id' => [
                'required_if:type,'.LibraryMemberType::GUARDIAN->value,
                'exists:guardians,id',
                Rule::unique('library_members', 'userable_id')
                    ->where(function ($query) {
                        $query->where('type', LibraryMemberType::GUARDIAN->value)
                            ->where('userable_type', Guardian::class);
                    })
            ],
            'card_number' => ['required', Rule::unique('library_members', 'card_number')->ignore($this->route('library_member'))],
            'member_number' => ['required', Rule::unique('library_members', 'member_number')->ignore($this->route('library_member'))],
            'borrow_limit' => ['required', 'integer'],
            'is_librarian' => ['required_if:type,'.LibraryMemberType::STUDENT->value, 'boolean'],
            'register_date' => ['required', 'date', 'before_or_equal:valid_from'],
            'valid_from' => ['required', 'date'],
            'valid_to' => ['required', 'date', 'after:valid_from'],
            'is_active' => ['required', 'boolean'],

            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation()],
            'gender' => ['required', Rule::in(Gender::values())],
            'nric' => ['nullable', Rule::unique('library_members', 'nric')->ignore($this->route('library_member')), 'digits:12'],
            'passport_number' => ['nullable', 'string', 'max:50'],
            'date_of_birth' => ['required', 'date'],
            'race_id' => ['nullable', Rule::exists('master_races', 'id')],
            'religion_id' => ['nullable', Rule::exists('master_religions', 'id')],
            'address' => ['nullable', 'string'],
            'postcode' => ['nullable', 'string'],
            'city' => ['nullable', 'string'],
            'state_id' => ['nullable', Rule::exists('master_states', 'id')],
            'country_id' => ['nullable', Rule::exists('master_countries', 'id')],
            'phone_number' => ['nullable', 'string'],
            'email' => ['nullable', 'string'],
            'photo' => ['nullable', 'image', 'max:2048'],
        ];
    }
}
