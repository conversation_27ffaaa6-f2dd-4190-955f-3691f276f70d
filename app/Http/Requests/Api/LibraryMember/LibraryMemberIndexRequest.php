<?php

namespace App\Http\Requests\Api\LibraryMember;

use App\Enums\LibraryMemberType;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class LibraryMemberIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'id' => ['nullable', 'integer'],
            'name' => ['nullable', 'string'],
            'type' => ['nullable', Rule::in(LibraryMemberType::values())],
            'member_number' => ['nullable', 'string'],
            'card_number' => ['nullable', 'string'],
            'member_or_card_number' => ['nullable', 'string'],
            'is_active' => ['nullable', 'boolean'],
            'nric' => ['nullable', 'string'],
            'phone_number' => ['nullable', 'string'],
            'email' => ['nullable', 'email'],
        ]);
    }
}
