<?php

namespace App\Http\Requests\Api\Exam;

use App\Http\Requests\Api\CommonApiValidationRequest;

class ExamIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name' => ['nullable', 'string'],
            'code' => ['nullable', 'string'],
            'results_entry_period_open' => ['nullable', 'boolean'],
        ]);
    }
}
