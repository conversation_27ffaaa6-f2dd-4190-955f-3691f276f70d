<?php

namespace App\Http\Requests\Api\Guardian;

use App\Http\Requests\Api\CommonApiValidationRequest;

class GuardianIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name' => ['nullable', 'string'],
            'nric' => ['nullable', 'string'],
            'email' => ['nullable', 'string'],
            'phone_number' => ['nullable', 'string'],
        ]);
    }
}
