<?php

namespace App\Http\Requests\Api\Accounting;

use App\Http\Requests\Api\CommonApiValidationRequest;
use App\Interfaces\Userable;
use App\Models\UnpaidItem;
use Illuminate\Validation\Rule;

class AccountingIndexAdminUnpaidItemRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'bill_to_type' => ['required', 'string', Rule::in(array_values(Userable::USER_TYPE_MAPPING))],
            'bill_to_id' => ['required', 'integer', 'required_with:bill_to_type'],
            'status' => ['nullable', Rule::in(UnpaidItem::STATUSES)],
            'period_from' => ['nullable', 'required_with:period_to', 'date'],
            'period_to' => ['nullable', 'required_with:period_from', 'date', 'after_or_equal:period_from'],
        ];

        if ($this->input('bill_to_type')) {
            $model = Userable::USERABLE_MAPPING[$this->input('bill_to_type')] ?? null;

            if ($model) {
                $rules['bill_to_id'][] = Rule::exists($model, 'id');
            }
        }

        return array_merge(parent::rules(), $rules);
    }
}
