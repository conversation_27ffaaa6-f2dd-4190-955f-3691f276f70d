<?php

namespace App\Http\Requests\Api\Hostel;

use Illuminate\Foundation\Http\FormRequest;

class HostelInOutRecordUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'check_in_datetime' => ['nullable', 'date_format:Y-m-d H:i:s'],
            'reason' => ['nullable'],
            'card_no' => ['nullable'],
        ];
    }
}
