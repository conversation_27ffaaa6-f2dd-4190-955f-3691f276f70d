<?php

namespace App\Http\Requests\Api\Hostel;

use App\Enums\HostelBlockType;
use App\Enums\HostelRoomGender;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class HostelRoomIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name' => ['nullable', 'string'],
            'hostel_block_id' => ['nullable', 'exists:hostel_blocks,id'],
            'hostel_block_type' => ['nullable', Rule::in(HostelBlockType::values())],
            'gender' => ['nullable', Rule::in(HostelRoomGender::values())],
            'is_active' => ['nullable', 'boolean'],
        ]);
    }
}
