<?php

namespace App\Http\Requests\Api\Hostel;

use App\Enums\HostelMeritDemeritType;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class HostelMeritDemeritSettingIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'type' => ['nullable', Rule::in(HostelMeritDemeritType::values())],
            'name' => ['nullable', 'string'],
        ]);
    }
}
