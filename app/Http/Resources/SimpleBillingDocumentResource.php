<?php

namespace App\Http\Resources;

use App\Models\Student;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SimpleBillingDocumentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'reference_number' => $this->reference_no,
            'document_date' => $this->document_date,
            'paid_at' => $this->paid_at !== null ? Carbon::parse($this->paid_at, 'UTC')->tz(config('school.timezone'))->toIso8601String() : null,
            'type' => $this->type,
            'sub_type' => $this->sub_type,
            'status' => $this->status,
            'payment_status' => $this->payment_status,
            'bill_to_name' => $this->bill_to_name,
            'bill_to_address' => $this->bill_to_address,
            'bill_to_reference_number' => $this->bill_to_reference_number,
            'tax_code' => $this->tax_code,
            'tax_percentage' => (float) $this->tax_percentage,
            'payment_due_date' => $this->payment_due_date,
            'currency_code' => $this->currency_code,
            'amount_before_tax' => (float) $this->amount_before_tax,
            'amount_before_tax_after_less_advance' => (float) $this->amount_before_tax_after_less_advance,
            'tax_amount' => (float) $this->tax_amount,
            'amount_after_tax' => (float) $this->amount_after_tax,
            'receipt_url' => $this->receipt_url,
        ];
    }
}
