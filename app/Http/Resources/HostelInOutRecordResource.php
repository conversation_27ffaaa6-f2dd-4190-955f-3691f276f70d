<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HostelInOutRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'student' => new StudentResource($this->whenLoaded('student')),
            'guardian' => new GuardianResource($this->whenLoaded('guardian')),
            'hostel_room_bed' => new HostelRoomBedResource($this->whenLoaded('bed')),
            'check_out_datetime' => $this->check_out_datetime,
            'check_out_by' => new UserResource($this->whenLoaded('checkOutBy')),
            'check_in_datetime' => $this->check_in_datetime,
            'check_in_by' => new UserResource($this->whenLoaded('checkInBy')),
            'card_no' => $this->card_no,
            'reason' => $this->reason,
        ];
    }
}
