<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MerchantResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'label' => $this->label,
            'type' => $this->type,
            'is_active' => $this->is_active,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'translations' => $this->translations,
            'user' => new UserResource($this->user),
        ];
    }
}
