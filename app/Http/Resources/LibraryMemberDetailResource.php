<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LibraryMemberDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'register_date' => $this->register_date ? $this->register_date->format('Y-m-d') : null,
            'name' => $this->name,
            'gender' => $this->gender,
            'nric' => $this->nric,
            'date_of_birth' => $this->date_of_birth ? $this->date_of_birth->format('Y-m-d') : null,
            'race' => $this->race ? new RaceResource($this->race) : null,
            'religion' => $this->religion ? new ReligionResource($this->religion) : null,
            'address' => $this->address,
            'postcode' => $this->postcode,
            'city' => $this->city,
            'state' => $this->state ? new StateResource($this->state) : null,
            'country' => $this->country ? new CountryResource($this->country) : null,
            'phone_no' => $this->phone_no,
            'email' => $this->email,
            'translations' => $this->translations
        ];
    }
}
