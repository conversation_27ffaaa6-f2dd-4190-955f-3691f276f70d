<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ConductSettingTeacherResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'employee' => new SimpleEmployeeResource($this->employee),
            'is_homeroom_teacher' => (bool) $this->is_homeroom_teacher,
            'is_active' => (bool) $this->is_active,
        ];
    }
}
