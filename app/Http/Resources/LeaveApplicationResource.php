<?php

namespace App\Http\Resources;

use App\Models\Employee;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LeaveApplicationResource extends JsonResource
{
    public function toArray(Request $request)
    {
        $applier = null;
        switch ($this->leave_applicable_type) {
            case Student::class:
                $applier = new SimpleStudentResource($this->whenLoaded('leaveApplicable'));
                break;
            case Employee::class:
                $applier = new SimpleEmployeeResource($this->whenLoaded('leaveApplicable'));
                break;
        }

        return array_merge([
            'id' => $this->id,
            'leave_applicable_type' => $this->leave_applicable_type,
            'leave_applicable_id' => $this->leave_applicable_id,
            'leave_applicable' => $applier,
            'status' => $this->status,
            'leave_application_type' => new LeaveApplicationTypeResource($this->whenLoaded('leaveApplicationType')),
            'leave_application_periods' => LeaveApplicationPeriodResource::collection($this->whenLoaded('leaveApplicationPeriods')),
            'reason' => $this->reason,
            'proof' => $this->proof_urls,
            'remarks' => $this->remarks,
            'is_present' => (bool) $this->is_present,
            'average_point_deduction' => (float) $this->average_point_deduction,
            'conduct_point_deduction' => (float) $this->conduct_point_deduction,
        ]);
    }
}
