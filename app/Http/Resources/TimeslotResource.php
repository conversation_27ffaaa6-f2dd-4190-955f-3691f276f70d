<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TimeslotResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'day' => $this->day,
            'period' => new PeriodResource($this->whenLoaded('period')),
            'class_subject' => new ClassSubjectResource($this->whenLoaded('classSubject')),
            'placeholder' => $this->placeholder,
            'attendance_from' => $this->attendance_from,
            'attendance_to' => $this->attendance_to,
            'default_init_status' => $this->default_init_status,
            'has_mark_deduction' => (boolean) $this->has_mark_deduction,
            'teachers' => TimeslotTeacherResource::collection($this->whenLoaded('teachers')),
        ];
    }
}
