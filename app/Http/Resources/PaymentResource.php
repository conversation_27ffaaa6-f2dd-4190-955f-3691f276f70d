<?php

namespace App\Http\Resources;

use App\Models\PaymentRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'payment_reference_no' => $this->payment_reference_no,
            'payment_reference_no_2' => $this->payment_reference_no_2,
            'amount_received' => (float) $this->amount_received,
            'paid_at' => $this->paid_at !== null ? Carbon::parse($this->paid_at, 'UTC')->tz(config('school.timezone'))->toIso8601String() : null,
            'remarks' => $this->remarks,
            'bank' => $this->whenLoaded('bank', function () {
                return new BankResource($this->bank);
            }),
            'payment_method' => $this->whenLoaded('paymentMethod', function () {
                return new PaymentMethodResource($this->paymentMethod);
            }),
            'payment_source' => $this->whenLoaded('paymentSource', function () {
                return match ($this->payment_source_type) {
                    PaymentRequest::class => new PaymentRequestResource($this->paymentSource),
                    default => null,
                };
            }),
            'created_by' => $this->whenLoaded('createdByEmployee', function () {
                return new SimpleEmployeeResource($this->createdByEmployee);
            }),

        ];
    }
}
