<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GuardianResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->when($this->pivot?->type, $this->pivot?->type),
            'nric' => $this->nric,
            'passport_number' => $this->passport_number,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'married_status' => $this->married_status,
            'occupation' => $this->occupation,
            'occupation_description' => $this->occupation_description,
            'translations' => $this->translations,
            'has_user_account' => $this->hasUserAccount(),
            'relation_to_student' => $this->when($this->pivot?->relation_to_student, $this->pivot?->relation_to_student),
            'is_primary' => $this->when($this->pivot, $this->pivot?->is_primary),
            'is_direct_dependant' => $this->when(isset($this->pivot?->is_direct_dependant), $this->pivot?->is_direct_dependant),
            'live_status' => $this->live_status,
            'nationality' => $this->whenLoaded('country', function () {
                return new CountryResource($this->country);
            }),
            'race' => $this->whenLoaded('race', function () {
                return new RaceResource($this->race);
            }),
            'religion' => $this->whenLoaded('religion', function () {
                return new ReligionResource($this->religion);
            }),
            'education' => $this->whenLoaded('education', function () {
                return new EducationResource($this->education);
            }),
        ];
    }
}
