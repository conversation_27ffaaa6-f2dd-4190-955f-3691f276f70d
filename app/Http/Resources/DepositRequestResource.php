<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepositRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'order_id' => (string)$this->order_id ?: null,
            'currency' => (string)$this->currency_code ?: null,
            'amount' => (float)$this->amount,
            'payment_url' => (string)$this->payment_url ?: null
        ];
    }
}
