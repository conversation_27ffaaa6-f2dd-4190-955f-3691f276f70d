<?php

namespace App\Http\Resources;

use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserableViewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'userable_id' => $this->userable_id,
            'userable_type' => $this->userable_type,
            'user_type_description' => $this->user_type_description,
            'name' => $this->name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'number' => $this->number,
            'wallets' => WalletResource::collection($this->whenLoaded('wallets')),
            'photo' => $this->userable->getProfilePicture(),
            'current_primary_class' => $this->when($this->userable_type === Student::class, function () {
                $student = $this->userable;
                return new StudentClassResource($student->currentSemesterPrimaryClass);
            }),
            'translations' => $this->translations,
        ];
    }
}
