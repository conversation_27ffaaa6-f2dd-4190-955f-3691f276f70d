<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EnrollmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'phone_number_2' => $this->phone_number_2,
            'nric' => $this->nric,
            'passport_number' => $this->passport_number,
            'admission_year' => $this->admission_year,
            'admission_grade' => $this->whenLoaded('admissionGrade', function () {
                return new GradeResource($this->admissionGrade);
            }),
            'join_date' => $this->join_date,
            'leave_date' => $this->leave_date,
            'leave_status' => $this->leave_status,
            'student_number' => $this->student_number,
            'birthplace' => $this->birthplace,
            'nationality' => $this->whenLoaded('nationality', function () {
                return new CountryResource($this->nationality);
            }),
            'date_of_birth' => $this->date_of_birth,
            'gender' => $this->gender->value,
            'birth_cert_number' => $this->birth_cert_number,
            'race' => $this->whenLoaded('race', function () {
                return new RaceResource($this->race);
            }),
            'religion' => $this->whenLoaded('religion', function () {
                return new ReligionResource($this->religion);
            }),
            'address' => $this->address,
            'postal_code' => $this->postal_code,
            'city' => $this->city,
            'state' => $this->whenLoaded('state', function () {
                return new StateResource($this->state);
            }),
            'country' => $this->whenLoaded('country', function () {
                return new CountryResource($this->country);
            }),
            'address_2' => $this->address_2,
            'is_hostel' => $this->is_hostel,
            'is_active' => $this->is_active,
            'remarks' => $this->remarks,
            'custom_field' => $this->custom_field,
            'dietary_restriction' => $this->dietary_restriction,
            'health_concern' => $this->whenLoaded('healthConcern', function () {
                return new HealthConcernResource($this->healthConcern);
            }),
            'primary_school' => $this->whenLoaded('primarySchool', function () {
                return new SchoolResource($this->primarySchool);
            }),
            'admission_type' => $this->admission_type,
            'enrollment_status' => $this->enrollment_status,
            'payment_status' => $this->payment_status,
            'have_siblings' => $this->have_siblings,
            'is_foreigner' => $this->is_foreigner,
            'conduct' => $this->conduct,
            'token' => $this->token,
            'registration_date' => $this->registration_date,
            'expiry_date' => $this->expiry_date,
            'enrollment_session' => $this->whenLoaded('enrollmentSession', function () {
                return new EnrollmentSessionResource($this->enrollmentSession);
            }),
            'enrollment_user' => $this->whenLoaded('enrollmentUser', function () {
                return new EnrollmentUserResource($this->enrollmentUser);
            }),
            'fees_to_be_paid' => $this->show_fee ? $this->determineFees() : null,
            'translations' => $this->translations,
            'billing_documents' => $this->whenLoaded('billingDocuments', function () {
                return BillingDocumentResource::collection($this->billingDocuments);
            }),
            'guardians' => $this->whenLoaded('guardians', function () {
                return EnrollmentGuardianResource::collection($this->guardians);
            }),
            'enrollment_exams' => $this->whenLoaded('enrollmentExams', function () {
                return EnrollmentExamResource::collection($this->enrollmentExams);
            }),
        ];
    }
}
