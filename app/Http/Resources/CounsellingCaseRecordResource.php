<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CounsellingCaseRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'student' => new StudentResource($this->whenLoaded('student')),
            'created_by' => new SimpleEmployeeResource($this->whenLoaded('createdBy')),
            'visit_datetime' => $this->visit_datetime,
            'note' => $this->note,
            'status' => $this->status,
        ];
    }
}
