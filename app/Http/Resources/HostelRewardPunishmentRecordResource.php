<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HostelRewardPunishmentRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'person_in_charge' => new SimpleEmployeeResource($this->personInCharge),
            'student' => new StudentResource($this->student),
            'date' => $this->date,
            'hostel_reward_punishment_setting' => new HostelRewardPunishmentSettingResource($this->hostelRewardPunishmentSetting),
            'remark' => $this->remark,
            'created_by' => new UserResource($this->createdBy),
        ];
    }
}
