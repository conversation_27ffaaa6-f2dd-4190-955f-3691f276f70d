<?php

namespace App\Interfaces;

use App\Enums\PaymentStatus;
use App\Models\PaymentGatewayLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

interface IPaymentGateway
{
    public function generateOrderId(PaymentGatewayLog|Model $request): string;

    public function setOrderId(string $order_id): IPaymentGateway;

    public function setAmount(float $amount): IPaymentGateway;

    public function setCurrency(string $currency): IPaymentGateway;

    public function setPaymentTypes(array $payment_types): IPaymentGateway;

    public function setCustomerName(string $customer_name): IPaymentGateway;

    public function setCustomerEmail(?string $customer_email): IPaymentGateway;

    public function setDescription(string $description): IPaymentGateway;

    // Lucas: If txn_id missing from payex, just set the whole payload as transaction and process from there
    public function setTransaction(array $callback_data): self;

    public function request(): string;

    public function getRequestData(): array;

    public function getResponseData(): array;

    public function validateCallbackData(array $callback_data): void;

    public function getTransaction(array $callback_data): array;

    public function getTransactionByReferenceNumber(string $reference_number): array;

    public function getTransactionAmount(): float;

    public function getTransactionOrderId(): string;

    public function getTransactionResponse(): ?string;

    // Lucas: Set this to nullable coz Payex will return null if didn't reach the bank page
    public function getTransactionId(): ?string;

    public function getTransactionStatus(): PaymentStatus;

    public function getTransactionDate(): Carbon|null;

    public function getPaymentTypeFromMapping(string $payment_type): string;
}
