<?php

namespace App\Interfaces;

use App\Models\EmployeeJobTitle;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;

interface StatusChangeableInterface
{
    public function leaveOrResign($effective_date): StatusChangeableInterface;

    public function validateLeaveOrResign($date): StatusChangeableInterface;

    public function returnOrReinstate($effective_date_string): StatusChangeableInterface;

    public function validateReturnOrReinstate(): StatusChangeableInterface;

    public function setStatusChangeable(StatusChangeable $status_changeable): StatusChangeableInterface;

    public function transfer($effective_date_string): StatusChangeableInterface;

    public function validateTransfer($effective_date_string): StatusChangeableInterface;

    public function setNewJobTitle(EmployeeJobTitle $new_job_title): StatusChangeableInterface;

    public function setSemesterSetting(SemesterSetting $semester_setting): StatusChangeableInterface;

    public function setSemesterClass(SemesterClass $semester_class): StatusChangeableInterface;
    public function setRemarks(?string $remarks): StatusChangeableInterface;
}
