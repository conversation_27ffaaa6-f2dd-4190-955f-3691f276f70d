<?php

namespace App\Repositories;

use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Models\BillingDocument;
use App\Models\Enrollment;
use App\Models\EnrollmentSession;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class EnrollmentSessionRepository extends BaseRepository
{
    private const ONE_DAY_AGO = 1;
    private const ONE_WEEK_AGO = 7;
    private const ONE_MONTH_AGO = 30;

    public function getModelClass(): string
    {
        return EnrollmentSession::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%" . $filters['name'] . "%");
            })
            ->when(isset($filters['from_date']), function (Builder $query) use ($filters) {
                $query->where('from_date', '>=', $filters['from_date']);
            })
            ->when(isset($filters['to_date']), function (Builder $query) use ($filters) {
                $query->where('to_date', '<=', $filters['to_date']);
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['course_id']), function (Builder $query) use ($filters) {
                $query->where('course_id', $filters['course_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function getSummary(EnrollmentSession $enrollment_session): array
    {
        $now = Carbon::now()->startOfDay();
        $one_day_ago = $now->copy()->subDays(self::ONE_DAY_AGO);
        $seven_days_ago = $now->copy()->subDays(self::ONE_WEEK_AGO);
        $thirty_days_ago = $now->copy()->subDays(self::ONE_MONTH_AGO);

        $enrollment_stats = DB::table('enrollments as e')
            ->selectRaw(
                '
                    -- total applications
                    COUNT(e.id) as total_applications,

                    -- submission date
                    SUM(CASE WHEN e.created_at >= ? THEN 1 ELSE 0 END) as one_day_ago,
                    SUM(CASE WHEN e.created_at >= ? AND e.created_at < ? THEN 1 ELSE 0 END) as seven_days_ago,
                    SUM(CASE WHEN e.created_at >= ? AND e.created_at < ? THEN 1 ELSE 0 END) as thirty_days_ago,

                    -- enrollment status
                    SUM(CASE WHEN e.enrollment_status = ? THEN 1 ELSE 0 END) as approved_count,
                    SUM(CASE WHEN e.enrollment_status = ? THEN 1 ELSE 0 END) as shortlisted_count,
                    SUM(CASE WHEN e.enrollment_status = ? THEN 1 ELSE 0 END) as rejected_count,

                    -- gender (APPROVED)
                    SUM(CASE WHEN e.gender = ? AND e.enrollment_status = ? THEN 1 ELSE 0 END) as male_count,
                    SUM(CASE WHEN e.gender = ? AND e.enrollment_status = ? THEN 1 ELSE 0 END) as female_count,

                    -- hostel (APPROVED)
                    SUM(CASE WHEN e.is_hostel = true AND e.enrollment_status = ? THEN 1 ELSE 0 END) as hostel_count,
                    SUM(CASE WHEN e.is_hostel = false AND e.enrollment_status = ? THEN 1 ELSE 0 END) as non_hostel_count,

                    -- citizenship (APPROVED)
                    SUM(CASE WHEN e.is_foreigner = true AND e.enrollment_status = ? THEN 1 ELSE 0 END) as foreigner_count,
                    SUM(CASE WHEN e.is_foreigner = false AND e.enrollment_status = ? THEN 1 ELSE 0 END) as local_count,

                    -- payment_status
                    SUM(CASE WHEN e.payment_status = ? THEN 1 ELSE 0 END) as paid_count,
                    SUM(CASE WHEN e.payment_status = ? THEN 1 ELSE 0 END) as unpaid_count

                ',
                [
                    // submission date
                    $one_day_ago,
                    $seven_days_ago,
                    $one_day_ago,
                    $thirty_days_ago,
                    $seven_days_ago,
                    // enrollment status
                    EnrollmentStatus::APPROVED->value,
                    EnrollmentStatus::SHORTLISTED->value,
                    EnrollmentStatus::REJECTED->value,
                    // gender MALE (APPROVED)
                    Gender::MALE->value,
                    EnrollmentStatus::APPROVED->value,
                    // gender FEMALE (APPROVED)
                    Gender::FEMALE->value,
                    EnrollmentStatus::APPROVED->value,
                    // hostel (APPROVED)
                    EnrollmentStatus::APPROVED->value,
                    EnrollmentStatus::APPROVED->value,
                    // citizenship (APPROVED)
                    EnrollmentStatus::APPROVED->value,
                    EnrollmentStatus::APPROVED->value,
                    // payment_status
                    EnrollmentPaymentStatus::PAID->value,
                    EnrollmentPaymentStatus::UNPAID->value,
                ],
            )
            ->where('e.enrollment_session_id', $enrollment_session->id)
            ->first();

        $exam_stats = DB::table('enrollments as e')
            ->leftJoin('enrollment_exams as ee', 'e.id', '=', 'ee.enrollment_id')
            ->selectRaw(
                '
                    COUNT(DISTINCT CASE WHEN ee.id IS NOT NULL THEN e.id END) as with_exam,
                    COUNT(DISTINCT CASE WHEN ee.id IS NULL THEN e.id END) as without_exam
                '
            )
            ->where('e.enrollment_session_id', $enrollment_session->id)
            ->first();

        $payment_stats = DB::table('billing_documents as bd')
            ->join('enrollments as e', 'bd.bill_to_id', '=', 'e.id')
            ->leftJoin('payments as p', 'bd.id', '=', 'p.billing_document_id')
            ->selectRaw(
                '
                    COALESCE(SUM(p.amount_received), 0) as total_fee_collection,
                    COUNT(DISTINCT CASE WHEN bd.payment_status = ? THEN bd.id END) as paid_count,
                    COUNT(DISTINCT CASE WHEN bd.payment_status IN (?, ?) THEN bd.id END) as unpaid_count
                ',
                [
                    BillingDocument::PAYMENT_STATUS_PAID,
                    BillingDocument::PAYMENT_STATUS_UNPAID,
                    BillingDocument::PAYMENT_STATUS_PARTIAL,
                ],
            )
            ->where('bd.bill_to_type', Enrollment::class)
            ->where('e.enrollment_session_id', $enrollment_session->id)
            ->first();

        return [
            // ENROLLMENTS
            'total_applications_submitted' => $enrollment_stats->total_applications ?? 0,
            'by_submission_date' => [
                'one_day_ago' => $enrollment_stats->one_day_ago ?? 0,
                'seven_days_ago' => $enrollment_stats->seven_days_ago ?? 0,
                'thirty_days_ago' => $enrollment_stats->thirty_days_ago ?? 0,
            ],
            'by_enrollment_status' => [
                'approved_count' => $enrollment_stats->approved_count ?? 0,
                'shortlisted_count' => $enrollment_stats->shortlisted_count ?? 0,
                'rejected_count' => $enrollment_stats->rejected_count ?? 0,
            ],
            'total_confirmed_students' => $enrollment_stats->approved_count ?? 0,
            'by_gender' => [
                'male_count' => $enrollment_stats->male_count ?? 0,
                'female_count' => $enrollment_stats->female_count ?? 0,
            ],
            'by_hostel' => [
                'hostel_count' => $enrollment_stats->hostel_count ?? 0,
                'non_hostel_count' => $enrollment_stats->non_hostel_count ?? 0,
            ],
            'by_citizenship' => [
                'foreigner_count' => $enrollment_stats->foreigner_count ?? 0,
                'local_count' => $enrollment_stats->local_count ?? 0,
            ],
            // EXAM
            'by_exam_requirement' => [
                'exam' => $exam_stats->with_exam ?? 0,
                'no_exam' => $exam_stats->without_exam ?? 0,
            ],
            // PAYMENTS
            'full_fee_collection' => number_format($payment_stats->total_fee_collection, 2) ?? 0,
            'by_full_payment_status' => [
                'paid_count' => $enrollment_stats->paid_count ?? 0,
                'unpaid_count' => $enrollment_stats->unpaid_count ?? 0,
            ],
        ];
    }
}
