<?php

namespace App\Repositories;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Spatie\Permission\Models\Permission;

class PermissionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Permission::class;
    }

    protected function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query->when(isset($filters['name']), function (Builder $query) use ($filters) {
            $query->where('name', "ILIKE", "%". $filters['name'] ."%");
        });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
