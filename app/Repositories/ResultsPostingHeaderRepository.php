<?php

namespace App\Repositories;

use App\Models\Grade;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ResultsPostingHeaderRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ResultsPostingHeader::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        $query
            ->when(isset($filters['report_card_output_code']), function (Builder $query) use ($filters) {
                $query->where('report_card_output_code', $filters['report_card_output_code']);
            })
            ->when(isset($filters['grade_id']), function (Builder $query) use ($filters) {
                $query->where('grade_id', $filters['grade_id']);
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                if (is_array($filters['status'])) {
                    $query->whereIn('status', $filters['status']);
                } else {
                    $query->where('status', $filters['status']);
                }
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->whereJsonContains('student_ids', $filters['student_id']);
            })
            ->when(isset($filters['active_report_card_only']), function (Builder $query) use ($filters) {
                if ($filters['active_report_card_only'] == true) {
                    $query->whereRelation('studentReportCard', 'is_active', true);
                }
            })
            ->when(isset($filters['publish_date']), function (Builder $query) use ($filters){
                $query->where('publish_date', $filters['publish_date']);
            });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function hasCurrentlyProcessingPosting(Grade $grade, SemesterSetting $semester)
    {
        return $this->getAll([
                'grade_id' => $grade->id,
                'semester_setting_id' => $semester->id,
                'status' => [ResultsPostingHeader::STATUS_PENDING, ResultsPostingHeader::STATUS_IN_PROGRESS],
            ])->count() > 0;

    }
}
