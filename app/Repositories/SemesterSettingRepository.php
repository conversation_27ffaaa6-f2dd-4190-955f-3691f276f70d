<?php

namespace App\Repositories;

use App\Models\Course;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SemesterSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return SemesterSetting::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        $order_by = optional(optional($filters)['order_by']);

        return parent::getQuery($filters)
            ->select($this->getModelTableName() . '.*')
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['semester_years']), function (Builder $query) use ($filters) {
                $query->whereRelation('semesterYearSetting', function (Builder $semester_year_setting_query) use ($filters) {
                    $semester_year_setting_query->whereIn('master_semester_year_settings.id', $filters['semester_years']);
                });
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->where($this->getModelTableName() . '.name', 'ILIKE', "%" . $filters['name'] . "%");
            })
            ->when(isset($filters['courses']), function (Builder $query) use ($filters) {
                $query->whereRelation('course', function (Builder $course_query) use ($filters) {
                    $course_query->whereIn('master_courses.id', $filters['courses']);
                });
            })
            ->when(isset($filters['from']), function (Builder $query) use ($filters) {
                $query->where('from', '>=', $filters['from']);
            })
            ->when(isset($filters['is_current_semester']), function (Builder $query) use ($filters) {
                $query->where('is_current_semester', $filters['is_current_semester']);
            })
            ->when(isset($filters['to']), function (Builder $query) use ($filters) {
                $query->where('to', '<=', $filters['to']);
            })
            ->when(isset($filters['current_date']), function (Builder $query) use ($filters) {
                $query->where('from', '<=', $filters['current_date'])
                    ->where('to', '>=', $filters['current_date']);
            })
            ->when($order_by['semester_year'], function (Builder $query, $sort_direction) {
                $query->join('master_semester_year_settings as sys', 'sys.id', '=', 'master_semester_settings.semester_year_setting_id');

                $this->setupOrderBy($query, ['year' => $sort_direction], SemesterYearSetting::class, 'sys.');
            })
            ->when($order_by['course'], function (Builder $query, $order_by) {
                $query->join('master_courses as c', 'c.id', '=', 'master_semester_settings.course_id');

                $this->setupOrderBy($query, ['name' => $order_by], Course::class, 'c.');
            });
    }
}
