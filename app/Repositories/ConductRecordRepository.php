<?php

namespace App\Repositories;

use App\Models\ConductRecord;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ConductRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ConductRecord::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['conduct_setting_id']), function (Builder $query) use ($filters) {
                $query->where('conduct_setting_id', $filters['conduct_setting_id']);
            })
            ->when(isset($filters['conduct_setting_teacher_id']), function (Builder $query) use ($filters) {
                $query->where('conduct_setting_teacher_id', $filters['conduct_setting_teacher_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
