<?php

namespace App\Repositories;

use App\Models\StudentHistory;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class StudentHistoryRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return StudentHistory::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
            $query->where('student_id', $filters['student_id']);
        })->when(isset($filters['event_type']), function (Builder $query) use ($filters) {
            $query->where('event_type', $filters['event_type']);
        });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getByIds(array $ids, mixed $with): Collection
    {
        return $this->getQuery(['with' => $with])
            ->whereIn('id', $ids)
            ->get();
    }
}
