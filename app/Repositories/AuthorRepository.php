<?php

namespace App\Repositories;

use App\Models\Author;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AuthorRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Author::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                if(is_array($filters['name'])) {
                    $query->whereIn('name', $filters['name']);
                } else {
                    $query->where('name', 'LIKE', "%" . $filters['name'] . "%");
                }
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
