<?php

namespace App\Repositories;

use App\Models\StudentSocietyPosition;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class StudentSocietyPositionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return StudentSocietyPosition::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                if (is_array($filters['student_id'])) {
                    $query->whereIn('student_id', $filters['student_id']);
                } else {
                    $query->where('student_id', $filters['student_id']);
                }
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                $query->where('semester_class_id', $filters['semester_class_id']);
            })
            ->when(isset($filters['society_position_id']), function (Builder $query) use ($filters) {
                $query->where('society_position_id', $filters['society_position_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function deleteAllPositionsForStudentsByClass(int $semester_class_id, array $student_ids): void
    {
        $this->getQuery()
            ->where('semester_class_id', $semester_class_id)
            ->whereIn('student_id', $student_ids)
            ->delete();
    }
}
