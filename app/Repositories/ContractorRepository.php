<?php

namespace App\Repositories;

use App\Models\Contractor;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ContractorRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Contractor::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    protected function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['contractor_number']), function (Builder $query) use ($filters) {
                if (is_array($filters['contractor_number'])) {
                    $query->whereIn('contractor_number', $filters['contractor_number']);
                } else {
                    $query->where('contractor_number', $filters['contractor_number']);
                }
            })
            ->when(isset($filters['user_id']), function (Builder $query) use ($filters) {
                $query->where('user_id', $filters['user_id']);
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->whereHas('classSubjects.semesterClass', function ($query) use ($filters) {
                    $query->where('semester_setting_id', $filters['semester_setting_id']);
                });
            })
            ->when(isset($filters['department']), function (Builder $query) use ($filters) {
                $query->where('department', $filters['department']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['email']), function (Builder $query) use ($filters) {
                $query->where('email', 'ILIKE', '%'.$filters['email'].'%');
            })
            ->when(isset($filters['phone_number']), function (Builder $query) use ($filters) {
                $query->where('phone_number', 'ILIKE', '%'.$filters['phone_number'].'%');
            })
            ->when(isset($filters['nric']), function (Builder $query) use ($filters) {
                $query->where('nric', $filters['nric']);
            })
            ->when(isset($filters['passport_number']), function (Builder $query) use ($filters) {
                $query->where('passport_number', $filters['passport_number']);
            });
    }
}
