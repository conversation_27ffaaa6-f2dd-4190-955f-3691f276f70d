<?php

namespace App\Repositories;

use App\Models\HostelBlock;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class HostelBlockRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return HostelBlock::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                if (is_array($filters['code'])) {
                    $query->whereIn('code', $filters['code']);
                } else {
                    $query->where('code', $filters['code']);
                }
            })
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function hostelBlockHasRooms(HostelBlock|int $hostel_block): bool
    {
        if ($hostel_block instanceof HostelBlock) {
            return $hostel_block->hasRooms();
        } else {
            return $this->findOrFail($hostel_block)->hasRooms();
        }
    }
}
