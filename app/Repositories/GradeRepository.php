<?php

namespace App\Repositories;

use App\Enums\ClassType;
use App\Models\Grade;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class GradeRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Grade::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        $filters = optional($filters);

        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getDataForEcommerceCanteenReport(array $filters = []): Collection
    {
        $filters = optional($filters);

        $this->with = [
            'classes' => function ($query) {
                $query->where('type', ClassType::PRIMARY);
            },
            'classes.semesterClasses' => function ($query) use ($filters) {
                $query->where('semester_classes.semester_setting_id', $filters['semester_setting_id']);
            }
        ];

        return parent::getQuery($filters)
            ->whereHas('classes.semesterClasses', function ($query) use ($filters) {
                $query->where('classes.type', ClassType::PRIMARY)
                    ->where('semester_classes.semester_setting_id', $filters['semester_setting_id']);
            })
            ->get();
    }

    protected function getQuery($filters = []): Builder
    {
        $this->with = $filters['includes'] ?? [];

        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            });
    }
}
