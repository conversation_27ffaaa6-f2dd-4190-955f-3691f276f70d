<?php

namespace App\Repositories;

use App\Enums\CardStatus;
use App\Enums\ClassType;
use App\Enums\Gender;
use App\Enums\StudentAdmissionType;
use App\Models\ClassModel;
use App\Models\Grade;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class StudentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Student::class;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getQuery($filters = []): Builder
    {
        if (isset($filters['attendance_date'])) {
            $includes['attendances'] = function ($query) use ($filters) {
                $query->where('date', $filters['attendance_date']);
            };

            $filters['includes'] = array_merge($filters['includes'] ?? [], $includes);
        }

        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['nric']), function (Builder $query) use ($filters) {
                if (is_array($filters['nric'])) {
                    $query->whereIn('nric', $filters['nric']);
                } else {
                    $query->where('nric', $filters['nric']);
                }
            })
            ->when(isset($filters['passport_number']), function (Builder $query) use ($filters) {
                if (is_array($filters['passport_number'])) {
                    $query->whereIn('passport_number', $filters['passport_number']);
                } else {
                    $query->where('passport_number', $filters['passport_number']);
                }
            })
            ->when(isset($filters['library_book_loan_date_from']), function (Builder $query) use ($filters) {
                $query->whereHas('libraryMember.bookLoans', function ($query) use ($filters) {
                    $query->where('loan_date', '>=', $filters['library_book_loan_date_from']);
                });
            })
            ->when(isset($filters['library_book_loan_date_to']), function (Builder $query) use ($filters) {
                $query->whereHas('libraryMember.bookLoans', function ($query) use ($filters) {
                    $query->where('loan_date', '<=', $filters['library_book_loan_date_to']);
                });
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['guardian_name']) || isset($filters['guardian_phone_number']) || isset($filters['guardian_email']), function (Builder $query) use ($filters) {
                $query->whereHas('guardians', function ($query) use ($filters) {
                    $query
                        ->when(isset($filters['guardian_name']), function (Builder $query) use ($filters) {
                            $query->whereTranslations('name', $filters['guardian_name'], 'ILIKE', true);
                        })
                        ->when(isset($filters['guardian_phone_number']), function (Builder $query) use ($filters) {
                            $query->where('phone_number', 'ILIKE', '%' . $filters['guardian_phone_number'] . '%');
                        })
                        ->when(isset($filters['guardian_email']), function (Builder $query) use ($filters) {
                            $query->where('email', 'ILIKE', '%' . $filters['guardian_email'] . '%');
                        });
                });
            })
            ->when(isset($filters['email']), function (Builder $query) use ($filters) {
                $query->where('students.email', 'ILIKE', '%' . $filters['email'] . '%');
            })
            ->when(isset($filters['phone_number']), function (Builder $query) use ($filters) {
                $query->where('students.phone_number', 'ILIKE', '%' . $filters['phone_number'] . '%');
            })
            ->when(isset($filters['admission_grade_id']), function (Builder $query) use ($filters) {
                $query->where('admission_grade_id', $filters['admission_grade_id']);
            })
            ->when(isset($filters['admission_type']), function (Builder $query) use ($filters) {
                $query->where('admission_type', $filters['admission_type']);
            })
            ->when(isset($filters['gender']), function (Builder $query) use ($filters) {
                $query->where('gender', $filters['gender']);
            })
            ->when(isset($filters['race_id']), function (Builder $query) use ($filters) {
                $query->where('race_id', $filters['race_id']);
            })
            ->when(isset($filters['religion_id']), function (Builder $query) use ($filters) {
                $query->where('religion_id', $filters['religion_id']);
            })
            ->when(isset($filters['state_id']), function (Builder $query) use ($filters) {
                $query->where('state_id', $filters['state_id']);
            })
            ->when(isset($filters['country_id']), function (Builder $query) use ($filters) {
                $query->where('country_id', $filters['country_id']);
            })
            ->when(isset($filters['is_hostel']), function (Builder $query) use ($filters) {
                $query->where('is_hostel', $filters['is_hostel']);
            })
            ->when(isset($filters['student_number']), function (Builder $query) use ($filters) {
                if (is_array($filters['student_number'])) {
                    $query->whereIn('student_number', $filters['student_number']);
                } else {
                    $query->where('student_number', $filters['student_number']);
                }
            })
            ->when(isset($filters['student_number_wildcard']), function (Builder $query) use ($filters) {
                $query->where('student_number', 'LIKE', "%" . $filters['student_number_wildcard'] . "%");
            })
            ->when(
                isset($filters['semester_setting_id']) ||
                isset($filters['semester_class_id']) ||
                isset($filters['semester_class_ids']) ||
                isset($filters['grade_id']) ||
                isset($filters['grade_ids']) ||
                isset($filters['class_stream']) ||
                isset($filters['is_latest_class_in_semester']),
                function (Builder $query) use ($filters) {
                    $query->whereHas('classes', function (Builder $query) use ($filters) {
                        // If not set, default to true, only use false if want to get all classes as history
                        if (isset($filters['is_latest_class_in_semester'])) {
                            $query->where('is_latest_class_in_semester', $filters['is_latest_class_in_semester']);
                        } else {
                            $query->where('is_latest_class_in_semester', true);
                        }

                        if (isset($filters['semester_setting_id'])) {
                            $query->where('semester_setting_id', $filters['semester_setting_id']);
                        }
                        if (isset($filters['semester_class_id'])) {
                            $query->where('semester_class_id', $filters['semester_class_id']);
                        }
                        if (isset($filters['semester_class_ids'])) {
                            $query->whereIn('semester_class_id', $filters['semester_class_ids']);
                        }
                        if (isset($filters['grade_id'])) {
                            $query->whereRelation('semesterClass.classModel', 'grade_id', $filters['grade_id']);
                        }
                        if (isset($filters['grade_ids'])) {
                            $query->whereHas('semesterClass.classModel', function ($query) use ($filters) {
                                $query->whereIn('classes.grade_id', $filters['grade_ids']);
                            });
                        }
                        if (isset($filters['class_stream'])) {
                            $query->whereHas('semesterClass.classModel', function ($query) use ($filters) {
                                $query->where('classes.stream', $filters['class_stream']);
                            });
                        }
                        if (isset($filters['only_active_class'])) {
                            $query->where('is_active', true);
                        }
                    });
                })
            ->when(isset($filters['latest_semester_class_id']), function (Builder $query) use ($filters) {
                $query->whereHas('latestPrimaryClassBySemesterSettings', function (Builder $query) use ($filters) {
                    if (is_array($filters['latest_semester_class_id'])) {
                        $query->whereIn('semester_class_id', $filters['latest_semester_class_id']);
                    } else {
                        $query->where('semester_class_id', $filters['latest_semester_class_id']);
                    }
                });
            })
            ->when(isset($filters['latest_class_semester_setting_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('latestPrimaryClassBySemesterSettings', 'semester_setting_id', $filters['latest_class_semester_setting_id']);
            })
            ->when(isset($filters['is_active']), function (Builder $query) use ($filters) {
                $query->where('is_active', $filters['is_active']);
            })
            ->when(isset($filters['common_search']), function (Builder $query) use ($filters) {
                $query->where(function ($query) use ($filters) {
                    $query->whereTranslations('name', $filters['common_search'], 'ILIKE', true)
                        ->when(!is_array($filters['common_search']), function ($query) use ($filters) {
                            $query->orWhere('student_number', "ILIKE", "%" . $filters['common_search'] . "%");
                        });
                });
            })
            ->when(isset($filters['is_checked_out']), function (Builder $query) use ($filters) {
                if ($filters['is_checked_out']) {
                    // filter student where in_out_record is incomplete
                    $query->whereRelation('hostelInOutRecords', 'check_in_datetime', null);
                } else {
                    // filter student where student is currently in
                    $query->whereHas('activeHostelBedAssignments')->whereDoesntHave('hostelInOutRecords', function ($query) {
                        $query->where('check_in_datetime', null);
                    });
                }
            })
            ->when(
                (isset($filters['has_active_bed']) ||
                    isset($filters['block_id']) ||
                    isset($filters['room_id']) ||
                    isset($filters['bed_id'])),
                function (Builder $query) use ($filters) {
                    if (!isset($filters['has_active_bed']) || $filters['has_active_bed']) {
                        $query->whereHas('activeHostelBedAssignments', function ($query) use ($filters) {
                            if (isset($filters['block_id'])) {
                                $query->whereRelation('bed.hostelRoom.hostelBlock', 'id', $filters['block_id']);
                            }

                            if (isset($filters['room_id'])) {
                                $query->whereRelation('bed.hostelRoom', 'id', $filters['room_id']);
                            }

                            if (isset($filters['bed_id'])) {
                                $query->whereRelation('bed', 'id', $filters['bed_id']);
                            }
                        });
                    } else {
                        $query->whereDoesntHave('activeHostelBedAssignments');
                    }
                }
            )
            ->orderByDesc($this->getModelTableName() . '.id');
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getByIds(array $ids, mixed $with = null): Collection
    {
        return $this->getQuery(['with' => $with])
            ->whereIn('id', $ids)
            ->get();
    }

    public function getFirstByStudentNumberOrCardNumber($number): Student|Model|null
    {
        return $this->getQuery()
            ->where('student_number', $number)
            ->orWhereHas('cards', function ($query) use ($number) {
                $query->where('status', CardStatus::ACTIVE->value)
                    ->where('card_number', $number);
            })->first();
    }

    public function getHostelBoarderListData(array $filters = []): Collection
    {
        $filters = optional($filters);

        $parent_filters = [
            'includes' => [
                'activeHostelBedAssignments.bed.hostelRoom.hostelBlock',
                'latestPrimaryClass' => function ($query) use ($filters) {
                    $query->with('semesterClass.classModel.grade');
                    $query->where('semester_setting_id', $filters['semester_setting_id']);
                },
            ]
        ];

        return $this->getQuery($parent_filters)
            ->select(['id', 'student_number', 'name'])
            ->orderBy('student_number', 'ASC')
            ->where('is_hostel', true)
            ->whereHas('classes', function ($query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id'])
                    ->when($filters['grade_id'], function ($query, $grade_id) {
                        $query->whereRelation('semesterClass.classModel', 'grade_id', $grade_id);
                    })
                    ->when($filters['semester_class_id'], function ($query, $semester_class_id) {
                        $query->where('semester_class_id', $semester_class_id);
                    });
            })
            ->get();
    }

    public function getHostelBoardersData(array $filters = []): Collection
    {
        $filters = optional($filters);

        $parent_filters = [
            'includes' => [
                'firstActiveHostelBedAssignment' => function ($query) {
                    $query->select(['id', 'assignable_type', 'assignable_id', 'hostel_room_bed_id', 'start_date', 'end_date']);
                    $query->with([
                        'bed' => function ($query) {
                            $query->select(['id', 'hostel_room_id', 'name']);
                            $query->with([
                                'hostelRoom' => function ($query) {
                                    $query->select(['id', 'name', 'hostel_block_id']);
                                    $query->with([
                                        'hostelBlock' => function ($query) {
                                            $query->select(['id', 'name']);
                                        }
                                    ]);
                                }
                            ]);
                        }
                    ]);
                },
                'latestPrimaryClass' => function ($query) use ($filters) {
                    $query->select(['id', 'student_id', 'semester_class_id']);
                    $query->with([
                        'semesterClass' => function ($query) {
                            $query->select(['id', 'class_id']);
                            $query->with([
                                'classModel' => function ($query) {
                                    $query->select(['id', 'name', 'grade_id']);
                                    $query->with('grade:id,name');
                                }
                            ]);
                        }
                    ]);

                    $query->where('semester_setting_id', $filters['semester_setting_id']);
                },
                'guardians' => function ($query) {
                    $query->select(['id', 'name', 'nric', 'phone_number']);
                    $query->orderBy('id', 'asc');
                },
            ]
        ];

        if (!empty($filters['no_guardians']) && $filters['no_guardians'] === true) {
            unset($parent_filters['includes']['guardians']); // remove guardian relationship
        }

        $data = $this->getQuery($parent_filters)
            ->select(['id', 'student_number', 'name', 'nric', 'address', 'phone_number', 'date_of_birth'])
            ->where('is_hostel', true)
            ->get();

        return $data
            ->transform(function ($boarder) use ($filters) {
                $guardians = [];

                if ($boarder->relationLoaded('guardians')) {
                    if (empty($filters['all_guardians'])) { // only get the first guardian

                        $first_guardian = $boarder?->guardians->first();

                        if (isset($first_guardian)) {
                            $guardians[] = $first_guardian->toArray();
                        }
                    } else {
                        $guardians = $boarder->guardians->toArray();
                    }
                }

                return [
                    'student_id' => $boarder->id,
                    'student_number' => $boarder->student_number,
                    'student_name' => $boarder->getTranslations('name'),
                    'student_nric' => $boarder->nric,
                    'student_address' => $boarder->address,
                    'student_phone_number' => $boarder->phone_number,
                    'student_date_of_birth' => $boarder->date_of_birth,
                    'start_date' => $boarder->firstActiveHostelBedAssignment?->start_date?->toDateString(),
                    'end_date' => $boarder->firstActiveHostelBedAssignment?->end_date?->toDateString(),
                    'block_name' => $boarder->firstActiveHostelBedAssignment?->bed?->hostelRoom?->hostelBlock?->getTranslation('name', app()->getLocale()),
                    'bed_name' => $boarder->firstActiveHostelBedAssignment?->bed?->name,
                    'room_name' => $boarder->firstActiveHostelBedAssignment?->bed?->hostelRoom?->name,
                    'class_name' => $boarder->latestPrimaryClass?->semesterClass?->classModel?->getTranslations('name'),
                    'grade_name' => $boarder->latestPrimaryClass?->semesterClass?->classModel?->grade?->getTranslations('name'),
                    'guardians' => $guardians,
                ];
            })
            ->sortBy([
                ['block_name', 'asc'],
                ['room_name', 'asc'],
                ['bed_name', 'asc'],
            ])->values();
    }

    public function getHostelBoardersStayBackData(array $filters = []): Collection
    {
        $filters = optional($filters);

        $parent_filters = [
            'includes' => [
                'firstActiveHostelBedAssignment' => function ($query) {
                    $query->select(['id', 'assignable_type', 'assignable_id', 'hostel_room_bed_id']);
                    $query->with([
                        'bed' => function ($query) {
                            $query->select(['id', 'hostel_room_id', 'name']);
                            $query->with([
                                'hostelRoom' => function ($query) {
                                    $query->select(['id', 'name', 'hostel_block_id']);
                                    $query->with([
                                        'hostelBlock' => function ($query) {
                                            $query->select(['id', 'name']);
                                        }
                                    ]);
                                }
                            ]);
                        }
                    ]);
                },
                'currentSemesterPrimaryClass' => function ($query) {
                    $query->select(['id', 'student_id', 'semester_class_id']);
                    $query->with([
                        'semesterClass' => function ($query) {
                            $query->select(['id', 'class_id']);
                            $query->with([
                                'classModel' => function ($query) {
                                    $query->select(['id', 'name', 'grade_id']);
                                    $query->with('grade:id,name');
                                }
                            ]);
                        }
                    ]);
                },
            ]
        ];

        $data = $this->getQuery($parent_filters)
            ->select(['id', 'student_number', 'name'])
            ->where('is_hostel', true)
            ->whereHas('firstActiveHostelBedAssignment')
            ->whereDoesntHave('incompleteHostelInOutRecords')
            ->when($filters['gender'], function (Builder $query, $gender) {
                $query->where('gender', $gender);
            })
            ->get();

        // TODO: Kimi (Not important for now) Data transformation can be done at service level
        $data->transform(function ($boarder) {
            return [
                'block_name' => $boarder->firstActiveHostelBedAssignment?->bed?->hostelRoom?->hostelBlock?->getTranslation('name', app()->getLocale()),
                'room_name' => $boarder->firstActiveHostelBedAssignment?->bed?->hostelRoom?->name,
                'bed_name' => $boarder->firstActiveHostelBedAssignment?->bed?->name,
                'student_number' => $boarder->student_number,
                'student_name' => $boarder->getTranslations('name'),
                'class_name' => $boarder->latestPrimaryClass?->semesterClass?->classModel?->getTranslations('name'),
                'grade_name' => $boarder->latestPrimaryClass?->semesterClass?->classModel?->grade?->getTranslations('name'),
            ];
        });

        return $data->sortBy([
            ['block_name', 'asc'],
            ['room_name', 'asc'],
            ['bed_name', 'asc'],
        ])->values();
    }

    public function getStudentsInGradeAndSemester(Grade $grade, SemesterSetting $semester, $relationships = [])
    {

        return $this->getAll([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester->id,
            'includes' => $relationships,
            'order_by' => ['student_number' => 'ASC'],
        ]);

    }

    public function transferredStudentListByAdmissionYearData($admission_year)
    {
        $students = Student::query()
            ->with([
                'latestPrimaryClassBySemesterSettings' => function ($query) {
                    $query->orderBy('class_enter_date');
                    $query->with([
                        'semesterClass' => function ($query) {
                            $query->select(['id', 'class_id']);
                            $query->with([
                                'classModel' => function ($query) {
                                    $query->select(['id', 'name']);
                                },
                            ]);
                        },
                    ]);
                }
            ])
            ->select('id', 'name', 'student_number')
            ->where('admission_type', StudentAdmissionType::TRANSFERRED->value)
            ->where('admission_year', $admission_year)
            ->get();

        $data = [];
        foreach ($students as $student) {
            $data[] = [
                'student_name' => $student->getTranslations('name'),
                'student_number' => $student->student_number,
                'class_name' => $student->latestPrimaryClassBySemesterSettings?->first()?->semesterClass?->classModel?->getFormattedTranslations('name') ?? '-',
            ];
        }

        return $data;
    }

    public function studentStatisticReportBySemesterData(SemesterSetting $semester_setting)
    {
        $grade_list = Grade::orderByDesc('sequence')->get()->keyBy('id')->all();

        $semester_class_list = SemesterClass::with([
            'classModel' => function ($query) {
                $query->select(['id', 'name', 'code']);
            }
        ])
            ->whereHas('classModel', function ($query) {
                $query->where('type', ClassType::SOCIETY)
                    ->where('is_active', true);
            })
            ->where('semester_setting_id', $semester_setting->id)
            ->get()
            ->keyBy('id')
            ->all();

        $society_student_ids_group_by_semester_class_id = StudentClass::select(['semester_setting_id', 'semester_class_id', 'student_id'])
            ->whereIn('semester_class_id', array_keys($semester_class_list))
            ->get()
            ->groupBy('semester_class_id')
            ->map(function ($group) {
                return $group->pluck('student_id')->toArray();
            })
            ->toArray();

        $student_ids = [];
        foreach ($society_student_ids_group_by_semester_class_id as $society_student_ids) {
            $student_ids = array_merge($student_ids, $society_student_ids);
        }

        $student_list = Student::select(['students.id', 'students.gender', 'classes.grade_id'])
            ->join('latest_primary_class_by_semester_setting_views', 'students.id', '=', 'latest_primary_class_by_semester_setting_views.student_id')
            ->join('semester_classes', 'latest_primary_class_by_semester_setting_views.semester_class_id', '=', 'semester_classes.id')
            ->join('classes', 'semester_classes.class_id', '=', 'classes.id')
            ->where('latest_primary_class_by_semester_setting_views.semester_setting_id', $semester_setting->id)
            ->whereNull('latest_primary_class_by_semester_setting_views.class_leave_date')
            ->whereIn('students.id', array_unique($student_ids))
            ->get()
            ->keyBy('id')
            ->all();

        $society_students_data = [
            'header' => [
                __('general.class_code'),
                __('general.society'),
                __('general.male'),
                __('general.female'),
                __('general.total_count'),
            ],
            'body' => [],
        ];
        $locale = app()->getLocale();
        foreach ($grade_list as $grade) {
            $society_students_data['header'][] = $grade->getTranslation('name', $locale); // if zh not set, fallback to en
        }

        foreach ($society_student_ids_group_by_semester_class_id as $semester_class_id => $society_student_ids) {
            if (count($society_student_ids) == 0) {
                continue;
            }
            $students = collect($society_student_ids)->map(function ($society_student_id) use ($student_list) {
                return $student_list[$society_student_id] ?? null;
            })->filter();

            $semester_class = $semester_class_list[$semester_class_id];

            $society_students_data['body'][$semester_class_id] = [
                $semester_class->classModel->code,
                $semester_class->classModel->getTranslation('name', $locale),
                $students->where('gender', Gender::MALE)->count(),
                $students->where('gender', Gender::FEMALE)->count(),
                $students->count(),
            ];
            foreach ($grade_list as $grade) {
                $society_students_data['body'][$semester_class_id][] = $students->where('grade_id', $grade->id)->count();
            }
        }

        $society_students_data['body'] = collect($society_students_data['body'])->sortBy(0)->toArray(); // sort by first column (class code) asc
        return $society_students_data;
    }

    public function studentAnalysisReportBySemesterGroupByGradeData(SemesterSetting $semester_setting)
    {
        $grade_list = Grade::orderByDesc('sequence')->get()->keyBy('id')->all();
        $class_ids = SemesterClass::where('semester_setting_id', $semester_setting->id)
            ->get()
            ->pluck('class_id')
            ->toArray();

        $class_list = ClassModel::select(['id', 'name', 'type', 'grade_id'])
            ->whereIn('id', $class_ids)
            ->where('type', ClassType::PRIMARY->value)
            ->orderBy('name', 'ASC')
            ->get();

        $students_data = LatestPrimaryClassBySemesterSettingView::select(['students.gender', 'students.is_hostel', 'classes.grade_id', 'classes.id as class_id'])
            ->join('students', 'latest_primary_class_by_semester_setting_views.student_id', '=', 'students.id')
            ->join('semester_classes', 'latest_primary_class_by_semester_setting_views.semester_class_id', '=', 'semester_classes.id')
            ->join('classes', 'semester_classes.class_id', '=', 'classes.id')
            ->where('latest_primary_class_by_semester_setting_views.semester_setting_id', $semester_setting->id)
            ->whereNull('latest_primary_class_by_semester_setting_views.class_leave_date')
            ->get();

        $data = [];

        $data['summary'] = [
            'total_students' => $students_data->count(),
            'total_male_students' => $students_data->where('gender', Gender::MALE->value)->count(),
            'total_female_students' => $students_data->where('gender', Gender::FEMALE->value)->count(),
            'total_hostel_students' => $students_data->where('is_hostel', true)->count(),
            'total_non_hostel_students' => $students_data->where('is_hostel', false)->count(),
        ];

        $locale = app()->getLocale();

        foreach ($grade_list as $grade) {
            $classes_data = [];
            $class_list_by_grade = $class_list->where('grade_id', $grade->id);
            foreach ($class_list_by_grade as $class) {
                $classes_data[] = [
                    'class_name' => $class->getTranslation('name', $locale),
                    'total_students' => $students_data->where('grade_id', $grade->id)->where('class_id', $class->id)->count(),
                    'total_male_students' => $students_data->where('gender', Gender::MALE->value)->where('grade_id', operator: $grade->id)->where('class_id', $class->id)->count(),
                    'total_female_students' => $students_data->where('gender', Gender::FEMALE->value)->where('grade_id', $grade->id)->where('class_id', $class->id)->count(),
                    'total_hostel_students' => $students_data->where('is_hostel', true)->where('grade_id', $grade->id)->where('class_id', $class->id)->count(),
                    'total_non_hostel_students' => $students_data->where('is_hostel', false)->where('grade_id', $grade->id)->where('class_id', $class->id)->count(),
                ];
            }

            $data['summary_by_grade'][$grade->id] = [
                'grade_name' => $grade->getTranslation('name', $locale),
                'total_students' => $students_data->where('grade_id', $grade->id)->count(),
                'total_male_students' => $students_data->where('gender', Gender::MALE->value)->where('grade_id', $grade->id)->count(),
                'total_female_students' => $students_data->where('gender', Gender::FEMALE->value)->where('grade_id', $grade->id)->count(),
                'total_hostel_students' => $students_data->where('is_hostel', true)->where('grade_id', $grade->id)->count(),
                'total_non_hostel_students' => $students_data->where('is_hostel', false)->where('grade_id', $grade->id)->count(),
                'classes_data' => $classes_data,
            ];
        }

        return $data;
    }
}
