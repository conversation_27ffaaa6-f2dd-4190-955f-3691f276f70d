<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\EcommerceProductCategory;
use App\Repositories\EcommerceProductCategoryRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class EcommerceProductCategoryService
{
    public function __construct(
        protected EcommerceProductCategoryRepository $productCategoryRepository
    ) {
    }

    public function getAllPaginatedEcommerceProductCategories($filters = []): LengthAwarePaginator
    {
        return $this->productCategoryRepository->getAllPaginated($filters);
    }

    public function getAllEcommerceProductCategories($filters = []): Collection
    {
        return $this->productCategoryRepository->getAll($filters);
    }

    public function createEcommerceProductCategory($data): ?Model
    {
        return $this->productCategoryRepository->create($data);
    }

    public function updateEcommerceProductCategory(EcommerceProductCategory $product_category, $data): ?Model
    {
        return $this->productCategoryRepository->update($product_category, $data);
    }

    public function deleteEcommerceProductCategory(EcommerceProductCategory $product_category): bool
    {
        if ($product_category->subCategories()->count() > 0) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ECOMMERCE_ERROR, 13001);
        }

        return $this->productCategoryRepository->delete($product_category);
    }
}
