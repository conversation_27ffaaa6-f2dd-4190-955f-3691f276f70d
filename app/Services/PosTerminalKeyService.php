<?php

namespace App\Services;

use App\Models\PosTerminalKey;
use App\Repositories\PosTerminalKeyRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class PosTerminalKeyService
{
    public function __construct(
        protected PosTerminalKeyRepository $posTerminalKeyRepository
    )
    {
    }

    public function getAllPaginatedPosTerminalKeys($filters = []): LengthAwarePaginator
    {
        return $this->posTerminalKeyRepository->getAllPaginated($filters);
    }

    public function createPosTerminalKey($data): PosTerminalKey
    {
        $data['secret'] = $this->generateSecret();

        return $this->posTerminalKeyRepository->create($data);
    }

    public function updatePosTerminalKey(PosTerminalKey $pos_terminal_key, array $data): PosTerminalKey
    {
        if (isset($data['regenerate']) && $data['regenerate'] === true) {
            $data['secret'] = $this->generateSecret();
        }

        return $this->posTerminalKeyRepository->update($pos_terminal_key, $data);
    }

    public function deletePosTerminalKey(PosTerminalKey $pos_terminal_key): bool
    {
        return $this->posTerminalKeyRepository->delete($pos_terminal_key);
    }

    private function generateSecret(): string
    {
        return md5(uniqid().now()->format('Y-m-d H:i:s.u'));
    }
}
