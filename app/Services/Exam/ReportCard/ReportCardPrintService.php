<?php

namespace App\Services\Exam\ReportCard;

use App\Services\DocumentPrintService;
use App\Traits\ExportFileAdaptable;


/**
 * Used to print PDF/Excel for report card (have a collection of rows/custom query)
 */
class ReportCardPrintService extends DocumentPrintService
{
    use ExportFileAdaptable;

    const string PRINT_FOLDER = 'report-card';

    protected string $paperOrientation = self::PAPER_ORIENTATION_PORTRAIT;

    public function __construct()
    {
        parent::__construct();
    }

    public function generate(): static
    {
        if (!isset($this->exportFileAdapter)) {
            throw new \Exception('Please define a report adapter first.');
        }

        $this->localFilePath = $this->exportFileAdapter->getFileNameWithExtension(self::PRINT_FOLDER . DIRECTORY_SEPARATOR . $this->fileName);

        $this->setFileName($this->exportFileAdapter->getFileNameWithExtension($this->fileName));

        $this->exportFileAdapter
            ->setOutputLocalFilePath($this->localFilePath)
            ->setPaperOrientation($this->paperOrientation)
            ->generate();

        return $this;

    }
}
