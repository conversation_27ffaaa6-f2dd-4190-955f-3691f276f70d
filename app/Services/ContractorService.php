<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\Contractor;
use App\Repositories\ContractorRepository;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class ContractorService
{
    public function __construct(
        protected ContractorRepository $contractorRepository,
        protected UserRepository $userRepository,
        protected DocumentRunningNumberService $documentRunningNumberService,
        protected UserService $userService
    ) {
    }

    public function getAllPaginatedContractors($filters = []): LengthAwarePaginator
    {
        return $this->contractorRepository->getAllPaginated($filters);
    }

    public function createContractor($data): ?Model
    {
        return DB::transaction(function () use ($data) {
            $user = $this->userService->createUser([
                'email' => $data['email'],
                'phone_number' => $data['phone_number'],
            ], Contractor::class);

            $data['contractor_number'] = $this->documentRunningNumberService
                ->setDocumentType(Contractor::class)
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
                ->generate();

            return $this->contractorRepository->create([
                'user_id' => $user->id,
                'contractor_number' => $data['contractor_number'],
                'department' => $data['department'],
                'status' => $data['status'],
                'employed_date' => $data['employed_date'],
                'name' => $data['name'],
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'nric' => $data['nric'],
                'passport_number' => $data['passport_number'],
            ]);
        });
    }

    public function updateContractor(Contractor $contractor, $data): ?Model
    {
        return DB::transaction(function () use ($contractor, $data) {
            $this->userRepository->update($contractor->user, [
                'email' => $data['email'],
                'phone_number' => $data['phone_number'],
            ]);

            return $this->contractorRepository->update($contractor, [
                'department' => $data['department'],
                'status' => $data['status'],
                'employed_date' => $data['employed_date'],
                'resignation_date' => $data['resignation_date'] ?? null,
                'name' => $data['name'],
                'email' => $data['email'],
                'phone_number' => $data['phone_number'],
                'nric' => $data['nric'],
                'passport_number' => $data['passport_number'],
            ]);
        });
    }

    public function deleteContractor(Contractor $contractor): bool
    {
        if (!$contractor->canBeDeleted()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::CONTRACTOR_ERROR, 38001);
        }

        return DB::transaction(function () use ($contractor) {
            $this->userRepository->delete($contractor->user);

            return $this->contractorRepository->delete($contractor);
        });
    }
}
