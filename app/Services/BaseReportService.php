<?php

namespace App\Services;

use App\Enums\ExportType;

/**
 * @method $this setExportType(?string $exportType)
 * @method $this setReportViewName(string $viewName)
 * @method $this setFileName(string $fileName)
 */
class BaseReportService
{
    private ?ExportType $exportType = null;
    private string $reportViewName;
    private string $fileName;

    public function setExportType(?string $export_type): self
    {
        if (!isset($export_type)) {
            return $this;
        }

        $this->exportType = ExportType::from($export_type);
        return $this;
    }

    public function getExportType(): ?ExportType
    {
        return $this->exportType;
    }

    public function setReportViewName(string $report_view_name): self
    {
        $this->reportViewName = $report_view_name;
        return $this;
    }

    public function getReportViewName(): string
    {
        return $this->reportViewName;
    }

    public function setFileName(string $file_name): self
    {
        $this->fileName = $file_name;
        return $this;
    }

    public function getFileName(): string
    {
        return $this->fileName;
    }
}
