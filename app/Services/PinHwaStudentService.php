<?php

namespace App\Services;

use App\Enums\StudentAdmissionType;
use App\Helpers\ErrorCodeHelper;
use App\Models\Student;

class PinHwaStudentService
{
    const NEW = 'NEW';
    const JUNIOR = 'JUNIOR';
    const SENIOR = 'SENIOR';

    const STUDENT_GRADE_MAPPING = [
        1 => self::JUNIOR,
        2 => self::JUNIOR,
        3 => self::JUNIOR,
        4 => self::JUNIOR,
        5 => self::SENIOR,
        6 => self::SENIOR,
        7 => self::SENIOR,
    ];
    const STUDENT_GRADE_INIT_NEXT_NUMBER = [
        self::JUNIOR => 601,
        self::SENIOR => 801,
    ];

    const STUDENT_NUMBER_PADDING = 3;
    const STUDENT_NUMBER_HOSTEL_PREFIX = 'H';
    private string $admissionYear;
    private bool $isHostel;
    private int $gradeId;
    private string $admissionType;
    private string $studentNumber;

    public function __construct() { }

    public function getAdmissionYear(): string
    {
        return $this->admissionYear;
    }

    public function setAdmissionYear(string $admission_year): self
    {
        $this->admissionYear = $admission_year;
        return $this;
    }

    public function getIsHostel(): bool
    {
        return $this->isHostel;
    }

    public function setIsHostel($is_hostel): self
    {
        $this->isHostel = $is_hostel;
        return $this;
    }

    public function getGradeId(): int
    {
        return $this->gradeId;
    }

    public function setGradeId($grade_id): self
    {
        $this->gradeId = $grade_id;
        return $this;
    }

    public function getAdmissionType(): string
    {
        return $this->admissionType;
    }

    public function setAdmissionType($admission_type): self
    {
        $this->admissionType = $admission_type;
        return $this;
    }

    public function getStudentNumber(): string
    {
        return $this->studentNumber;
    }

    public function setStudentNumber($student_number): self
    {
        $this->studentNumber = $student_number;
        return $this;
    }

    public function generateStudentEmail(): string
    {
        $student_number = trim($this->getStudentNumber());

        // Pinhwa doesn't need 'H' prefix in email
        $student_number = str_replace(self::STUDENT_NUMBER_HOSTEL_PREFIX, '', $student_number);

        return $student_number . config('school.email_domain');
    }

    public function getGradeAndInitNextNumber()
    {
        $admission_type = $this->getAdmissionType();

        if ($admission_type === StudentAdmissionType::NEW->value) {
            return [self::NEW, 1];
        } elseif ($admission_type === StudentAdmissionType::TRANSFERRED->value) {
            $grade_id = $this->getGradeId();

            if (!isset(self::STUDENT_GRADE_MAPPING[$grade_id])) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29010);
            }
            $grade = self::STUDENT_GRADE_MAPPING[$grade_id];
            $init_next_number = self::STUDENT_GRADE_INIT_NEXT_NUMBER[$grade];

            return [$grade, $init_next_number];
        } else {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29011);
        }
    }

    public function generateStudentNumber(): string
    {
        [$grade, $initNextNumber] = $this->getGradeAndInitNextNumber();
        $admission_year = $this->getAdmissionYear();
        $is_hostel = $this->getIsHostel();

        return resolve(DocumentRunningNumberService::class)
            ->setDocumentType(Student::class)
            ->setYear($admission_year)
            ->setCustomPadding(self::STUDENT_NUMBER_PADDING)
            ->setIdentifier1($grade)
            ->setInitNextNumber($initNextNumber)
            ->addPresetComponent($is_hostel ? self::STUDENT_NUMBER_HOSTEL_PREFIX : '')
            ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_YEAR)
            ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
            ->generate();
    }

    public function reformatStudentNumberByStudentHostel(): string
    {
        $student_number = str_replace(self::STUDENT_NUMBER_HOSTEL_PREFIX, '', $this->getStudentNumber());
        $is_hostel = $this->getIsHostel();

        if ($is_hostel) {
            $student_number = self::STUDENT_NUMBER_HOSTEL_PREFIX . $student_number;
        }

        return $student_number;
    }
}
