<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\LeadershipPositionRecordRepository;
use App\Repositories\SemesterClassRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class LeadershipPositionRecordService
{
    public function __construct(
        protected LeadershipPositionRecordRepository $leadershipPositionRecordRepository,
        protected SemesterClassRepository $semesterClassRepository
    ) {
    }

    public function getAllLeadershipPositionRecords($filters = []): Collection
    {
        return $this->leadershipPositionRecordRepository->getAll($filters);
    }

    public function getAllPaginatedLeadershipPositionRecords($filters = []): LengthAwarePaginator
    {
        return $this->leadershipPositionRecordRepository->getAllPaginated($filters);
    }

    public function createBulkLeadershipPositionRecords(User $user, $data): bool
    {
        $create_data = $this->mapCreateData($user, $data);

        $semester_class_id = $data['semester_class_id'];

        return DB::transaction(function () use ($create_data, $semester_class_id) {
            $this->deleteRecordsBySemesterClassId($semester_class_id);

            foreach ($create_data as $data) {
                $this->createLeadershipPositionRecord($data);
            }

            return true;
        });
    }

    public function createLeadershipPositionRecord($data): ?Model
    {
        return $this->leadershipPositionRecordRepository->create($data);
    }

    public function mapCreateData(User $user, $data): array
    {
        $create_data = [];

        $semester_class = $this->semesterClassRepository->find($data['semester_class_id']);

        foreach ($data['leadership_positions'] as $leadership_position) {
            if ($leadership_position['student_id']) {
                $create_data[] = [
                    'semester_setting_id' => $semester_class->semester_setting_id,
                    'semester_class_id' => $data['semester_class_id'],
                    'student_id' => $leadership_position['student_id'],
                    'leadership_position_id' => $leadership_position['id'],
                    'created_by' => $user->id,
                ];
            }
        }

        return $create_data;
    }

    public function deleteRecordsBySemesterClassId($semester_setting_id): void
    {
        $this->leadershipPositionRecordRepository->deleteRecordsBySemesterClassId($semester_setting_id);
    }
}
