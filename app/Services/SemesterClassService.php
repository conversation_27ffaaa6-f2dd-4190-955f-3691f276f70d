<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\SemesterClass;
use App\Repositories\SemesterClassRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class SemesterClassService
{
    public function __construct(
        protected SemesterClassRepository $semesterClassRepository
    ) {
    }

    public function getAllPaginatedSemesterClasses($filters = []): LengthAwarePaginator
    {
        return $this->semesterClassRepository->getAllPaginated($filters);
    }

    public function getAllSemesterClasses($filters = []): Collection
    {
        return $this->semesterClassRepository->getAll($filters);
    }

    public function updateSemesterClass(SemesterClass $semester_class, $data): ?Model
    {
        return $this->semesterClassRepository->update($semester_class, $data);
    }

    public function deleteSemesterClass(SemesterClass $semester_class): bool
    {
        if (!$semester_class->canBeDeleted()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::CLASS_ERROR, 6005);
        }

        return $this->semesterClassRepository->delete($semester_class);
    }
}
