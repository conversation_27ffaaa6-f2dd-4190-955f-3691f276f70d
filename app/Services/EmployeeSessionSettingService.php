<?php

namespace App\Services;

use App\Models\EmployeeSessionSetting;
use App\Repositories\EmployeeSessionSettingRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;

class EmployeeSessionSettingService
{
    private EmployeeSessionSettingRepository $employeeSessionSettingRepository;

    public function __construct(EmployeeSessionSettingRepository $employee_session_setting_repository)
    {
        $this->employeeSessionSettingRepository = $employee_session_setting_repository;
    }

    public function getAllPaginatedEmployeeSessionSettings($filters = []): LengthAwarePaginator
    {
        return $this->employeeSessionSettingRepository->getAllPaginated($filters);
    }

    public function createEmployeeSessionSetting($data): ?Model
    {
        return $this->employeeSessionSettingRepository->create($data);
    }

    public function updateEmployeeSessionSetting(EmployeeSessionSetting $employee_session_setting, $data): ?Model
    {
        return $this->employeeSessionSettingRepository->update($employee_session_setting, $data);
    }

    public function deleteEmployeeSessionSetting(EmployeeSessionSetting $employee_session_setting): bool
    {
        return $this->employeeSessionSettingRepository->delete($employee_session_setting);
    }
}
