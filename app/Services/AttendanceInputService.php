<?php

namespace App\Services;

use App\Console\Commands\Attendance\StudentAttendancePosting;
use App\Enums\AttendanceCheckInStatus;
use App\Enums\CardStatus;
use App\Enums\Day;
use App\Helpers\ConfigHelper;
use App\Helpers\ErrorCodeHelper;
use App\Jobs\AttendancePostingJob;
use App\Models\AttendanceInput;
use App\Models\AttendanceInputErrorLog;
use App\Models\Card;
use App\Models\Config;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Student;
use App\Repositories\AttendanceInputRepository;
use App\Services\Timetable\StudentTimetableService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AttendanceInputService
{
    private AttendanceInputRepository $attendanceInputRepository;
    private AttendanceService $attendanceService;
    protected bool $repostSchoolAttendance;

    public function __construct(
        AttendanceInputRepository $attendanceInputRepository,
        AttendanceService $attendanceService
    ) {
        $this->attendanceInputRepository = $attendanceInputRepository;
        $this->attendanceService = $attendanceService;
    }

    public function getAll($filters = []): Collection
    {
        return $this->attendanceInputRepository->getAll($filters);
    }

    public function getAllPaginated($filters = []): LengthAwarePaginator
    {
        return $this->attendanceInputRepository->getAllPaginated($filters);
    }


    public function bulkCreateAttendanceInputWithCard($validated_payload)
    {
        $card_numbers = array_column($validated_payload['card_numbers_with_datetime'], 'card_number');
        $card_numbers = collect($card_numbers)->unique()->values();

        $cards = Card::with([
            'userable' => function ($query) {
                $query->select('id');
            }
        ])
            ->select(['id', 'userable_type', 'userable_id', 'card_number', 'status'])
            ->whereIn('card_number', $card_numbers)
            ->get()
            ->keyBy('card_number');

        $invalid_card_numbers = [];
        $attendance_input = [];

        foreach ($validated_payload['card_numbers_with_datetime'] as $card_number_with_datetime) {
            $card = $cards[$card_number_with_datetime['card_number']] ?? null;

            $date = Carbon::parse($card_number_with_datetime['datetime'])->timezone(config('school.timezone'))->toDateString();

            if (!isset($card)) {
                $invalid_card_numbers[] = [
                    'card_number' => $card_number_with_datetime['card_number'],
                    'time' => $card_number_with_datetime['datetime'],
                    'error_message' => 'Invalid card number',
                    'created_at' => now(),
                    'updated_at' => now(),
                    'terminal_id' => $this->terminalId,
                ];
            } elseif ($card->status == CardStatus::INACTIVE) {
                $invalid_card_numbers[] = [
                    'card_number' => $card_number_with_datetime['card_number'],
                    'time' => $card_number_with_datetime['datetime'],
                    'error_message' => 'Inactive card',
                    'created_at' => now(),
                    'updated_at' => now(),
                    'terminal_id' => $this->terminalId,
                ];
            } else {
                $attendance_input[] = [
                    'attendance_recordable_type' => $card['userable_type'],
                    'attendance_recordable_id' => $card['userable_id'],
                    'card_id' => $card->id,
                    'date' => $date,
                    'record_datetime' => $card_number_with_datetime['datetime'],
                    'created_at' => now(),
                    'updated_at' => now(),
                    'terminal_id' => $this->terminalId,
                ];
            }
        }

        DB::transaction(function () use ($invalid_card_numbers, $attendance_input) {
            AttendanceInputErrorLog::insert($invalid_card_numbers);
            AttendanceInput::insert($attendance_input);
        });

        $attendance_input_group_by_date_and_attendance_recordable_type = collect($attendance_input)->groupBy(['date', 'attendance_recordable_type']);
        foreach ($attendance_input_group_by_date_and_attendance_recordable_type as $date => $attendance_input_group_by_attendance_recordable_type) {
            $student_ids = [];
            $employee_ids = [];
            $contractor_ids = [];
            foreach ($attendance_input_group_by_attendance_recordable_type as $attendance_recordable_type => $attendance_input) {
                $ids = $attendance_input->pluck('attendance_recordable_id')->toArray();
                switch ($attendance_recordable_type) {
                    case Student::class:
                        $student_ids = $ids;
                        break;
                    case Employee::class:
                        $employee_ids = $ids;
                        break;
                    case Contractor::class:
                        $contractor_ids = $ids;
                        break;
                }
            }
            $this->triggerAttendancePostingJob($date, $student_ids, $employee_ids, $contractor_ids);
        }

        return true;
    }

    public function createAttendanceInputWithCard($card_number, $datetime)
    {
        $card = Card::with('userable')
            ->select(['id', 'userable_type', 'userable_id', 'card_number', 'status'])
            ->where('card_number', $card_number)
            ->first();

        if (!isset($card)) {
            AttendanceInputErrorLog::create([
                'card_number' => $card_number,
                'time' => $datetime,
                'error_message' => 'Invalid card number',
                'terminal_id' => $this->terminalId,
            ]);
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42007);
        } elseif ($card->status == CardStatus::INACTIVE) {
            AttendanceInputErrorLog::create([
                'card_number' => $card_number,
                'time' => $datetime,
                'error_message' => 'Inactive card',
                'terminal_id' => $this->terminalId,
            ]);
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42011);
        }

        $interval = ConfigHelper::get(Config::ATTENDANCE_TAP_CARD_INTERVAL_SECOND);
        $cache_key = 'card-lock-' . $card->card_number;
        if (Cache::has($cache_key)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42006);
        }
        Cache::put($cache_key, true, $interval);

        $datetime_school_timezone = Carbon::parse($datetime)->timezone(config('school.timezone'));
        $date = $datetime_school_timezone->toDateString();
        $day = Day::carbonWeekdayToDay($datetime_school_timezone->dayOfWeek);

        AttendanceInput::create([
            'attendance_recordable_type' => $card->userable_type,
            'attendance_recordable_id' => $card->userable_id,
            'card_id' => $card->id,
            'date' => $date,
            'record_datetime' => $datetime,
            'terminal_id' => $this->terminalId,
        ]);

        $attendance_status = null;
        if ($card->userable_type == Student::class) {
            $student_attendance_period = app()->make(StudentTimetableService::class)
                ->setStudent($card->userable)
                ->setSimpleOutput(true)
                ->setDayFilter($day)
                ->getAttendancePeriods($date, $date)
                ->whereNotNull('attendance_from')
                ->first();

            if (!isset($student_attendance_period)) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42008);
            }

            $attendance_input_count = AttendanceInput::query()
                ->where('attendance_recordable_type', $card->userable_type)
                ->where('attendance_recordable_id', $card->userable_id)
                ->where('date', $date)
                ->count();

            $studentAttendancePosting = app()->make(StudentAttendancePosting::class)->setDate($date);
            if ($attendance_input_count == 1) { // will create attendance input first before checking attendance input count
                $attendance_status = $studentAttendancePosting->determineAttendanceCheckInStatus($student_attendance_period['attendance_from'], $datetime);
            } else {
                $attendance_status = $studentAttendancePosting->determineAttendanceCheckOutStatus($student_attendance_period['attendance_to'], $datetime);
            }
        } else {
            $attendance_status = AttendanceCheckInStatus::ON_TIME->value;
        }

        $userable = $card->userable;
        $userable->attendance_status = $attendance_status;

        switch (get_class($userable)) {
            case Student::class:
                $this->triggerAttendancePostingJob($date, [$userable->id], [], []);
                break;
            case Employee::class:
                $this->triggerAttendancePostingJob($date, [], [$userable->id], []);
                break;
            case Contractor::class:
                $this->triggerAttendancePostingJob($date, [], [], [$userable->id]);
                break;
        }

        return $userable;
    }

    public function setUserable($userable)
    {
        $this->userable = $userable;
        return $this;
    }

    public function setCard(?Card $card)
    {
        $this->card = $card;
        return $this;
    }

    public function setRemarks($remarks)
    {
        $this->remarks = $remarks;
        return $this;
    }

    public function setDate($date)
    {
        $this->date = $date;
        return $this;
    }

    public function setRecordDatetime($record_datetime)
    {
        $this->recordDatetime = $record_datetime;
        return $this;
    }

    public function setEmployee(Employee $employee)
    {
        $this->employee = $employee;
        return $this;
    }

    public function setTerminalId(int $terminal_id)
    {
        $this->terminalId = $terminal_id;
        return $this;
    }

    public function setRepostSchoolAttendance(bool $repost_school_attendance)
    {
        $this->repostSchoolAttendance = $repost_school_attendance;
        return $this;
    }

    public function create()
    {
        $data = [
            'attendance_recordable_type' => get_class($this->userable),
            'attendance_recordable_id' => $this->userable->id,
            'card_id' => $this->card ? $this->card->id : null,
            'remarks' => $this->remarks,
            'date' => $this->date,
            'record_datetime' => $this->recordDatetime,
            'is_manual' => true,
            'updated_by_employee_id' => $this->employee->id,
        ];

        $this->attendanceInputRepository->create($data);

        switch (get_class($this->userable)) {
            case Student::class:
                $this->triggerAttendancePostingJob($this->date, [$this->userable->id], [], []);
                break;
            case Employee::class:
                $this->triggerAttendancePostingJob($this->date, [], [$this->userable->id], []);
                break;
            case Contractor::class:
                $this->triggerAttendancePostingJob($this->date, [], [], [$this->userable->id]);
                break;
        }
    }

    public function update(AttendanceInput $attendance_input): ?Model
    {
        $data = [
            'card_id' => $this->card ? $this->card->id : null,
            'remarks' => $this->remarks,
            'date' => $this->date,
            'record_datetime' => $this->recordDatetime,
            'is_manual' => true,
            'updated_by_employee_id' => $this->employee->id,
        ];

        return $this->attendanceInputRepository->update($attendance_input, $data);
    }

    public function delete(AttendanceInput $attendance_input): bool
    {
        return $this->attendanceInputRepository->delete($attendance_input);
    }

    public function triggerAttendancePostingJob($date, $student_ids = [], $employee_ids = [], $contractor_ids = [])
    {
        if ($this->repostSchoolAttendance == true) {
            if (app()->environment('testing')) {
                AttendancePostingJob::dispatch($date, $student_ids, $employee_ids, $contractor_ids);
            } else {
                AttendancePostingJob::dispatch($date, $student_ids, $employee_ids, $contractor_ids)->onConnection('attendance-posting')->onQueue('attendance-posting');
            }
        }
    }
}
