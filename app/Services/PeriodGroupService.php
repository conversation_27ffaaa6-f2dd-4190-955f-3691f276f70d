<?php

namespace App\Services;

use App\Enums\Day;
use App\Helpers\ErrorCodeHelper;
use App\Repositories\PeriodGroupRepository;
use App\Repositories\PeriodRepository;
use App\Repositories\StudentRepository;
use App\Repositories\TimetableRepository;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class PeriodGroupService
{
    private PeriodGroupRepository $periodGroupRepository;

    public function __construct(PeriodGroupRepository $period_group_repository)
    {
        $this->periodGroupRepository = $period_group_repository;
    }

    public function getAllPaginatedPeriodGroups($filters = []): LengthAwarePaginator
    {
        return $this->periodGroupRepository->getAllPaginated($filters);
    }

    public function getAllPeriodGroups($filters = []): Collection
    {
        return $this->periodGroupRepository->getAll($filters);
    }

    public function createPeriodGroup($data): ?Model
    {
        return $this->periodGroupRepository->create($data);
    }

    public function updatePeriodGroup($id, $data): ?Model
    {
        return $this->periodGroupRepository->update($id, $data);
    }

    public function deletePeriodGroup($id): bool
    {
        return $this->periodGroupRepository->delete($id);
    }

    public function getPeriodGroupLabelByStudentIds(array $student_ids)
    {
        $studentRepository = app()->make(StudentRepository::class);
        $timetableRepository = app()->make(TimetableRepository::class);
        $periodRepository = app()->make(PeriodRepository::class);

        $students = $studentRepository->getAll([
            'includes' => ['primaryClass.semesterClass.classModel'],
            'id' => $student_ids,
        ]);

        $student_list_semester_class_id_as_key = [];
        foreach ($students as $student) {
            $student_class = $student->primaryClass;
            if (!$student_class) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::TIMETABLE_ERROR, 40008, ['student_name' => $student->name]);
            }
            $student_list_semester_class_id_as_key[$student_class->semester_class_id][] = $student;
        }

        $semester_class_ids = array_keys($student_list_semester_class_id_as_key);

        $timetables = $timetableRepository->getAll([
            'is_active' => true,
            'semester_class_id' => $semester_class_ids,
        ])->keyBy('semester_class_id');

        $student_list_period_group_id_as_key = [];
        foreach ($student_list_semester_class_id_as_key as $semester_class_id => $student_list) {
            if (!isset($timetables[$semester_class_id])) {
                $student_name_array = collect($student_list)->sortBy('name')->pluck('name')->toArray();
                ErrorCodeHelper::throwError(ErrorCodeHelper::TIMETABLE_ERROR, 40009, ['student_names' => implode(', ', $student_name_array)]);
            }

            $timetable = $timetables[$semester_class_id];
            foreach ($student_list as $student) {
                $student_list_period_group_id_as_key[$timetable->period_group_id][] = $student;
            }
        }

        $period_group_id = array_keys($student_list_period_group_id_as_key);

        $period_groups = $this->periodGroupRepository->getAll([
            'id' => $period_group_id,
            'includes' => ['periodLabels'],
        ])->keyBy('id');

        $data = [];
        foreach ($student_list_period_group_id_as_key as $period_group_id => $student_list) {
            $period_group = $period_groups[$period_group_id];
            // period labels
            $periods = $periodRepository->getAll([
                'period_group_id' => $period_group_id,
                'day' => Day::MONDAY->value,
            ])->keyBy('period');

            $labels = [];
            foreach ($period_group->periodLabels as $period_label) {
                $period = $periods[$period_label->period]; // get Monday period
                $labels[] = [
                    'id' => $period_label->id,
                    'name' => $period_label->getFormattedTranslations('name'),
                    'translations' => $period_label->translations,
                    'time' => $period->from_time . " - " . $period->to_time,
                    'period' => $period_label->period,
                    'is_attendance_required' => $period_label->is_attendance_required,
                    'can_apply_leave' => $period_label->can_apply_leave,
                ];
            }
            // student
            $students = [];
            foreach ($student_list as $student) {
                $students[] = [
                    'id' => $student->id,
                    'student_number' => $student->student_number,
                    'student_name' => $student->getFormattedTranslations('name'),
                    'class_name' => $student->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                    'class_name_translations' => $student->primaryClass->semesterClass->classModel->translations,
                ];
            }

            $data[] = [
                'period_group_id' => $period_group->id,
                'period_group_name' => $period_group->getFormattedTranslations('name'),
                'period_group_translations' => $period_group->translations,
                'period_labels' => $labels,
                'students' => collect($students)->sortBy('student_name')->values()->toArray(),
            ];
        }

        return $data;
    }

    public function getAllPeriodGroupLabelWithGroupedPeriods(): array
    {
        $period_groups = $this->periodGroupRepository->getAll([
            'includes' => ['periodLabels', 'periods'],
            'order_by' => ['id' => 'asc'],
        ])->toArray();

        $period_groups = collect($period_groups)->map(function ($period_group) {
            $period_group['period_labels'] = collect($period_group['period_labels'])->keyBy('period');
            $first_periods = collect($period_group['periods'])->groupBy('day')->first()->keyBy('period');

            $period_group['period_labels'] = collect($period_group['period_labels'])->map(function ($period_label) use ($first_periods) {
                $period_label['from_time'] = $first_periods[$period_label['period']]['from_time'];
                $period_label['to_time'] = $first_periods[$period_label['period']]['to_time'];
                return $period_label;
            });

            unset($period_group['periods']);

            return $period_group;
        });

        $max_number_of_periods = $period_groups->pluck('number_of_periods')->max();

        return [
            'max_number_of_periods' => $max_number_of_periods,
            'period_groups' => $period_groups,
        ];
    }
}
