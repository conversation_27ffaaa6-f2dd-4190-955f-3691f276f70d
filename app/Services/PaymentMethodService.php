<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\PaymentMethod;
use App\Repositories\PaymentMethodRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;

class PaymentMethodService
{
    public function __construct(
        protected PaymentMethodRepository $paymentMethodRepository
    )
    {
    }

    public function getAllPaymentMethods($filters = [])
    {
        return $this->paymentMethodRepository->getAll($filters);
    }

    public function getAllPaginatedPaymentMethods($filters = []): LengthAwarePaginator
    {
        return $this->paymentMethodRepository->getAllPaginated($filters);
    }

    public function createPaymentMethod($data): ?Model
    {
        return $this->paymentMethodRepository->create($data);
    }

    public function updatePaymentMethod(PaymentMethod $payment_method, $data): ?Model
    {
        return $this->paymentMethodRepository->update($payment_method, $data);
    }

    public function deletePaymentMethod(PaymentMethod $payment_method): bool
    {
        if (!$payment_method->canBeDeleted()){
            ErrorCodeHelper::throwError(ErrorCodeHelper::MASTER_DATA_ERROR, '20001');
        }

        return $this->paymentMethodRepository->delete($payment_method);
    }
}
