<?php

namespace App\Services;

use App\Repositories\RewardPunishmentRecordRepository;
use App\Repositories\RewardPunishmentRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class RewardPunishmentRecordService
{
    private RewardPunishmentRecordRepository $rewardPunishmentRecordRepository;
    private RewardPunishmentRepository $rewardPunishmentRepository;

    public function __construct(
        RewardPunishmentRecordRepository $reward_punishment_record_repository,
        RewardPunishmentRepository $reward_punishment_repository,
    ) {
        $this->rewardPunishmentRecordRepository = $reward_punishment_record_repository;
        $this->rewardPunishmentRepository = $reward_punishment_repository;
    }

    public function getAllPaginatedRewardPunishmentRecords($filters = []): LengthAwarePaginator
    {
        return $this->rewardPunishmentRecordRepository->getAllPaginated($filters);
    }

    public function createRewardPunishmentRecord($data)
    {
        $reward_punishment = $this->rewardPunishmentRepository->find($data['reward_punishment_id']);

        DB::transaction(function () use ($data, $reward_punishment) {
            foreach ($data['student_ids'] as $student_id) {

                $this->rewardPunishmentRecordRepository->create([
                    'date' => $data['date'],
                    'student_id' => $student_id,
                    'reward_punishment_id' => $data['reward_punishment_id'],
                    'average_exam_marks' => (float) $reward_punishment->average_exam_marks,
                    'conduct_marks' => (float) $reward_punishment->conduct_marks,
                    'display_in_report_card' => $data['display_in_report_card'],
                    'status' => $data['status'],
                ]);
            }
        });
    }

    public function updateRewardPunishmentRecord($id, $data): ?Model
    {
        return DB::transaction(function () use ($id, $data) {
            $reward_punishment_record = $this->rewardPunishmentRepository->find($data['reward_punishment_id']);

            $data['average_exam_marks'] = (float) $reward_punishment_record?->average_exam_marks ?? 0;
            $data['conduct_marks'] = (float) $reward_punishment_record?->conduct_marks ?? 0;

            $reward_punishment_record = $this->rewardPunishmentRecordRepository->update($id, $data);

            // TODO Post announcement to users

            return $reward_punishment_record;
        });
    }

    public function updateRewardPunishmentRecordStatusInBulk($data): ?Model
    {
        return DB::transaction(function () use ($data) {
            foreach ($data['reward_punishment_record_ids'] as $id) {

                $this->rewardPunishmentRecordRepository->update($id, [
                    'status' => $data['status'],
                ]);

                // TODO Post announcement to users
            }
        });
    }

    public function deleteRewardPunishmentRecordById($id): bool
    {
        return $this->rewardPunishmentRecordRepository->delete($id);
    }
}
