<?php

namespace App\Services;

use App\Enums\AttendanceStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Helpers\ErrorCodeHelper;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\PeriodLabel;
use App\Models\Student;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Repositories\AttendanceRepository;
use App\Repositories\CalendarTargetRepository;
use App\Repositories\LeaveApplicationPeriodRepository;
use App\Repositories\PeriodAttendanceRepository;
use App\Repositories\SchoolAttendancePeriodOverrideRepository;
use App\Repositories\SubstituteRecordRepository;
use App\Repositories\TimeslotOverrideRepository;
use App\Repositories\TimeslotRepository;
use App\Repositories\TimeslotTeacherRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class AttendanceService
{
    protected bool $isAllStudents;
    protected bool $isAllEmployees;
    protected bool $isAllContractors;
    protected array $studentIds;
    protected array $employeeIds;
    protected array $contractorIds;
    const int FIRST_CLASS_AFFAIR_PERIOD = 1; // period 1 is first 班务 of the day

    public function __construct(
        protected SchoolAttendancePeriodOverrideRepository $schoolAttendancePeriodOverrideRepository,
        protected AttendanceRepository $attendanceRepository,
        protected PeriodAttendanceRepository $periodAttendanceRepository,
        protected LeaveApplicationPeriodRepository $leaveApplicationPeriodRepository,
        protected TimeslotRepository $timeslotRepository,
        protected TimeslotTeacherRepository $timeslotTeacherRepository,
        protected SubstituteRecordRepository $substituteRecordRepository,
        protected CalendarTargetRepository $calendarTargetRepository,
        protected TimeslotOverrideRepository $timeslotOverrideRepository,
    ) {
        $this->isAllStudents = false;
        $this->isAllEmployees = false;
        $this->isAllContractors = false;
        $this->studentIds = [];
        $this->employeeIds = [];
        $this->contractorIds = [];
    }

    public function getAllPaginatedAttendances($filters = []): LengthAwarePaginator
    {
        return $this->attendanceRepository->getAllPaginated($filters);
    }

    public function getAllAttendances($filters = []): Collection
    {
        return $this->attendanceRepository->getAll($filters);
    }

    public function deleteAttendance(Attendance $attendance): bool
    {
        return $this->attendanceRepository->delete($attendance);
    }

    public function setEmployee(Employee $employee)
    {
        $this->employee = $employee;
        return $this;
    }

    public function setDate($date)
    {
        $this->date = $date;
        return $this;
    }

    public function getFormattedPeriodOptionLabel(array $timeslots)
    {
        $timeslots = collect($timeslots);
        switch (get_class($timeslots->first())) {
            case Timeslot::class:
                $period_labels = PeriodLabel::query()
                    ->where('period_group_id', $timeslots->first()->period->period_group_id)
                    ->whereIn('period', $timeslots->pluck('period')->flatten()->pluck('period')->toArray())
                    ->get();
                $period_string = $period_labels->pluck('name')->join(", ");
                $from = Carbon::parse($timeslots->first()->attendance_from)->format('H:i');
                $to = Carbon::parse($timeslots->last()->attendance_to)->format('H:i');
                $subject_name = $timeslots->first()->classSubject?->subject->name;
                $placeholder = $timeslots->first()->placeholder;
                $class_name = $timeslots->first()->timetable->semesterClass->classModel->name;

                $class_name = '[' . $class_name . ']';
                $subject_or_placeholder = (isset($subject_name) || isset($placeholder)) ? ' - ' . ($subject_name ?? $placeholder) : null;

                return [
                    'label' => "{$class_name}{$period_string} ({$from} - {$to}){$subject_or_placeholder}",
                    'period_labels' => $period_labels,
                ];
            case TimeslotOverride::class:
                $period_string = $timeslots->pluck('period')->join(",");
                $from = Carbon::parse($timeslots->first()->attendance_from)->format('H:i');
                $to = Carbon::parse($timeslots->last()->attendance_to)->format('H:i');
                $placeholder = $timeslots->first()->placeholder;
                $override_text = app()->getLocale() == 'en' ? '(Override)' : '(代替)';

                return [
                    'label' => "Period {$period_string} ({$from} - {$to}) - {$placeholder} {$override_text}",
                    'period_labels' => collect([]),
                ];
            default:
                ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42009);
        }
    }

    public function getPeriodOptionIsDisabledStatus($attendance_from_in_datetime)
    {
        $current_datetime = Carbon::now(config('school.timezone'));
        // current datetime 2024-04-12 15:00:00, timeslot 15:30:00
        // teacher select 2024-04-11, attendance_from_in_datetime will be 2024-04-11 15:30:00, 2024-04-11 15:30:00 >  2024-04-12 15:00:00 = false (is_disabled = false)
        // teacher select 2024-04-12, attendance_from_in_datetime will be 2024-04-12 15:00:00, 2024-04-12 15:30:00 >  2024-04-12 15:00:00 = true (is_disabled = true)
        return $attendance_from_in_datetime->gt($current_datetime);
    }

    public function getPeriodsByTimeslotTeacher()
    {
        $current_datetime = Carbon::now(config('school.timezone'));

        $day = strtoupper(Carbon::parse($this->date)->format('l'));

        $substitute_or_requestor_records = $this->substituteRecordRepository->getAll([
            'substitute_or_requestor_teacher_id' => $this->employee->id,
            'substitute_date' => $this->date,
        ]);

        $timeslot_ids = $this->timeslotTeacherRepository->getAll([
            'employee_id' => $this->employee->id,
            'not_timeslot_id' => $substitute_or_requestor_records->where('requestor_id', $this->employee->id)->pluck('timeslot_id')->toArray(), // remove if the timeslot already sub to other teacher
        ])->pluck('timeslot_id')->toArray();

        $substitute_timeslot_ids = $substitute_or_requestor_records->where('substitute_teacher_id', $this->employee->id)->pluck('timeslot_id')->toArray();

        $timeslot_ids = array_unique(array_merge($timeslot_ids, $substitute_timeslot_ids)); // merge timeslot and substitute timeslot

        $data = [];

        if (empty($timeslot_ids)) {
            return $data;
        }

        // timeslots (active timetable)
        $timeslots_group_by_timetable_id = $this->timeslotRepository->getAll([
            'id' => $timeslot_ids,
            'day' => $day,
            'active_timetable' => true,
            'includes' => ['classSubject.subject', 'timetable.semesterClass.classModel', 'period'],
        ])
            ->sortBy('period.period')
            ->groupBy(['timetable_id']); // same semester class

        foreach ($timeslots_group_by_timetable_id as $timetable_id => $timeslots) {
            $temp = [];
            foreach ($timeslots as $timeslot) {
                $period = $timeslot->period->period;
                $first_period = $period;

                // e.g period 2, check period 1 exist or not
                if (isset($temp[$period - 1])) {
                    $previous_period = $temp[$period - 1];

                    if (
                        // class_subject_id and placeholder both must be same
                        $previous_period['class_subject_id'] == $timeslot->class_subject_id &&
                        $previous_period['placeholder'] == $timeslot->placeholder
                    ) {
                        $first_period = $previous_period['first_period'];
                    }
                }

                if (!isset($temp[$period])) {
                    $temp[$period] = [
                        'class_subject_id' => $timeslot->class_subject_id,
                        'placeholder' => $timeslot->placeholder,
                        'first_period' => $first_period,
                        'period' => $period,
                        'timeslot_id' => $timeslot->id,
                        'attendance_from' => $timeslot->attendance_from,
                        'attendance_to' => $timeslot->attendance_to,
                        'timeslot' => $timeslot,
                    ];
                }
            }

            $data_continuous_mapped = collect($temp)->groupBy('first_period');
            foreach ($data_continuous_mapped as $first_period => $timeslots) {
                $attendance_from_in_datetime = Carbon::parse($this->date . ' ' . $timeslots->first()['attendance_from'], config('school.timezone'));
                $attendance_to_in_datetime = Carbon::parse($this->date . ' ' . $timeslots->last()['attendance_to'], config('school.timezone'));
                $formatted_period_option_label = $this->getFormattedPeriodOptionLabel($timeslots->pluck('timeslot')->all());
                $data[] = [
                    'timeslot_type' => Timeslot::class,
                    'timeslot_id' => $timeslots->pluck('timeslot_id')->toArray(),
                    'current_class' => $current_datetime->between($attendance_from_in_datetime, $attendance_to_in_datetime),
                    'label' => $formatted_period_option_label['label'],
                    'period_labels' => $formatted_period_option_label['period_labels'],
                    'is_disabled' => $this->getPeriodOptionIsDisabledStatus($attendance_from_in_datetime),
                    'period' => $first_period,
                ];
            }
        }

        // timeslot override
        $timeslot_overrides_group_by_period = $this->timeslotOverrideRepository->getAll([
            'date' => $this->date,
            'employee_id' => $this->employee->id,
            'class_attendance_required' => true,
            'is_empty' => false,
        ])
            ->groupBy('period')
            ->all();

        foreach ($timeslot_overrides_group_by_period as $period => $timeslot_overrides) {
            $timeslot_override = $timeslot_overrides->first(); // if 10 students, will have 10 overrides, but attendance_from, attendance_to and placeholder should be same (TimeslotOverrideBulkCreateRequest)
            $attendance_from_in_datetime = Carbon::parse($this->date . ' ' . $timeslot_override->attendance_from, config('school.timezone'));
            $attendance_to_in_datetime = Carbon::parse($this->date . ' ' . $timeslot_override->attendance_to, config('school.timezone'));
            $formatted_period_option_label = $this->getFormattedPeriodOptionLabel([$timeslot_override]);
            $data[] = [
                'timeslot_type' => TimeslotOverride::class,
                'timeslot_id' => [$timeslot_override->period],
                'current_class' => $current_datetime->between($attendance_from_in_datetime, $attendance_to_in_datetime),
                'label' => $formatted_period_option_label['label'],
                'period_labels' => $formatted_period_option_label['period_labels'],
                'is_disabled' => $this->getPeriodOptionIsDisabledStatus($attendance_from_in_datetime),
                'period' => $timeslot_override->period,
            ];
        }

        $data = collect($data)->sortBy('period')->values()->toArray();
        return $data;
    }

    public function setTimeslotType($timeslot_type): self
    {
        $this->timeslotType = $timeslot_type;
        return $this;
    }

    public function setTimeslotId(int $timeslot_id): self
    {
        $this->timeslotId = $timeslot_id;
        return $this;
    }

    public function setTimeslotIds(array $timeslot_ids): self
    {
        $this->timeslotIds = $timeslot_ids;
        return $this;
    }

    public function setPeriod(int $period): self
    {
        $this->period = $period;
        return $this;
    }

    public function getStudentCurrentClass($student)
    {
        return $student->primaryClass ? $student->primaryClass->semesterClass->classModel->getFormattedTranslations('name') : null;
    }

    public function getStudentClassAttendances($period_attendances, $is_attendance_required, $school_attendance_status)
    {
        $class_attendances = [];

        foreach ($period_attendances as $period_attendance) {
            $leave_application_info = null;
            $leave_application = null;
            if ($period_attendance->leaveApplication) {
                $leave_application = $period_attendance->leaveApplication;
                $leave_application_info = $leave_application->remarks ? $leave_application->reason . " ({$leave_application->remarks})" : $leave_application->reason;
            }

            $updated_by_employee = [];
            if ($period_attendance->employee) {
                $employee = $period_attendance->employee;
                $updated_by_employee = [
                    'employee_name' => $employee->name,
                    'employee_name_translations' => $employee->getTranslations('name'),
                    'attendance_recorded_at' => $period_attendance->updated_at,
                ];
            }

            // only today date + no leave application + school not absent will check $is_attendance_required
            // not today = cannot edit
            // with leave application = cannot edit
            // school attendance absent = cannot edit
            // first period (first 班务) = cannot edit
            // then check is attendance required, if yes, allow edit
            $is_editable = 
                ($this->date != Carbon::now(config('school.timezone'))->toDateString()) || 
                !is_null($leave_application) || 
                $school_attendance_status == AttendanceStatus::ABSENT ||
                $period_attendance->period == self::FIRST_CLASS_AFFAIR_PERIOD  
            ? false : $is_attendance_required;

            $class_attendances[$period_attendance->period] = [
                'period' => $period_attendance->period,
                'timeslot_type' => $period_attendance->timeslot_type,
                'timeslot_id' => $period_attendance->timeslot_id,
                'class_attendance_status' => $period_attendance->status,
                'is_editable' => $is_editable,
                'is_default' => $period_attendance->updated_by_employee_id == null ? true : false,
                'leave_application_id' => $leave_application ? $leave_application->id : null,
                'leave_application_info' => $leave_application_info,
                'updated_by_employee' => $updated_by_employee,
            ];
        }
        return $class_attendances;
    }

    public function getClassAttendancePeriodLabel($timeslots)
    {
        $timeslots = collect($timeslots);
        $period_labels = [];
        switch (get_class($timeslots->first())) {
            case Timeslot::class:
                $period_labels = PeriodLabel::query()
                    ->where('period_group_id', $timeslots->first()->period->period_group_id)
                    ->whereIn('period', $timeslots->pluck('period')->flatten()->pluck('period')->toArray())
                    ->get()
                    ->keyBy('period')
                    ->all();
                foreach ($timeslots as $timeslot) {
                    $period = $timeslot->period->period;
                    $from = Carbon::parse($timeslot->attendance_from)->format('H:i');
                    $to = Carbon::parse($timeslot->attendance_to)->format('H:i');
                    $period_label_name = $period_labels[$period]->getTranslation('name', app()->getLocale());
                    $period_labels[$period] = [
                        'period' => $period,
                        'label' => "{$period_label_name} ({$from} - {$to})"
                    ];
                }
                break;
            case TimeslotOverride::class:
                foreach ($timeslots as $timeslot) {
                    $period = $timeslot->period;
                    $from = Carbon::parse($timeslot->attendance_from)->format('H:i');
                    $to = Carbon::parse($timeslot->attendance_to)->format('H:i');
                    $period_labels[$period] = [
                        'period' => $period,
                        'label' => "Period {$period} ({$from} - {$to})"
                    ];
                }
                break;
            default:
                ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42009);
        }
        return $period_labels;
    }

    public function getClassAttendanceByTimeslot(): array
    {
        switch ($this->timeslotType) {
            case Timeslot::class:
                $timeslots = Timeslot::with(['timetable.semesterClass.activeStudentClasses', 'period'])->findOrFail($this->timeslotIds);
                $seat_no_list_with_student_id_as_key = $timeslots->first()->timetable->semesterClass->activeStudentClasses->pluck('seat_no', 'student_id')->toArray();
                break;
            case TimeslotOverride::class:
                $seat_no_list_with_student_id_as_key = [];
                $timeslot_overrides = $this->timeslotOverrideRepository->getAll([
                    'date' => $this->date,
                    'employee_id' => $this->employee->id,
                    'period' => $this->timeslotIds, // getPeriodsByTimeslotTeacher pass period instead of timeslot override id for override scenario
                ]);
                $timeslots = $timeslot_overrides->keyBy('period');
                $this->timeslotIds = $timeslot_overrides->pluck('id')->toArray(); // overwrite to actual ids (if 10 students mean will have 10 timeslot override ids)
                break;
            default:
                ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42009);
        }

        $period_attendances_group_by_student_id = $this->periodAttendanceRepository->getAll([
            'date' => $this->date,
            'timeslot_id' => $this->timeslotIds, // array, could be 1 period attendance, or more than 1 period attendance with different period
            'timeslot_type' => $this->timeslotType,
            'includes' => ['leaveApplication', 'employee'],
        ])
            ->sortBy('period')
            ->groupBy('student_id')
            ->all();

        $student_ids = array_keys($period_attendances_group_by_student_id);

        $students_school_is_attendance_required = $this->calendarTargetRepository->getStudentsSchoolDayOrNonSchoolDayByDate($this->date, $student_ids);

        $student_school_attendances = Attendance::query()
            ->select(['id', 'attendance_recordable_type', 'attendance_recordable_id', 'check_in_datetime', 'date', 'status'])
            ->where('date', $this->date)
            ->whereIn('attendance_recordable_id', $student_ids)
            ->where('attendance_recordable_type', Student::class)
            ->get()
            ->keyBy('attendance_recordable_id')
            ->all();

        $students = Student::with(['primaryClass.semesterClass.classModel', 'media'])
            ->select(['id', 'name', 'student_number'])
            ->whereIn('id', $student_ids)
            ->where('is_active', true)
            ->whereHas('primaryClass')
            ->get()
            ->keyBy('id')
            ->all();

        // populate data
        $data = [];
        $students_data = [];
        foreach ($students as $student) {
            $students_data[$student->id] = [
                'student_id' => $student->id,
                'student_number' => $student->student_number,
                'student_name' => $student->name,
                'student_name_translations' => $student->translations,
                'student_photo' => $student->photo,
                'seat_no' => $seat_no_list_with_student_id_as_key[$student->id] ?? null,
                'school_check_in_datetime' => (isset($student_school_attendances[$student->id]) && $student_school_attendances[$student->id]->check_in_datetime !== null) ? Carbon::parse($student_school_attendances[$student->id]->check_in_datetime)->toISOString() : null, // UTC
                'current_class' => $this->getStudentCurrentClass($student),
                'class_attendances' => $this->getStudentClassAttendances(
                    $period_attendances_group_by_student_id[$student->id],
                    $students_school_is_attendance_required[$student->id] ?? false,
                    isset($student_school_attendances[$student->id]) ? $student_school_attendances[$student->id]->status : AttendanceStatus::ABSENT,
                ),
            ];
        }

        $students_data = collect($students_data)
            ->sortBy([
                ['seat_no', 'asc'],
                ['student_name', 'asc']
            ])
            ->values()
            ->toArray();

        $data['periods'] = $this->getClassAttendancePeriodLabel($timeslots);
        $data['students'] = $students_data;

        return $data;
    }

    public function bulkUpdateClassAttendance(array $class_attendance_data_list)
    {
        $summary = [];
        // remove array without student_id or class_attendance_status
        $class_attendance_data_list = array_filter($class_attendance_data_list, function ($item) {
            return isset($item['student_id']) && isset($item['class_attendance_status']);
        });

        if (count($class_attendance_data_list) == 0) {
            return $summary;
        }

        $student_ids = array_column($class_attendance_data_list, 'student_id');

        // existing period attendances
        $student_existing_period_attendances = $this->periodAttendanceRepository->getAll([
            'student_id' => $student_ids,
            'date' => $this->date,
            'period' => $this->period,
        ])
            ->keyBy(['student_id'])
            ->all();

        if (count($student_existing_period_attendances) == 0) {
            return $summary;
        }

        $period_attendances_to_be_updated_by_id = [];
        foreach ($class_attendance_data_list as $class_attendance_data) {
            $student_id = $class_attendance_data['student_id'];
            $existing_period_attendance = $student_existing_period_attendances[$student_id] ?? null;

            if ($existing_period_attendance == null) {
                continue;
            }

            $period_attendances_to_be_updated_by_id[$existing_period_attendance->id] = [
                'student_id' => $student_id,
                'date' => $this->date,
                'timeslot_id' => $existing_period_attendance->timeslot_id,
                'timeslot_type' => $existing_period_attendance->timeslot_type,
                'updated_by_employee_id' => $this->employee->id,
                'status' => $class_attendance_data['class_attendance_status'],
                'leave_application_id' => $existing_period_attendance->leave_application_id,
                'created_at' => $existing_period_attendance->created_at,
                'updated_at' => now(),
                'period' => $this->period,
                'has_mark_deduction' => $existing_period_attendance->has_mark_deduction,
            ];
        }

        DB::transaction(function () use ($period_attendances_to_be_updated_by_id) {
            foreach ($period_attendances_to_be_updated_by_id as $id => $period_attendance_data) {
                $this->periodAttendanceRepository->updateById($id, $period_attendance_data);
            }
        });


        $period_attendances = collect($period_attendances_to_be_updated_by_id);
        $timeslot_type = $period_attendances->first()['timeslot_type'];
        $timeslot_id = $period_attendances->first()['timeslot_id'];
        switch ($timeslot_type) {
            case Timeslot::class:
                $timeslot = Timeslot::with(['period'])->findOrFail($timeslot_id);
                $from = Carbon::parse($timeslot->attendance_from)->format('H:i');
                $to = Carbon::parse($timeslot->attendance_to)->format('H:i');
                $period_label = PeriodLabel::query()
                    ->where('period_group_id', $timeslot->period->period_group_id)
                    ->where('period', $this->period)
                    ->firstOrFail();
                $period_label_name = $period_label->getTranslation('name', app()->getLocale());
                $summary['header'] = "{$period_label_name} ({$from} - {$to})";
                break;
            case TimeslotOverride::class:
                $timeslot_override = TimeslotOverride::findOrFail($timeslot_id);
                $from = Carbon::parse($timeslot_override->attendance_from)->format('H:i');
                $to = Carbon::parse($timeslot_override->attendance_to)->format('H:i');
                $summary['header'] = "Period {$this->period} ({$from} - {$to})";
                break;
            default:
                ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42009);
        }
        $summary['body'] = [
            [
                PeriodAttendanceStatus::LATE->value => $period_attendances->where('status', PeriodAttendanceStatus::LATE->value)->count(),
            ],
            [
                PeriodAttendanceStatus::ABSENT->value => $period_attendances->where('status', PeriodAttendanceStatus::ABSENT->value)->count(),
            ],
            [
                PeriodAttendanceStatus::PRESENT->value => $period_attendances->where('status', PeriodAttendanceStatus::PRESENT->value)->count(),
            ]
        ];
        $summary['footer'] = [
            'TOTAL' => $period_attendances->count(),
        ];

        return $summary;
    }

    public function setIsAllStudents(bool $is_all_students)
    {
        $this->isAllStudents = $is_all_students;
        return $this;
    }

    public function setIsAllEmployees(bool $is_all_employees)
    {
        $this->isAllEmployees = $is_all_employees;
        return $this;
    }

    public function setIsAllContractors(bool $is_all_contractors)
    {
        $this->isAllContractors = $is_all_contractors;
        return $this;
    }

    public function setStudentIds(array $student_ids)
    {
        $this->studentIds = $student_ids;
        return $this;
    }

    public function setEmployeeIds(array $employee_ids)
    {
        $this->employeeIds = $employee_ids;
        return $this;
    }

    public function setContractorIds(array $contractor_ids)
    {
        $this->contractorIds = $contractor_ids;
        return $this;
    }

    public function triggerAttendancePosting($throw_error = true)
    {
        $posting_status = false;

        $date = $this->date;

        if ($this->isAllStudents == true) {
            Artisan::call('posting:student-attendance-input', ['--date' => $date]);
            $posting_status = true;
        } elseif (count($this->studentIds) > 0) {
            $student_ids_string = implode(',', $this->studentIds);
            Artisan::call('posting:student-attendance-input', ['--date' => $date, '--student_ids' => $student_ids_string]);
            $posting_status = true;
        }

        $employee_contractor_posting_params = [];
        if ($this->isAllEmployees == true) {
            $employee_contractor_posting_params['--is-all-employees'] = true;
        } elseif (count($this->employeeIds) > 0) {
            $employee_ids_string = implode(',', $this->employeeIds);
            $employee_contractor_posting_params['--employee_ids'] = $employee_ids_string;
        }

        if ($this->isAllContractors == true) {
            $employee_contractor_posting_params['--is-all-contractors'] = true;
        } elseif (count($this->contractorIds) > 0) {
            $contractor_ids_string = implode(',', $this->contractorIds);
            $employee_contractor_posting_params['--contractor_ids'] = $contractor_ids_string;
        }

        if (!empty($employee_contractor_posting_params)) {
            $employee_contractor_posting_params['--date'] = $date;
            Artisan::call('posting:employee-contractor-attendance-input', $employee_contractor_posting_params);
            $posting_status = true;
        }

        if ($posting_status == false && $throw_error == true) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42005);
        }
    }
}
