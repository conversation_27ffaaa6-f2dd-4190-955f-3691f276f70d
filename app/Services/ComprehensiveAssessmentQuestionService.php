<?php

namespace App\Services;

use App\Repositories\ComprehensiveAssessmentQuestionRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ComprehensiveAssessmentQuestionService
{
    private ComprehensiveAssessmentQuestionRepository $comprehensiveAssessmentQuestionRepository;

    public function __construct(ComprehensiveAssessmentQuestionRepository $comprehensive_assessment_question_repository)
    {
        $this->comprehensiveAssessmentQuestionRepository = $comprehensive_assessment_question_repository;
    }

    public function getAllComprehensiveAssessmentQuestions($filters = []): Collection
    {
        return $this->comprehensiveAssessmentQuestionRepository->getAll($filters);
    }

    public function getAllPaginatedComprehensiveAssessmentQuestions($filters = []): LengthAwarePaginator
    {
        return $this->comprehensiveAssessmentQuestionRepository->getAllPaginated($filters);
    }

    public function createComprehensiveAssessmentQuestion($data): ?Model
    {
        return DB::transaction(function () use ($data) {
            return $this->comprehensiveAssessmentQuestionRepository->create($data);
        });
    }

    public function updateComprehensiveAssessmentQuestion($id, $data): ?Model
    {
        return DB::transaction(function () use ($id, $data) {
            return $this->comprehensiveAssessmentQuestionRepository->update($id, $data);
        });
    }

    public function deleteComprehensiveAssessmentQuestion($id): bool
    {
        return $this->comprehensiveAssessmentQuestionRepository->delete($id);
    }
}
