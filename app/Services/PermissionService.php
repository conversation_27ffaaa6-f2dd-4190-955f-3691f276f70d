<?php

namespace App\Services;

use App\Repositories\PermissionRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class PermissionService
{
    public function __construct(protected PermissionRepository $permissionRepository) {}

    public function getAllPaginatedPermissions($filters = []): LengthAwarePaginator
    {
        return $this->permissionRepository->getAllPaginated($filters);
    }

    public function getAllPermissions($filters = []): Collection
    {
        return $this->permissionRepository->getAll($filters);
    }
}
