<?php

namespace App\Services;

use App\Interfaces\ReportExportable;
use App\Traits\ExportFileAdaptable;


/**
 * Used to print PDF/Excel for a report (have a collection of rows/custom query)
 */
class ReportPrintService extends DocumentPrintService
{
    use ExportFileAdaptable;

    protected string $paperOrientation = self::PAPER_ORIENTATION_PORTRAIT;

    public function __construct()
    {
        parent::__construct();
    }

    public function generate(): static
    {
        if (!isset($this->exportFileAdapter)) {
            throw new \Exception('Please define a report adapter first.');
        }

        $this->localFilePath = $this->exportFileAdapter->getFileNameWithExtension(static::PRINT_FOLDER . DIRECTORY_SEPARATOR . $this->fileName);

        $this->setFileName($this->exportFileAdapter->getFileNameWithExtension($this->fileName));

        $this->exportFileAdapter
            ->setOutputLocalFilePath($this->localFilePath)
            ->setPaperOrientation($this->paperOrientation)
            ->generate();

        return $this;

    }

}
