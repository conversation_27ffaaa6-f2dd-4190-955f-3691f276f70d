<?php

namespace App\Services\PushNotification;

use App\Interfaces\IPushNotification;
use Aws\Sns\SnsClient;

class AndroidPushNotificationService extends PushNotificationService implements IPushNotification
{
    protected SnsClient $client;

    public function __construct()
    {
        parent::__construct();

        $this->client = new SnsClient([
            'version' => 'latest',
            'region' => config('services.sns.region'),
            'credentials' => [
                'key' => config('services.sns.key'),
                'secret' => config('services.sns.secret'),
            ]
        ]);
    }

    public function registerTokenWithProvider($token): ?string
    {
        if (!isset($this->user)) {
            throw new \Exception('Please specify user');
        }

        $result = $this->client->createPlatformEndpoint([
            // Lucas - 24 Jan 2025: CustomUserData is disabled so that it wont cause re-addition of the same token
            // 'CustomUserData' => $this->user->id . '/' . $this->user->email . '/' . $this->user->phone_number,
            'PlatformApplicationArn' => config('school.sns.fcm_application_arn'),
            'Token' => $token,
        ]);

        $this->token = $result['EndpointArn'];

        return $result['EndpointArn'] ?? null;
    }

    public function send(): array
    {
        $payload = $this->getEncodedPayload();

        return $this->client->publish([
            'TargetArn' => $this->getToken(),
            'Message' => $payload,
            'MessageStructure' => 'json',
        ])->toArray();
    }

    public function getEncodedPayload(): string
    {
        $payload = json_encode([
            'notification' => [
                'title' => $this->getTitle(),
                'body' => $this->getBody(),
                'sound' => 'default',
                'clickAction' => $this->getClickAction(),
            ],
            "data" => $this->getData(),
        ]);

        return json_encode(['GCM' => $payload]);
    }

    public function getClient(): SnsClient
    {
        return $this->client;
    }

    public function deleteTokenAtProvider(string $token)
    {
        $this->client->deleteEndpoint([
            'EndpointArn' => $token
        ]);

        return true;
    }


}
