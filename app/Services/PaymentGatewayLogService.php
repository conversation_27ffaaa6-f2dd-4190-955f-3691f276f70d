<?php

namespace App\Services;

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Interfaces\TransactionLoggable;
use App\Models\BillingDocument;
use App\Models\Currency;
use App\Models\EnrollmentUser;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Repositories\PaymentGatewayLogRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class PaymentGatewayLogService
{
    const DEFAULT_DESCRIPTION_WALLET_DEPOSIT = 'Deposit Wallet';
    const DEFAULT_DESCRIPTION_PAY_UNPAID_ITEM = 'Pay Unpaid Item';
    const DEFAULT_DESCRIPTION_ENROLLMENT_FEE = 'Pay Enrollment Fee';
    protected ?Model $model;
    protected PaymentGatewayLogRepository $paymentGatewayLogRepository;
    protected array $data = [];
    private TransactionLoggable $transactionLoggable;
    protected array $paymentGatewayTransaction;
    protected BillingDocument $billingDocument;
    protected PaymentMethod $paymentMethod;

    public function __construct(
        PaymentGatewayLogRepository $payment_gateway_log_repository
    ) {
        $this->paymentGatewayLogRepository = $payment_gateway_log_repository;
    }

    public function setModel(Model $model): static
    {
        $this->model = $model;

        return $this;
    }

    public function setUser(EnrollmentUser|User $user): self
    {
        $this->user = $user;
        $this->data['requested_by_id'] = $user->id;
        $this->data['requested_by_type'] = get_class($user);

        return $this;
    }

    public function setStatus(PaymentStatus $status): self
    {
        $this->data['status'] = $status;

        return $this;
    }

    public function setTransactionDatetime(Carbon|null $transaction_date): static
    {
        $this->data['transaction_datetime'] = $transaction_date;

        return $this;
    }

    public function setType(PaymentType $type): static
    {
        $this->data['type'] = $type;

        return $this;
    }

    public function setProvider(PaymentProvider $provider): static
    {
        $this->data['provider'] = $provider;

        return $this;
    }

    public function setTransactionLoggable(TransactionLoggable $loggable): static
    {
        $this->transactionLoggable = $loggable;
        $this->data['transaction_loggable_type'] = $loggable->getMorphClass();
        $this->data['transaction_loggable_id'] = $loggable->id;

        return $this;
    }

    public function setPaymentUrl(string $payment_url): static
    {
        $this->data['payment_url'] = $payment_url;

        return $this;
    }

    public function setOrderId(string $order_id): static
    {
        $this->data['order_id'] = $order_id;

        return $this;
    }

    public function setCurrency(Currency $currency): static
    {
        $this->data['currency_id'] = $currency->id;
        $this->data['currency_code'] = $currency->code;
        $this->data['currency_name'] = $currency->name;

        return $this;
    }

    public function setAmount(float $amount): static
    {
        $this->data['amount'] = $amount;

        return $this;
    }

    public function setDescription(string $description): static
    {
        $this->data['description'] = $description;

        return $this;
    }

    public function setRemark(?string $remark): static
    {
        $this->data['remark'] = $remark;

        return $this;
    }

    public function setReferenceId(?string $reference_id): static
    {
        $this->data['reference_id'] = $reference_id;

        return $this;
    }

    public function setRequestData(array $request_data): static
    {
        $this->data['request_data'] = $request_data;

        return $this;
    }

    public function setResponseData(array $response_data): static
    {
        $this->data['response_data'] = $response_data;

        return $this;
    }

    public function setCallbackData(array $callback_data): static
    {
        $this->data['callback_data'] = $callback_data;

        return $this;
    }

    public function createPaymentGatewayLog(): ?Model
    {
        $this->data['token'] = Str::uuid()->toString();
        $this->model = $this->paymentGatewayLogRepository->create($this->data);

        return $this->model;
    }

    public function updatePaymentGatewayLog(): ?Model
    {
        $this->model = $this->paymentGatewayLogRepository->update($this->model, $this->data);

        return $this->model;
    }

    public function createWalletDepositTransaction($amount): Model
    {

        $this->setType(PaymentType::WALLET_DEPOSIT)
            ->setAmount($amount)
            ->setCurrency($this->transactionLoggable->getCurrency())
            ->setDescription(self::DEFAULT_DESCRIPTION_WALLET_DEPOSIT)
            ->setStatus(PaymentStatus::PENDING);

        return $this->createPaymentGatewayLog();
    }

    public function createPayUnpaidItemTransaction($amount): Model
    {
        $this->setType(PaymentType::FEE_PAYMENT)
            ->setAmount($amount)
            ->setCurrency($this->transactionLoggable->getCurrency())
            ->setDescription(self::DEFAULT_DESCRIPTION_PAY_UNPAID_ITEM)
            ->setStatus(PaymentStatus::PENDING);

        return $this->createPaymentGatewayLog();
    }

    public function createUnpaidItemForEnrollmentTransaction($amount): Model
    {
        $this->setType(PaymentType::ENROLLMENT_PAYMENT)
            ->setAmount($amount)
            ->setCurrency($this->billingDocument->getCurrency())
            ->setDescription(self::DEFAULT_DESCRIPTION_ENROLLMENT_FEE)
            ->setStatus(PaymentStatus::PENDING);

        return $this->createPaymentGatewayLog();
    }

    public function setPaymentGatewayTransaction(array $payment_gateway_transaction): self
    {
        $this->paymentGatewayTransaction = $payment_gateway_transaction;

        return $this;
    }

    public function setBillingDocument(BillingDocument $billing_document): PaymentGatewayLogService
    {
        $this->billingDocument = $billing_document;
        $this->data['billing_document_id'] = $billing_document->id;

        return $this;
    }

    public function setPaymentMethod(PaymentMethod $paymentMethod): PaymentGatewayLogService
    {
        $this->data['payment_method_id'] = $paymentMethod->id;
        return $this;
    }


}
