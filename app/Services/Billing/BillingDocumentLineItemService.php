<?php

namespace App\Services\Billing;

use App\Interfaces\Billable;
use App\Interfaces\BillableItem;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\GlAccount;
use App\Models\Product;
use App\Models\Uom;
use App\Repositories\BillingDocumentLineItemRepository;

class BillingDocumentLineItemService {


    protected string $description;
    protected ?Product $product;
    protected ?BillableItem $billableItem;
    protected string $currency;
    protected string $uom;
    protected float $unitPrice;
    protected float $quantity;
    protected ?float $amountBeforeTax;
    protected ?int $offsetBillingDocumentId;

    protected string $glAccountCode;
    protected bool $isDiscount;
    protected ?DiscountSetting $discount;
    protected ?BillingDocumentLineItem $discountOriginalLineItem;

    protected BillingDocumentLineItemRepository $billingDocumentLineItemRepository;

    public function __construct(BillingDocumentLineItemRepository $billingDocumentLineItemRepository)
    {
        $this->billingDocumentLineItemRepository = $billingDocumentLineItemRepository;
        $this->init();
    }

    public function init() {
        $this->product = null;
        $this->offsetBillingDocumentId = null;
        $this->billableItem = null;
        $this->uom = Uom::CODE_DEFAULT;
        $this->glAccountCode = GlAccount::CODE_OTHERS;
        $this->amountBeforeTax = null;
        $this->unitPrice = 0;
        $this->quantity = 0;
        $this->isDiscount = false;
        return $this;
    }

    public function make() : BillingDocumentLineItem {

        $line_item = new BillingDocumentLineItem();
        $line_item->description = trim($this->description) ?? null;
        $line_item->product_id = isset($this->product) ? $this->product->id : null;
        $line_item->billable_item_type = isset($this->billableItem) ? get_class($this->billableItem) : null;
        $line_item->billable_item_id = isset($this->billableItem) ? $this->billableItem->id : null;
        $line_item->currency_code = $this->currency;
        $line_item->uom_code = $this->uom;
        $line_item->unit_price = $this->unitPrice;
        $line_item->quantity = $this->quantity;
        $line_item->amount_before_tax = $this->amountBeforeTax ?? bcmul($this->unitPrice, $this->quantity, 2);
        $line_item->gl_account_code = $this->glAccountCode;
        $line_item->offset_billing_document_id = $this->offsetBillingDocumentId ?? null;
        $line_item->is_discount = $this->isDiscount;
        $line_item->discount_id = isset($this->discount) ? $this->discount->id : null;
        $line_item->discount_original_line_item_id = isset($this->discountOriginalLineItem) ? $this->discountOriginalLineItem->id : null;

        return $line_item;

    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): BillingDocumentLineItemService
    {
        $this->description = $description;
        return $this;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function setProduct(Product $product): BillingDocumentLineItemService
    {
        $this->product = $product;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): BillingDocumentLineItemService
    {
        $this->currency = $currency;
        return $this;
    }

    public function getUom(): string
    {
        return $this->uom;
    }

    public function setUom(string $uom): BillingDocumentLineItemService
    {
        $this->uom = $uom;
        return $this;
    }

    public function getUnitPrice(): float
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(float $unitPrice): BillingDocumentLineItemService
    {
        $this->unitPrice = $unitPrice;
        return $this;
    }

    public function getQuantity(): float
    {
        return $this->quantity;
    }

    public function setQuantity(float $quantity): BillingDocumentLineItemService
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getGlAccountCode(): string
    {
        return $this->glAccountCode;
    }

    public function setGlAccountCode(string $glAccountCode): BillingDocumentLineItemService
    {
        $this->glAccountCode = $glAccountCode;
        return $this;
    }

    public function getBillableItem(): BillableItem
    {
        return $this->billableItem;
    }

    public function setBillableItem(BillableItem $billableItem): BillingDocumentLineItemService
    {
        $this->billableItem = $billableItem;
        return $this;
    }

    public function getAmountBeforeTax(): float
    {
        return $this->amountBeforeTax;
    }

    public function setAmountBeforeTax(float $amountBeforeTax): BillingDocumentLineItemService
    {
        $this->amountBeforeTax = $amountBeforeTax;
        return $this;
    }

    public function getOffsetBillingDocumentId(): int
    {
        return $this->offsetBillingDocumentId;
    }

    public function setOffsetBillingDocumentId(int $offsetBillingDocumentId): BillingDocumentLineItemService
    {
        $this->offsetBillingDocumentId = $offsetBillingDocumentId;
        return $this;
    }

    public function isDiscount(): bool
    {
        return $this->isDiscount;
    }

    public function setIsDiscount(bool $isDiscount): BillingDocumentLineItemService
    {
        $this->isDiscount = $isDiscount;
        return $this;
    }

    public function getDiscount(): ?DiscountSetting
    {
        return $this->discount;
    }

    public function setDiscount(?DiscountSetting $discount): BillingDocumentLineItemService
    {
        $this->discount = $discount;
        return $this;
    }

    public function getDiscountOriginalLineItem(): ?BillingDocumentLineItem
    {
        return $this->discountOriginalLineItem;
    }

    public function setDiscountOriginalLineItem(?BillingDocumentLineItem $discountOriginalLineItem): BillingDocumentLineItemService
    {
        $this->discountOriginalLineItem = $discountOriginalLineItem;
        return $this;
    }

}
