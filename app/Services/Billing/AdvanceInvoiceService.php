<?php

namespace App\Services\Billing;

use App\Exceptions\BillingLogicRestrictionException;
use App\Models\BillingDocument;
use App\Repositories\AdvanceTransactionRepository;
use App\Repositories\BillingDocumentRepository;
use App\Services\DocumentRunningNumberService;

class AdvanceInvoiceService extends BillingDocumentService
{
    protected AdvanceTransactionRepository $advanceTransactionRepository;

    public function __construct(BillingDocumentRepository $billingDocumentRepository, DocumentRunningNumberService $documentRunningNumberService, AdvancePaymentService $advancePaymentService, AdvanceTransactionRepository $advanceTransactionRepository)
    {
        parent::__construct($billingDocumentRepository, $documentRunningNumberService, $advancePaymentService);
        $this->advanceTransactionRepository = $advanceTransactionRepository;
    }

    public function changeStatusTo($new_status) {

        \DB::transaction(function () use (&$new_status) {

            parent::changeStatusTo($new_status);

            if ( $new_status === BillingDocument::STATUS_VOIDED ) {

                $this->validateStatusChangeToVoided();

                $transactions = $this->advanceTransactionRepository->getAll([
                    'advance_invoice_id' => $this->billingDocument->id,
                    'empty_used_in_invoice_id' => true,
                    'order_by' => ['id' => 'desc'],
                ]);

                // cancel advance balance
                // get current advance transaction, reverse it and create new row
                foreach ( $transactions as $transaction ) {

                    $this->advanceTransactionRepository->create([
                        'billable_type' => $transaction->billable_type,
                        'billable_id' => $transaction->billable_id,
                        'advance_invoice_id' => $transaction->advance_invoice_id,
                        'amount_before_tax' => -abs($transaction->amount_before_tax),
                        'currency_code' => $transaction->currency_code,
                        'gl_account_code' => $transaction->gl_account_code,
                        'used_in_invoice_id' => null,
                    ]);

                }
            }

        });

        return $this;

    }

    public function validateStatusChangeToVoided() {

        // if advance invoice is used, cannot void.
        if ( $this->advanceTransactionRepository->isAdvanceInvoicedUsed($this->billingDocument) ) {
            throw new BillingLogicRestrictionException('Unable to void advance invoice that was previously used.');
        }

        return true;

    }
}
