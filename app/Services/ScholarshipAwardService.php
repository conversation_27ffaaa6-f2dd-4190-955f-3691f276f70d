<?php

namespace App\Services;

use App\Models\DiscountSetting;
use App\Models\Scholarship;
use App\Models\ScholarshipAward;
use App\Models\Student;
use App\Repositories\ScholarshipAwardRepository;
use App\Services\Billing\DiscountSettingService;
use Carbon\Carbon;

class ScholarshipAwardService
{
    protected ScholarshipAwardRepository $scholarshipAwardRepository;
    protected Student $awardToStudent;
    protected Scholarship $scholarship;
    protected Carbon $effectiveFrom;
    protected Carbon $effectiveTo;
    protected ScholarshipAward $scholarshipAward;

    public function __construct(ScholarshipAwardRepository $scholarshipAwardRepository)
    {
        $this->scholarshipAwardRepository = $scholarshipAwardRepository;
    }

    public function create() {

        $this->validate();

        $this->scholarshipAward = $this->scholarshipAwardRepository->create([
            'scholarship_id' => $this->scholarship->id,
            'student_id' => $this->awardToStudent->id,
            'effective_from' => $this->effectiveFrom->toDateString(),
            'effective_to' => $this->effectiveTo->toDateString(),
        ]);

        return $this;
    }

    public function updateEffectiveDates() {

        $this->validate();

        $this->scholarshipAward->effective_from = $this->effectiveFrom->toDateString();
        $this->scholarshipAward->effective_to = $this->effectiveTo->toDateString();

        $this->scholarshipAward->save();

        return $this;
    }

    public function validate() {

        if ( $this->effectiveTo->isBefore($this->effectiveFrom) ) {
            throw new \Exception('Invalid effective from/to dates.');
        }

        if ( !isset($this->scholarship) ) {
            throw new \Exception('Please specify a scholarship to be awarded.');
        }

        if ( !isset($this->awardToStudent) ) {
            throw new \Exception('Please specify a student to receive the scholarship.');
        }

        return true;
    }

    public function getEffectiveFrom(): Carbon
    {
        return $this->effectiveFrom;
    }

    public function setEffectiveFrom(Carbon $effectiveFrom): ScholarshipAwardService
    {
        $this->effectiveFrom = $effectiveFrom;
        return $this;
    }

    public function getEffectiveTo(): Carbon
    {
        return $this->effectiveTo;
    }

    public function setEffectiveTo(Carbon $effectiveTo): ScholarshipAwardService
    {
        $this->effectiveTo = $effectiveTo;
        return $this;
    }

    public function getScholarshipAward(): ScholarshipAward
    {
        return $this->scholarshipAward;
    }

    public function setScholarshipAward(ScholarshipAward $scholarshipAward): ScholarshipAwardService
    {
        $this->scholarshipAward = $scholarshipAward;
        return $this;
    }

    public function getScholarship(): Scholarship
    {
        return $this->scholarship;
    }

    public function setScholarship(Scholarship $scholarship): ScholarshipAwardService
    {
        $this->scholarship = $scholarship;
        return $this;
    }

    public function getAwardToStudent(): Student
    {
        return $this->awardToStudent;
    }

    public function setAwardToStudent(Student $awardToStudent): ScholarshipAwardService
    {
        $this->awardToStudent = $awardToStudent;
        return $this;
    }


}
