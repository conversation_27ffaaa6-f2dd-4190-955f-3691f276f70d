<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ResetPasswordService
{
    public function __construct(
        protected UserService $userService,
        protected UserLoginOtpService $userLoginOtpService,
    )
    {
    }

    public function resetPasswordByEmail(array $input)
    {
        /** @var ?User */
        $user = $this->userService->firstUserByEmail($input['email']);

        if (!$user) {
            return;
        }

        if ($user->isInactive()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 4007);
        }

        DB::transaction(function () use ($user, $input) {
            $this->validateOtp($user, $input['otp']);

            $this->userService->changePassword($user, $input['password']);
        });
    }

    private function validateOtp(User|Model $user, string $otp): void
    {
        $valid_otp = $this->userLoginOtpService->validateAndDeleteOtp($user, $otp);

        if (!$valid_otp) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 4004);
        }
    }
}
