<?php

namespace App\Services;

use App\Helpers\ConfigHelper;
use App\Helpers\SystemHelper;
use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\Config;
use App\Models\GlAccount;
use App\Models\PaymentMethod;
use App\Models\Student;
use App\Models\WithdrawalReason;
use App\Repositories\AdvanceTransactionRepository;
use App\Repositories\BillingDocumentRepository;
use App\Services\Billing\AdvanceInvoiceService;
use App\Services\Billing\AdvancePaymentService;
use App\Services\Billing\BillingDocumentLineItemService;
use App\Services\Billing\BillingDocumentService;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\PaymentService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class HostelSavingsAccountService
{
    protected AdvancePaymentService $advancePaymentService;
    protected AdvanceInvoiceService $advanceInvoiceService;
    protected InvoiceService $invoiceService;
    protected PaymentService $paymentService;

    protected Student $student;

    protected float $amount;
    protected PaymentMethod $paymentMethod;
    protected ?Bank $bank;
    protected ?string $remarks;
    protected ?string $referenceNo;
    protected Carbon $paymentDate;
    protected WithdrawalReason $withdrawalReason;

    public function __construct(AdvancePaymentService $advancePaymentService, AdvanceInvoiceService $advanceInvoiceService, PaymentService $paymentService, InvoiceService $invoiceService) {
        $this->advancePaymentService = $advancePaymentService;
        $this->advanceInvoiceService = $advanceInvoiceService;
        $this->invoiceService = $invoiceService;
        $this->paymentService = $paymentService;
    }

    public function getBalance($currency_code) {

        $balance = $this->advancePaymentService->setBillable($this->student)
            ->getEligibleAdvancesForCurrency($currency_code)
            ->getAdvanceBalances()
            ->where('glAccountCode', GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS)
            ->sum('balanceBeforeTax');

        return bcadd($balance, 0, 2);

    }

    public function deposit() {

        $default_bank_account = BankAccount::findOrFail(ConfigHelper::get(Config::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT));

        if ( !\Auth::user()->isEmployee() ) {
            throw new \Exception('Only employee can perform this action.');
        }

        DB::transaction(function () use (&$default_bank_account) {

            // create advance invoice with GL account code and tie to student
            $line_item = app()->make(BillingDocumentLineItemService::class)
                ->setGlAccountCode(GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS)
                ->setDescription('Hostel Savings Account Deposit for ' . $this->student->name)
                ->setAmountBeforeTax($this->amount)
                ->setProduct(SystemHelper::getHostelSavingsAccountProduct())
                ->setCurrency(config('school.currency_code'))
                ->make();

            $this->advanceInvoiceService->init()
                ->setStatus(BillingDocument::STATUS_CONFIRMED)
                ->setType(BillingDocument::TYPE_ADVANCE_INVOICE)
                ->setSubType(BillingDocument::SUB_TYPE_HOSTEL_SAVINGS_ACCOUNT)
                ->setCurrency(config('school.currency_code'))
                ->setDocumentDate(Carbon::now())
                ->setLegalEntity(SystemHelper::getDefaultLegalEntity())
                ->setBillToParty($this->student)
                ->setPaymentTerm(SystemHelper::getDefaultPaymentTerm())
                ->setRemitToBankAccount($default_bank_account)
                ->addLineItem($line_item)
                ->calculateAmountBeforeTax()
                ->applyTax(SystemHelper::getNotApplicableTax())
                ->calculatePaymentDueDate()
                ->generateReferenceNumber()
                ->create();     // no need change status to paid. The payment code below will change adv invoice status to paid

            $advance_invoice = $this->advanceInvoiceService->getBillingDocument();

            $this->paymentService->setCreatedByEmployee(\Auth::user()->employee)
                ->setBillingDocument($advance_invoice)
                ->setPaymentMethod($this->paymentMethod)
                ->setPaymentReferenceNo($this->referenceNo ?? 'NA')
                ->setBank($this->bank ?? null)
                ->setPaidAmount($this->amount)
                ->setPaidAt($this->paymentDate)
                ->setRemarks($this->remarks)
                ->create()
                ->triggerPostPaymentProcesses();        // this will mark adv inv as paid, and will trigger advance transactions creation

        });

        return $this;

    }


    public function withdraw() {

        $default_bank_account = BankAccount::findOrFail(ConfigHelper::get(Config::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT));

        if ( !\Auth::user()->isEmployee() ) {
            throw new \Exception('Only employee can perform this action.');
        }
        if ( !isset($this->withdrawalReason) ) {
            throw new \Exception('Must provide withdrawal reason.');
        }
        if ( GlAccount::where('code', GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS)->first() === null) {
            throw new \Exception('Please set up GL Account ' . GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS . ' first.');
        }

        $balance = $this->getBalance(config('school.currency_code'));

        if ( bccomp($this->amount, $balance, 2) === 1 ) {
            throw new \Exception('Withdraw amount cannot be more than account balance of ' . number_format($balance, 2));
        }

        DB::transaction(function () use (&$default_bank_account) {

            // create invoice with GL account code and tie to student
            $line_item = app()->make(BillingDocumentLineItemService::class)
                ->setGlAccountCode(GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS)
                ->setDescription('Hostel Savings Account Withdraw for ' . $this->student->name . '. Reason: ' . $this->withdrawalReason->name)
                ->setAmountBeforeTax($this->amount)
                ->setProduct(SystemHelper::getHostelSavingsAccountProduct())
                ->setCurrency(config('school.currency_code'))
                ->make();

            $this->invoiceService->init()
                ->setStatus(BillingDocument::STATUS_CONFIRMED)
                ->setType(BillingDocument::TYPE_INVOICE)
                ->setSubType(BillingDocument::SUB_TYPE_HOSTEL_SAVINGS_ACCOUNT)
                ->setCurrency(config('school.currency_code'))
                ->setDocumentDate(Carbon::now())
                ->setLegalEntity(SystemHelper::getDefaultLegalEntity())
                ->setBillToParty($this->student)
                ->setPaymentTerm(SystemHelper::getDefaultPaymentTerm())
                ->setRemitToBankAccount($default_bank_account)
                ->addLineItem($line_item)
                ->calculateAmountBeforeTax()
                ->applyAdvanceOffset()      // deduct advance
                ->applyTax(SystemHelper::getNotApplicableTax())
                ->calculatePaymentDueDate()
                ->generateReferenceNumber()
                ->create();

            $invoice = $this->invoiceService->getBillingDocument();

            $this->paymentService->setCreatedByEmployee(\Auth::user()->employee)
                ->setBillingDocument($invoice)
                ->setPaymentMethod($this->paymentMethod)
                ->setPaymentReferenceNo($this->referenceNo ?? 'NA')
                ->setBank($this->bank ?? null)
                ->setPaidAmount($invoice->amount_after_tax)
                ->setPaidAt($this->paymentDate)
                ->setRemarks($this->remarks)
                ->create();

            $this->invoiceService->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, $this->paymentDate);

        });

        return $this;

    }

    public function getTransactions($filters = []) {

        // get 1 student transactions only
        $filters['bill_to_type'] = $this->student->getBillToType();
        $filters['bill_to_id'] = $this->student->getBillToId();

        $filters['paid_at_from'] = $filters['period_from'];
        $filters['paid_at_to'] = $filters['period_to'];

        return app()->make(BillingDocumentRepository::class)
            ->getHostelSavingsAccountTransactions($filters, true);

    }

    public function getStudent(): Student
    {
        return $this->student;
    }

    public function setStudent(Student $student): HostelSavingsAccountService
    {
        $this->student = $student;
        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): HostelSavingsAccountService
    {
        $this->amount = $amount;
        return $this;
    }

    public function getPaymentMethod(): PaymentMethod
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(PaymentMethod $paymentMethod): HostelSavingsAccountService
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    public function getBank(): ?Bank
    {
        return $this->bank;
    }

    public function setBank(?Bank $bank): HostelSavingsAccountService
    {
        $this->bank = $bank;
        return $this;
    }

    public function getRemarks(): ?string
    {
        return $this->remarks;
    }

    public function setRemarks(?string $remarks): HostelSavingsAccountService
    {
        $this->remarks = $remarks;
        return $this;
    }

    public function getReferenceNo(): ?string
    {
        return $this->referenceNo;
    }

    public function setReferenceNo(?string $referenceNo): HostelSavingsAccountService
    {
        $this->referenceNo = $referenceNo;
        return $this;
    }

    public function getPaymentDate(): Carbon
    {
        return $this->paymentDate;
    }

    public function setPaymentDate(Carbon $paymentDate): HostelSavingsAccountService
    {
        $this->paymentDate = $paymentDate;
        return $this;
    }

    public function getWithdrawalReason(): WithdrawalReason
    {
        return $this->withdrawalReason;
    }

    public function setWithdrawalReason(WithdrawalReason $withdrawalReason): HostelSavingsAccountService
    {
        $this->withdrawalReason = $withdrawalReason;
        return $this;
    }

}
