<?php

namespace App\Services;

use App\Models\SchoolAttendancePeriodOverride;
use App\Repositories\SchoolAttendancePeriodOverrideRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class SchoolAttendancePeriodOverrideService
{
    private SchoolAttendancePeriodOverrideRepository $schoolAttendancePeriodOverrideRepository;

    public function __construct(SchoolAttendancePeriodOverrideRepository $school_attendance_period_override_repository)
    {
        $this->schoolAttendancePeriodOverrideRepository = $school_attendance_period_override_repository;
    }

    public function getAllPaginated($filters = []): LengthAwarePaginator
    {
        return $this->schoolAttendancePeriodOverrideRepository->getAllPaginated($filters);
    }

    public function getAll($filters = []): Collection
    {
        return $this->schoolAttendancePeriodOverrideRepository->getAll($filters);
    }

    public function create($data): SchoolAttendancePeriodOverride
    {
        return $this->schoolAttendancePeriodOverrideRepository->create($data);
    }

    public function update(SchoolAttendancePeriodOverride $school_attendance_period_override, $data): SchoolAttendancePeriodOverride
    {
        return $this->schoolAttendancePeriodOverrideRepository->update($school_attendance_period_override, $data);
    }

    public function delete(SchoolAttendancePeriodOverride $school_attendance_period_override): bool
    {
        return $this->schoolAttendancePeriodOverrideRepository->delete($school_attendance_period_override);
    }
}