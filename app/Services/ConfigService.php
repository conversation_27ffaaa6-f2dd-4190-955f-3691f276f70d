<?php

namespace App\Services;

use App\Helpers\ConfigHelper;
use App\Models\Config;
use App\Repositories\ConfigRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ConfigService
{
    private ConfigRepository $configRepository;

    public function __construct(ConfigRepository $config_repository)
    {
        $this->configRepository = $config_repository;
    }

    public function getAllPaginatedConfigs($filters = []): LengthAwarePaginator
    {
        $config = $this->getAllConfigs($filters);
        return new LengthAwarePaginator($config, count($config), $filters['per_page'], $filters['page']);
    }

    public function getAllConfigs($filters = []): array
    {
        $filters = ['keys' => array_keys(Config::AVAILABLE_CONFIGS)];

        $db_configs = $this->configRepository->getAll($filters)
            ->pluck('value', 'key')
            ->toArray();

        $default_configs = Config::AVAILABLE_CONFIGS;

        // Flip the array to get the key as value and value as key
        // current data: ['CATEGORY_GENERAL' => ['GENERAL'], 'CATEGORY_LIBRARY' => ['LIBRARY']]
        // flipped data: ['GENERAL' => 'CATEGORY_GENERAL', 'LIBRARY' => 'CATEGORY_LIBRARY']

        return collect($default_configs)->merge($db_configs)->map(function ($value, $key) {
            return [
                'key' => $key,
                'value' => $value,
                'category' => Config::getConfigCategoryList($key) ?? Config::CATEGORY_GENERAL,
                'type' => Config::CONFIG_DATA_TYPE[$key] ?? null,
                'options' => Config::getAvailableConfigOptions($key) ?? []
            ];
        })->values()->toArray();
    }

    public function updateOrCreateConfig($key, $value, $category): ?array
    {
        $updated_config = null;

        DB::transaction(function () use ($key, $value, $category, &$updated_config) {
            $updated_config = ConfigHelper::put($key, $value, $category);

            ConfigHelper::refreshDataCompulsoryLocales();
        });

        return $updated_config ? ['key' => $key, 'value' => $updated_config, 'category' => $category] : null;
    }

    public function getConfigValueAndLabel(string $key): array
    {
        $config = $this->getConfig($key);

        return Arr::where($config['options'], function ($value) use ($config) {
            return in_array($value['value'], $config['value']);
        });
    }

    public function getConfig(mixed $key, $throw_exception = true): mixed
    {
        $value = $this->configRepository->getConfigByKey($key)?->value;

        if (is_null($value)) {
            $value = $this->getConstantConfigValue($key, $throw_exception);

            if (is_null($value)) {
                return null;
            }
        }

        return $this->_reformat($key, $value);
    }

    public function getConfigValue(string $key, $throw_exception = true): mixed
    {
        $config = $this->getConfig($key, $throw_exception);
        return $config['value'] ?? null;
    }

    public function getUserConfigs(): array
    {
        $available_user_config_keys = array_keys(Config::AVAILABLE_USER_CONFIGS);

        foreach ($available_user_config_keys as $key) {
            $value = ConfigHelper::get($key);

            if (!$value){
                $value = $this->getConstantConfigValue($key, false);
            }
            $response[] = ['key' => $key, 'value' => $value];
        }
        return $response;
    }

    public function getConstantConfigValue(mixed $key, $throw_exception = true)
    {
        if (!isset(Config::AVAILABLE_CONFIGS[$key])) {
            if ($throw_exception) {
                throw new ModelNotFoundException('Config settings "' . $key . '" not found');
            } else {
                return null;
            }
        }
        // get default key from AVAILABLE_CONFIGS
        return Config::AVAILABLE_CONFIGS[$key];
    }

    protected function _reformat($key, $value): array
    {
        return [
            'key' => $key,
            'value' => $value,
            'type' => Config::CONFIG_DATA_TYPE[$key],
            'category' => Config::getConfigCategoryList($key) ?? Config::CATEGORY_GENERAL,
            'options' => Config::getAvailableConfigOptions($key) ?? []
        ];
    }
}
