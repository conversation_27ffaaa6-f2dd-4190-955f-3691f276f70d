<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use Illuminate\Support\Facades\Http;

class ISmsService
{
    const string SEND_SMS_PATH = '/isms_send_all_id.php';

    public function __construct()
    {
    }

    public function sendSms($phone_number, $message)
    {
        $sanitized_phone_number = preg_replace('/[^0-9]/', '', $phone_number);

        if (!$sanitized_phone_number) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 4009);
        }

        logger(Http::withHeaders([
            'X-PROXY-AUTH' => config('services.isms.proxy_auth_key'),
        ])->get($this->getUrl(self::SEND_SMS_PATH), [
            'un' => config('services.isms.username'),
            'pwd' => config('services.isms.password'),
            'dstno' => $sanitized_phone_number,
            'msg' => $message,
            'type' => config('services.isms.type'),
            'sendid' => config('services.isms.sendid'),
            'agreedterm' => 'YES'
        ])->body());
    }

    public function getUrl($url)
    {
        return config('services.isms.url').$url;
    }
}
