<?php

namespace App\Services;

use App\Models\School;
use App\Repositories\SchoolRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class SchoolService
{
    private SchoolRepository $schoolRepository;

    public function __construct(SchoolRepository $schoolRepository)
    {
        $this->schoolRepository = $schoolRepository;
    }

    public function getAllPaginatedSchools($filters = []): LengthAwarePaginator
    {
        return $this->schoolRepository->getAllPaginated($filters);
    }

    public function getAllSchools($filters = []): Collection
    {
        return $this->schoolRepository->getAll($filters);
    }

    public function createSchool($data): ?School
    {
        return $this->schoolRepository->create($data);
    }

    public function updateSchool(School $school, $data): ?Model
    {
        return $this->schoolRepository->update($school, $data);
    }

    public function deleteSchool(School $school): bool
    {
        if($school->canBeDeleted()) {
            return $this->schoolRepository->delete($school);
        }
        return false;
    }

}
