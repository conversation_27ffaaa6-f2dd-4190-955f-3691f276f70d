<?php

namespace App\Services\Report;

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceStatus;
use App\Enums\Day;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Models\Student;
use App\Repositories\AttendanceRepository;
use App\Repositories\EmployeeRepository;
use App\Repositories\PeriodAttendanceRepository;
use App\Repositories\PeriodLabelRepository;
use App\Repositories\PeriodRepository;
use App\Services\BaseReportService;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class AttendanceReportService extends BaseReportService
{
    public function __construct(
        protected AttendanceRepository $attendanceRepository,
        protected ReportPrintService $reportPrintService,
        protected PeriodAttendanceRepository $periodAttendanceRepository,
        protected PeriodLabelRepository $periodLabelRepository,
        protected PeriodRepository $periodRepository,
        protected EmployeeRepository $employeeRepository,
    ) {
    }

    public function getReportByAttendanceSummaryData(array $filters = []): mixed
    {
        $data = $this->attendanceRepository->getAttendanceSummaryData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $report_data = [
            'data' => $data,
            'date_from' => $filters['date_from'],
            'date_to' => $filters['date_to'],
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    public function getReportByClassAttendanceTakingData(array $filters = []): mixed
    {
        $data = $this->attendanceRepository->getClassAttendanceTakingData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $report_data = [
            'data' => $data,
            'date' => $filters['date'],
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    public function getStudentAttendance(array $filters = []): array
    {
        $filters['attendance_recordable_type'] = Student::class;

        $filters['latest_semester_setting_id'] = $filters['semester_setting_id'];
        unset($filters['semester_setting_id']);

        if (isset($filters['semester_class_id'])) {
            $filters['latest_semester_class_id'] = $filters['semester_class_id'];
            unset($filters['semester_class_id']);
        }

        $filters['includes'] = [
            'leaveApplication',
            'attendanceRecordable' => function (MorphTo $morph_to) use ($filters) {
                $morph_to->morphWith([
                    Student::class => [
                        'classes' => function (HasMany $query) use ($filters) {
                            $query->where([
                                'semester_setting_id' => $filters['latest_semester_setting_id'],
                                'class_type' => $filters['class_type'],
                                'is_latest_class_in_semester' => true,
                            ])
                                ->when(isset($filters['latest_semester_class_id']), function ($query) use ($filters) {
                                    $query->where('semester_class_id', $filters['latest_semester_class_id']);
                                })
                                ->with('semesterClass.classModel');
                        }
                    ]
                ]);
            }
        ];


        if (isset($filters['status'])) {
            if ($filters['status'] === AttendanceStatus::PRESENT->value) {
                $filters['check_in_status'] = AttendanceCheckInStatus::ON_TIME->value;
            } else {
                if ($filters['status'] === AttendanceCheckInStatus::LATE->value) {
                    $filters['check_in_status'] = AttendanceCheckInStatus::LATE->value;
                }
            }
        }

        $attendances = $this->attendanceRepository->getAll($filters);

        $response = [];

        foreach ($attendances as $attendance) {
            $class_id = $attendance->attendanceRecordable->classes[0]->semesterClass->class_id;

            if (!isset($response[$class_id])) {
                $response[$class_id] = [
                    'class_name' => $attendance->attendanceRecordable->classes[0]->semesterClass->classModel->name,
                    'dates' => [],
                ];
            }

            if (!isset($response[$class_id]['dates'][$attendance->date])) {
                $response[$class_id]['dates'][$attendance->date] = [
                    'date' => Carbon::parse($attendance->date)->tz(config('school.timezone'))->format('F j, Y (l)'),
                    'students' => [],
                    'total' => 0,
                    'total_present' => 0,
                    'total_absent' => 0,
                    'total_late' => 0,
                ];
            }

            $attendance_status = $attendance->status;

            switch ($attendance->status) {
                case AttendanceStatus::PRESENT:
                    if ($attendance->check_in_status === AttendanceCheckInStatus::LATE->value) {
                        $attendance_status = AttendanceCheckInStatus::LATE->value;
                        $response[$class_id]['dates'][$attendance->date]['total_late'] += 1;
                        break;
                    }
                    $response[$class_id]['dates'][$attendance->date]['total_present'] += 1;
                    break;
                case AttendanceStatus::ABSENT:
                    $response[$class_id]['dates'][$attendance->date]['total_absent'] += 1;
                    break;
            }

            $response[$class_id]['dates'][$attendance->date]['total'] += 1;

            $response[$class_id]['dates'][$attendance->date]['students'][] = [
                'student_number' => $attendance->attendanceRecordable->student_number,
                'student_name' => $attendance->attendanceRecordable->name,
                'attendance_time_in' => $attendance->check_in_datetime?->tz(config('school.timezone'))->format('H:i:s'),
                'attendance_time_out' => $attendance->check_out_datetime?->tz(config('school.timezone'))->format('H:i:s'),
                'attendance_status' => $attendance_status,
                'attendance_reason' => $attendance->leaveApplication?->reason
            ];
        }

        // Sort students within each date by student_number and reindex dates
        foreach ($response as &$class_data) {
            // Sort dates in ascending order
            ksort($class_data['dates']);

            // Sort students within each date
            foreach ($class_data['dates'] as &$date_data) {
                if (isset($date_data['students'])) {
                    usort($date_data['students'], function ($a, $b) {
                        return $a['student_number'] <=> $b['student_number'];
                    });
                }
            }

            // Reindex dates to 0,1,2,...
            $class_data['dates'] = array_values($class_data['dates']);
        }

        // Reset class IDs to 0,1,2,...
        ksort($response);
        return array_values($response);
    }

    public function getStudentAbsent(array $filters = []): array
    {
        $filters['attendance_recordable_type'] = Student::class;
        $filters['latest_semester_setting_id'] = $filters['semester_setting_id'];
        unset($filters['semester_setting_id']);

        if (isset($filters['semester_class_id'])) {
            $filters['latest_semester_class_id'] = $filters['semester_class_id'];
            unset($filters['semester_class_id']);
        }

        $filters['includes'] = [
            'leaveApplication',
            'attendanceRecordable' => function (MorphTo $morph_to) use ($filters) {
                $morph_to->morphWith([
                    Student::class => [
                        'classes' => function (HasMany $query) use ($filters) {
                            $query->where([
                                'semester_setting_id' => $filters['latest_semester_setting_id'],
                                'class_type' => $filters['class_type'],
                                'is_latest_class_in_semester' => true,
                            ])
                                ->when(isset($filters['latest_semester_class_id']), function ($query) use ($filters) {
                                    $query->where('semester_class_id', $filters['latest_semester_class_id']);
                                })
                                ->with('semesterClass.classModel');
                        }
                    ]
                ]);
            }
        ];

        $response = [];

        if (isset($filters['absent_count']) && $filters['absent_count'] > 0) {
            $filters['status'] = AttendanceStatus::ABSENT->value;
            $attendance_id = $this->attendanceRepository->getAttendanceRecordableIDByAbsentCount($filters);

            if (isset($attendance_id[Student::class])) {
                $filters['attendance_recordable_id'] = $attendance_id[Student::class]->pluck('attendance_recordable_id')->toArray();
            } else {
                return $response;
            }
        }

        $attendances = $this->attendanceRepository->getAll($filters);

        foreach ($attendances as $attendance) {
            if ($attendance->attendanceRecordable == null) {
                continue;
            }

            $student_number = $attendance->attendanceRecordable->student_number;

            if (!isset($response[$student_number])) {
                $response[$student_number] = [
                    'student_name' => $attendance->attendanceRecordable->name,
                    'student_number' => $attendance->attendanceRecordable?->student_number,
                    'class' => $attendance->attendanceRecordable->classes[0]->semesterClass->classModel->name,
                    'present_dates' => [],
                    'absent_dates' => []
                ];
            }

            $attendance_date = [
                'attendance_status' => $attendance->status->value,
                'date' => $attendance->date,
                'attendance_time_in' => $attendance->check_in_datetime?->tz(config('school.timezone'))->toTimeString(),
                'attendance_time_out' => $attendance->check_out_datetime?->tz(config('school.timezone'))->toTimeString(),
            ];

            if ($attendance->status->value == AttendanceStatus::PRESENT->value) {
                $response[$student_number]['present_dates'][] = $attendance_date;
            } else {
                $response[$student_number]['absent_dates'][] = $attendance_date;
            }
        }

        usort($response, function ($a, $b) {
            return $a['class'] <=> $b['class'];
        });
        return $response;
    }

    public function getReportByAttendanceMarkDeductionData(array $filters = []): mixed
    {
        $data = $this->attendanceRepository->getStudentAttendanceMarkDeductionData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $report_data = [
            'data' => $data,
            'date_from' => $filters['date_from'],
            'date_to' => $filters['date_to'],
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    public function getClassAttendanceTakingStatus(array $filters = []): array
    {
        $filters['includes'] = [
            'timeslot.timetable',
            'timeslot.classSubject.subject',
            'timeslot.classSubject.semesterClass.classModel',
        ];

        $filters['order_by'] = 'date';
        $filters['is_attendance_required'] = true;

        $employee = $this->employeeRepository->find($filters['employee_id']);

        $data = [
            'period_groups' => [],
            'dates' => [],
            'date_from' => $filters['date_from'],
            'date_to' => $filters['date_to'],
            'employee' => [
                'number' => $employee->employee_number,
                'name' => $employee->name,
            ]
        ];

        $period_attendances = $this->periodAttendanceRepository->getAll($filters);

        if ($period_attendances->count()) {
            $period_group_ids = $period_attendances
                ->pluck('timeslot.timetable.period_group_id')
                ->unique()
                ->toArray();

            $periods = $this->periodRepository->getAll([
                'period_group_id' => $period_group_ids,
                'day' => Day::MONDAY,
                'order_by' => ['from_time', 'to_time']
            ]);

            $period_labels = $this->periodLabelRepository->getAll([
                'period_group_id' => $period_group_ids,
                'order_by' => 'period'
            ])
                ->groupBy('period_group_id')
                ->map(function ($period_labels) {
                    return $period_labels->keyBy('period');
                });

            $period_groups = [];

            foreach ($periods as $period) {
                $period_groups[$period->period_group_id]['period_group_name'] ??= $period->periodGroup->name;
                $period_groups[$period->period_group_id]['periods'][] = [
                    'period' => $period->period,
                    'period_label_name' => isset($period_labels[$period->period_group_id][$period->period]) ? $period_labels[$period->period_group_id][$period->period]->getTranslation('name', app()->getLocale()) : null,
                    'from_time' => Carbon::createFromFormat('H:i:s', $period->from_time)->format('H:i'),
                    'to_time' => Carbon::createFromFormat('H:i:s', $period->to_time)->format('H:i'),
                ];
            }

            $data['period_groups'] = array_values($period_groups);

            foreach ($period_attendances as $period_attendance) {
                if (!isset($data['dates'][$period_attendance->date])) {
                    $data['dates'][$period_attendance->date] = [
                        'date' => $period_attendance->date,
                        'day' => Carbon::parse($period_attendance->date)->format('l'),
                        'attendances' => []
                    ];

                    foreach ($periods as $period) {
                        $data['dates'][$period_attendance->date]['attendances'][$period->period] = null;
                    }
                }

                $data['dates'][$period_attendance->date]['attendances'][$period_attendance->period] = [
                    'period_attendance_id' => $period_attendance->id,
                    'timeslot_id' => $period_attendance->timeslot_id,
                    'subject_name' => $period_attendance->timeslot->placeholder ?? $period_attendance->timeslot->classSubject->subject->name,
                    'class_name' => $period_attendance->timeslot->timetable->semesterClass->classModel->name,
                    'period' => $period_attendance->period,
                    'time_taken_at' => Carbon::parse($period_attendance->updated_at)->tz(config('school.timezone'))->format('H:i'),
                ];
            }

            foreach ($data['dates'] as &$date) {
                $date['attendances'] = array_values($date['attendances']);
            }

            $data['dates'] = array_values($data['dates']);
        }

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $report_data = [
            'data' => $data,
            'title' => __('attendance.title.teacher_attendance'),
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }
}

