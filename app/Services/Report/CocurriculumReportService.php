<?php

namespace App\Services\Report;

use App\Enums\ContractorDepartment;
use App\Repositories\ContractorRepository;
use Illuminate\Database\Eloquent\Collection;

class CocurriculumReportService
{
    public function __construct(
        protected ContractorRepository $contractorRepository,
    )
    {

    }

    public function getReportByTrainerDetail(array $filters = []): Collection
    {
        $filters['department'] = ContractorDepartment::COCURRICULUM;
        $filters['includes'] = [
            'firstActiveCard',
            'classSubjects.semesterClass' => function ($query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            },
            'classSubjects.semesterClass.classModel'
        ];

        return $this->contractorRepository->getAll($filters);
    }
}
