<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\EnrollmentUser;
use App\Repositories\EnrollmentLoginOtpRepository;
use Illuminate\Database\Eloquent\Model;

class EnrollmentLoginOtpService
{
    const OTP_EXPIRY_SECONDS = 5 * 60;
    const MAX_OTP_REQUESTS = 3;

    private EnrollmentLoginOtpRepository $enrollmentLoginOtpRepository;

    public function __construct(EnrollmentLoginOtpRepository $enrollment_login_otp_repository)
    {
        $this->enrollmentLoginOtpRepository = $enrollment_login_otp_repository;
    }

    public function checkOtpRequestLimit($enrollment_user): bool
    {
        // Delete all invalid OTPs
        $this->deleteAllInvalidOtp($enrollment_user);

        $valid_otp_count = $this->enrollmentLoginOtpRepository->countValidOtpByUser($enrollment_user);

        if ($valid_otp_count >= self::MAX_OTP_REQUESTS) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 4005);
        }

        return true;
    }

    public function createOtpForEnrollmentUser($enrollment_user, $otp): ?Model
    {
        return $this->enrollmentLoginOtpRepository->create([
            'enrollment_user_id' => $enrollment_user->id,
            'otp' => hash('sha256', $otp),
            'expired_at' => now()->addSeconds(self::OTP_EXPIRY_SECONDS)
        ]);
    }

    public function validateAndDeleteOtp($enrollment_user, $otp): bool
    {
        $otp = hash('sha256', $otp);

        $otp = $this->enrollmentLoginOtpRepository->firstValidOtpByUserAndOtp($enrollment_user, $otp);

        if (!$otp) {
            return false;
        }

        $enrollment_user->otps()->delete();

        return true;
    }

    public function deleteAllInvalidOtp(?EnrollmentUser $enrollment_user = null): void
    {
        $this->enrollmentLoginOtpRepository->deleteAllInvalidOtp($enrollment_user);
    }
}
