<?php

namespace App\Services;

use App\Enums\EcommerceOrderPaymentStatus;
use App\Enums\EcommerceOrderStatus;
use App\Enums\MerchantType;
use App\Enums\PushNotificationPage;
use App\Exceptions\RepositoryException;
use App\Helpers\ConfigHelper;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\Billable;
use App\Models\BillingDocument;
use App\Models\EcommerceOrder;
use App\Models\EcommerceOrderItem;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\Student;
use App\Models\User;
use App\Models\WalletTransaction;
use App\Repositories\EcommerceOrderRepository;
use App\Services\Billing\BillingDocumentLineItemService;
use App\Services\Billing\BillingDocumentService;
use App\Services\Billing\PaymentService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class EcommerceOrderService
{
    private EcommerceOrderRepository $orderRepository;
    private StudentService $studentService;
    private Billable $billTo;
    private float $amount_before_tax;
    private float $amount_after_tax;
    private float $tax_amount;
    private array $tax_breakdown;
    private EcommerceOrderStatus|string $status;
    private EcommerceOrderPaymentStatus|string $payment_status;
    private EcommerceOrder $order;
    private User $user;
    private WalletTransaction $walletTransaction;
    private string $merchantType;

    public function __construct(
        EcommerceOrderRepository $order_repository,
        StudentService $student_service
    ) {
        $this->orderRepository = $order_repository;
        $this->studentService = $student_service;
    }

    public function getAllPaginatedEcommerceOrders($filters = []): LengthAwarePaginator
    {
        return $this->orderRepository->getAllPaginated($filters);
    }

    public function createEcommerceOrder(): ?Model
    {
        $order_number = $this->generateReferenceNumber();
        $recipient_student_class_id = $this->getCurrentSemesterStudentClassId($this->billTo->getBillToType(), $this->billTo->getBillToId());

        $data = [
            'merchant_type' => $this->getMerchantType(),
            'order_reference_number' => $order_number,
            'buyer_userable_type' => $this->billTo->getBillToType(),
            'buyer_userable_id' => $this->billTo->getBillToId(),
            'recipient_student_class_id' => $recipient_student_class_id,
            'cancel_before_datetime' => $this->getAllowedCancellationDate(),        // cancellation is 1 day or cut-off date whichever earlier
            'status' => $this->status,
            'payment_status' => $this->payment_status,
            'currency_code' => config('school.currency_code'),
            'amount_before_tax' => $this->amount_before_tax ?? 0,
            'amount_after_tax' => $this->amount_after_tax ?? 0,
            'tax_amount' => $this->tax_amount ?? 0,
            'tax_breakdown' => $this->tax_breakdown ?? [],
            'user_id' => $this->user->id,
        ];

        $this->order = $this->orderRepository->create($data);
        return $this->order;
    }

    public function generateReferenceNumber()
    {
        $service = app()->make(DocumentRunningNumberService::class);

        return $service
            ->setDocumentType(EcommerceOrder::class)
            ->setYear('2024')
            ->setCustomPadding(5)
            ->addCustomComponent('ORD')
            ->addPresetComponent(\App\Services\DocumentRunningNumberService::PLACEHOLDER_YEAR)
            ->addPresetComponent(\App\Services\DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
            ->generate();
    }

    public function getCurrentSemesterStudentClassId(string $userable_type, int $userable_id)
    {
        if ($userable_type != Student::class) {
            return null;
        }

        return $this->studentService->getCurrentSemesterStudentPrimaryClassId($userable_id);
    }

    public function getAllowedCancellationDate(): Carbon
    {
        // for non-canteen just return now
        if (!isset($this->merchantType) || $this->merchantType !== MerchantType::CANTEEN->value) {
            return now('UTC');
        }

        // either use now() + 1 day or cutoff date, whichever earlier
        $default = now(config('school.timezone'))->addDay()->tz('UTC');
        $cutoff_date = ConfigHelper::getCanteenCutoffDateUtc();

        if ($cutoff_date->lte($default)) {
            return $cutoff_date;
        } else {
            return $default;
        }

    }

    public function recalculateAmountsFromItems()
    {

        $this->amount_before_tax = 0;
        $this->amount_after_tax = 0;
        $this->tax_amount = 0;
        $this->tax_breakdown = [];

        foreach ($this->order->items as $item) {
            $this->amount_before_tax = bcadd($this->amount_before_tax, $item->amount_before_tax, 2);

            if (!isset($this->tax_breakdown[$item->tax_id])) {
                $this->tax_breakdown[$item->tax_id] = [
                    'tax_percentage' => $item->tax_percentage,
                    'taxable_amount' => 0,
                    'tax_amount' => 0,
                ];
            }

            $this->tax_breakdown[$item->tax_id]['taxable_amount'] = bcadd($this->tax_breakdown[$item->tax_id]['taxable_amount'], $item->amount_before_tax, 2);
        }

        foreach ($this->tax_breakdown as $tax_id => $tax_data) {
            $this->tax_breakdown[$tax_id]['tax_amount'] = round($this->tax_breakdown[$tax_id]['taxable_amount'] * $this->tax_breakdown[$tax_id]['tax_percentage'] / 100, 2);
            $this->tax_amount = bcadd($this->tax_amount, $this->tax_breakdown[$tax_id]['tax_amount'], 2);
        }

        $this->amount_after_tax = bcadd($this->amount_before_tax, $this->tax_amount, 2);
        return $this;

    }

    public function createPaidInvoice()
    {

        $line_items = collect([]);
        $this->order->items->load(['tax']);
        $tax = $this->order->items->first()->tax;

        // create line items
        foreach ($this->order->items as $item) {

            $line_item = app()->make(BillingDocumentLineItemService::class)
                ->setGlAccountCode(GlAccount::CODE_ECOMMERCE)
                ->setDescription($this->_formatLineItemDescription($item))
                ->setUnitPrice($item->product_unit_price)
                ->setAmountBeforeTax($item->amount_before_tax)
                ->setQuantity($item->quantity)
                ->setCurrency($item->currency_code)
                ->setBillableItem($item)
                ->make();

            $line_items->push($line_item);

        }

        $invoice_service = app()->make(BillingDocumentService::class);

        $billing_document = $invoice_service->init()
            ->setType(BillingDocument::TYPE_INVOICE)
            ->setSubType(BillingDocument::SUB_TYPE_ECOMMERCE)
            ->setStatus(BillingDocument::STATUS_CONFIRMED)
            ->setDefaultValuesForECommerceOrders()
            ->setCurrency($this->order->currency_code)
            ->setBillToParty($this->billTo)
            ->setLineItems($line_items)
            ->calculateAmountBeforeTax()
            ->applyTax($tax)
            ->calculatePaymentDueDate()
            ->generateReferenceNumber()
            ->create()
            ->getBillingDocument();

        // create payment
        $payment_service = app()->make(PaymentService::class);

        $payment_service
            ->setBillingDocument($billing_document)
            ->setWalletTransactionAndPopulateData($this->walletTransaction)
            ->create()
            ->triggerPostPaymentProcesses();

        return $this;
    }

    public function setAmountBeforeTax(int $amount_before_tax): static
    {
        $this->amount_before_tax = $amount_before_tax;
        return $this;
    }

    public function setStatus(EcommerceOrderStatus|string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function cancel(): static
    {

        DB::transaction(function () {
            $service = app()->make(WalletTransactionService::class);
            $wallet_transaction = $service->getWalletTransactionByReferenceNo($this->order->order_reference_number);

            $this->setStatus(EcommerceOrderStatus::CANCELED)
                ->setPaymentStatus(EcommerceOrderPaymentStatus::REFUNDED)
                ->updateEcommerceOrder();

            $service
                ->setWalletTransactable($this->order)
                ->setWalletTransaction($wallet_transaction)
                ->refundWalletTransaction([
                    'remark' => 'E-Commerce Cancellation: ' . $this->order->order_reference_number,
                ]);

            // create credit note for the refund/cancellation
            $billing_document_service = app()->make(BillingDocumentService::class);

            $line_items = collect();
            $this->order->items->load(['tax']);
            $tax = $this->order->items->first()->tax;

            foreach ($this->order->items as $item) {

                $line_item = app()->make(BillingDocumentLineItemService::class)
                    ->setGlAccountCode(GlAccount::CODE_ECOMMERCE)
                    ->setDescription('[CANCEL] ' . $this->_formatLineItemDescription($item))
                    ->setUnitPrice($item->product_unit_price)
                    ->setAmountBeforeTax($item->amount_before_tax)
                    ->setQuantity($item->quantity)
                    ->setCurrency($item->currency_code)
                    ->setBillableItem($item)
                    ->make();

                $line_items->push($line_item);

            }

            $billing_document_service->init()
                ->setType(BillingDocument::TYPE_CREDIT_NOTE)
                ->setSubType(BillingDocument::SUB_TYPE_ECOMMERCE)
                ->setStatus(BillingDocument::STATUS_CONFIRMED)
                ->setDefaultValuesForECommerceOrders()
                ->setCurrency($this->order->currency_code)
                ->setBillToParty($this->order->buyerUserable)
                ->setLineItems($line_items)
                ->calculateAmountBeforeTax()
                ->applyTax($tax)
                ->calculatePaymentDueDate()
                ->generateReferenceNumber()
                ->create()
                ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, now());
        });

        return $this;

    }

    public function updateEcommerceOrder(): ?Model
    {
        $data = [];

        if (isset($this->status)) {
            $data['status'] = $this->status;
        }
        if (isset($this->payment_status)) {
            $data['payment_status'] = $this->payment_status;
        }
        if (isset($this->amount_before_tax)) {
            $data['amount_before_tax'] = $this->amount_before_tax;
        }
        if (isset($this->amount_after_tax)) {
            $data['amount_after_tax'] = $this->amount_after_tax;
        }
        if (isset($this->tax_amount)) {
            $data['tax_amount'] = $this->tax_amount;
        }
        if (isset($this->tax_breakdown)) {
            $data['tax_breakdown'] = $this->tax_breakdown;
        }

        $updated_order = $this->orderRepository->update($this->order, $data);

        if ($data['status'] == EcommerceOrderStatus::COMPLETED->value) {
            $this->sendCompletedNotification();
        }

        return $updated_order;
    }

    public function setPaymentStatus(EcommerceOrderPaymentStatus|string $payment_status): static
    {
        $this->payment_status = $payment_status;

        return $this;
    }

    public function validateOrderCancellation(): static
    {
        if (now()->isAfter($this->order->cancel_before_datetime)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ECOMMERCE_ERROR, 13006);
        }

        if ($this->order->status !== EcommerceOrderStatus::PROCESSING) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ECOMMERCE_ERROR, 13007);
        }

        if ($this->order->payment_status !== EcommerceOrderPaymentStatus::PAID) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ECOMMERCE_ERROR, 13007);
        }

        if (!$this->isAuthorizeToCancel($this->billTo->getBillToType(), $this->billTo->getBillToId())) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ECOMMERCE_ERROR, 13008);
        }

        return $this;
    }

    public function isAuthorizeToCancel(string $userable_type, int $userable_id): bool
    {
        if ($userable_type === Employee::class) {
            return true;
        }

        if ($userable_type === $this->order->buyer_userable_type
            && $userable_id === $this->order->buyer_userable_id) {
            return true;
        }

        return false;
    }

    public function getBillTo(): Billable
    {
        return $this->billTo;
    }

    public function setBillTo(Billable $billTo): EcommerceOrderService
    {
        $this->billTo = $billTo;
        return $this;
    }

    public function setAmountAfterTax(int $amount_after_tax): static
    {
        $this->amount_after_tax = $amount_after_tax;
        return $this;
    }

    public function getOrder(): EcommerceOrder
    {
        return $this->order;
    }

    public function setOrder(EcommerceOrder $order): EcommerceOrderService
    {
        $this->order = $order;
        return $this;
    }

    public function getTaxAmount(): float
    {
        return $this->tax_amount;
    }

    public function setTaxAmount(float $tax_amount): EcommerceOrderService
    {
        $this->tax_amount = $tax_amount;
        return $this;
    }

    public function getTaxBreakdown(): array
    {
        return $this->tax_breakdown;
    }

    public function setTaxBreakdown(array $tax_breakdown): EcommerceOrderService
    {
        $this->tax_breakdown = $tax_breakdown;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): EcommerceOrderService
    {
        $this->user = $user;
        return $this;
    }

    public function getWalletTransaction(): WalletTransaction
    {
        return $this->walletTransaction;
    }

    public function setWalletTransaction(WalletTransaction $walletTransaction): EcommerceOrderService
    {
        $this->walletTransaction = $walletTransaction;
        return $this;
    }

    public function setMerchantType(string $merchant_type): EcommerceOrderService
    {
        $this->merchantType = $merchant_type;
        return $this;
    }

    public function getMerchantType(): string
    {
        return $this->merchantType;
    }

    /**
     * @throws RepositoryException
     */
    public function markAsComplete(): static
    {
        $this->orderRepository->update($this->getOrder(), ['status' => EcommerceOrderStatus::COMPLETED]);

        return $this;
    }

    public function sendCompletedNotification(): void
    {
        $order = $this->getOrder();

        app()->make(AdHocNotificationService::class)
            ->setData(['id' => $order->id, 'page' => PushNotificationPage::ECOMMERCE_ORDER->value])
            ->setUserable($order->buyerUserable)
            ->setTitle("Order #{$order->order_reference_number} Confirmed.")
            ->setMessage("Order #{$order->order_reference_number} is confirmed.")
            ->determineRecipients()
            ->send();
    }

    protected function _formatLineItemDescription(EcommerceOrderItem $item): string
    {
        if ($item->product_delivery_date !== null) {
            $prefix = $item->product_delivery_date->toDateString() . ': ';
        } else {
            $prefix = '';
        }

        $description = $prefix . $item->product_name . "\n";
        $description .= "SELLER: " . $item->merchant->name;

        return $description;
    }
}
