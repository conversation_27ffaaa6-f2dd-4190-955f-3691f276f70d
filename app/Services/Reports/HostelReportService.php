<?php

namespace App\Services\Reports;

use App\Enums\Gender;
use App\Enums\HostelInOutType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\ConfigHelper;
use App\Models\HostelBedByYearView;
use App\Repositories\EmployeeRepository;
use App\Repositories\HostelBedAssignmentRepository;
use App\Repositories\HostelInOutRecordRepository;
use App\Repositories\HostelRewardPunishmentRecordRepository;
use App\Repositories\HostelRoomBedRepository;
use App\Repositories\SemesterSettingRepository;
use App\Repositories\StudentRepository;
use App\Services\BaseReportService;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;

class HostelReportService extends BaseReportService
{
    public function __construct(
        protected StudentRepository $studentRepository,
        protected EmployeeRepository $employeeRepository,
        protected HostelRoomBedRepository $hostelRoomBedRepository,
        protected HostelBedAssignmentRepository $hostelBedAssignmentRepository,
        protected HostelInOutRecordRepository $hostelInOutRecordRepository,
        protected ReportPrintService $reportPrintService,
        protected HostelRewardPunishmentRecordRepository $hostelRewardPunishmentRecordRepository
    ) {
    }

    /**
     * @throws \Exception
     */
    public function getHostelBoarderListReportData(array $filters): array
    {
        $data = $this->studentRepository->getHostelBoarderListData($filters)
            ->sortBy(function ($student) {
                $active_bed = $student->activeHostelBedAssignments->first();

                return [
                    $active_bed?->bed?->hostelRoom?->hostelBlock->getTranslation('name', app()->getLocale()),
                    $active_bed?->bed?->hostelRoom->name,
                    $active_bed?->bed->name,
                ];
            });

        $report_data = [
            'data' => $data,
            'locales' => ConfigHelper::getAvailableLocales()
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     * @throws \Exception
     */
    public function getAvailableBedReportData(array $filters = []): mixed
    {
        $data = $this->hostelRoomBedRepository->getAvailableBedReportData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $report_data = [
            'data' => $data,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     * @throws \Exception
     */
    public function getCheckoutRecordReportData(array $filters = []): mixed
    {
        $data = $this->hostelBedAssignmentRepository->getCheckoutRecord($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $report_data = [
            'data' => $data,
            'year' => $filters['year'],
        ];


        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     * get info : bed, room, student_no, student_name, nric, address, phone_number,
     * guardian_name, class_name, grade_name, checked_in_date
     *
     * @throws \Exception
     *
     */
    public function getBoardersListInfoReportData(array $filters = []): mixed
    {
        $filters['all_guardians'] = false; // only get 1 guardian

        $data = $this->studentRepository->getHostelBoardersData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $semester_setting = (new SemesterSettingRepository())->find($filters['semester_setting_id']);

        $report_data = [
            'data' => $data,
            'semester_setting' => $semester_setting,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     * get info : bed, room, student_no, student_name, class_name, grade_name, guardian_type,
     * guardian_name, guardian_nric, guardian_phone_number
     *
     * @throws \Exception
     *
     */
    public function getBoardersContactInfoReportData(array $filters = []): mixed
    {
        $filters['all_guardians'] = true; // get all guardians

        $data = $this->studentRepository->getHostelBoardersData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $semester_setting = (new SemesterSettingRepository())->find($filters['semester_setting_id']);

        $report_data = [
            'data' => $data,
            'semester_setting' => $semester_setting,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     * get info : bed, room, student_no, student_name, class_name, grade_name, date_of_birth
     *
     * @throws \Exception
     *
     */
    public function getBoardersDateOfBirthReportData(array $filters = []): mixed
    {
        $filters['no_guardians'] = true;

        $data = $this->studentRepository->getHostelBoardersData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $semester_setting = (new SemesterSettingRepository())->find($filters['semester_setting_id']);

        $report_data = [
            'data' => $data,
            'semester_setting' => $semester_setting,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     * filter by gender, always download current stayback students
     * get info : room, bed, student_no, student_name, class_name, grade_name
     *
     * @throws \Exception
     *
     */
    public function getBoardersStaybackReportData(array $filters = []): mixed
    {
        $data = $this->studentRepository->getHostelBoardersStayBackData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $today = now()->toDateString();
        $title = Gender::getLabelFromString($filters['gender']) . " Hostel Boarders ({$today})";

        $report_data = [
            'data' => $data,
            'title' => $title,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     *
     * get info : Block, Room, Employee Name (zh), Employee Name (En), gender, phone_number, address, report_date
     *
     * @throws \Exception
     *
     */
    public function getEmployeeLodgingReportData(array $filters = []): mixed
    {
        $data = $this->employeeRepository->getEmployeeLodging($filters);

        $title = 'Employee Lodging';

        $report_data = [
            'data' => $data,
            'title' => $title,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     *
     * @throws \Exception
     *
     */
    public function getGoHomeOrOutReportData(array $filters = []): mixed
    {
        $data = $this->hostelInOutRecordRepository->getHostelBoardersGoHomeOrOutData($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $title = $this->getBoardersGoHomeOrOutingTitle($filters);

        $report_data = [
            'data' => $data,
            'title' => $title,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    /**
     *
     * @throws \Exception
     *
     */
    public function getChangeRoomReportData(array $filters = []): mixed
    {
        $data = $this->hostelBedAssignmentRepository->getChangeRoomRecord($filters);

        if (!isset($filters['export_type'])) {
            return $data;
        }

        $title = $filters['year'] . ' Change Room Name List';

        $report_data = [
            'data' => $data,
            'title' => $title,
        ];

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    private function getBoardersGoHomeOrOutingTitle($filters): string
    {
        $title = '';

        $yet_return = $filters['yet_return'] ?? false;
        $leave_school = $filters['leave_school'] ?? false;
        $returned = $filters['returned'] ?? false;
        $in_out_type = $filters['in_out_type'] ?? null;
        $date_from = $filters['date_from'] ?? '';
        $date_to = $filters['date_to'] ?? '';

        if ($yet_return && $in_out_type == HostelInOutType::HOME->value) {
            $title = 'Hostel Boarders Go Home Yet Return';
        } elseif ($yet_return && $in_out_type == HostelInOutType::OUTING->value) {
            $title = 'Hostel Boarders Go Outing Yet Return';
        } elseif ($leave_school && $in_out_type == HostelInOutType::HOME->value) {
            $title = 'Hostel Boarders Leave For Home';
        } elseif ($leave_school && $in_out_type == HostelInOutType::OUTING->value) {
            $title = 'Hostel Boarders Leave For Outing';
        } elseif ($returned && $in_out_type == HostelInOutType::HOME->value) {
            $title = 'Hostel Boarders Return From Home';
        } elseif ($returned && $in_out_type == HostelInOutType::OUTING->value) {
            $title = 'Hostel Boarders Return From Outing';
        }

        $title = $title != '' ? "{$title} ({$date_from} - {$date_to})" : '';

        return $title;
    }

    public function getHostelRewardPunishmentReportByBlock(array $filters = []): mixed
    {
        HostelBedByYearView::refreshViewTable();

        $data = $this->hostelRewardPunishmentRecordRepository->getHostelRewardPunishmentReportDataByYear($filters);

        $data->transform(function ($hostel_reward_punishment) {
            return [
                'date' => $hostel_reward_punishment->date->toDateString(),
                'student_number' => $hostel_reward_punishment->student->student_number,
                'student_name' => $hostel_reward_punishment->student->getTranslations('name'),
                'block_id' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->hostelRoom->hostelBlock->id,
                'block_code' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->hostelRoom->hostelBlock->code,
                'room' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->hostelRoom->name,
                'merit_demerit_item' => $hostel_reward_punishment->hostelRewardPunishmentSetting->hostelMeritDemeritSetting->name,
                'reward_punishment_item' => $hostel_reward_punishment->hostelRewardPunishmentSetting->name,
                'person_in_charge' => $hostel_reward_punishment->personInCharge->name,
                'remark' => $hostel_reward_punishment->remark
            ];
        });

        $data = $data->sortBy([
            ['block_code', 'asc'],
            ['room', 'asc'],
        ]);
        
        $report_data = [
            'data' => $data,
            'year' => date("Y", strtotime($filters['date_from'])),
            'locales' => ConfigHelper::getAvailableLocales()
        ];
        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    public function getHostelRewardPunishmentReportByStudent(array $filters = []): mixed
    {
        HostelBedByYearView::refreshViewTable();

        $data = $this->hostelRewardPunishmentRecordRepository->getHostelRewardPunishmentReportDataByYear($filters);
        $hostel_reward_punishment_balance = ConfigHelper::get('HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS');

        $data->transform(function ($hostel_reward_punishment) use (&$hostel_reward_punishment_balance) {
            return [
                'room' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->hostelRoom->name,
                'bed' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->name,
                'date' => $hostel_reward_punishment->date->toDateString(),
                'merit_demerit_item' => $hostel_reward_punishment->hostelRewardPunishmentSetting->hostelMeritDemeritSetting->name,
                'reward_punishment_item' => $hostel_reward_punishment->hostelRewardPunishmentSetting->name,
                'person_in_charge' => $hostel_reward_punishment->personInCharge->name,
                'deduction_points' => $hostel_reward_punishment->hostelRewardPunishmentSetting->points ?? 0,
                'balance' => $hostel_reward_punishment_balance += $hostel_reward_punishment->hostelRewardPunishmentSetting->points,
                'remark' => $hostel_reward_punishment->remark,
                'student_number' => $hostel_reward_punishment->student->student_number,
                'student_name' => $hostel_reward_punishment->student->getTranslations('name'),
            ];
        });

        $data = $data->sortBy([
            ['room', 'asc'],
            ['bed', 'asc'],
        ]);

        $student = $this->studentRepository->getQuery(['id' => $filters['student_id']])->first();
        $photo = $student->getProfilePicture();

        $report_data = [
            'data' => $data,
            'photo' => $photo,
            'name_en' => $student->getTranslation('name', 'en'),
            'name_zh' => $student->getTranslation('name', 'zh'),
            'room_bed' => !$data->isEmpty() ? $data[0]['room'] . "/" . $data[0]["bed"] : "N/A",
            'student_number' => $student->student_number,
            'total_deducted_points' => sprintf("%+d", $hostel_reward_punishment_balance - ConfigHelper::get('HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS')),
            'total_balance_points' => $hostel_reward_punishment_balance,
            'year' => date("Y", strtotime($filters['date_from'])),
            'locales' => ConfigHelper::getAvailableLocales()
        ];
        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    public function getHostelRewardPunishmentReportByRoom(array $filters = []): mixed
    {
        HostelBedByYearView::refreshViewTable();

        $data = $this->hostelRewardPunishmentRecordRepository->getHostelRewardPunishmentReportDataByYear($filters);
        $hostel_reward_punishment_balance = ConfigHelper::get('HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS');

        $data->transform(function ($hostel_reward_punishment) {
            $data = [
                'room' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->hostelRoom->name,
                'room_id' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->hostelRoom->id,
                'bed' => $hostel_reward_punishment->student->hostelBedByYear[0]->bed->name,
                'date' => $hostel_reward_punishment->date->toDateString(),
                'merit_demerit_item' => $hostel_reward_punishment->hostelRewardPunishmentSetting->hostelMeritDemeritSetting->name,
                'reward_punishment_item' => $hostel_reward_punishment->hostelRewardPunishmentSetting->name,
                'person_in_charge' => $hostel_reward_punishment->personInCharge->name,
                'deduction_points' => $hostel_reward_punishment->hostelRewardPunishmentSetting->points ?? 0,
                'remark' => $hostel_reward_punishment->remark,
                'student_number' => $hostel_reward_punishment->student->student_number,
                'student_name' => $hostel_reward_punishment->student->getTranslations('name'),
            ];
            return $data;
        });

        $data = $data->sortBy([
            ['room', 'desc'],
            ['bed', 'desc'],
            ['date', 'asc']
        ])
            ->groupBy('student_number');

        $data->transform(function ($hostel_reward_punishment_group) use ($hostel_reward_punishment_balance) {
            $balance = $hostel_reward_punishment_balance;

            $table_data = $hostel_reward_punishment_group->transform(function ($hostel_reward_punishment) use (&$balance) {
                $row_data = array_merge(
                    $hostel_reward_punishment,
                    ['balance' => $balance += $hostel_reward_punishment['deduction_points']]
                );
                return $row_data;
            });

            $data = [
                'student_number' => $hostel_reward_punishment_group[0]['student_number'],
                'name_en' => $hostel_reward_punishment_group[0]['student_name']['en'],
                'name_zh' => $hostel_reward_punishment_group[0]['student_name']['zh'],
                'room' => $hostel_reward_punishment_group[0]['room'],
                'bed' => $hostel_reward_punishment_group[0]['bed'],
                'total_balance_points' => $balance,
                'total_deduction_points' =>
                    sprintf("%+d", $balance - ConfigHelper::get('HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS')),
                'table_data' => $table_data
            ];
            return $data;
        });

        $report_data = [
            'data' => $data,
            'year' => date("Y", strtotime($filters['date_from'])),
            'locales' => ConfigHelper::getAvailableLocales()
        ];
        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }
}
