<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class ProcessLogging extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'log:process {--once}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reads pending log items from Redis and sends them to Logstash';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expire_at = Carbon::now()->addMinutes(1);

        while (Carbon::now()->isBefore($expire_at)) {

            $processed = false;

            $start = round(microtime(true) * 1000);
            $end = round(microtime(true) * 1000);

            try {
                $processed = $this->process();
            } catch (\Exception $e) {

            } finally {
                $end = round(microtime(true) * 1000);
            }

            $this->info("Time taken: " . ($end - $start) . "ms");

            if ($this->option('once')) {
                break;
            }

            if ($processed) {
                usleep(5000);
            } else {
                $this->info("Nothing processed");
                sleep(1);
            }

        }

        sleep(1);

        return true;
    }

    public function process()
    {

        $key = 'pending-logs';

        $payload_raw = Redis::connection('logging')->lpop($key);

        if ($payload_raw !== null) {

            $payload = json_decode($payload_raw, true);

            if ($payload['driver'] === 'logstash') {

                $url = 'http://' . config('school.logstash.host') . ':' . config('school.logstash.port');

                try {

                    Http::timeout(8)
                        ->withOptions([
                            'synchronous' => false,
                        ])
                        ->post($url, $payload);

                } catch (\Exception $e) {
                    Log::error($e->getMessage());
                    Log::error($e->getTraceAsString());
                }

            }

            $this->info("Log sent");
            return true;

        } else {
            // sleep for a while if no more things to process
            return false;
        }

    }
}
