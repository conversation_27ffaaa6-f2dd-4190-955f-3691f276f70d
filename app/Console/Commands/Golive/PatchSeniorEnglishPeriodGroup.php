<?php

namespace App\Console\Commands\Golive;

use App\Enums\ClassType;
use App\Models\PeriodGroup;
use App\Models\SemesterSetting;
use App\Models\StudentTimetable;
use App\Models\Timetable;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchSeniorEnglishPeriodGroup extends Command
{

    protected $isActual = false;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:english-period-groups {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run patching patch senior english classes period group';

    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;

        if ( !$this->isActual ) {
            $this->info("nothing to run..");
            return;
        }

        DB::transaction(function() {

            // 1. copy Senior primary class period label to new Senior English
            $senior_period_group = PeriodGroup::with(['periods', 'periodLabels'])->where(\DB::raw('name->>\'en\''), 'Senior Period Group')->firstOrFail();

            $english_period_group = $senior_period_group->replicate();
            $english_period_group->name = [
                'en' => 'English Class Period Group (Senior)',
                'zh' => '英语班组合(高中)'
            ];
            $english_period_group->save();

            $this->info("New english period group created " . $english_period_group->name);

            $new_periods = [];

            foreach ($senior_period_group->periods as $period) {
                $new_period = $period->replicate();
                $new_period->period_group_id = $english_period_group->id;
                $new_period->save();

                $new_periods[$new_period->day->value][$new_period->period] = $new_period;
            }
            foreach ($senior_period_group->periodLabels as $period_label) {
                $new_period_label = $period_label->replicate();
                $new_period_label->period_group_id = $english_period_group->id;
                $new_period_label->save();
            }

            $latest_semester_setting = SemesterSetting::where('is_current_semester', true)->first();

            $timetables = Timetable::query()
                ->with(['semesterClass.classModel.grade', 'timeslots.period'])
                ->whereHas('semesterClass.classModel.grade', function($q){
                    $q->whereIn(DB::raw('name->>\'en\''), ['SENIOR 1', 'SENIOR 2', 'SENIOR 3']);
                })
                ->whereHas('semesterClass.classModel', function($q){
                    $q->where('type', ClassType::ENGLISH->value);
                })
                ->whereHas('semesterClass', function($q) use ($latest_semester_setting){
                    $q->where('semester_setting_id', $latest_semester_setting->id);
                })
                ->get();

            foreach ($timetables as $timetable) {

                $this->info("Updating timetable {$timetable->name} period_group to {$english_period_group->name}");

                // 2. foreach timetable for english class, update period_group_id
                $timetable->period_group_id = $english_period_group->id;
                $timetable->save();

                foreach ( $timetable->timeslots as $timeslot ) {
                    $new_period = $new_periods[$timeslot->day->value][$timeslot->period->period] ?? null;

                    if ( $new_period === null ){
                        $this->error("New period not found for {$timeslot->day->value} " . $timeslot->attendance_from.'-'.$timeslot->attendance_to . ' Period ' . $timeslot->period->period);
                        die();
                    }

                    // 3. foreach timeslots in timetable, update period_id and attendance period to follow new period
                    $timeslot->period_id = $new_period->id;
                    $timeslot->attendance_from = $new_period->from_time;
                    $timeslot->attendance_to = $new_period->to_time;
                    $timeslot->save();
                }

            }
        });

        // 4. to manually change period group name for Existing english one to (Junior)

        StudentTimetable::refreshViewTable();
        $this->info("DONE!");
    }
}
