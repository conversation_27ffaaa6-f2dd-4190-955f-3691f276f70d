<?php

namespace App\Console\Commands\Exam;

use App\Repositories\StudentReportCardRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;

class PublishStudentReportCard extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'publish-student-report-card:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish all report cards related to result posting header where publish date is equal to current date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("Starting PublishStudentReportCard script..");
        $repository = app()->make(StudentReportCardRepository::class);
        $repository->changeReportCardVisibilityByPublishDate(Carbon::now()->tz(config('school.timezone'))->toDateString(), true);
        $this->info("End PublishStudentReportCard script..");
    }
}
