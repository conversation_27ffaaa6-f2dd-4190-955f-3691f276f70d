<?php

namespace App\Console\Commands;

use App\Helpers\LogHelper;
use App\Services\Billing\UnpaidItemAssignmentService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Psr\Log\LogLevel;

class ProcessUnpaidItemAssignment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'unpaid-item-assignment:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process unpaid item assignment with status NEW';

    public function __construct(
        protected UnpaidItemAssignmentService $unpaidItemAssignmentService,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        LogHelper::write('Process unpaid item assignments START...', null, LogLevel::INFO, LogHelper::LOG_NAME_UNPAID_ITEM_ASSIGNMENTS);

        $unpaid_item_assignments = $this->unpaidItemAssignmentService->getApplicableAssignments();

        LogHelper::write('Processing ' . count($unpaid_item_assignments) . ' unpaid item assignments', null, LogLevel::INFO, LogHelper::LOG_NAME_UNPAID_ITEM_ASSIGNMENTS);

        foreach ($unpaid_item_assignments as $unpaid_item_assignment) {

            try {
                LogHelper::write('Processing Unpaid Item Assignments by id : ' . $unpaid_item_assignment->id, null, LogLevel::INFO, LogHelper::LOG_NAME_UNPAID_ITEM_ASSIGNMENTS);

                $this->unpaidItemAssignmentService
                    ->setUnpaidItemAssignment($unpaid_item_assignment)
                    ->applyToStudents();

            } catch (\Throwable $th) {
                LogHelper::write('Processing Unpaid Item Assignments by id : ' . $unpaid_item_assignment->id . ' FAILED ' . $th->getMessage() . "\n" . $th->getTraceAsString(), null, LogLevel::ERROR, LogHelper::LOG_NAME_UNPAID_ITEM_ASSIGNMENTS);
            }
        }

        LogHelper::write('Process unpaid item assignments END...', null, LogLevel::INFO, LogHelper::LOG_NAME_UNPAID_ITEM_ASSIGNMENTS);
    }
}
