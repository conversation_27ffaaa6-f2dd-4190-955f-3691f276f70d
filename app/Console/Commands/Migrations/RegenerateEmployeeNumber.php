<?php

namespace App\Console\Commands\Migrations;

use App\Enums\EmployeeStatus;
use App\Models\Employee;
use App\Models\RunningNumber;
use App\Services\DocumentRunningNumberService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RegenerateEmployeeNumber extends Command
{
    protected $isActual = false;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'regenerate:employee-numbers {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate employee numbers again';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;

        $employees = Employee::where('status', '!=', EmployeeStatus::RESIGNED)
            ->whereNotIn('name->en', [
                'admin',
                'PBS',
                '簿记',
                '章川源/潘新融',
                '林为佳/潘新融',
                '何博爱/潘新融',
                'SYSTEM'
            ])
            ->whereNull('old_employee_number')
            ->orderBy('employment_start_date', 'ASC')
            ->get();

        $this->info(count($employees) . ' employees found.');

        DB::transaction(function () use ($employees) {

            // first, we set running number to 1 first
            $existing_running_number = RunningNumber::where('document_type', Employee::class)->first();
            $this->info("Current running number: " . json_encode($existing_running_number));

            if ($this->isActual) {
                $existing_running_number->delete();
            }

            foreach ($employees as $employee) {

                if ($this->isActual) {

                    $new_number = app()->make(DocumentRunningNumberService::class)
                        ->setDocumentType(Employee::class)
                        ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
                        ->generate();

                    $this->info("New running number for {$employee->name} is {$new_number}, replacing old number {$employee->employee_number}.");

                    $employee->old_employee_number = $employee->employee_number;
                    $employee->employee_number = $new_number;
                    $employee->save();

                } else {
                    $this->info("New running number for {$employee->name} is XXXX, replacing old number {$employee->employee_number}.");
                }

            }

        });


    }


}
