<?php

namespace App\Console\Commands\Migrations;

use App\Models\BillingDocument;
use App\Models\RunningNumber;
use Illuminate\Console\Command;

class PatchRunningNumberForBillingDocuments extends Command
{
    protected $isActual = false;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:running-number {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch running number table for billing document to include document type';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;

        $running_numbers = RunningNumber::where('document_type', BillingDocument::class)
            ->whereNull('identifier1')
            ->get();

        foreach ( $running_numbers as $rn ) {

            $identifier1 = BillingDocument::DOCUMENT_NUMBER_PREFIX[BillingDocument::TYPE_INVOICE];
            $this->info("Updating running number set identifier1 to {$identifier1} for " . json_encode($rn));

            if ( $this->isActual ) {
                $rn->identifier1 = $identifier1;
                $rn->save();
            }

        }

    }


}
