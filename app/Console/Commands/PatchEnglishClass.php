<?php

namespace App\Console\Commands;

use App\Enums\ClassType;
use App\Models\ClassSubject;
use App\Models\Subject;
use App\Models\Timeslot;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchEnglishClass extends Command
{
    protected $isActual = false;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:english-class {--actual}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Patch golive phase 2 english class';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->isActual = (bool) $this->option('actual') ?? false;

        // updating timeslot with the correct english classes
        $timetables = Timetable::with(['semesterClass.classModel.grade', 'semesterClass.semesterSetting'])
            ->whereHas('semesterClass.semesterSetting', function ($q) {
                $q->where('is_current_semester', true);
            })
            ->whereHas('semesterClass.classModel', function ($q) {
                $q->where('type', ClassType::ENGLISH->value);
            })
            ->where('is_active', true)
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->semesterClass->classModel->code => $item];
            });

        $english_subject = Subject::where('code', '03')->first();

        $class_subjects = ClassSubject::with([
            'semesterClass.classModel',
            'teachers'
        ])->where('subject_id', $english_subject->id)
            ->whereHas('semesterClass.classModel', function ($q) {
                $q->where('type', ClassType::ENGLISH->value);
            })
            ->whereHas('semesterClass.semesterSetting', function ($q) {
                $q->where('is_current_semester', true);
            })
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->semesterClass->classModel->code => $item];
            });

        $updated_timeslot_count = 0;
        $updated_timetable_count = 0;

        DB::transaction(function () use (&$v1_data, &$timetables, &$class_subjects, &$updated_timeslot_count, &$updated_timetable_count) {
            // Clear all english timeslot's class_subject_id and timeslot_teacher

            foreach ($timetables as $timetable) {
                $this->info('Clearing timeslot for timetable ' . $timetable->name . ', Timetable id ' . $timetable->id);

                $to_be_deleted_timeslot = TimeslotTeacher::whereIn('timeslot_id', $timetable->timeslots->pluck('id')->toArray());
                $this->info('Number of timeslot to be deleted: ' . $to_be_deleted_timeslot->count());

                if ($this->isActual) {
                    $to_be_deleted_timeslot->delete();

                    $timetable->timeslots()->update([
                        'class_subject_id' => null,
                    ]);
                }
            }
            $english_timetable_timeslots = $this->getEnglishTimeslot();

            // Patch english class
            foreach ($timetables as $timetable) {
                $english_timeslot = $english_timetable_timeslots[$timetable->semesterClass->classModel->grade->name];
                foreach ($english_timeslot as $day => $period) {
                    $timeslots = Timeslot::where('timetable_id', $timetable->id)
                        ->where('day', $day)
                        ->whereHas('period', function ($q) use ($period) {
                            $q->whereIn('period', $period);
                        })
                        ->get();

                    foreach ($timeslots as $timeslot) {
                        $class_subject_id = $class_subjects[$timetable->semesterClass->classModel->code]->id;

                        if ($this->isActual) {
                            $timeslot->update([
                                'class_subject_id' => $class_subject_id,
                            ]);

                            $employee_ids = [];
                            foreach ($class_subjects[$timetable->semesterClass->classModel->code]->teachers as $teacher) {
                                $employee_ids[$teacher->id]['type'] = $teacher->pivot->type->value;
                            }

                            $timeslot->teachers()->sync($employee_ids);
                        }

                        $updated_timeslot_count++;
                    }
                }
                $updated_timetable_count++;
            }

            $this->info("Number of timeslots updated: {$updated_timeslot_count}");
            $this->info("Number of timetables updated: {$updated_timetable_count}");
            $this->info("Expected number of timeslots updated: " . count($timetables) * 7);

            // Patch primary class timetable to have english class placeholder
            $primary_timetables = Timetable::with(['semesterClass.classModel.grade', 'semesterClass.semesterSetting', 'timeslots.period'])
                ->whereHas('semesterClass.semesterSetting', function ($q) {
                    $q->where('is_current_semester', true);
                })
                ->whereHas('semesterClass.classModel', function ($q) {
                    $q->where('type', ClassType::PRIMARY->value);
                })
                ->where('is_active', true)
                ->get();

            foreach ($primary_timetables as $primary_timetable) {
                $this->info('Updating timetable ' . $primary_timetable->name . ', Timetable id ' . $primary_timetable->id);
                $english_timeslot = $english_timetable_timeslots[$primary_timetable->semesterClass->classModel->grade->name];
                foreach ($primary_timetable->timeslots as $timeslot) {
                    if ($timeslot->placeholder == 'English Class') {
                        $this->info('Removing timeslot id ' . $timeslot->id . ' English Class placeholder, ' . $timeslot->day->value . ' period ' . $timeslot->period->period);

                        if ($this->isActual) {
                            $timeslot->update([
                                'placeholder' => null,
                            ]);
                        }
                    }


                    if (!isset($english_timeslot[$timeslot->day->value]) || !in_array($timeslot->period->period, $english_timeslot[$timeslot->day->value])) {
                        continue;
                    }

                    $this->info('Adding timeslot id ' . $timeslot->id . ' English Class placeholder, ' . $timeslot->day->value . ' period ' . $timeslot->period->period);
                    if ($this->isActual) {
                        $timeslot->update([
                            'placeholder' => 'English Class',
                        ]);
                    }
                }
            }
        });
    }

    private function getEnglishTimeslot(): array
    {
        return [
            'JUNIOR 1' => [
                'MONDAY' => [
                    11, 12 // 第7节，第8节
                ],
                'TUESDAY' => [
                    13, 14 // 第9节，第10节
                ],
                'THURSDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'FRIDAY' => [
                    8 // 第5节
                ]
            ],
            'JUNIOR 2' => [
                'MONDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'TUESDAY' => [
                    5, 6 // 第3节，第4节
                ],
                'WEDNESDAY' => [
                    11, 12 // 第7节，第8节
                ],
                'THURSDAY' => [
                    11 // 第7节
                ],
            ],
            'JUNIOR 3' => [
                'MONDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'TUESDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'WEDNESDAY' => [
                    5, 6 // 第3节，第4节
                ],
                'THURSDAY' => [
                    12 // 第8节
                ],
            ],
            'SENIOR 1' => [
                'MONDAY' => [
                    5, 6 // 第3节，第4节
                ],
                'TUESDAY' => [
                    12 // 第8节
                ],
                'WEDNESDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'FRIDAY' => [
                    9, 10 // 第6节，第7节
                ],
            ],
            'SENIOR 2' => [
                'MONDAY' => [
                    13, 14 // 第9节，第10节
                ],
                'WEDNESDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'THURSDAY' => [
                    8, 9 // 第5节，第6节
                ],
                'FRIDAY' => [
                    12 // 第8节
                ],
            ],
            'SENIOR 3' => [
                'TUESDAY' => [
                    2, 3 // 第1节，第2节
                ],
                'WEDNESDAY' => [
                    13, 14 // 第9节，第10节
                ],
                'THURSDAY' => [
                    5 // 第3节
                ],
                'FRIDAY' => [
                    5, 6 // 第3节，第4节
                ],
            ],

        ];
    }
}
