<?php

namespace App\Console\Commands\Test;

use App\Enums\AuditAction;
use App\Enums\Module;
use App\Helpers\LogHelper;
use App\Models\User;
use App\Services\ISmsService;
use Illuminate\Console\Command;
use Psr\Log\LogLevel;

class TestSms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:sms {phone}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test sms message';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phone = $this->argument('phone');
        $this->info("Sending test sms message to " . $phone);

        app()->make(IsmsService::class)->sendSms($phone, 'Test sms message ' . now()->toIso8601String());
    }
}
