<?php

return [
    'date' => 'Date',
    'day' => 'Day',
    'total_sales' => 'Total Sales',
    'grand_total' => 'Grand Total',
    'print_date' => 'Print Date',

    'room' => 'Room',
    'bed' => 'Bed',
    'block' => 'Block',
    'room_number' => 'Room No.',
    'bed_number' => 'Bed No.',
    'capacity' => 'Capacity',
    'occupied' => 'Occupied',
    'available' => 'Available',

    'student_class' => 'Student Class',
    'student_no' => 'Student No.',
    'student_number' => 'Student Number',
    'student_name' => 'Student Name',
    'class' => 'Class',
    'grade' => 'Grade',
    'guardian_type' => 'Guardian Type',
    'name' => 'Name',
    'nric' => 'NRIC',
    'phone_number' => 'Phone Number',
    'seat_number' => 'Seat No.',

    'person_in_charge' => 'PIC',
    'merit_demerit' => 'Merit/Demerit',
    'item' => 'Item',
    'deduction_points' => 'Deduction Points',
    'deducted_points' => 'Deducted Points',
    'balance_points' => 'Balance Points',

    'number' => 'No.',
    'date_of_birth' => 'Date Of Birth',

    'ic_no' => 'I/C No.',
    'address' => 'Address',

    'phone_no' => 'Phone No.',
    'guardian' => 'Guardian',
    'check_in_date' => 'Check-In Date',
    'check_out_date' => 'Check-Out Date',
    'check_in' => 'Check-In',
    'check_out' => 'Check-Out',
    'remarks' => 'Remarks',

    'others' => 'Others',

    'receipt_no' => 'Receipt No.',
    'payment_method' => 'Payment Method',
    'payment_date' => 'Payment Date',
    'sn' => 'S/N',
    'item_name' => 'Item Name',
    'unit_price' => 'Unit Price',
    'quantity' => 'Quantity',
    'total_quantity' => 'Total Quantity',
    'total' => 'Total',
    'total_amount' => 'Total Amount',
    'net_amount' => 'Net Amount',

    'order_no' => 'Order No.',
    'buyer_name' => 'Buyer Name',
    'buyer_no' => 'Buyer No.',
    'paid_amount' => 'Paid Amount',
    'ordered_at' => 'Ordered At',
    'order_for' => 'Order For',

    'unit' => 'Unit',
    'total_collection' => 'Total Collection',

    'items' => 'Items',
    'total_item_sold' => 'Total Item Sold',
    'total_receipt' => 'Total Receipt',
    'avg_per_item' => 'Avg $ Per Item',
    'avg_per_receipt' => 'Avg $ Per Receipt',

    'member_no' => 'Member No.',
    'due_date' => 'Due Date',
    'return_date' => 'Return Date',
    'balance' => 'Balance',

    'guardian_teacher' => 'Guardian Teacher',

    'homeroom_teacher' => 'Homeroom Teacher',
    'photo' => 'Photo',
    'gender' => 'Gender',
    'guardian_name' => 'Guardian Name',
    'guardian_phone_no' => 'Guardian Phone No.',

    'semester' => 'Semester',
    'student_school_from' => 'Student School From',
    'student_address' => 'Student Address',
    'student_dob' => 'Student DOB',
    'nativeplace' => 'Nativeplace',
    'religion' => 'Religion',

    'student_father_information' => 'STUDENT (FATHER) INFORMATION',
    'father_name' => 'Father Name',
    'father_religion' => 'Father Religion',
    'father_nationality' => 'Father Nationality',
    'father_phone_no' => 'Father Phone No.',
    'father_email' => 'Father Email',
    'father_work' => 'Father Work',
    'father_education' => 'Father Education',

    'student_mother_information' => 'STUDENT (MOTHER) INFORMATION',
    'mother_name' => 'Mother Name',
    'mother_religion' => 'Mother Religion',
    'mother_nationality' => 'Mother Nationality',
    'mother_phone_no' => 'Mother Phone No.',
    'mother_email' => 'Mother Email',
    'mother_work' => 'Mother Work',
    'mother_education' => 'Mother Education',

    'student_information' => 'STUDENT INFORMATION',
    'student_guardian_information' => 'STUDENT (GUARDIAN) INFORMATION',
    'guardian_religion' => 'Guardian Religion',
    'guardian_nationality' => 'Guardian Nationality',
    'guardian_email' => 'Guardian Email',
    'guardian_work' => 'Guardian Work',
    'guardian_education' => 'Guardian Education',

    'trainer_detail' => 'Trainer Detail',
    'cocurriculum' => 'Cocurriculum',
    'department' => 'Department',
    'card_number' => 'Card Number',
    'card_type' => 'Card Type',

    'class_code' => 'Class Code',
    'class_guardian' => 'Class Guardian',

    'trainer_no' => 'Trainer No.',

    'total_count' => 'Total Count',
    'chinese_name' => 'Chinese Name',
    'chinese' => 'Chinese',

    'contractor_number' => 'Contractor Number',
    'contractor_name' => 'Contractor Name',

    'employee_number' => 'Employee Number',
    'employee_name' => 'Employee Name',

    'full_number' => 'Number',

    'english_name' => 'English Name',
    'english' => 'English',
    'no' => 'No',
    'society' => 'Society',
    'male' => 'Male',
    'female' => 'Female',

    'no_data_available' => 'No data available',
    'amount' => 'Amount',
    'month' => 'Month',
    'hostel' => 'Hostel',
    'non_hostel' => 'Non Hostel',

    'reference_no' => 'Reference No.',
    'type' => 'Type',
    'status' => 'Status',
    'transaction_amount' => 'Transaction Amount',
    'balance_before' => 'Balance Before',
    'balance_after' => 'Balance After',
    'description' => 'Description',
    'created_at' => 'Created At',

    'user_name' => 'User Name',
    'user_type' => 'User Type',
    'user_no' => 'User No.',

    'outstanding_balance_report_title' => 'Outstanding Balance',
    'fee' => 'Fee',
    'amount_with_currency' => 'Amount (:currency)',
    'teacher_guardian' => 'Teacher Guardian',
    'no_of_debtors' => 'No of Debtor(s)',
    'consecutive_months_in_arrears' => 'Consecutive Months in Arrears',
    'total_arrears' => 'Total Arrears',

    'invoice_date' => 'Invoice Date',
    'invoice_no' => 'Invoice No.',
    'bill_to_name' => 'Bill To Name',
    'bill_to_reference_no' => 'Bill To Reference No.',
    'currency' => 'Currency',
    'payment' => 'Payment',

    'attend' => 'Attend',
    'late' => 'Late',
    'absent' => 'Absent',
    'total_student' => 'Total Student',
    'student' => 'Student',

    'reason' => 'Reason',

    'one_day_ago' => 'One Day Ago',
    'seven_days_ago' => '7 Days Ago',
    'thirty_days_ago' => '30 Days Ago',
    'exam' => 'Exam',
    'without_exam' => 'Without Exam',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'shortlisted' => 'Shortlisted',
    'foreigner' => 'Foreigner',
    'local' => 'Local',
    'paid' => 'Paid',
    'unpaid' => 'Unpaid',

    'total_school' => 'All School',
    'hostel_student' => 'Hostel',
    'non_hostel_student' => 'Non Hostel',
    'gender_or_grade' => 'Gender/Grade',
    'student_analysis' => 'Student Analysis',
    'attend_only' => 'Attend Only',
    'all_contractors' => 'All Contractors',
    'all_trainers' => 'All Trainers',
    'Trainer Attendance (Cocurriculum)' => 'Trainer Attendance (Cocurriculum)',
    'attendance' => 'Attendance',
    'present' => 'Present',
    'category' => 'Category',
    'period' => 'Period',
    'deduct_average_point' => 'Deduct Average Point',
    'signature' => 'Signature',
    'bank' => 'Bank',

    'average' => 'Average',
    'teacher' => 'Teacher',

    'gross_total' => 'Gross Total',
    'gross_average' => 'Gross Average',
    'mark_subtracted' => 'Mark Subtracted',
    'mark_added' => 'Mark Added',
    'net_average' => 'Net Average',
    'grade_exam' => 'Grade',
    'position_in_class' => 'Position In Class',
    'position_in_standard' => 'Position In Standard',
    'conduct' => 'Conduct',
    'examination_result_sem1_report_title' => ':semester_year Semester 1 Examination Result',
    'examination_result_sem2_report_title' => ':semester_year Semester 2 Examination Result',
    'examination_result_final_report_title' => ':semester_year Year Average Report',
];
