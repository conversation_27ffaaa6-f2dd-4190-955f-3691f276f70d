<?php

return [
    'date' => '日期',
    'day' => '星期',
    'total_sales' => '总销售额',
    'grand_total' => '总计',
    'print_date' => '打印日期',

    'room' => '房间',
    'bed' => '床位',
    'block' => 'Block',
    'room_number' => '房间号',
    'bed_number' => '床号',
    'capacity' => '容量',
    'occupied' => '已占用',
    'available' => '可用',

    'student_class' => '学生班级',
    'student_no' => '学号',
    'student_number' => '学生编号',
    'student_name' => '学生姓名',
    'class' => '班级',
    'grade' => '年级',
    'guardian_type' => '监护人类型',
    'name' => '姓名',
    'nric' => '身份证号码',
    'phone_number' => '电话号码',
    'seat_number' => '座位号',

    'person_in_charge' => 'PIC',
    'merit_demerit' => 'Merit/Demerit',
    'item' => 'Item',
    'deduction_points' => 'Deduction Points',
    'deducted_points' => 'Deducted Points',
    'balance_points' => 'Balance Points',

    'number' => '编号',
    'date_of_birth' => '出生日期',

    'ic_no' => '身份证号',
    'address' => '地址',

    'phone_no' => '电话号码',
    'guardian' => '监护人',
    'check_in_date' => '入住日期',
    'check_out_date' => '退宿日期',
    'check_in' => '签到',
    'check_out' => '签离',
    'remarks' => '备注',

    'others' => '其他',

    'receipt_no' => '收据单号',
    'payment_method' => '付款方式',
    'payment_date' => '付款日期',
    'sn' => '序',
    'item_name' => '商品名称',
    'unit_price' => '单价',
    'quantity' => '数量',
    'total_quantity' => '总数',
    'total' => '共计',
    'total_amount' => '共计',
    'net_amount' => '净额',

    'order_no' => '订单号',
    'buyer_name' => '买家姓名',
    'buyer_no' => '买家编号',
    'paid_amount' => '付款总额',
    'ordered_at' => '下单日期',

    'order_for' => '订单日期',
    'unit' => '个',
    'total_collection' => '收款总额',

    'items' => '商品',

    'total_item_sold' => '总计销售量',
    'total_receipt' => '总订单量',
    'avg_per_item' => '平均商品金额',
    'avg_per_receipt' => '平均订单金额',

    'member_no' => '会员编号',

    'due_date' => '截止日期',
    'return_date' => '归还日期',
    'balance' => '余额',

    'guardian_teacher' => 'Guardian Teacher',

    'homeroom_teacher' => '班主任',
    'photo' => '照片',
    'gender' => '性别',
    'guardian_name' => '监护人姓名',
    'guardian_phone_no' => '监护人电话',

    'semester' => '学期',
    'student_school_from' => '之前学校',
    'student_address' => '学生地址',
    'student_dob' => '学生生日',
    'nativeplace' => '出生地点',
    'religion' => '宗教',

    'student_father_information' => '学生父亲资料',
    'father_name' => '父亲名字',
    'father_religion' => '父亲宗教',
    'father_nationality' => '父亲国籍',
    'father_phone_no' => '父亲电话号',
    'father_email' => '父亲邮件',
    'father_work' => '父亲职业',
    'father_education' => '父亲教育水平',

    'student_mother_information' => '学生母亲资料',
    'mother_name' => '母亲名字',
    'mother_religion' => '母亲宗教',
    'mother_nationality' => '母亲国籍',
    'mother_phone_no' => '母亲电话号',
    'mother_email' => '母亲邮件',
    'mother_work' => '母亲职业',
    'mother_education' => '母亲教育水平',

    'student_guardian_information' => '学生监护人资料',
    'student_information' => '学生资料',
    'guardian_religion' => '监护人宗教',
    'guardian_nationality' => '监护人国籍',
    'guardian_email' => '监护人邮件',
    'guardian_work' => '监护人职业',
    'guardian_education' => '监护人教育水平',

    'trainer_detail' => '教练资料',
    'cocurriculum' => '课外活动',
    'department' => '部门',
    'card_number' => '卡号',
    'card_type' => '卡类',

    'class_code' => '班级编号',
    'class_guardian' => '班主任',

    'total_count' => '总数',
    'chinese_name' => '中文名',
    'chinese' => '中文',
    'trainer_no' => '教练编号',

    'contractor_number' => '承包商编号',
    'contractor_name' => '承包商名称',

    'employee_number' => '员工编号',
    'employee_name' => '员工名称',

    'full_number' => '号码',

    'english_name' => '英文名',
    'english' => '英文',
    'no' => '序',
    'society' => '学会',
    'male' => '男',
    'female' => '女',
    'amount' => '金额',
    'month' => '月份',
    'hostel' => '宿舍',

    'reference_no' => '参考号',
    'type' => '类型',
    'status' => '状况',
    'transaction_amount' => '交易金额',
    'balance_before' => '之前余额',
    'balance_after' => '之后余额',
    'description' => '描述',
    'created_at' => '交易时间',

    'user_name' => '用户名',
    'user_type' => '用户类别',
    'user_no' => '用户编号',

    'outstanding_balance_report_title' => '欠款记录',
    'fee' => '收费',
    'amount_with_currency' => '金额 (:currency)',
    'teacher_guardian' => '班导师',
    'no_of_debtors' => '欠款人数',
    'consecutive_months_in_arrears' => '连续欠款月份',
    'total_arrears' => '欠款总额',

    'invoice_date' => '发票日期',
    'invoice_no' => '发票号',
    'bill_to_name' => '账单当事人',
    'bill_to_reference_no' => '账单当事人号码',
    'currency' => '货币',
    'payment' => '付款',

    'attend' => '出席',
    'late' => '迟到',
    'absent' => '缺席',
    'total_student' => '学生总数',
    'student' => '学生',

    'reason' => '理由',

    'total_school' => '全校',
    'hostel_student' => '宿舍生',
    'non_hostel_student' => '走读生',
    'gender_or_grade' => '性别/年级',
    'student_analysis' => '学生人数统计',
    'attend_only' => '限于出席者',
    'all_contractors' => '全部承包商',
    'all_trainers' => '全部教练',
    'Trainer Attendance (Cocurriculum)' => '教练出缺席记录(联课处)',
    'attendance' => '出缺席',
    'present' => '出席',
    'category' => '类别',
    'period' => '期间',
    'deduct_average_point' => '扣除平均分数',
    'signature' => '签名',
    'bank' => '银行',

    'average' => '平均分数',
    'teacher' => '导师',

    'gross_total' => '总分',
    'gross_average' => '总平均',
    'mark_subtracted' => '扣分',
    'mark_added' => '校外学艺得分',
    'net_average' => '实得平均',
    'grade_exam' => '成绩等级',
    'position_in_class' => '班级名次',
    'position_in_standard' => '全级名次',
    'conduct' => '操行',
    'examination_result_sem1_report_title' => ':semester_year年第1学期成绩总表',
    'examination_result_sem2_report_title' => ':semester_year年第2学期成绩总表',
    'examination_result_final_report_title' => ':semester_year年份全年成绩总表',
];
