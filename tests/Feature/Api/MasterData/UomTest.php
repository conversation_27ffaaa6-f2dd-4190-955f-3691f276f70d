<?php

use App\Models\Uom;
use App\Models\User;
use App\Services\UomService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mo<PERSON>y\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(Uom::class)->getTable();
    $this->routeNamePrefix = 'master-data.uoms';
});

test('index', function () {
    $uoms = Uom::factory(3)->state(new Sequence(
        [
            'name->en' => 'Pack',
            'code' => 'PACK',
            'is_active' => true,
        ],
        [
            'name->en' => 'Piece',
            'code' => 'PIECE',
            'is_active' => true,
        ],
        [
            'name->en' => 'Dozen',
            'code' => 'DOZEN',
            'is_active' => false,
        ]
    ))->create();

    //Filter by name = Dozen
    $filters = [
        'name' => 'Dozen',
        'code' => 'DOZEN',
        'is_active' => false,
    ];

    $this->mock(UomService::class, function (MockInterface $mock) use ($filters, $uoms) {
        $mock->shouldReceive('getAllPaginatedUoms')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([$uoms[2]], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $uoms[2]->id,
            'code' => $uoms[2]->code,
            'name' => $uoms[2]->name,
            'is_active' => $uoms[2]->is_active,
            'translations' => $uoms[2]->translations
        ]);
});

test('index - getAll', function () {
    $uoms = Uom::factory(3)->state(new Sequence(
        [
            'name->en' => 'Pack',
            'code' => 'PACK',
            'is_active' => true,
        ],
        [
            'name->en' => 'Piece',
            'code' => 'PIECE',
            'is_active' => true,
        ],
        [
            'name->en' => 'Dozen',
            'code' => 'DOZEN',
            'is_active' => false,
        ]
    ))->create();

    //Filter by name = Dozen
    $filters = [
        'name' => 'Dozen',
        'code' => 'DOZEN',
        'is_active' => false,
        'per_page' => -1,
        'page' => 1,
    ];

    $this->mock(UomService::class, function (MockInterface $mock) use ($filters, $uoms) {
        $uom = Uom::where('code', 'DOZEN')->get();
        $mock->shouldReceive('getAllUoms')
            ->once()
            ->andReturn($uom);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $uoms[2]->id,
            'code' => $uoms[2]->code,
            'name' => $uoms[2]->name,
            'is_active' => $uoms[2]->is_active,
            'translations' => $uoms[2]->translations
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(UomService::class, function (MockInterface $mock) {
        $uom = Uom::factory()->create();

        $mock->shouldReceive('getAllPaginatedUoms')
            ->once()
            ->andReturn(new LengthAwarePaginator([$uom], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(UomService::class, function (MockInterface $mock) {
        $uoms = Uom::factory(2)->create();

        $mock->shouldReceive('getAllUoms')->once()->andReturn($uoms);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});
