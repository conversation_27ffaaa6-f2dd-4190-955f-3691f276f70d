<?php

use App\Models\RewardPunishmentCategory;
use App\Models\User;
use App\Services\RewardPunishmentCategoryService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create();

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(RewardPunishmentCategory::class)->getTable();

    $this->routeNamePrefix = 'master-data.reward-punishment-categories';
});

test('index', function () {
    $reward_punishment_categories = RewardPunishmentCategory::factory(3)->state(new Sequence(
        [
            'name->en' => 'Category 1',
            'name->zh' => 'zh Category 1',
        ],
        [
            'name->en' => 'Category 2',
            'name->zh' => 'zh Category 2',
        ],
        [
            'name->en' => 'Group 3',
            'name->zh' => 'zh Group 3',
        ]
    ))->create();

    //Filter by name = Category 1
    $payload = [
        'name' => 'Category 1'
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Category 1')
        );

    //Filter by partial name = Category
    $payload = [
        'name' => 'Category'
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Category 1'),
            fn($data) => $data->toHaveKey('name', 'Category 2'),
        );

    //Filter non-existing name = Non Exist
    $payload = [
        'name' => 'Non Exist'
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Category 1'),
            fn($data) => $data->toHaveKey('name', 'Category 2'),
            fn($data) => $data->toHaveKey('name', 'Group 3'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Group 3'),
            fn($data) => $data->toHaveKey('name', 'Category 2'),
            fn($data) => $data->toHaveKey('name', 'Category 1'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[0]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[2]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[0]->id),
        );

    //Test pattern
    $payload = [
        'name' => 'Category 1'
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toMatchArray([
            'id' => $reward_punishment_categories[0]->id,
            'name' => $reward_punishment_categories[0]->name,
            'translations' => $reward_punishment_categories[0]->translations
        ]);
});

test('index - getAll', function () {
    $reward_punishment_categories = RewardPunishmentCategory::factory(3)->state(new Sequence(
        [
            'name->en' => 'Category 1',
            'name->zh' => 'zh Category 1',
        ],
        [
            'name->en' => 'Category 2',
            'name->zh' => 'zh Category 2',
        ],
        [
            'name->en' => 'Group 3',
            'name->zh' => 'zh Group 3',
        ]
    ))->create();

    //Filter by name = Category 1
    $payload = [
        'name' => 'Category 1',
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Category 1')
        );

    //Filter by partial name = Category
    $payload = [
        'name' => 'Category',
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Category 1'),
            fn($data) => $data->toHaveKey('name', 'Category 2'),
        );

    //Filter non-existing name = Non Exist
    $payload = [
        'name' => 'Non Exist',
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Category 1'),
            fn($data) => $data->toHaveKey('name', 'Category 2'),
            fn($data) => $data->toHaveKey('name', 'Group 3'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Group 3'),
            fn($data) => $data->toHaveKey('name', 'Category 2'),
            fn($data) => $data->toHaveKey('name', 'Category 1'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[0]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[2]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[1]->id),
            fn($data) => $data->toHaveKey('id', $reward_punishment_categories[0]->id),
        );

    //Test pattern
    $payload = [
        'name' => 'Category 1',
        'per_page'=> -1
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toMatchArray([
            'id' => $reward_punishment_categories[0]->id,
            'name' => $reward_punishment_categories[0]->name,
            'translations' => $reward_punishment_categories[0]->translations
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(RewardPunishmentCategoryService::class, function (MockInterface $mock) {
        $rewardPunishmentCategory = RewardPunishmentCategory::factory()->create();

        $mock->shouldReceive('getAllPaginatedRewardPunishmentCategories')
            ->once()
            ->andReturn(new LengthAwarePaginator([$rewardPunishmentCategory], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(RewardPunishmentCategoryService::class, function (MockInterface $mock) {
        $rewardPunishmentcategories = RewardPunishmentCategory::factory(2)->create();

        $mock->shouldReceive('getAllRewardPunishmentCategories')->once()->andReturn($rewardPunishmentcategories);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});


test('show', function () {
    $reward_punishment_category = RewardPunishmentCategory::factory()->create([
        'name->en' => 'Category 1',
        'name->zh' => 'zh Category 1'
    ]);

    //test with id exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['reward_punishment_category' => $reward_punishment_category->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $reward_punishment_category->id,
            'name' => $reward_punishment_category->name,
            'translations' => $reward_punishment_category->translations
        ]);

    //test with id not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['reward_punishment_category' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake('zh_CN')->name,
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => RewardPunishmentCategory::first()->id,
            'name' => $payload['name']['en'],
            'translations' => [
                'name' => [
                    'en' => $payload['name']['en'],
                    'zh' => $payload['name']['zh'],
                ]
            ]
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);
});

test('update', function () {
    $reward_punishment_category = RewardPunishmentCategory::factory()->create();

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'name' => [
            'en' => 'Category 1',
            'zh' => 'zh Category 1',
        ],
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['reward_punishment_category' => $reward_punishment_category->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $reward_punishment_category->id,
            'name' => $payload['name']['en'],
            'translations' => [
                'name' => [
                    'en' => $payload['name']['en'],
                    'zh' => $payload['name']['zh'],
                ]
            ]
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);

    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 3',
        ],
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['reward_punishment_category' => 9999]), $payload)->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    // Assert nothing updated
    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => 'Category 1',
        'name->zh' => 'zh Category 1',
    ]);
});

test('destroy', function () {
    $reward_punishment_category = RewardPunishmentCategory::factory()->create();
    $other_reward_punishment_categories = RewardPunishmentCategory::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['reward_punishment_category' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['reward_punishment_category' => $reward_punishment_category->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $reward_punishment_category->id]);

    foreach ($other_reward_punishment_categories as $other_category) {
        $this->assertDatabaseHas($this->table, ['id' => $other_category->id]);
    }
});
