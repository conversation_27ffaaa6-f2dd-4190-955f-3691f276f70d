<?php

use App\Models\SemesterYearSetting;
use App\Models\User;
use App\Services\SemesterYearSettingService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\SemesterYearSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([InternationalizationSeeder::class, PermissionSeeder::class]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en',
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);
    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(SemesterYearSetting::class)->getTable();
    $this->routeNamePrefix = 'master-data.semester-year-settings.';
});

test('index', function () {
    $first_semester_year_setting = SemesterYearSetting::create(['year' => 2024]);
    $second_semester_year_setting = SemesterYearSetting::create(['year' => 2025]);
    $third_semester_year_setting = SemesterYearSetting::create(['year' => 2026]);


    // Filter by year = 2024
    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', ['year' => 2024])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0]['id'])->toEqual($first_semester_year_setting->id);

    // Filter by year = 2025
    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', ['year' => 2025])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0]['id'])->toEqual($second_semester_year_setting->id);

    // Filter by non-existing year
    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', ['year' => 2027])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toBeEmpty();

    // Sort by id asc
    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', [
            'order_by' => ['id' => 'asc'],
        ])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0]['id'])->toEqual($first_semester_year_setting->id)
        ->and($response['data'][1]['id'])->toEqual($second_semester_year_setting->id)
        ->and($response['data'][2]['id'])->toEqual($third_semester_year_setting->id);

    // Sort by id desc
    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', [
            'order_by' => ['id' => 'desc'],
        ])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0]['id'])->toEqual($third_semester_year_setting->id)
        ->and($response['data'][1]['id'])->toEqual($second_semester_year_setting->id)
        ->and($response['data'][2]['id'])->toEqual($first_semester_year_setting->id);

    // Test data format
    $response = $this->getJson(
        route($this->routeNamePrefix . 'index')
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toEqual([
            'id' => $first_semester_year_setting->id,
            'year' => $first_semester_year_setting->year,
        ]);
});

test('index - getAll', function () {
    $first_semester_year_setting = SemesterYearSetting::create(['year' => 2024]);
    $second_semester_year_setting = SemesterYearSetting::create(['year' => 2025]);
    $third_semester_year_setting = SemesterYearSetting::create(['year' => 2026]);

    // Filter by year = 2024
    $payload = [
        'year' => 2024,
        'per_page' => -1,
        'page' => 1,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', $payload)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0]['id'])->toEqual($first_semester_year_setting->id);

    // Sort by id desc

    $payload = [
        'order_by' => ['id' => 'desc'],
        'per_page' => -1,
        'page' => 1,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0]['id'])->toEqual($third_semester_year_setting->id)
        ->and($response['data'][1]['id'])->toEqual($second_semester_year_setting->id)
        ->and($response['data'][2]['id'])->toEqual($first_semester_year_setting->id);
});

test('index - getPaginated when per_page is set', function () {
    $this->mock(SemesterYearSettingService::class, function (MockInterface $mock) {
        $semesterYearSetting = SemesterYearSetting::factory()->create();

        $mock->shouldReceive('getAllPaginatedSemesterYearSettings')
            ->once()
            ->andReturn(new LengthAwarePaginator([$semesterYearSetting], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix.'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index - getAll when per_page = -1 ', function () {

    $this->mock(SemesterYearSettingService::class, function (MockInterface $mock) {
        $semesterYearSettings = SemesterYearSetting::factory(2)->create(new Sequence(
            [
                'year' => 2024
            ],
            [
                'year' => 2025
            ]
        ));
        $mock->shouldReceive('getAllSemesterYearSettings')->once()->andReturn($semesterYearSettings);
    });

    $response = $this->getJson(route($this->routeNamePrefix.'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('show', function () {
    $master_semester_year_setting = SemesterYearSetting::create(['year' => 2024]);

    //test with id exist
    $response = $this->getJson(
        route($this->routeNamePrefix . 'show', [
            'master_semester_year_setting' => $master_semester_year_setting->id,
        ])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $master_semester_year_setting->id,
            'year' => $master_semester_year_setting->year,
        ]);

    //test with id not exist
    $response = $this->getJson(
        route($this->routeNamePrefix . 'show', [
            'master_semester_year_setting' => 9999,
        ])
    )->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('create without same year pass', function () {
    $this->assertDatabaseCount(($this->table), 0);

    $payload = [
        'year' => 2024,
    ];

    $this->assertDatabaseCount($this->table, 0);

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'year' => $payload['year'],
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'year' => $payload['year'],
    ]);
});

test('create with same year fail', function () {
    $master_semester_year_setting = SemesterYearSetting::create(['year' => 2024]);

    // Store success
    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'year' => $master_semester_year_setting->year,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['data'])->toBeEmpty()
        ->and($response['error'])->toEqual([
            'year' => ['The year has already been taken.']
        ]);
});

test('update without same year pass', function () {
    $first_semester_year_setting = SemesterYearSetting::create(['year' => 2024]);

    // first update with id exist
    $this->assertDatabaseCount(($this->table), 1);
    $first_update = [
        'year' => 2025, // Update year to 2025
    ];

    $response = $this->putJson(
        route($this->routeNamePrefix . 'update', [
            'master_semester_year_setting' => $first_semester_year_setting->id,
        ]),
        $first_update
    )->json();

    // Assert response status
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $first_semester_year_setting->id,
            'year' => $first_update['year'],
        ]);

    // Assert database state after update
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'year' => $first_update['year'],
    ]);

    // Update with id not exist
    $payload = [
        'year' => 2025,
    ];

    $response = $this->json(
        'PUT',
        route($this->routeNamePrefix . 'update', [
            'master_semester_year_setting' => 9999,
        ]),
        $payload
    )->json();

    // Assert response status for not found
    expect($response)->toHaveModelResourceNotFoundResponse();

    // Assert database state not affected
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $first_semester_year_setting->id,
    ]);
});

test('update with same year fail', function () {
    $first_semester_year_payload = [
        'year' => 2024,
    ];

    $second_semester_year_payload = [
        'year' => 2025,
    ];

    $first_semester_year = SemesterYearSetting::create($first_semester_year_payload);
    $second_semester_year = SemesterYearSetting::create($second_semester_year_payload);

    $this->assertDatabaseCount(($this->table), 2);

    //update the first semester with the second semester's payload
    $response = $this->putJson(
        route($this->routeNamePrefix . 'update', [
            'master_semester_year_setting' => $first_semester_year->id,
        ]),
        $second_semester_year_payload
    )->json();

    // Assert response status
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toEqual([
            'year' => ['The year has already been taken.'],
        ]);

    // Assert database state after update
    $this->assertDatabaseCount($this->table, 2);

    $this->assertDatabaseHas($this->table, $first_semester_year_payload);

    $this->assertDatabaseHas($this->table, $second_semester_year_payload);
});

test('destroy', function () {
    $first_semester_year_setting = SemesterYearSetting::create(['year' => 2024]);
    $other_semester_year_settings = [
        SemesterYearSetting::create(['year' => 2025]),
        SemesterYearSetting::create(['year' => 2026]),
        SemesterYearSetting::create(['year' => 2027]),
    ];

    $this->assertDatabaseCount(($this->table), 4);

    //id not exist
    $response = $this->deleteJson(
        route($this->routeNamePrefix . 'destroy', [
            'master_semester_year_setting' => 9999,
        ])
    )->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount(($this->table), 4);

    //delete success
    $response = $this->deleteJson(
        route($this->routeNamePrefix . 'destroy', [
            'master_semester_year_setting' => $first_semester_year_setting->id,
        ])
    )->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount(($this->table), 3);
    $this->assertDatabaseMissing(($this->table), [
        'id' => $first_semester_year_setting->id,
    ]);

    foreach ($other_semester_year_settings as $other_semester_setting) {
        $this->assertDatabaseHas(($this->table), [
            'id' => $other_semester_setting->id,
        ]);
    }
});
