<?php

use App\Models\Author;
use App\Models\User;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(Author::class)->getTable();
    $this->routeNamePrefix = 'master-data.authors';
});

test('index', function () {
    $authors = Author::factory(3)->state(new Sequence(
        [
            'name' => 'English'
        ],
        [
            'name' => 'English 2'
        ],
        [
            'name' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'English 2')
        );

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'English'),
            fn($data) => $data->toHaveKey('name', 'English 2')
        );

    //Filter non-existing name = No Exist
    $payload = [
        'name' => 'No Exist'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'English'),
            fn($data) => $data->toHaveKey('name', 'English 2'),
            fn($data) => $data->toHaveKey('name', 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Tamil'),
            fn($data) => $data->toHaveKey('name', 'English 2'),
            fn($data) => $data->toHaveKey('name', 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $authors[0]->id),
            fn($data) => $data->toHaveKey('id', $authors[1]->id),
            fn($data) => $data->toHaveKey('id', $authors[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $authors[2]->id),
            fn($data) => $data->toHaveKey('id', $authors[1]->id),
            fn($data) => $data->toHaveKey('id', $authors[0]->id),
        );

    //Test pattern
    $payload = [
        'name' => 'Tamil'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toMatchArray([
            'id' => $authors[2]->id,
            'name' => $authors[2]->name,
        ]);
});

test('show', function () {
    $author = Author::factory()->create([
        'name' => 'English',
    ]);

    //test with id exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['author' => $author->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $author->id,
            'name' => $author->name,
        ]);

    //test with id not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['author' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => fake()->name(),
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name'],
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
    ]);
});

test('update', function () {
    $author = Author::factory()->create([
        'name' => 'Test EN',
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => 'Test 2',
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['author' => $author->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $author->id,
            'name' => $payload['name'],
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
    ]);

    //update with id not exist
    $payload = [
        'name' => 'Test 3',
    ];

    $response = $this->json('PUT', route("$this->routeNamePrefix.update", ['author' => 9999]), $payload)->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    // Assert nothing updated
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name' => 'Test 2'
    ]);
});

test('destroy', function () {
    $author = Author::factory()->create();
    $other_authors = Author::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['author' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['author' => $author->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $author->id]);

    foreach ($other_authors as $other_author) {
        $this->assertDatabaseHas($this->table, ['id' => $other_author->id]);
    }
});
