<?php

use App\Models\Country;
use App\Models\User;
use App\Services\CountryService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);
    $user->assignRole('Super Admin');

    // Need to truncate for this test case, because creating a
    // user will create a student, and student will create a country
    Country::truncate();

    Sanctum::actingAs($user);

    $this->table = resolve(Country::class)->getTable();
    $this->routeNamePrefix = 'master-data.countries';
});

test('index', function () {
    $countries = Country::factory(3)->state(new Sequence(
        [
            'name->en' => 'United States',
            'name->zh' => 'some char',
        ],
        [
            'name' => 'United Kingdom'
        ],
        [
            'name' => 'Malaysia'
        ]
    ))->create();

    //Filter by name = United States
    $payload = [
        'name' => [
            'en' => 'United States'
        ]
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'United States')
        );

    //Filter by partial name = United
    $payload = [
        'name' => [
            'en' => 'United'
        ],
        'order_by' => 'id'
    ];
    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'United States'),
            fn($data) => $data->toHaveKey('name', 'United Kingdom')
        );

    //Filter non-existing name = United What
    $payload = [
        'name' => [
            'en' => 'United What'
        ]
    ];
    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Malaysia'),
            fn($data) => $data->toHaveKey('name', 'United Kingdom'),
            fn($data) => $data->toHaveKey('name', 'United States'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'United States'),
            fn($data) => $data->toHaveKey('name', 'United Kingdom'),
            fn($data) => $data->toHaveKey('name', 'Malaysia'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $countries[0]->id),
            fn($data) => $data->toHaveKey('id', $countries[1]->id),
            fn($data) => $data->toHaveKey('id', $countries[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $countries[2]->id),
            fn($data) => $data->toHaveKey('id', $countries[1]->id),
            fn($data) => $data->toHaveKey('id', $countries[0]->id),
        );

    //Test pattern
    $payload = [
        'name' => ['en' => 'Malaysia']
    ];
    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toMatchArray([
            'id' => $countries[2]->id,
            'name' => $countries[2]->name,
            'translations' => $countries[2]->translations
        ]);
});

test('index - getAll', function () {
    $countries = Country::factory(3)->state(new Sequence(
        [
            'name->en' => 'United States',
            'name->zh' => 'some char',
        ],
        [
            'name' => 'United Kingdom'
        ],
        [
            'name' => 'Malaysia'
        ]
    ))->create();

    //Filter by partial name = United
    $payload = [
        'name' => [
            'en' => 'United'
        ],
        'order_by' => 'id',
        'per_page' => -1,
        'page' => 1,
    ];
    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'United States'),
            fn($data) => $data->toHaveKey('name', 'United Kingdom')
        );

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
        'per_page' => -1,
        'page' => 1,
    ];

    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'Malaysia'),
            fn($data) => $data->toHaveKey('name', 'United Kingdom'),
            fn($data) => $data->toHaveKey('name', 'United States'),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->json('GET', route("$this->routeNamePrefix.index"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $countries[2]->id),
            fn($data) => $data->toHaveKey('id', $countries[1]->id),
            fn($data) => $data->toHaveKey('id', $countries[0]->id),
        );
});

test('index - getPaginated when per_page is set', function () {
    $this->mock(CountryService::class, function (MockInterface $mock) {
        $country = Country::factory()->create();

        $mock->shouldReceive('getAllPaginatedCountries')
            ->once()
            ->andReturn(new LengthAwarePaginator([$country], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix.'.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index - getAll when per_page = -1 ', function () {

    $this->mock(CountryService::class, function (MockInterface $mock) {
        $countries = Country::factory(2)->create();

        $mock->shouldReceive('getAllCountries')->once()->andReturn($countries);
    });

    $response = $this->getJson(route($this->routeNamePrefix.'.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('show', function () {
    $country = Country::factory()->create([
        'name->en' => 'United States',
        'name->zh' => 'some char',
    ]);

    //test with id exist
    $response = $this->json('GET', route("$this->routeNamePrefix.show", ['master_country' => $country->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $country->id,
            'name' => $country->name,
            'translations' => $country->translations
        ]);

    //test with id not exist
    $response = $this->json('GET', route("$this->routeNamePrefix.show", ['master_country' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
    ];

    $response = $this->json('POST', route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name']['en'],
            'translations' => [
                'name' => [
                    'en' => $payload['name']['en'],
                    'zh' => $payload['name']['zh'],
                ]
            ]
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);
});

test('update', function () {
    $country = Country::factory()->create([
        'name->en' => 'Test EN',
        'name->zh' => 'Test ZH',
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Test 2',
            'zh' => 'Test 3',
        ],
    ];

    $response = $this->json('PUT', route("$this->routeNamePrefix.update", ['master_country' => $country->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $country->id,
            'name' => $payload['name']['en'],
            'translations' => [
                'name' => [
                    'en' => $payload['name']['en'],
                    'zh' => $payload['name']['zh'],
                ]
            ]
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);

    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 3',
        ],
    ];

    $response = $this->json('PUT', route("$this->routeNamePrefix.update", ['master_country' => 9999]), $payload)->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    // Assert nothing updated
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => 'Test 2',
        'name->zh' => 'Test 3',
    ]);
});

test('destroy', function () {
    $country = Country::factory()->create();
    $other_countries = Country::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->json('DELETE', route("$this->routeNamePrefix.destroy", ['master_country' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->json('DELETE', route("$this->routeNamePrefix.destroy", ['master_country' => $country->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $country->id]);

    foreach ($other_countries as $other_country) {
        $this->assertDatabaseHas($this->table, ['id' => $other_country->id]);
    }
});
