<?php

use App\Models\EmployeeCategory;
use App\Models\User;
use App\Services\EmployeeCategoryService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(EmployeeCategory::class)->getTable();
    $this->routeNamePrefix = 'master-data.employee-categories';
});

test('index', function () {
    $employee_categories = EmployeeCategory::factory(3)->state(new Sequence(
        [
            'name->en' => 'English',
            'is_teacher' => true
        ],
        [
            'name->en' => 'English 2',
            'is_teacher' => true
        ],
        [
            'name->en' => 'Tamil',
            'is_teacher' => true
        ]
    ))->create();

    //Filter by name = English 2
    $filters = [
        'name' => 'English 2',
        'is_teacher' => true
    ];

    $this->mock(EmployeeCategoryService::class, function (MockInterface $mock) use ($filters, $employee_categories) {
        $mock->shouldReceive('getAllPaginatedEmployeeCategories')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([$employee_categories[1]], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'id' => $employee_categories[1]->id,
            'name' => $employee_categories[1]->name,
            'translations' => $employee_categories[1]->translations
        ]);
});

test('index - getAll', function () {
    $employee_categories = EmployeeCategory::factory(3)->state(new Sequence(
        [
            'name->en' => 'English',
            'is_teacher' => true
        ],
        [
            'name->en' => 'English 2',
            'is_teacher' => true
        ],
        [
            'name->en' => 'Tamil',
            'is_teacher' => true
        ]
    ))->create();

    //Filter by name = English 2
    $filters = [
        'name' => 'English 2',
        'is_teacher' => true,
        'per_page' => -1
    ];

    $this->mock(EmployeeCategoryService::class, function (MockInterface $mock) {
        $employeeCategory = EmployeeCategory::where('name->en', 'English 2')->get();

        $mock->shouldReceive('getAllEmployeeCategories')
            ->once()
            ->andReturn($employeeCategory);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'id' => $employee_categories[1]->id,
            'name' => $employee_categories[1]->name,
            'translations' => $employee_categories[1]->translations
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(EmployeeCategoryService::class, function (MockInterface $mock) {
        $employeeCategory = EmployeeCategory::factory()->create();

        $mock->shouldReceive('getAllPaginatedEmployeeCategories')
            ->once()
            ->andReturn(new LengthAwarePaginator([$employeeCategory], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(EmployeeCategoryService::class, function (MockInterface $mock) {
        $employeeCategories = EmployeeCategory::factory(2)->create();

        $mock->shouldReceive('getAllEmployeeCategories')->once()->andReturn($employeeCategories);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});
