<?php

use App\Models\Currency;
use App\Models\User;
use App\Services\CurrencyService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(Currency::class)->getTable();
    $this->routeNamePrefix = 'master-data.currencies';
});

test('index', function () {
    $currencies = Currency::factory(2)->state(new Sequence(
        [
            'name' => 'SGD',
            'code' => 'SGD',
            'symbol' => 'SGD',
            'is_active' => false,
        ],
        [
            'name' => 'AUD',
            'code' => 'AUD',
            'symbol' => 'AUD',
            'is_active' => true,
        ],
    ))->create();

    $filters = [
        'code' => 'AUD',
        'is_active' => true,
    ];

    $this->mock(CurrencyService::class, function (MockInterface $mock) use ($filters, $currencies) {
        $mock->shouldReceive('getAllPaginatedCurrencies')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([$currencies[1]], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $currencies[1]->id,
            'code' => $currencies[1]->code,
            'name' => $currencies[1]->name,
            'symbol' => $currencies[1]->symbol,
            'is_active' => $currencies[1]->is_active,
        ]);
});

test('index - getAll', function () {
    $currencies = Currency::factory(2)->state(new Sequence(
        [
            'name' => 'SGD',
            'code' => 'SGD',
            'symbol' => 'SGD',
            'is_active' => false,
        ],
        [
            'name' => 'AUD',
            'code' => 'AUD',
            'symbol' => 'AUD',
            'is_active' => true,
        ],
    ))->create();

    $filters = [
        'code' => 'AUD',
        'is_active' => true,
        'per_page' => -1,
        'page' => 1,
    ];

    $this->mock(CurrencyService::class, function (MockInterface $mock) {
        $currency = Currency::where('code', 'AUD')->get();
        $mock->shouldReceive('getAllCurrencies')
            ->once()
            ->andReturn($currency);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $currencies[1]->id,
            'code' => $currencies[1]->code,
            'name' => $currencies[1]->name,
            'symbol' => $currencies[1]->symbol,
            'is_active' => $currencies[1]->is_active,
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(CurrencyService::class, function (MockInterface $mock) {
        $uom = Currency::factory()->create();

        $mock->shouldReceive('getAllPaginatedCurrencies')
            ->once()
            ->andReturn(new LengthAwarePaginator([$uom], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {
    $this->mock(CurrencyService::class, function (MockInterface $mock) {
        $currencies = Currency::factory(2)->create();

        $mock->shouldReceive('getAllCurrencies')->once()->andReturn($currencies);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});
