<?php

use App\Enums\GradingSchemeType;
use App\Http\Resources\GradingSchemeItemResource;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\User;
use App\Services\GradingSchemeService;
use Database\Seeders\PermissionSeeder;
use Illuminate\Pagination\LengthAwarePaginator;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        PermissionSeeder::class
    ]);

    $this->user = User::factory()->create();
    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->baseUrl = 'grading-schemes.';
    $this->tableName = 'master_grading_schemes';
});

test('index', function () {
    $first_grading_scheme = GradingScheme::factory()->withItems()->create();
    $second_grading_scheme = GradingScheme::factory()->withItems(2)->create();

    $response = $this->getJson(route($this->baseUrl.'index', ['order_by' => ['id']]));

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_grading_scheme->id,
                'type' => $first_grading_scheme->type,
                'code' => $first_grading_scheme->code,
                'name' => $first_grading_scheme->name,
                'is_active' => $first_grading_scheme->is_active,
                'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($first_grading_scheme->gradingSchemeItems)),
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_grading_scheme->id,
                'type' => $second_grading_scheme->type,
                'code' => $second_grading_scheme->code,
                'name' => $second_grading_scheme->name,
                'is_active' => $second_grading_scheme->is_active,
                'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($second_grading_scheme->gradingSchemeItems)),
            ]),
        );
});

test('index : filter by name', function () {
    $first_grading_scheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Sem 1 2024',
    ]);

    $second_grading_scheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Sem 2 2024',
    ]);

    GradingScheme::factory()->create();

    $response = $this->getJson(route($this->baseUrl.'index', ['name' => 'Sem 1']));

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)->toEqual([
            [
                'id' => $first_grading_scheme->id,
                'type' => $first_grading_scheme->type,
                'code' => $first_grading_scheme->code,
                'name' => $first_grading_scheme->name,
                'is_active' => $first_grading_scheme->is_active,
                'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($first_grading_scheme->gradingSchemeItems)),
            ]
        ]);
});

test('index : order by name', function () {
    GradingScheme::factory()->withItems()->create([
        'name' => 'Sem 1 2024',
    ]);

    GradingScheme::factory()->withItems()->create([
        'name' => 'Sem 2 2024',
    ]);

    $response = $this->getJson(route($this->baseUrl.'index', ['order_by' => ['name']]));

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toMatchArray([
                'name' => 'Sem 1 2024',
            ]),
            fn($response) => $response->toMatchArray([
                'name' => 'Sem 2 2024',
            ])
        );
});

test('index - getAll', function () {
    $first_grading_scheme = GradingScheme::factory()->withItems()->create();
    $second_grading_scheme = GradingScheme::factory()->withItems(2)->create();

    $payload = [
        'order_by' => 'id',
        'per_page' => -1
    ];

    $response = $this->getJson(route($this->baseUrl.'index', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_grading_scheme->id,
                'type' => $first_grading_scheme->type,
                'code' => $first_grading_scheme->code,
                'name' => $first_grading_scheme->name,
                'is_active' => $first_grading_scheme->is_active,
                'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($first_grading_scheme->gradingSchemeItems)),
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_grading_scheme->id,
                'type' => $second_grading_scheme->type,
                'code' => $second_grading_scheme->code,
                'name' => $second_grading_scheme->name,
                'is_active' => $second_grading_scheme->is_active,
                'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($second_grading_scheme->gradingSchemeItems)),
            ]),
        );
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(GradingSchemeService::class, function (MockInterface $mock) {
        $gradingScheme = GradingScheme::factory()->create();

        $mock->shouldReceive('getAllPaginatedGradingSchemes')
            ->once()
            ->andReturn(new LengthAwarePaginator([$gradingScheme], 1, 1));
    });

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(GradingSchemeService::class, function (MockInterface $mock) {
        $gradingSchemes = GradingScheme::factory(2)->create();

        $mock->shouldReceive('getAllGradingSchemes')->once()->andReturn($gradingSchemes);
    });

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});


test('show success', function () {
    $grading_scheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Sem 1 2024',
    ]);

    $second_grading_scheme = GradingScheme::factory()->withItems(2)->create([
        'name' => 'Sem 2 2024',
    ]);

    $response = $this->getJson(route($this->baseUrl.'show', $grading_scheme->id));

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $grading_scheme->id,
            'type' => $grading_scheme->type,
            'code' => $grading_scheme->code,
            'name' => $grading_scheme->name,
            'is_active' => $grading_scheme->is_active,
            'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($grading_scheme->gradingSchemeItems)),
        ]);
});

test('show failed because invalid grading_scheme id', function () {
    expect(GradingScheme::count())->toBe(0);

    $response = $this->getJson(route($this->baseUrl.'show', 1));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('create success with 2 grading_scheme_items', function () {
    expect(GradingScheme::count())->toBe(0)
        ->and(GradingSchemeItem::count())->toBe(0);

    $payload = [
        'type' => GradingSchemeType::CONDUCT->value,
        'name' => 'Sem 1',
        'code' => 'code1',
        'is_active' => true,
        'grading_scheme_items' => [
            [
                'name' => 'A',
                'display_as_name' => 'A',
                'from' => 60,
                'to' => 100,
                'extra_marks' => 0,
            ],
            [
                'name' => 'F',
                'display_as_name' => 'F',
                'from' => 0,
                'to' => 59,
                'extra_marks' => 0,
            ],
        ],
    ];

    $response = $this->postJson(route($this->baseUrl.'create'), $payload);

    $response->assertStatus(200);

    expect(GradingScheme::count())->toBe(1)
        ->and(GradingSchemeItem::count())->toBe(2);

    $created = GradingScheme::first();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $created->id,
            'type' => $payload['type'],
            'code' => strtoupper($payload['code']),
            'name' => $payload['name'],
            'is_active' => $payload['is_active'],
            'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($created->gradingSchemeItems)),
        ]);
});

test('create failed because validation error', function () {
    expect(GradingScheme::count())->toBe(0);

    $response = $this->postJson(route($this->baseUrl.'create'), []);

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'name' => [
                    'The name field is required.'
                ],
                'code' => [
                    'The code field is required.'
                ],
                'type' => [
                    'The type field is required.'
                ],
                'is_active' => [
                    'The is active field is required.'
                ],
                'grading_scheme_items' => [
                    'The grading scheme items field is required.'
                ],
            ],
            'data' => null
        ])
        ->and(GradingScheme::count())->toBe(0);

    // -------------------------------------------------------------------------------------

    $response = $this->postJson(route($this->baseUrl.'create'), [
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'CODE1',
        'name' => 'Sem 1',
        'is_active' => true,
        'grading_scheme_items' => [
            []
        ]
    ]);

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'grading_scheme_items.0.name' => [
                    'The grading_scheme_items.0.name field is required.'
                ],
                'grading_scheme_items.0.display_as_name' => [
                    'The grading_scheme_items.0.display_as_name field is required.'
                ],
                'grading_scheme_items.0.from' => [
                    'The grading_scheme_items.0.from field is required.'
                ],
                'grading_scheme_items.0.to' => [
                    'The grading_scheme_items.0.to field is required.'
                ],
                "grading_scheme_items.0.extra_marks" => [
                    "The grading_scheme_items.0.extra_marks field is required."
                ]
            ],
            'data' => null
        ])
        ->and(GradingScheme::count())->toBe(0);

});

test('update success with 1 grading_scheme_items, only update grading_scheme details', function () {
    $grading_scheme = GradingScheme::factory()->withItems()->create();
    $first_scheme_item = $grading_scheme->gradingSchemeItems->first();

    $payload = [
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'NEWCODE',
        'name' => 'Updated Sem 1 2024',
        'is_active' => true,
        'grading_scheme_items' => [
            [ // grading_scheme_items remains same
                'id' => $first_scheme_item->id,
                'name' => $first_scheme_item->name,
                'display_as_name' => $first_scheme_item->display_as_name,
                'from' => $first_scheme_item->from,
                'to' => $first_scheme_item->to,
                'extra_marks' => $first_scheme_item->extra_marks,
            ],
        ],
    ];

    $response = $this->putJson(route($this->baseUrl.'update', $grading_scheme->id), $payload);

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $grading_scheme->id,
            'type' => $payload['type'],
            'code' => $payload['code'],
            'name' => $payload['name'],
            'is_active' => $payload['is_active'],
            'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($grading_scheme->gradingSchemeItems)),
        ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $grading_scheme->id,
        'type' => GradingSchemeType::CONDUCT->value,
        'code' => 'NEWCODE',
        'name' => 'Updated Sem 1 2024',
        'is_active' => true,
    ]);

    $this->assertDatabaseHas('master_grading_scheme_items', [
        'id' => $payload['grading_scheme_items'][0]['id'],
        'name' => $payload['grading_scheme_items'][0]['name'],
        'display_as_name' => $payload['grading_scheme_items'][0]['display_as_name'],
        'from' => $payload['grading_scheme_items'][0]['from'],
        'to' => $payload['grading_scheme_items'][0]['to'],
        'extra_marks' => $payload['grading_scheme_items'][0]['extra_marks'],
    ]);
});

test('update success with 1 grading_scheme_items, only update grading_scheme_item details', function () {
    $grading_scheme = GradingScheme::factory()->withItems()->create();
    $first_scheme_item = $grading_scheme->gradingSchemeItems->first();

    $payload = [
        'type' => $grading_scheme->type,
        'code' => $grading_scheme->code,
        'name' => $grading_scheme->name,
        'is_active' => $grading_scheme->is_active,
        'grading_scheme_items' => [
            [ // grading_scheme_items remains same
                'id' => $first_scheme_item->id,
                'name' => 'updated A',
                'display_as_name' => 'updated A display',
                'from' => 0,
                'to' => 100,
                'extra_marks' => 0,
            ],
        ],
    ];

    $response = $this->putJson(route($this->baseUrl.'update', $grading_scheme->id), $payload);

    $response->assertStatus(200);

    $grading_scheme->refresh();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $grading_scheme->id,
            'type' => $payload['type'],
            'code' => $payload['code'],
            'name' => $payload['name'],
            'is_active' => $payload['is_active'],
            'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($grading_scheme->gradingSchemeItems)),
        ]);

    $this->assertDatabaseHas('master_grading_scheme_items', [
        'id' => $first_scheme_item->id,
        'name' => 'updated A',
        'display_as_name' => 'updated A display',
        'from' => 0,
        'to' => 100,
        'extra_marks' => 0,
    ]);
});

test('update success, only update item details : remove 1 item, add 1 new item, update 1 existing item', function () {
    $grading_scheme = GradingScheme::factory()->withItems(2)->create();
    $scheme_item_1 = $grading_scheme->gradingSchemeItems[0]; // to be updated
    $scheme_item_2 = $grading_scheme->gradingSchemeItems[1]; // to be removed

    $payload = [
        'type' => $grading_scheme->type,
        'code' => $grading_scheme->code,
        'name' => $grading_scheme->name,
        'is_active' => $grading_scheme->is_active,
        'grading_scheme_items' => [
            [
                'id' => $scheme_item_1->id,
                'name' => 'updated A',
                'display_as_name' => 'updated A display',
                'from' => 90,
                'to' => 100,
                'extra_marks' => 0,
            ],
            [ // created new
                'name' => 'F',
                'display_as_name' => 'F',
                'from' => 0,
                'to' => 64,
                'extra_marks' => 0,
            ],
        ],
    ];

    $response = $this->putJson(route($this->baseUrl.'update', $grading_scheme->id), $payload);

    $response->assertStatus(200);

    $grading_scheme->refresh();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $grading_scheme->id,
            'type' => $payload['type'],
            'code' => $payload['code'],
            'name' => $payload['name'],
            'is_active' => $payload['is_active'],
            'grading_scheme_items' => resourceToArray(GradingSchemeItemResource::collection($grading_scheme->gradingSchemeItems)),
        ]);

    $this->assertDatabaseHas('master_grading_scheme_items', [
        'id' => $scheme_item_1->id,
        'name' => 'updated A',
        'display_as_name' => 'updated A display',
        'from' => 90,
        'to' => 100,
        'extra_marks' => 0,
    ]);

    // assert newly created
    $this->assertDatabaseHas('master_grading_scheme_items', [
        'grading_scheme_id' => $grading_scheme->id,
        'name' => 'F',
        'display_as_name' => 'F',
        'from' => 0,
        'to' => 64,
        'extra_marks' => 0,
    ]);

    // assert $scheme_item_3 removed
    $this->assertDatabaseMissing('master_grading_scheme_items', [
        'id' => $scheme_item_2->id,
    ]);
});

test('update failed because validation error', function () {
    $grading_scheme = GradingScheme::factory()->withItems()->create();

    $response = $this->putJson(route($this->baseUrl.'update', $grading_scheme->id), []);

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'type' => [
                    'The type field is required.'
                ],
                'code' => [
                    'The code field is required.'
                ],
                'name' => [
                    'The name field is required.'
                ],
                'is_active' => [
                    'The is active field is required.'
                ],
                'grading_scheme_items' => [
                    'The grading scheme items field is required.'
                ],
            ],
            'data' => null
        ]);


    // -------------------------------------------------------------------------------------

    $response = $this->putJson(route($this->baseUrl.'update', $grading_scheme->id), [
        'type' => GradingSchemeType::CONDUCT->value,
        'name' => 'Sem 1',
        'code' => 'Code1',
        'is_active' => true,
        'grading_scheme_items' => [
            []
        ]
    ]);

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'grading_scheme_items.0.name' => [
                    'The grading_scheme_items.0.name field is required.'
                ],
                'grading_scheme_items.0.display_as_name' => [
                    'The grading_scheme_items.0.display_as_name field is required.'
                ],
                'grading_scheme_items.0.from' => [
                    'The grading_scheme_items.0.from field is required.'
                ],
                'grading_scheme_items.0.to' => [
                    'The grading_scheme_items.0.to field is required.'
                ],
                "grading_scheme_items.0.extra_marks" => [
                    "The grading_scheme_items.0.extra_marks field is required."
                ]
            ],
            'data' => null
        ]);
});

test('update failed because validation error, grading_items id provide dont belong to the grading scheme', function () {
    $grading_scheme = GradingScheme::factory()->withItems()->create();
    $invalid_scheme_item = GradingSchemeItem::factory()->create();

    $payload = [
        'type' => $grading_scheme->type,
        'code' => $grading_scheme->code,
        'name' => $grading_scheme->name,
        'is_active' => $grading_scheme->is_active,
        'grading_scheme_items' => [
            [
                'id' => $invalid_scheme_item->id, // invalid grading_scheme_item from another grading_scheme
                'name' => 'updated A',
                'display_as_name' => 'updated A display',
                'from' => 90,
                'to' => 100,
                'extra_marks' => 0,
            ],
        ],
    ];

    $response = $this->putJson(route($this->baseUrl.'update', $grading_scheme->id), $payload);

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'grading_scheme_items.0.id' => [
                    __('validation.custom.grading_scheme.dont_belong')
                ],
            ],
            'data' => null
        ]);
});

test('update failed because invalid id', function () {
    $response = $this->getJson(route($this->baseUrl.'update', 1), []);

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('destroy success', function () {
    $first_grading_scheme = GradingScheme::factory()->withItems()->create();
    $other_grading_schemes = GradingScheme::factory()->count(3)->create();

    //delete success
    $response = $this->deleteJson(route($this->baseUrl.'destroy',
        ['grading_scheme' => $first_grading_scheme->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and(GradingScheme::count())->toBe(3)
        ->and(GradingSchemeItem::count())->toBe(0);

    $this->assertDatabaseMissing($this->tableName, ['id' => $first_grading_scheme->id]);

    foreach ($other_grading_schemes as $other) {
        $this->assertDatabaseHas($this->tableName, ['id' => $other->id]);
    }
});

test('destroy failed because invalid id', function () {
    $response = $this->deleteJson(route($this->baseUrl.'destroy', 9999))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});
