<?php

use App\Enums\ClassType;
use App\Models\ClassModel;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\ReportCardOutput;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\StudentReportCard;
use App\Models\User;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {

    Cache::clear();

    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'exam-results-posting';
});


test('getEligibleMasterGradingFrameworks', function () {

    $semester_settings = SemesterSetting::factory(2)->create();
    $grades = Grade::factory(2)->create();
    $students = Student::factory(4)->create();

    $classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name->en' => 'Grade 0 Class A',
            'grade_id' => $grades[0]->id,
        ],
        [
            'name->en' => 'Grade 0 Class B',
            'grade_id' => $grades[0]->id,
        ],
        [
            'name->en' => 'Grade 1 Class A',
            'grade_id' => $grades[1]->id,
        ]
    ))->create();

    $semester_classes = SemesterClass::factory(6)->state(new Sequence(
        [
            'semester_setting_id' => $semester_settings[0]->id,
            'class_id' => $classes[0]->id
        ],
        [
            'semester_setting_id' => $semester_settings[0]->id,
            'class_id' => $classes[1]->id
        ],
        [
            'semester_setting_id' => $semester_settings[0]->id,
            'class_id' => $classes[2]->id
        ],
        [
            'semester_setting_id' => $semester_settings[1]->id,
            'class_id' => $classes[0]->id
        ],
        [
            'semester_setting_id' => $semester_settings[1]->id,
            'class_id' => $classes[1]->id
        ],
        [
            'semester_setting_id' => $semester_settings[1]->id,
            'class_id' => $classes[2]->id
        ],
    ))->create();

    StudentClass::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_classes[0]->id
        ],
        [
            'student_id' => $students[1]->id,
            'semester_class_id' => $semester_classes[0]->id
        ],
        [
            'student_id' => $students[2]->id,
            'semester_class_id' => $semester_classes[1]->id
        ],
        [
            'student_id' => $students[3]->id,
            'semester_class_id' => $semester_classes[2]->id
        ],
        [
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_classes[3]->id
        ],
        [
            'student_id' => $students[1]->id,
            'semester_class_id' => $semester_classes[3]->id
        ],
        [
            'student_id' => $students[2]->id,
            'semester_class_id' => $semester_classes[4]->id
        ],
        [
            'student_id' => $students[3]->id,
            'semester_class_id' => $semester_classes[5]->id
        ],
    ))->create();

    $grading_frameworks = GradingFramework::factory(3)->state(new Sequence(
        [
            'name->en' => 'Junior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Junior 2 High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Senior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ]
    ))->create();

    $sgfs = StudentGradingFramework::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[2]->id,
            'grading_framework_id' => $grading_frameworks[1]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[3]->id,
            'grading_framework_id' => $grading_frameworks[2]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[0]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => false
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => false
        ],
        [
            'student_id' => $students[2]->id,
            'grading_framework_id' => $grading_frameworks[1]->id,
            'is_active' => false
        ],
        [
            'student_id' => $students[3]->id,
            'grading_framework_id' => $grading_frameworks[2]->id,
            'is_active' => false
        ],
    ))->create();

    $payload = [
        'grade_id' => $grades[0]->id,
        'semester_setting_id' => $semester_settings[0]->id
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleMasterGradingFrameworks', $payload)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $expect_response = $response['data'];
    $expect_response = collect($expect_response)->sortBy('id')->values()->all();

    expect($expect_response)->toHaveCount(2)
        ->and($expect_response[0])->toMatchArray([
            'id' => $grading_frameworks[0]->id,
            'name' => $grading_frameworks[0]->name
        ])
        ->and($expect_response[1])->toMatchArray([
            'id' => $grading_frameworks[1]->id,
            'name' => $grading_frameworks[1]->name
        ]);

    $payload = [
        'grade_id' => 99999,
        'semester_setting_id' => 9999
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleMasterGradingFrameworks', $payload)
    )->json();

    expect($response)->toHaveFailedGeneralResponse();
    expect($response['code'])->toBe(422)
        ->and($response['error'])->toMatchArray([
            'grade_id' => ['The selected grade id is invalid.'],
            'semester_setting_id' => ['The selected semester setting id is invalid.']
        ]);

});


test('getEligibleReportCardOutputCodes', function () {

    $students = Student::factory(4)->create();
    $grading_frameworks = GradingFramework::factory(3)->state(new Sequence(
        [
            'name->en' => 'Junior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Senior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ],
        [
            'name->en' => 'Other High School Grading Framework',
            'name->zh' => '初中生成绩模板',
        ]
    ))->create();

    $sgfs = StudentGradingFramework::factory(3)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'grading_framework_id' => $grading_frameworks[0]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[1]->id,
            'is_active' => true
        ],
        [
            'student_id' => $students[1]->id,
            'grading_framework_id' => $grading_frameworks[2]->id,
            'is_active' => true
        ]
    ))->create();

    $report_card_outputs = ReportCardOutput::factory(6)->state(new Sequence(
        [
            'student_grading_framework_id' => $sgfs[0],
            'code' => 'SEM1RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[0],
            'code' => 'SEM2RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[0],
            'code' => 'FINALRESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[1],
            'code' => 'NEWSEM1RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[1],
            'code' => 'NEWSEM2RESULT'
        ],
        [
            'student_grading_framework_id' => $sgfs[1],
            'code' => 'NEWFINALRESULT'
        ]
    ))->create();


    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleReportCardOutputCodes', ['grading_framework_id' => $grading_frameworks[0]->id])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse();
    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toMatchArray(['code' => $report_card_outputs[0]->code])
        ->and($response['data'][1])->toMatchArray(['code' => $report_card_outputs[1]->code])
        ->and($response['data'][2])->toMatchArray(['code' => $report_card_outputs[2]->code]);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleReportCardOutputCodes', ['grading_framework_id' => $grading_frameworks[2]->id])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse();
    expect($response['data'])->toHaveCount(0);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getEligibleReportCardOutputCodes', ['grading_framework_id' => 9999])
    )->json();

    expect($response)->toHaveFailedGeneralResponse();
    expect($response['code'])->toBe(422)
        ->and($response['error'])->toMatchArray([
            'grading_framework_id' => ['The selected grading framework id is invalid.'],
        ]);

});


test('createPostingSession', function () {

    Employee::factory()->create(['user_id' => $this->user->id]);

    \Carbon\Carbon::setTestNow('2024-12-10 13:50:01.000');

    $grade = Grade::factory()->create();

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();

    $course = Course::factory()->uec()->create();

    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 1',
    ]);

    $class = ClassModel::factory()->create([
        'grade_id' => $grade->id,
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
    ]);

    // in sem 1, student 1 & 2 in class 1, student 3 in class 2
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $student1->id,
        'is_active' => true,
    ]);
    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $student2->id,
        'is_active' => true,
    ]);

    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf1->id,
        'code' => 'OUTPUT1',
    ]);

    ReportCardOutput::factory()->create([
        'student_grading_framework_id' => $sgf2->id,
        'code' => 'OUTPUT1',
    ]);

    $payload = [
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting->id,
        'code' => 'output1',
        'publish_date' => '2025-05-05'
    ];

    $response = $this->postJson(route("$this->routeNamePrefix" . ".createExamPostingSession"), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'code' => '20241210-1733838601000',
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting->id,
        'report_card_output_code' => 'OUTPUT1',
        'report_card_template_service' => 'PinHwaDefaultReportCardTemplateService',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => json_encode([$student1->id, $student2->id]),
        'posted_by_employee_id' => $this->user->employee->id,
        'publish_date' => $payload['publish_date'],
        'posted_at' => now(),
        'metadata' => null,
    ]);

    // validation check
    $payload = [
        'grade_id' => 99999,
        'semester_setting_id' => 99999,
        'code' => 'OUTPUT99999',
        'publish_date' => '1234567'
    ];
    $response = $this->postJson(route("$this->routeNamePrefix" . ".createExamPostingSession"), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'grade_id' => ['The selected grade id is invalid.'],
            'semester_setting_id' => ['The selected semester setting id is invalid.'],
            'code' => ['The selected code is invalid.'],
            'publish_date' => [
                'The publish date field must match the format Y-m-d.',
                'The publish date field must be a date after or equal to today.'
            ]
        ]);

    $payload = [];
    $response = $this->postJson(route("$this->routeNamePrefix" . ".createExamPostingSession"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'grade_id' => ['The grade id field is required.'],
            'semester_setting_id' => ['The semester setting id field is required.'],
            'code' => ['The code field is required.'],
            'publish_date' => ['The publish date field is required.']
        ]);
});

test('publishStudentReportCardByHeader', function(){
    $grades = Grade::factory(3)->create();
    $semester_settings = SemesterSetting::factory(3)->create();

    $students = Student::factory(5)->create();

    $headers = ResultsPostingHeader::factory(3)->state(new Sequence(
        [
            'code' => 'SEM1EXAM',
            'report_card_output_code' => 1,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
            'posted_by_employee_id' => 1,
            'publish_date' => now(),
        ],
        [
            'code' => 'SEM1EXAM',
            'report_card_output_code' => 1,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
            'posted_by_employee_id' => 1,
            'publish_date' => Carbon::yesterday(),
        ],
        [
            'code' => 'SEM1EXAM',
            'report_card_output_code' => 1,
            'status' => ResultsPostingHeader::STATUS_PENDING,
            'posted_by_employee_id' => 1,
            'publish_date' => now(),
        ],

    ))->create();

    $report_cards = StudentReportCard::factory(4)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'results_posting_header_id' => $headers[0]->id,
            'is_active' => true,
            'is_visible_to_student' => false
        ],
        [
            'student_id' => $students[1]->id,
            'results_posting_header_id' => $headers[0]->id,
            'is_active' => true,
            'is_visible_to_student' => false
        ],
        [
            'student_id' => $students[0]->id,
            'results_posting_header_id' => $headers[1]->id,
            'is_active' => true,
            'is_visible_to_student' => false
        ],
        [
            'student_id' => $students[0]->id,
            'results_posting_header_id' => $headers[2]->id,
            'is_active' => true,
            'is_visible_to_student' => false
        ],
    ))->create();

    $payload = [
        'results_posting_header_id' => $headers[0]->id,
    ];
    $response = $this->postJson(route("$this->routeNamePrefix" . ".publish-report-card-by-header"), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $published_report_cards = StudentReportCard::where(['is_visible_to_student' => true])
        ->get()
        ->toArray();

    expect($published_report_cards)->toHaveCount(2)
        ->and($published_report_cards[0])->toMatchArray([
            'id' => $report_cards[0]->id,
            'student_id' => $report_cards[0]->student_id,
            'is_active' => true
        ])
        ->and($published_report_cards[1])->toMatchArray([
            'id' => $report_cards[1]->id,
            'student_id' => $report_cards[1]->student_id,
            'is_active' => true
        ]);
});
