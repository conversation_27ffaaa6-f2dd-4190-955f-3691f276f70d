<?php

use App\Models\Grade;
use App\Models\ReportCardByMorph;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\User;
use App\Services\Exam\ReportCard\ReportCardByMorphService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;


beforeEach(function () {

    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'reports.academy.';
});


test('index', function () {

    $header_1 = ResultsPostingHeader::factory()->create(['id' => 100]);
    $header_2 = ResultsPostingHeader::factory()->create(['id' => 200]);

    $grade = Grade::factory()->create(['id' => 100]);
    $semester_class1 = SemesterClass::factory()->create(['id' => 100]);
    $semester_class2 = SemesterClass::factory()->create(['id' => 200]);

    $semester_setting1 = SemesterSetting::factory()->create(['id' => 100]);
    $semester_setting2 = SemesterSetting::factory()->create(['id' => 200]);

    $report_cards = ReportCardByMorph::factory(3)->state(new Sequence(
        [
            'results_posting_header_id' => $header_1->id,
            'report_card_morphable_type' => Grade::class,
            'report_card_morphable_id' => $grade->id,
            'semester_setting_id' => $semester_setting1->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $header_1->id,
            'report_card_morphable_type' => SemesterClass::class,
            'report_card_morphable_id' => $semester_class1->id,
            'semester_setting_id' => $semester_setting1->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $header_2->id,
            'report_card_morphable_type' => SemesterClass::class,
            'report_card_morphable_id' => $semester_class2->id,
            'semester_setting_id' => $semester_setting2->id,
            'is_active' => false,
        ]
    ))->create();

    $payload = [
        'report_card_morphable_type' => 'App\\Models\\Grade',
        'report_card_morphable_id' => $grade->id,
        'results_posting_header_id' => $header_1->id,
    ];
    $response = $this->getJson(route($this->routeNamePrefix . 'report-card-by-morph-report', $payload))->json();

    expect($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray(['id' => $report_cards[0]->id]);
    // dd($response);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(ReportCardByMorphService::class, function (MockInterface $mock) {
        $report_card = ReportCardByMorph::factory()->create();

        $mock->shouldReceive('getAllPaginatedReportCardsByMorph')
            ->once()
            ->andReturn(new LengthAwarePaginator([$report_card], 1, 1));
    });

    $response = $this->getJson(route("$this->routeNamePrefix" . "report-card-by-morph-report", [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(ReportCardByMorphService::class, function (MockInterface $mock) {
        $report_cards = ReportCardByMorph::factory(2)->create();

        $mock->shouldReceive('getAllReportCardByMorph')->once()->andReturn($report_cards);
    });

    $response = $this->getJson(route("$this->routeNamePrefix" . "report-card-by-morph-report", [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});
