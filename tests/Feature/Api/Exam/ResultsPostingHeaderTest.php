<?php

use App\Http\Resources\EmployeeResource;
use App\Http\Resources\GradeResource;
use App\Http\Resources\SemesterSettingResource;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentReportCard;
use App\Models\User;
use App\Services\Exam\ResultsPostingHeaderService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {

    Cache::clear();

    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'results-posting-header';
});


test('getAllResultsPostingHeader', function () {
    $grades = Grade::factory(3)->create();
    $employees = Employee::factory(2)->create();

    $time_1_MYT = Carbon::parse('2024-10-30 07:40:00', config('school.timezone'));
    $time_2_MYT = Carbon::parse('2024-10-30 08:40:00', config('school.timezone'));

    $semester_setting1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
    ]);
    $semester_setting2 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 2',
    ]);

    $result_posting_headers = ResultsPostingHeader::factory(3)->state(new Sequence(
        [
            'code' => '20250317-1111',
            'report_card_output_code' => 'SEM1RESULT',
            'report_card_template_service' => 'PinHwaDefaultReportCardTemplateService',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_setting1->id,
            'status' => ResultsPostingHeader::STATUS_PENDING,
            'posted_by_employee_id' => $employees[0]->id,
            'errors' => null,
            'posted_at' => now(),
            'processing_start' => $time_1_MYT,
            'processing_end' => $time_2_MYT,
        ],
        [
            'code' => '20250317-2222',
            'report_card_output_code' => 'SEM2RESULT',
            'report_card_template_service' => 'PinHwaDefaultReportCardTemplateService',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_setting1->id,
            'status' => ResultsPostingHeader::STATUS_IN_PROGRESS,
            'posted_by_employee_id' => $employees[0]->id,
            'errors' => null,
            'posted_at' => now(),
            'processing_start' => $time_1_MYT,
            'processing_end' => $time_2_MYT,
        ],
        [
            'code' => '20250317-3333',
            'report_card_output_code' => 'FINALRESULT',
            'report_card_template_service' => 'PinHwaDefaultReportCardTemplateService',
            'grade_id' => $grades[1]->id,
            'semester_setting_id' => $semester_setting2->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
            'posted_by_employee_id' => $employees[1]->id,
            'errors' => null,
            'posted_at' => now(),
            'processing_start' => $time_1_MYT,
            'processing_end' => $time_2_MYT,
        ]
    ))->create();

    $payload = ['includes' => ['grade', 'semesterSetting', 'postedByEmployee']];
    $response = $this->getJson(route($this->routeNamePrefix . '.index', $payload))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['code'])->toBe(200)
        ->and($response['data'][0])->toMatchArray([
            'id' => $result_posting_headers[0]->id,
            'report_card_output_code' => $result_posting_headers[0]->report_card_output_code,
            'report_card_template_service' => $result_posting_headers[0]->report_card_template_service,
            'status' => $result_posting_headers[0]->status,
            'processing_start' => $result_posting_headers[0]->processing_start->toISOString(),
            'processing_end' => $result_posting_headers[0]->processing_end->toISOString(),
            'posted_at' => $result_posting_headers[0]->posted_at->toISOString(),
            'grade' => resourceToArray(new GradeResource($grades[0])),
            'semester_setting' => resourceToArray(new SemesterSettingResource($semester_setting1)),
            'posted_by_employee' => resourceToArray(new EmployeeResource($employees[0]))
        ]);

});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(ResultsPostingHeaderService::class, function (MockInterface $mock) {
        $result_posting_header = ResultsPostingHeader::factory()->create();

        $mock->shouldReceive('getAllPaginatedResultsPostingHeader')
            ->once()
            ->andReturn(new LengthAwarePaginator([$result_posting_header], 1, 1));
    });

    $response = $this->getJson(route("$this->routeNamePrefix" . ".index", [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(ResultsPostingHeaderService::class, function (MockInterface $mock) {
        $result_posting_header = ResultsPostingHeader::factory(2)->create();

        $mock->shouldReceive('getAllResultsPostingHeader')->once()->andReturn($result_posting_header);
    });

    $response = $this->getJson(route("$this->routeNamePrefix" . ".index", [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});


test('getResultPostingHeaderByGrade', function () {
    $grades = Grade::factory(3)->create();
    $semester_settings = SemesterSetting::factory(3)->create();

    $headers = ResultsPostingHeader::factory(5)->state(new Sequence(
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT ALT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_ERROR,
        ],
        [
            'report_card_output_code' => 'FINALRESULT',
            'grade_id' => $grades[1]->id,
            'semester_setting_id' => $semester_settings[1]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ]
    ))->create();

    StudentReportCard::factory(4)->state(new Sequence(
        [
            'results_posting_header_id' => $headers[0]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[1]->id,
            'is_active' => false,
        ],
        [
            'results_posting_header_id' => $headers[2]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[4]->id,
            'is_active' => true,
        ]
    ))->create();

    // $headers[1] is ignored because it does not have active report card
    // $headers[3] is ignored because status = error, and $headers[4] does not meet filter criteria
    $payload = [
        'grade_id' => $grades[0]->id,
        'semester_setting_id' => $semester_settings[0]->id
    ];

    $response = $this->getJson(route($this->routeNamePrefix . '.getResultPostingHeaderByGrade', $payload))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $headers[0]->id,
            'report_card_output_code' => $headers[0]->report_card_output_code,
            'status' => $headers[0]->status
        ])
        ->and($response['data'][1])->toMatchArray([
            'id' => $headers[2]->id,
            'report_card_output_code' => $headers[2]->report_card_output_code,
            'status' => $headers[2]->status
        ]);

    // only headers[4] should be displayed
    $payload = [
        'grade_id' => $grades[1]->id,
        'semester_setting_id' => $semester_settings[1]->id
    ];

    $response = $this->getJson(route($this->routeNamePrefix . '.getResultPostingHeaderByGrade', $payload))->json();
    expect($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'id' => $headers[4]->id,
            'report_card_output_code' => $headers[4]->report_card_output_code,
            'status' => $headers[4]->status
        ]);

});

test('getResultPostingHeaderByStudent', function () {
    $grades = Grade::factory(3)->create();
    $semester_settings = SemesterSetting::factory(3)->create();

    $students = Student::factory(3)->create();

    $headers = ResultsPostingHeader::factory(5)->state(new Sequence(
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
            'student_ids' => [
                $students[0]->id, 
                $students[1]->id
            ]
        ],
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
            'student_ids' => [
                $students[0]->id, 
                $students[1]->id
            ]
        ],
        [
            'report_card_output_code' => 'SEM1RESULT ALT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
            'student_ids' => [
                $students[0]->id, 
                $students[1]->id
            ]
        ],
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'status' => ResultsPostingHeader::STATUS_ERROR,
            'student_ids' => [
                $students[0]->id, 
                $students[1]->id
            ]
        ],
        [
            'report_card_output_code' => 'FINALRESULT',
            'grade_id' => $grades[1]->id,
            'semester_setting_id' => $semester_settings[1]->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
            'student_ids' => [ $students[2]->id ]
        ]
    ))->create();

    StudentReportCard::factory(4)->state(new Sequence(
        [
            'results_posting_header_id' => $headers[0]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[1]->id,
            'is_active' => false,
        ],
        [
            'results_posting_header_id' => $headers[2]->id,
            'is_active' => true,
        ],
        [
            'results_posting_header_id' => $headers[4]->id,
            'is_active' => true,
        ]
    ))->create();

    // $headers[1] is ignored because it does not have active report card
    // $headers[3] is ignored because status = error, and $headers[4] does not meet filter criteria
    $payload = ['student_id' => $students[0]->id ];
    $response = $this->getJson(route($this->routeNamePrefix . '.getResultPostingHeaderByStudent', $payload))->json();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $headers[0]->id,
            'report_card_output_code' => $headers[0]->report_card_output_code,
            'status' => $headers[0]->status
        ])
        ->and($response['data'][1])->toMatchArray([
            'id' => $headers[2]->id,
            'report_card_output_code' => $headers[2]->report_card_output_code,
            'status' => $headers[2]->status
        ]);

    // only headers[4] should be displayed
    $payload = ['student_id' => $students[2]->id];
    $response = $this->getJson(route($this->routeNamePrefix . '.getResultPostingHeaderByStudent', $payload))->json();

    expect($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'id' => $headers[4]->id,
            'report_card_output_code' => $headers[4]->report_card_output_code,
            'status' => $headers[4]->status
        ]);

});
