<?php

use App\Http\Resources\CountryResource;
use App\Http\Resources\EducationResource;
use App\Http\Resources\RaceResource;
use App\Http\Resources\ReligionResource;
use App\Models\Employee;
use App\Models\Guardian;
use App\Services\GuardianService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Pagination\LengthAwarePaginator;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    app()->setLocale('en');

    $this->user = Employee::factory()->create()->user;
    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'guardians.';
});

test('index', function () {
    $first_guardian = Guardian::factory()->create();
    $second_guardian = Guardian::factory()->create();

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'order_by' => 'id',
        'includes' => ['country', 'race', 'religion', 'education']
    ]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $first_guardian->id,
            'name' => $first_guardian->name,
            'nric' => $first_guardian->nric,
            'passport_number' => $first_guardian->passport_number,
            'phone_number' => $first_guardian->phone_number,
            'email' => $first_guardian->email,
            'nationality' => resourceToArray(new CountryResource($first_guardian->country)),
            'race' => resourceToArray(new RaceResource($first_guardian->race)),
            'religion' => resourceToArray(new ReligionResource($first_guardian->religion)),
            'education' => resourceToArray(new EducationResource($first_guardian->education)),
            'married_status' => $first_guardian->married_status->value,
            'occupation' => $first_guardian->occupation,
            'occupation_description' => $first_guardian->occupation_description,
            'translations' => $first_guardian->translations,
            'has_user_account' => $first_guardian->hasUserAccount(),
            'live_status' => $first_guardian->live_status->value,
        ]);
});

test('index: test mock available filters', function () {
    $first_guardian = Guardian::factory()->create([
        'name->en' => 'John Doe',
        'nric' => '**********',
        'email' => '<EMAIL>',
        'phone_number' => '+**********',
    ]);
    $second_guardian = Guardian::factory()->create();

    $payload = [
        'name' => 'John Doe',
        'nric' => '**********',
        'email' => '<EMAIL>',
        'phone_number' => '+**********',
    ];

    $this->mock(GuardianService::class, function (MockInterface $mock) use ($payload, $first_guardian) {
        $mock->shouldReceive('getAllPaginatedGuardians')
            ->once()
            ->with($payload)
            ->andReturn(new LengthAwarePaginator([$first_guardian], 1, 1));
    });

    $this->getJson(route($this->routeNamePrefix . 'index', $payload));
});

test('index - getAll', function () {
    $first_guardian = Guardian::factory()->create();
    $second_guardian = Guardian::factory()->create();

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'order_by' => 'id',
        'includes' => ['country', 'race', 'religion', 'education'],
        'per_page' => -1
    ]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $first_guardian->id,
            'name' => $first_guardian->name,
            'nric' => $first_guardian->nric,
            'passport_number' => $first_guardian->passport_number,
            'phone_number' => $first_guardian->phone_number,
            'email' => $first_guardian->email,
            'nationality' => resourceToArray(new CountryResource($first_guardian->country)),
            'race' => resourceToArray(new RaceResource($first_guardian->race)),
            'religion' => resourceToArray(new ReligionResource($first_guardian->religion)),
            'education' => resourceToArray(new EducationResource($first_guardian->education)),
            'married_status' => $first_guardian->married_status->value,
            'occupation' => $first_guardian->occupation,
            'occupation_description' => $first_guardian->occupation_description,
            'translations' => $first_guardian->translations,
            'has_user_account' => $first_guardian->hasUserAccount(),
            'live_status' => $first_guardian->live_status->value,
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(GuardianService::class, function (MockInterface $mock) {
        $guardian = Guardian::factory()->create();

        $mock->shouldReceive('getAllPaginatedGuardians')
            ->once()
            ->andReturn(new LengthAwarePaginator([$guardian], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(GuardianService::class, function (MockInterface $mock) {
        $guardians = Guardian::factory(2)->create();

        $mock->shouldReceive('getAllGuardians')->once()->andReturn($guardians);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

