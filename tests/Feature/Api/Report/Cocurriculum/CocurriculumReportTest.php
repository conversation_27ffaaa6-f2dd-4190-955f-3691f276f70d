<?php

use App\Enums\CardStatus;
use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Enums\Gender;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectContractor;
use App\Models\Contractor;
use App\Models\Grade;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.cocurriculum';

    $this->contractors = Contractor::factory(2)->create();

    $this->cards = Card::factory(3)
        ->state(new Sequence(
            [
                'userable_type' => Contractor::class,
                'userable_id' => $this->contractors[0]->id,
                'status' => CardStatus::ACTIVE,
            ],
            [
                'userable_type' => Contractor::class,
                'userable_id' => $this->contractors[0]->id,
                'status' => CardStatus::INACTIVE,
            ],
            [
                'userable_type' => Contractor::class,
                'userable_id' => $this->contractors[1]->id,
                'status' => CardStatus::ACTIVE,
            ],
        ))
        ->create();

    $this->semesterSettings = SemesterSetting::factory(2)
        ->state(new Sequence(
            [
                'name' => 'semester 1',
                'from' => '2024-01-01',
                'to' => '2024-07-31',
            ],
            [
                'name' => 'semester 2',
                'from' => '2024-08-01',
                'to' => '2025-01-31',
            ],
        ))
        ->create();

    $this->classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name->en' => 'Class 1',
        ],
        [
            'name->en' => 'Class 2',
        ],
        [
            'name->en' => 'Class 3',
        ],
    ))->create();

    $this->semesterClasses = SemesterClass::factory(3)
        ->state(new Sequence(
            [
                'class_id' => $this->classes[0]->id,
                'semester_setting_id' => $this->semesterSettings[0]->id,
            ],
            [
                'class_id' => $this->classes[1]->id,
                'semester_setting_id' => $this->semesterSettings[0]->id,
            ],
            [
                'class_id' => $this->classes[2]->id,
                'semester_setting_id' => $this->semesterSettings[1]->id,
            ],
        ))
        ->create();

    $this->classSubjects = ClassSubject::factory(3)
        ->state(new Sequence(
            [
                'semester_class_id' => $this->semesterClasses[0]->id,
            ],
            [
                'semester_class_id' => $this->semesterClasses[1]->id,
            ],
            [
                'semester_class_id' => $this->semesterClasses[2]->id,
            ],
        ))
        ->create();

    $this->classSubjectContractors = ClassSubjectContractor::factory(3)
        ->state(new Sequence(
            [
                'class_subject_id' => $this->classSubjects[0]->id,
                'contractor_id' => $this->contractors[0]->id,
            ],
            [
                'class_subject_id' => $this->classSubjects[1]->id,
                'contractor_id' => $this->contractors[0]->id,
            ],
            [
                'class_subject_id' => $this->classSubjects[2]->id,
                'contractor_id' => $this->contractors[1]->id,
            ],
        ))
        ->create();
    SnappyPdf::fake();
    Excel::fake();
});

test('reportByTrainerDetail return data', function () {
    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semesterSettings[0]->id,
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.trainer-detail", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'name' => $this->contractors[0]->getTranslations('name'),
                'trainer_number' => $this->contractors[0]->contractor_number,
                'phone_number' => $this->contractors[0]->phone_number,
                'department' => $this->contractors[0]->department->value,
                'class_names' => [
                    $this->classes[0]->name,
                    $this->classes[1]->name,
                ],
                'card_number1' => $this->cards[0]->card_number,
                'card_number2' => $this->cards[0]->card_number2,
                'card_number3' => $this->cards[0]->card_number3,
            ])
        );
});

test('reportByTrainerDetail return excel', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semesterSettings[0]->id,
        'export_type' => ExportType::EXCEL->value
    ];

    $filename = 'cocurriculum-report-trainer-detail';
    $extension = '.xlsx';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.trainer-detail', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportByTrainerDetail return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semesterSettings[0]->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'cocurriculum-report-trainer-detail';
    $extension = '.pdf';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.trainer-detail', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('studentStatisticReportBySemester return excel', function () {
    Grade::truncate();
    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(3)->create(new Sequence(
        [
            'name->en' => 'Junior 1',
            'name->zh' => '初一',
            'sequence' => 0,
        ],
        [
            'name->en' => 'Junior 2',
            'name->zh' => '初二',
            'sequence' => 1,
        ],
        [
            'name->en' => 'Junior 3',
            'name->zh' => '初三',
            'sequence' => 2,
        ],
    ));

    $active_society_class = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
        'is_active' => true,
        'grade_id' => $grades[0]->id,
        'name->en' => 'Drama Club',
        'name->zh' => '戏剧',
    ]);

    $active_society_class2 = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
        'is_active' => true,
        'grade_id' => $grades[0]->id,
        'name->en' => 'Art Club',
        'name->zh' => '美术',
    ]);

    $semester_classes_society = SemesterClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $active_society_class->id
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $active_society_class2->id
        ],
    ));

    $primary_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
    ]);

    $primary_class2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[2]->id,
    ]);

    $semester_classes_primary = SemesterClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $primary_class->id
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $primary_class2->id
        ],
    ));

    // first society semester class (4 Male, 3 Female, 3 Junior 2, 4 Junior 3)
    for ($i = 1; $i <= 4; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::MALE->value,
            'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[0]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString()
        ]);
        // 2 Junior 2, 2 Junior 3
        if ($i <= 2) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }
    for ($i = 1; $i <= 3; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::FEMALE->value,
            'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[0]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString()
        ]);
        // 1 Junior 2, 2 Junior 3
        if ($i <= 1) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }

    // second society semester class (2 Male, 4 Female, 4 Junior 2, 2 Junior 3)
    for ($i = 1; $i <= 2; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::MALE->value,
            'admission_grade_id' => $semester_classes_primary[1]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[1]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString()
        ]);
        // 1 Junior 2, 1 Junior 3
        if ($i <= 1) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }
    for ($i = 1; $i <= 4; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::FEMALE->value,
            'admission_grade_id' => $semester_classes_primary[1]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[1]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString(),

        ]);
        // 3 Junior 2, 1 Junior 3
        if ($i <= 3) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $semester_setting->id,
        'export_type' => ExportType::EXCEL->value
    ];

    $filename = 'cocurriculum-student-statistic-report-by-semester';
    $extension = '.xlsx';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-statistic-report-by-semester', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

