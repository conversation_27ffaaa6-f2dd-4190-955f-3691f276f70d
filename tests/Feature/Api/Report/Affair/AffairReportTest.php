<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ExportType;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\BookLanguageSeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        BookLanguageSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.attendances';

    $this->student_late = Student::factory()->create();
    $this->student_attendance_late = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_late->id,
        'date' => '2024-12-01',
        'check_in_datetime' => '2024-12-01 08:00:00',
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->student_on_time = Student::factory()->create();
    $this->student_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_on_time->id,
        'date' => '2024-12-02',
        'check_in_datetime' => '2024-12-01 07:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->student_absent = Student::factory()->create();
    $this->student_attendance_absent = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    $this->teacher_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Employee::class,
        'attendance_recordable_id' => Employee::factory(),
        'date' => '2024-12-03',
        'check_in_datetime' => '2024-12-01 07:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => '2024-12-01 15:00:00',
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->semester_setting1 = SemesterSetting::factory()->create(['id' => 100]);
    $this->semester_setting1_semester_class = SemesterClass::factory()->create([
        'id' => 100,
        'semester_setting_id' => $this->semester_setting1->id,
    ]);
    $this->semester_setting1_semester_class2 = SemesterClass::factory()->create([
        'id' => 200,
        'semester_setting_id' => $this->semester_setting1->id,
    ]);

    $this->semester_setting2 = SemesterSetting::factory()->create(['id' => 200]);
    $this->semester_setting2_semester_class = SemesterClass::factory()->create([
        'id' => 300,
        'semester_setting_id' => $this->semester_setting2->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class->id,
        'student_id' => $this->student_late->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'student_id' => $this->student_on_time->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $this->student_absent->id,
        'is_active' => true,
    ]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    SnappyPdf::fake();
});

test('studentAttendanceReport return data', function () {
    $payload = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'semester_setting_id' => $this->semester_setting2->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'status' => AttendanceStatus::ABSENT->value,
        'class_type' => 'PRIMARY'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.student-attendance-report", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting2_semester_class->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_absent->student_number,
                                'student_name' => $this->student_absent->name,
                                'attendance_time_in' => null,
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::ABSENT->value,
                                'attendance_reason' => null
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 0,
                        'total_absent' => 1,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('studentAttendanceReport return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'semester_setting_id' => $this->semester_setting2->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'export_type' => ExportType::PDF->value,
        'status' => AttendanceStatus::ABSENT->value,
        'class_type' => 'PRIMARY'
    ];

    $filename = 'student-attendance-report';
    $extension = '.pdf';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-attendance-report', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('studentAbsentReport return pdf', function () {

    $filters = [
        'report_language' => 'en',
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'export_type' => ExportType::PDF->value,
        'absent_count' => 0,
        'class_type' => 'PRIMARY'
    ];

    $filename = 'student-attendance-report';
    $extension = '.pdf';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-absent-report', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});
