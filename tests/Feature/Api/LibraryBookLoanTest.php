<?php

use App\Enums\BookLoanSettingType;
use App\Enums\BookStatus;
use App\Enums\LibraryBookLoanPaymentStatus;
use App\Enums\LibraryBookLoanStatus;
use App\Enums\LibraryMemberType;
use App\Enums\LibraryPenaltyPaymentMethod;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Http\Resources\SimpleBookResource;
use App\Http\Resources\SimpleLibraryMemberResource;
use App\Models\Book;
use App\Models\BookLoanSetting;
use App\Models\Config;
use App\Models\Currency;
use App\Models\Employee;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\LibraryPaymentTransaction;
use App\Models\Student;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(LibraryBookLoan::class)->getTable();
    $this->book_table = resolve(Book::class)->getTable();
    $this->library_payment_transaction_table = resolve(LibraryPaymentTransaction::class)->getTable();
    $this->wallet_table = resolve(Wallet::class)->getTable();
    $this->wallet_transaction_table = resolve(WalletTransaction::class)->getTable();

    $this->routeNamePrefix = 'libraries.book-loans';

    $config = [
        Config::LIBRARY_BORROW_LIMIT_EMPLOYEE => 3,
        Config::LIBRARY_BORROW_LIMIT_STUDENT => 2,
        Config::LIBRARY_BORROW_LIMIT_LIBRARIAN => 5,
        Config::LIBRARY_BORROW_LIMIT_OTHER => 4,

        Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE => false,
        Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT => true,
        Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN => false,
        Config::BORROW_WITH_UNRETURNED_BOOK_OTHER => false,

        Config::LIBRARY_FINE_PER_DAY_EMPLOYEE => 0.1,
        Config::LIBRARY_FINE_PER_DAY_STUDENT => 0.2,
        Config::LIBRARY_FINE_PER_DAY_LIBRARIAN => 0.1,
        Config::LIBRARY_FINE_PER_DAY_OTHER => 0,
    ];

    foreach ($config as $key => $value) {
        Config::factory()->create([
            'key' => $key,
            'value' => $value
        ]);
    }
});

test('index', function () {
    $books = Book::factory(2)->create();
    $members = LibraryMember::factory(2)->create();

    $book_loans = LibraryBookLoan::factory(3)->state(new Sequence(
        [
            'member_id' => $members[0]->id,
            'book_id' => $books[0]->id,
            'due_date' => now()->subDays(2),
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
            'loan_date' => now()->subDays(8),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(2),
            'loan_status' => LibraryBookLoanStatus::RETURNED,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->subDays(3),
        ],
        [
            'member_id' => $members[1]->id,
            'book_id' => $books[1]->id,
            'due_date' => now()->addDays(3),
            'loan_status' => LibraryBookLoanStatus::LOST,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID,
            'loan_date' => now()->addDays(1),
        ]
    ))->create();

    //Filter by ids
    $payload = [
        'ids' => [$book_loans[1]->id, $book_loans[2]->id],
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[2]->id),
        );

    //Filter by member_id
    $payload = [
        'member_id' => $members[0]->id,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[0]->id),
        );

    //Filter by book_id
    $payload = [
        'book_id' => $books[1]->id,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[2]->id),
        );

    //Filter by status
    $payload = [
        'loan_status' => LibraryBookLoanStatus::BORROWED,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[0]->id),
        );

    //Filter by penalty_payment_status
    $payload = [
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[0]->id),
        );

    //Filter by is_overdue
    $payload = [
        'is_overdue' => true,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[0]->id),
        );

    //Filter by is_overdue
    $payload = [
        'is_overdue' => false,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[2]->id),
        );

    //Filter by period loan date
    $start_loan_date = now()->subDays(4)->format('Y-m-d');
    $end_loan_date = now()->addDays(1)->format('Y-m-d');
    $payload = [
        'period_loan_date_from' => $start_loan_date,
        'period_loan_date_to' => $end_loan_date,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[2]->id),
        );

    // only period_loan_date_from
    $start_loan_date = now()->format('Y-m-d'); // start from now
    $payload = [
        'period_loan_date_from' => $start_loan_date,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[2]->id),
        );

    // only period_loan_date_to
    $end_loan_date = now()->format('Y-m-d');
    $payload = [
        'period_loan_date_to' => $end_loan_date,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[0]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
        );

    //Filter by period due date
    $start_due_date = now()->addDays(2)->format('Y-m-d');
    $end_due_date = now()->addDays(4)->format('Y-m-d');
    $payload = [
        'period_due_date_from' => $start_due_date,
        'period_due_date_to' => $end_due_date,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[2]->id),
        );

    // only period_due_date_from
    $start_due_date = now()->format('Y-m-d'); // start from now
    $payload = [
        'period_due_date_from' => $start_due_date,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[2]->id),
        );

    // only period_due_date_to
    $end_due_date = now()->addDays(2)->format('Y-m-d');
    $payload = [
        'period_due_date_to' => $end_due_date,
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $book_loans[0]->id),
            fn($data) => $data->toHaveKey('id', $book_loans[1]->id),
        );

    // check data returned
    $payload = [
        'ids' => [$book_loans[0]->id],
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    $book_loan = LibraryBookLoan::find($book_loans[0]->id);
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toMatchArray([
            'id' => $book_loan->id,
            'book' => resourceToArray(new SimpleBookResource($book_loan->book)),
            'member' => resourceToArray(new SimpleLibraryMemberResource($book_loan->member)),
            'loan_date' => $book_loan->loan_date ? $book_loan->loan_date->format('Y-m-d') : null,
            'due_date' => $book_loan->due_date ? $book_loan->due_date->format('Y-m-d') : null,
            'return_date' => $book_loan->return_date ? $book_loan->return_date->format('Y-m-d') : null,
            'penalty_overdue_amount' => $book_loan->penalty_overdue_amount,
            'penalty_lost_amount' => $book_loan->penalty_lost_amount,
            'penalty_total_fine_amount' => $book_loan->penalty_total_fine_amount,
            'penalty_paid_amount' => $book_loan->penalty_paid_amount,
            'penalty_payment_status' => $book_loan->penalty_payment_status,
            'loan_status' => $book_loan->loan_status->value,
            'remarks' => $book_loan->remarks
        ]);
});

test('store', function () {
    //validation required failed
    $payload = [];
    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toHaveKey('member_id.0', 'The member id field is required.')
        ->toHaveKey('books.0', 'The books field is required.');

    //Validation valid failed
    $payload = [
        'member_id' => 9999,
        'books' => [
            [
                'id' => 9999,
                'due_date' => now()->addDays(12)
            ]
        ]
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->and($response['error']['member_id'][0])->toBe('The selected member id is invalid.')
        ->and($response['error']['books.0.id'][0])->toBe('The selected books.0.id is invalid.');

    //store success
    $this->assertDatabaseCount($this->table, 0);

    $books = Book::factory(6)->state(new Sequence(
        [
            'book_no' => "1234",
        ],
        [
            'book_no' => "1235",
        ],
        [
            'book_no' => "1236",
        ],
        [
            'book_no' => "1237",
        ],
        [
            'book_no' => "1239",
        ],
        [
            'book_no' => "12310",
        ],
    ))->create();

    foreach ($books as $book) {
        BookLoanSetting::factory(4)
            ->state(new Sequence(
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::STUDENT->value,
                    'loan_period_day' => 3,
                    'can_borrow' => true
                ],
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::EMPLOYEE->value,
                    'loan_period_day' => 4,
                    'can_borrow' => true
                ],
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::LIBRARIAN->value,
                    'loan_period_day' => 5,
                    'can_borrow' => true
                ],
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::OTHERS->value,
                    'loan_period_day' => 6,
                    'can_borrow' => true
                ],
            ))
            ->create();
    }

    $book_lost = Book::factory()->create([
        'book_no' => "1238",
        'status' => BookStatus::LOST
    ]);

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book_lost->id,
                'type' => BookLoanSettingType::STUDENT->value,
                'loan_period_day' => 3,
                'can_borrow' => false
            ],
            [
                'book_id' => $book_lost->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
                'loan_period_day' => 4,
                'can_borrow' => false
            ],
            [
                'book_id' => $book_lost->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
                'loan_period_day' => 5,
                'can_borrow' => false
            ],
            [
                'book_id' => $book_lost->id,
                'type' => BookLoanSettingType::OTHERS->value,
                'loan_period_day' => 6,
                'can_borrow' => false
            ],
        ))
        ->create();

    $book_with_setting_cant_borrow = Book::factory()->create([
        'book_no' => "12311",
    ]);

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book_with_setting_cant_borrow->id,
                'type' => BookLoanSettingType::STUDENT->value,
                'loan_period_day' => 3,
                'can_borrow' => false
            ],
            [
                'book_id' => $book_with_setting_cant_borrow->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
                'loan_period_day' => 4,
                'can_borrow' => false
            ],
            [
                'book_id' => $book_with_setting_cant_borrow->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
                'loan_period_day' => 5,
                'can_borrow' => false
            ],
            [
                'book_id' => $book_with_setting_cant_borrow->id,
                'type' => BookLoanSettingType::OTHERS->value,
                'loan_period_day' => 6,
                'can_borrow' => false
            ],
        ))
        ->create();

    //member = employee, loan_period_day = 4, borrow limit = 3, borrow with unreturned book = false
    $employee = Employee::factory()->create();

    $member_employee = LibraryMember::factory()->create([
        'type' => LibraryMemberType::EMPLOYEE,
        'userable_type' => Employee::class,
        'userable_id' => $employee->id,
        'borrow_limit' => 3
    ]);
    //success borrow

    $payload = [
        'member_id' => $member_employee->id,
        'books' => [
            [
                'id' => $books[0]->id,
                'due_date' => now()->addDays(4)
            ],
            [
                'id' => $books[1]->id,
                'due_date' => now()->addDays(4)
            ],
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($books, $member_employee) {
                $data->toMatchArray([
                    'loan_status' => LibraryBookLoanStatus::BORROWED->value,
                    'loan_date' => now()->format('Y-m-d'),
                    'due_date' => now()->addDays(4)->format('Y-m-d'),
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member_employee)),
                ]);

                $data->book->toMatchArray([
                    'id' => $books[0]->id,
                    'status' => BookStatus::BORROWED->value,
                ]);
            },
            function ($data) use ($books, $member_employee) {
                $data->toMatchArray([
                    'loan_status' => LibraryBookLoanStatus::BORROWED->value,
                    'loan_date' => now()->format('Y-m-d'),
                    'due_date' => now()->addDays(4)->format('Y-m-d'),
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member_employee)),
                ]);

                $data->book->toMatchArray([
                    'id' => $books[1]->id,
                    'status' => BookStatus::BORROWED->value,
                ]);
            },
        );

    //book loan table inserted 2 records
    $this->assertDatabaseCount($this->table, 2);

    $this->assertDatabaseHas($this->table, [
        'book_id' => $books[0]->id,
        'member_id' => $member_employee->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED->value,
        'loan_date' => now()->format('Y-m-d'),
        'due_date' => now()->addDays(4)->format('Y-m-d'),
    ]);

    $this->assertDatabaseHas($this->table, [
        'book_id' => $books[1]->id,
        'member_id' => $member_employee->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED->value,
        'loan_date' => now()->format('Y-m-d'),
        'due_date' => now()->addDays(4)->format('Y-m-d'),
    ]);

    //books status updated from AVAILABLE to BORROWED
    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[0]->id,
        'status' => BookStatus::BORROWED->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[1]->id,
        'status' => BookStatus::BORROWED->value
    ]);

    //Not borrowed books still in AVAILABLE status
    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[2]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[3]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[4]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[5]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $book_lost->id,
        'status' => BookStatus::LOST->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $book_with_setting_cant_borrow->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    //hit borrow limit
    $payload = [
        'member_id' => $member_employee->id,
        'books' => [
            [
                'id' => $books[2]->id,
                'due_date' => now()->addDays(4)->format('Y-m-d'),
            ],
            [
                'id' => $books[3]->id,
                'due_date' => now()->addDays(4)->format('Y-m-d'),
            ],
            [
                'id' => $books[4]->id,
                'due_date' => now()->addDays(4)->format('Y-m-d'),
            ],
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)
        ->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('Member has reached the maximum borrow books limit.');


    //member = student, loan_period_day = 3, borrow limit = 2, borrow with unreturned book = true
    $student = Student::factory()->create();

    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'borrow_limit' => 2
    ]);

    //book status = lost not able to borrow
    $payload = [
        'member_id' => $member_student->id,
        'books' => [
            [
                'id' => $book_lost->id,
                'due_date' => now()->addDays(4)->format('Y-m-d'),
            ]
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)
        ->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('Book [No:1238] cannot be borrowed.');

    //book_loan_setting.can_borrow = false not able to borrow
    $payload = [
        'member_id' => $member_student->id,
        'books' => [
            [
                'id' => $book_with_setting_cant_borrow->id,
                'due_date' => now()->addDays(4)->format('Y-m-d'),
            ]
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)
        ->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('Book [No:12311] cannot be borrowed.');

    //book is borrowed by other
    $payload = [
        'member_id' => $member_student->id,
        'books' => [
            [
                'id' => $books[0]->id,
                'due_date' => now()->addDays(4)->format('Y-m-d'),
            ]
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)
        ->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('Book [No:1234] cannot be borrowed.');

    //success borrow
    $payload = [
        'member_id' => $member_student->id,
        'books' => [
            [
                'id' => $books[2]->id,
                'due_date' => now()->addDays(3)->format('Y-m-d'),
            ],
            [
                'id' => $books[3]->id,
                'due_date' => now()->addDays(3)->format('Y-m-d'),
            ]
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($books, $member_student) {
                $data->toMatchArray([
                    'loan_status' => LibraryBookLoanStatus::BORROWED->value,
                    'loan_date' => now()->format('Y-m-d'),
                    'due_date' => now()->addDays(3)->format('Y-m-d'),
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member_student)),
                ]);

                $data->book->toMatchArray([
                    'id' => $books[2]->id,
                    'status' => BookStatus::BORROWED->value,
                ]);
            },
            function ($data) use ($books, $member_student) {
                $data->toMatchArray([
                    'loan_status' => LibraryBookLoanStatus::BORROWED->value,
                    'loan_date' => now()->format('Y-m-d'),
                    'due_date' => now()->addDays(3)->format('Y-m-d'),
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member_student)),
                ]);

                $data->book->toMatchArray([
                    'id' => $books[3]->id,
                    'status' => BookStatus::BORROWED->value,
                ]);
            }
        );

    //book loan table inserted 2 records
    $this->assertDatabaseCount($this->table, 4);

    $this->assertDatabaseHas($this->table, [
        'book_id' => $books[2]->id,
        'member_id' => $member_student->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED->value,
        'loan_date' => now()->format('Y-m-d'),
        'due_date' => now()->addDays(3)->format('Y-m-d'),
    ]);

    $this->assertDatabaseHas($this->table, [
        'book_id' => $books[3]->id,
        'member_id' => $member_student->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED->value,
        'loan_date' => now()->format('Y-m-d'),
        'due_date' => now()->addDays(3)->format('Y-m-d'),
    ]);

    //books status updated from AVAILABLE to BORROWED
    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[0]->id,
        'status' => BookStatus::BORROWED->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[1]->id,
        'status' => BookStatus::BORROWED->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[2]->id,
        'status' => BookStatus::BORROWED->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[3]->id,
        'status' => BookStatus::BORROWED->value
    ]);

    //Not borrowed books still in AVAILABLE status
    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[4]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[5]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $book_lost->id,
        'status' => BookStatus::LOST->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $book_with_setting_cant_borrow->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    //able to borrow with hit limit
    $payload = [
        'member_id' => $member_student->id,
        'books' => [
            [
                'id' => $books[4]->id,
                'due_date' => now()->addDays(3)->format('Y-m-d'),
            ]
        ],
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($books, $member_student) {
                $data->toMatchArray([
                    'loan_status' => LibraryBookLoanStatus::BORROWED->value,
                    'loan_date' => now()->format('Y-m-d'),
                    'due_date' => now()->addDays(3)->format('Y-m-d'),
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member_student)),
                ]);

                $data->book->toMatchArray([
                    'id' => $books[4]->id,
                    'status' => BookStatus::BORROWED->value,
                ]);
            }
        );
});

test('return - validation failed', function () {

    //validation required failed
    $payload = [];
    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toHaveKey('payment_method.0', 'The payment method field must be present.')
        ->toHaveKey('book_loans.0', 'The book loans field is required.');

    //Validation valid failed
    $payload = [
        'payment_method' => 'TEST',
        'book_loans' => [
            [
                'id' => 999,
                'is_lost' => 'TEST',
                'penalty_lost_amount' => 'TEST',
                'penalty_paid_amount' => 'TEST',
                'penalty_payment_status' => 'TEST'
            ]
        ]
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toHaveKey('payment_method.0', 'The selected payment method is invalid.')
        ->and($response['error']['book_loans.0.id'])->toHaveKey('0', 'The selected book_loans.0.id is invalid.')
        ->and($response['error']['book_loans.0.is_lost'])->toHaveKey('0', 'The book_loans.0.is_lost field must be true or false.')
        ->and($response['error']['book_loans.0.penalty_lost_amount'])->toHaveKey('0', 'The book_loans.0.penalty_lost_amount field must be a number.')
        ->and($response['error']['book_loans.0.penalty_paid_amount'])->toHaveKey('0', 'The book_loans.0.penalty_paid_amount field must be a number.');

});


test('return - failed book not borrowed', function () {

    //book is not borrowed
    $book_already_returned = Book::factory()->create([
        'status' => BookStatus::AVAILABLE,
        'book_no' => 'A12345'
    ]);

    $book_loan_already_returned = LibraryBookLoan::factory()->create([
        'book_id' => $book_already_returned->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED
    ]);

    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::CASH->value,
        'book_loans' => [
            [
                'id' => $book_loan_already_returned->id,
                'is_lost' => false,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 0,
            ]
        ]
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('Book [No:A12345] is not borrowed.');

});


test('return - success book returned without fine', function () {

    //book returned without fine
    $this->assertDatabaseCount($this->library_payment_transaction_table, 0);

    $user = User::factory()->create();
    $currency = Currency::factory()->create([
        'code' => config('school.currency_code')
    ]);

    $employee = Employee::factory()->create([
        'user_id' => $user->id,
    ]);

    $member = LibraryMember::factory()->create([
        'userable_id' => $employee->id,
        'userable_type' => Employee::class,
        'type' => LibraryMemberType::EMPLOYEE
    ]);

    $books = Book::factory(2)->state(new Sequence(
        [
            'book_no' => "B1234",
        ],
        [
            'book_no' => "B1235",
        ]
    ))->create();

    $book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $books[0]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(3)->format('Y-m-d'),
            'penalty_overdue_amount' => 0,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ],
        [
            'book_id' => $books[1]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(3)->format('Y-m-d'),
            'penalty_overdue_amount' => 0,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ]
    ))->create();

    $payload = [
        'payment_method' => null,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => false,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 0,
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 0,
            ],
        ]
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($books, $book_loans, $member) {
                $data->toMatchArray([
                    'id' => $book_loans[0]->id,
                    'loan_status' => LibraryBookLoanStatus::RETURNED->value,
                    'return_date' => now()->format('Y-m-d'),
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member)),
                    'penalty_overdue_amount' => 0,
                    'penalty_paid_amount' => 0,
                    'penalty_total_fine_amount' => 0,
                    'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value,
                ]);

                $data->book->toMatchArray([
                    'id' => $books[0]->id,
                    'status' => BookStatus::AVAILABLE->value,
                ]);
            },
            function ($data) use ($books, $member, $book_loans) {
                $data->toMatchArray([
                    'id' => $book_loans[1]->id,
                    'loan_status' => LibraryBookLoanStatus::RETURNED->value,
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member)),
                    'penalty_overdue_amount' => 0,
                    'penalty_paid_amount' => 0,
                    'penalty_total_fine_amount' => 0,
                    'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value
                ]);

                $data->book->toMatchArray([
                    'id' => $books[1]->id,
                    'status' => BookStatus::AVAILABLE->value,
                ]);
            }
        );

    //check db record
    $this->assertDatabaseCount($this->library_payment_transaction_table, 0);

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loans[0]->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED->value,
        'return_date' => now()->format('Y-m-d'),
        'member_id' => $member->id,
        'penalty_overdue_amount' => 0,
        'penalty_paid_amount' => 0,
        'penalty_total_fine_amount' => 0,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value,
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[0]->id,
        'status' => BookStatus::AVAILABLE->value,
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loans[1]->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED->value,
        'member_id' => $member->id,
        'penalty_overdue_amount' => 0,
        'penalty_paid_amount' => 0,
        'penalty_total_fine_amount' => 0,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[1]->id,
        'status' => BookStatus::AVAILABLE->value,
    ]);

});

test('return - success book returned with fine - CASH', function () {
    $user = User::factory()->create();
    $currency = Currency::factory()->create([
        'code' => config('school.currency_code')
    ]);

    $employee = Employee::factory()->create([
        'user_id' => $user->id,
    ]);

    $member = LibraryMember::factory()->create([
        'userable_id' => $employee->id,
        'userable_type' => Employee::class,
        'type' => LibraryMemberType::EMPLOYEE
    ]);

    $books = Book::factory(2)->state(new Sequence(
        [
            'book_no' => "B1234",
        ],
        [
            'book_no' => "B1235",
        ]
    ))->create();

    //book returned with fine - CASH
    $book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $books[0]->id,
            'member_id' => $member->id,
            'due_date' => now()->subDays(3)->format('Y-m-d'),
            'penalty_overdue_amount' => 0.6,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID
        ],
        [
            'book_id' => $books[1]->id,
            'member_id' => $member->id,
            'due_date' => now()->subDays(2)->format('Y-m-d'),
            'penalty_overdue_amount' => 0.2,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID
        ]
    ))->create();

    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::CASH->value,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 0.8,
                'penalty_overdue_amount' => 0.8,
                'remarks' => 'remark 1',
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_paid_amount' => 0.2,
                'penalty_overdue_amount' => 0.2,
                'remarks' => 'remark 2',
            ],
        ]
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($books, $member, $book_loans) {
                $data->toMatchArray([
                    'id' => $book_loans[0]->id,
                    'loan_status' => LibraryBookLoanStatus::RETURNED->value,
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member)),
                    'penalty_overdue_amount' => 0.8,
                    'penalty_paid_amount' => 0.8,
                    'penalty_total_fine_amount' => 0.8,
                    'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value,
                    'remarks' => 'remark 1',
                ]);

                $data->book->toMatchArray([
                    'id' => $books[0]->id,
                    'status' => BookStatus::AVAILABLE->value,
                ]);
            },
            function ($data) use ($books, $member, $book_loans) {
                $data->toMatchArray([
                    'id' => $book_loans[1]->id,
                    'loan_status' => LibraryBookLoanStatus::RETURNED->value,
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member)),
                    'penalty_overdue_amount' => 0.2,
                    'penalty_paid_amount' => 0.2,
                    'penalty_total_fine_amount' => 0.2,
                    'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value,
                    'remarks' => 'remark 2',
                ]);

                $data->book->toMatchArray([
                    'id' => $books[1]->id,
                    'status' => BookStatus::AVAILABLE->value,
                ]);
            }
        );

    //check db record
    $this->assertDatabaseCount($this->library_payment_transaction_table, 2);

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loans[0]->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED->value,
        'member_id' => $member->id,
        'penalty_overdue_amount' => 0.8,
        'penalty_paid_amount' => 0.8,
        'penalty_total_fine_amount' => 0.8,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value,
        'remarks' => 'remark 1',
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[0]->id,
        'status' => BookStatus::AVAILABLE->value,
    ]);

    $this->assertDatabaseHas($this->library_payment_transaction_table, [
        'member_id' => $member->id,
        'book_loan_id' => $book_loans[0]->id,
        'payment_method' => LibraryPenaltyPaymentMethod::CASH->value,
        'payment_amount' => 0.8,
        'description' => 'remark 1',
        'wallet_transaction_id' => null,
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loans[1]->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED->value,
        'member_id' => $member->id,
        'penalty_overdue_amount' => 0.2,
        'penalty_paid_amount' => 0.2,
        'penalty_total_fine_amount' => 0.2,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value,
        'remarks' => 'remark 2',
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[1]->id,
        'status' => BookStatus::AVAILABLE->value,
    ]);

    $this->assertDatabaseHas($this->library_payment_transaction_table, [
        'member_id' => $member->id,
        'book_loan_id' => $book_loans[1]->id,
        'payment_method' => LibraryPenaltyPaymentMethod::CASH->value,
        'payment_amount' => 0.2,
        'description' => 'remark 2',
        'wallet_transaction_id' => null,
    ]);
});

test('return - failed because overpaid', function () {

    $user = User::factory()->create();
    $currency = Currency::factory()->create([
        'code' => config('school.currency_code')
    ]);

    $employee = Employee::factory()->create([
        'user_id' => $user->id,
    ]);

    $member = LibraryMember::factory()->create([
        'userable_id' => $employee->id,
        'userable_type' => Employee::class,
        'type' => LibraryMemberType::EMPLOYEE
    ]);

    $books = Book::factory(2)->state(new Sequence(
        [
            'book_no' => "B1234",
        ],
        [
            'book_no' => "B1235",
        ]
    ))->create();

    //book lost
    $book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $books[0]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(3)->format('Y-m-d'),
            'penalty_overdue_amount' => 0,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ],
        [
            'book_id' => $books[1]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(2)->format('Y-m-d'),
            'penalty_overdue_amount' => 0,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ]
    ))->create();

    //overpaid
    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::CASH->value,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => true,
                'penalty_lost_amount' => 10.20,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 12.00,
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 0,
            ],
        ]
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)
        ->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe("Book [No:B1234] fine is overpaid.");

});

test('return - failed with wallet insufficient balance', function () {

    $user = User::factory()->create();
    $currency = Currency::factory()->create([
        'code' => config('school.currency_code')
    ]);

    $employee = Employee::factory()->create([
        'user_id' => $user->id,
    ]);

    $member = LibraryMember::factory()->create([
        'userable_id' => $employee->id,
        'userable_type' => Employee::class,
        'type' => LibraryMemberType::EMPLOYEE
    ]);

    $books = Book::factory(2)->state(new Sequence(
        [
            'book_no' => "B1234",
        ],
        [
            'book_no' => "B1235",
        ]
    ))->create();

    //book lost
    $book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $books[0]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(3)->format('Y-m-d'),
            'penalty_overdue_amount' => 0,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ],
        [
            'book_id' => $books[1]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(2)->format('Y-m-d'),
            'penalty_overdue_amount' => 0,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ]
    ))->create();

    //pay via wallet = not enough balance
    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 9,
    ]);

    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => true,
                'penalty_lost_amount' => 20,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 10.20,
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 0,
            ],
        ]
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe("User has insufficient balance.");

});

test('return - success with wallet', function () {

    $user = User::factory()->create();
    $currency = Currency::factory()->create([
        'code' => config('school.currency_code')
    ]);

    $employee = Employee::factory()->create([
        'user_id' => $user->id,
    ]);

    $member = LibraryMember::factory()->create([
        'userable_id' => $employee->id,
        'userable_type' => Employee::class,
        'type' => LibraryMemberType::EMPLOYEE
    ]);

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $currency->id,
        'balance' => 100,
    ]);

    $books = Book::factory(2)->state(new Sequence(
        [
            'book_no' => "B1234",
        ],
        [
            'book_no' => "B1235",
        ]
    ))->create();

    //book lost
    $book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $books[0]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(3)->format('Y-m-d'),
            'penalty_overdue_amount' => 2,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ],
        [
            'book_id' => $books[1]->id,
            'member_id' => $member->id,
            'due_date' => now()->addDays(2)->format('Y-m-d'),
            'penalty_overdue_amount' => 0,
            'penalty_lost_amount' => 0,
            'penalty_paid_amount' => 0,
            'penalty_total_fine_amount' => 0,
            'penalty_payment_status' => LibraryBookLoanPaymentStatus::UNPAID->value
        ]
    ))->create();

    //pay via wallet
    $this->assertDatabaseCount($this->wallet_transaction_table, 0);

    $payload = [
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'book_loans' => [
            [
                'id' => $book_loans[0]->id,
                'is_lost' => true,
                'penalty_lost_amount' => 20,
                'penalty_overdue_amount' => 2,
                'penalty_paid_amount' => 10.20,
            ],
            [
                'id' => $book_loans[1]->id,
                'is_lost' => false,
                'penalty_overdue_amount' => 0,
                'penalty_paid_amount' => 0,
            ],
        ]
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.return"), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($books, $member, $book_loans) {
                $data->toMatchArray([
                    'id' => $book_loans[0]->id,
                    'loan_status' => LibraryBookLoanStatus::LOST->value,
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member)),
                    'penalty_overdue_amount' => 2,
                    'penalty_paid_amount' => 10.20,
                    'penalty_lost_amount' => 20,
                    'penalty_total_fine_amount' => 22,
                    'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value
                ]);

                $data->book->toMatchArray([
                    'id' => $books[0]->id,
                    'status' => BookStatus::LOST->value,
                ]);
            },
            function ($data) use ($books, $member, $book_loans) {
                $data->toMatchArray([
                    'id' => $book_loans[1]->id,
                    'loan_status' => LibraryBookLoanStatus::RETURNED->value,
                    'member' => resourceToArray(new SimpleLibraryMemberResource($member)),
                    'penalty_overdue_amount' => 0,
                    'penalty_paid_amount' => 0,
                    'penalty_lost_amount' => 0,
                    'penalty_total_fine_amount' => 0,
                    'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value
                ]);

                $data->book->toMatchArray([
                    'id' => $books[1]->id,
                    'status' => BookStatus::AVAILABLE->value,
                ]);
            }
        );

    //check db record
    $this->assertDatabaseCount($this->wallet_transaction_table, 1);
    $this->assertDatabaseCount($this->library_payment_transaction_table, 1);

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loans[0]->id,
        'loan_status' => LibraryBookLoanStatus::LOST->value,
        'member_id' => $member->id,
        'penalty_overdue_amount' => 2,
        'penalty_paid_amount' => 10.20,
        'penalty_lost_amount' => 20,
        'penalty_total_fine_amount' => 22,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value
    ]);

    $this->assertDatabaseHas($this->library_payment_transaction_table, [
        'member_id' => $member->id,
        'book_loan_id' => $book_loans[0]->id,
        'payment_method' => LibraryPenaltyPaymentMethod::WALLET->value,
        'payment_amount' => 10.20,
        'wallet_transaction_id' => WalletTransaction::orderBy('id', 'desc')->first()->id,
    ]);

    $this->assertDatabaseHas($this->wallet_transaction_table, [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::TRANSACTION->value,
        'status' => WalletTransactionStatus::SUCCESS->value,
        'wallet_transactable_type' => LibraryBookLoan::class,
        'wallet_transactable_id' => $book_loans[0]->id,
        'userable_type' => get_class($member->userable),
        'userable_id' => $member->userable->id,
        'total_amount' => -10.20,
        'amount_before_tax' => -10.20,
        'amount_after_tax' => -10.20,
        'balance_before' => 100,
        'balance_after' => 100 - 10.20,
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[0]->id,
        'status' => BookStatus::LOST->value,
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $book_loans[1]->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED->value,
        'member_id' => $member->id,
        'penalty_overdue_amount' => 0,
        'penalty_paid_amount' => 0,
        'penalty_lost_amount' => 0,
        'penalty_total_fine_amount' => 0,
        'penalty_payment_status' => LibraryBookLoanPaymentStatus::PAID->value
    ]);

    $this->assertDatabaseHas($this->book_table, [
        'id' => $books[1]->id,
        'status' => BookStatus::AVAILABLE->value,
    ]);

    $this->assertDatabaseHas($this->wallet_table, [
        'id' => $wallet->id,
        'balance' => 100 - 10.20
    ]);
});

test('extend', function () {
    $book = Book::factory()->create([
        'book_no' => 'A1234'
    ]);

    $book_loan = LibraryBookLoan::factory()->create([
        'book_id' => $book->id,
        'due_date' => now()->format('Y-m-d'),
        'loan_status' => LibraryBookLoanStatus::RETURNED
    ]);

    //validation required failed
    $payload = [];
    $response = $this->postJson(route("$this->routeNamePrefix.extend", ['book_loan' => $book_loan->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toHaveKey('book_loans.0', 'The book loans field is required.');

    //validation invalid
    $payload = [
        'book_loans' => [
            [
                'id' => 9999,
                'due_date' => 'dsadasd',
            ]
        ]
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.extend", ['book_loan' => $book_loan->id]), $payload)->json();


    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['book_loans.0.id'][0])->toBe('The selected book_loans.0.id is invalid.')
        ->and($response['error']['book_loans.0.due_date'][0])->toBe('The book_loans.0.due_date field must be a valid date.');

    //Book is returned and cant be extend
    $payload = [
        'book_loans' => [
            [
                'id' => $book_loan->id,
                'due_date' => now()->addDays(14)->format('Y-m-d'),
            ]
        ]
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.extend", ['book_loan' => $book_loan->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toBe('Book [No:A1234] is not borrowed.');

    //Success extend
    $book_loan = LibraryBookLoan::factory()->create([
        'book_id' => $book->id,
        'due_date' => \Carbon\Carbon::parse('2024-09-01')->format('Y-m-d'),
        'loan_status' => LibraryBookLoanStatus::BORROWED
    ]);

    $new_due_date = \Carbon\Carbon::parse('2024-09-10')->format('Y-m-d');

    $payload = [
        'book_loans' => [
            [
                'id' => $book_loan->id,
                'due_date' => $new_due_date,
            ]
        ]
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.extend", ['book_loan' => $book_loan->id]), $payload)->json();

    expect($response)
        ->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->table, [
        'book_id' => $book->id,
        'loan_status' => LibraryBookLoanStatus::BORROWED->value,
        'due_date' => $new_due_date,
    ]);
});
