<?php

use App\Enums\DietaryRestriction;
use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\ExportType;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\LiveStatus;
use App\Enums\MarriedStatus;
use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Enums\SchoolLevel;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\SystemHelper;
use App\Http\Resources\BillingDocumentResource;
use App\Http\Resources\BillToResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\EnrollmentExamResource;
use App\Http\Resources\EnrollmentGuardianResource;
use App\Http\Resources\EnrollmentSessionResource;
use App\Http\Resources\EnrollmentUserResource;
use App\Http\Resources\GradeResource;
use App\Http\Resources\HealthConcernResource;
use App\Http\Resources\RaceResource;
use App\Http\Resources\ReligionResource;
use App\Http\Resources\SchoolResource;
use App\Http\Resources\StateResource;
use App\Interfaces\Userable;
use App\Models\Bank;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Country;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Enrollment;
use App\Models\EnrollmentExam;
use App\Models\EnrollmentExamMark;
use App\Models\EnrollmentSession;
use App\Models\EnrollmentUser;
use App\Models\Grade;
use App\Models\HealthConcern;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\Product;
use App\Models\Race;
use App\Models\Religion;
use App\Models\School;
use App\Models\State;
use App\Models\Student;
use App\Models\Subject;
use App\Models\User;
use App\Services\Billing\AccountingService;
use App\Services\Billing\BillingDocumentService;
use App\Services\DocumentPrintService;
use App\Services\EnrollmentService;
use App\Services\ReportPrintService;
use Carbon\Carbon;
use Database\Seeders\BankSeeder;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\ProductSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;
use PhpOffice\PhpSpreadsheet\Shared\Date;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        CurrencySeeder::class,
        ProductSeeder::class,
        BankSeeder::class,
    ]);
    $this->seedAccountingData();

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456')
    ]);

    // Only guardian or employee able to access enrollment page -> EnrollmentController middleware
    $employee = Employee::factory()->create([
        'user_id' => $this->user->id
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeName = 'admin.enrollments';

    $this->enrollmentTableName = 'enrollments';

    $this->payexBaseUrl = config('services.payment_gateway.payex.base_url');
    $this->payexAuthUrl = $this->payexBaseUrl . '/' . config('services.payment_gateway.payex.auth_url');

    config(['media-library.disk_name' => 'local']);

    $this->reportPrintService = resolve(ReportPrintService::class);
});

dataset('enrollment session', [
    function () {
        $enrollment_session = EnrollmentSession::factory()->create([
            'name' => 'Session 2025',
            'from_date' => '2025-01-01',
            'to_date' => '2025-03-01',
            'code' => '0001',
        ]);

        $subjects = Subject::factory(4)->state(new Sequence(
            [
                'code' => 'ENGLISH',
                'name->en' => 'English',
            ],
            [
                'code' => 'MATHS',
                'name->en' => 'Mathematics',
            ],
            [
                'code' => 'SCIENCE',
                'name->en' => 'Science',
            ],
            [
                'code' => 'ART',
                'name->en' => 'Art',
            ],
        ))->create();

        $enrollment_session->examSubjects()->attach($subjects);

        return $enrollment_session;
    }
]);


test('index (ADMIN)', function () {
    $enrollments = Enrollment::factory(3)->state(new Sequence(
        [
            'name->en' => 'John Doe',
            'nric' => '*********',
        ],
        [
            'name->en' => 'Jane Smith',
            'nric' => '*********',
        ],
        [
            'name->en' => 'Alice Johnson',
            'nric' => '*********',
        ],
    ))->create();

    $billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollments[2]->id, // only the third enrollment has billing documents
    ]);

    $filters = [
        'name' => $enrollments[2]->name,
        'admission_grade_id' => $enrollments[2]->admission_grade_id,
        'nationality_id' => $enrollments[2]->nationality_id,
        'race_id' => $enrollments[2]->race_id,
        'religion_id' => $enrollments[2]->religion_id,
        'birth_cert_number' => $enrollments[2]->birth_cert_number,
        'nric' => $enrollments[2]->nric,
        'gender' => $enrollments[2]->gender->value,
        'enrollment_status' => $enrollments[2]->enrollment_status,
        'payment_status' => $enrollments[2]->payment_status->value,
        'enrollment_user_id' => $enrollments[2]->enrollment_user_id,
        'includes' => [
            'admissionGrade',
            'nationality',
            'race',
            'religion',
            'state',
            'country',
            'enrollmentSession',
            'healthConcern',
            'primarySchool',
            'enrollmentUser',
            'billingDocuments',
            'guardians',
        ],
    ];

    $this->mock(EnrollmentService::class, function (MockInterface $mock) use ($filters, $enrollments) {
        $mock->shouldReceive('getAllPaginatedEnrollments')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([
                $enrollments[2]->loadMissing([
                    'admissionGrade',
                    'nationality',
                    'race',
                    'religion',
                    'state',
                    'country',
                    'enrollmentSession',
                    'healthConcern',
                    'primarySchool',
                    'enrollmentUser',
                    'billingDocuments',
                    'guardians',
                ])
            ], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeName . '.admin-index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $enrollments[2]->id,
            'name' => $enrollments[2]->name,
            'email' => $enrollments[2]->email,
            'phone_number' => $enrollments[2]->phone_number,
            'phone_number_2' => $enrollments[2]->phone_number_2,
            'nric' => $enrollments[2]->nric,
            'passport_number' => $enrollments[2]->passport_number,
            'admission_year' => $enrollments[2]->admission_year,
            'admission_grade' => resourceToArray(new GradeResource($enrollments[2]->admissionGrade)),
            'join_date' => $enrollments[2]->join_date,
            'leave_date' => $enrollments[2]->leave_date,
            'leave_status' => $enrollments[2]->leave_status,
            'student_number' => $enrollments[2]->student_number,
            'birthplace' => $enrollments[2]->birthplace,
            'nationality' => resourceToArray(new CountryResource($enrollments[2]->nationality)),
            'date_of_birth' => $enrollments[2]->date_of_birth,
            'gender' => $enrollments[2]->gender->value,
            'birth_cert_number' => $enrollments[2]->birth_cert_number,
            'race' => resourceToArray(new RaceResource($enrollments[2]->race)),
            'religion' => resourceToArray(new ReligionResource($enrollments[2]->religion)),
            'address' => $enrollments[2]->address,
            'postal_code' => $enrollments[2]->postal_code,
            'city' => $enrollments[2]->city,
            'state' => resourceToArray(new StateResource($enrollments[2]->state)),
            'country' => resourceToArray(new CountryResource($enrollments[2]->country)),
            'address_2' => $enrollments[2]->address_2,
            'is_hostel' => $enrollments[2]->is_hostel,
            'is_active' => $enrollments[2]->is_active,
            'remarks' => $enrollments[2]->remarks,
            'custom_field' => $enrollments[2]->custom_field,
            'dietary_restriction' => $enrollments[2]->dietary_restriction,
            'health_concern' => resourceToArray(new HealthConcernResource($enrollments[2]->healthConcern)),
            'primary_school' => resourceToArray(new SchoolResource($enrollments[2]->primarySchool)),
            'admission_type' => $enrollments[2]->admission_type->value,
            'enrollment_status' => $enrollments[2]->enrollment_status,
            'payment_status' => $enrollments[2]->payment_status->value,
            'have_siblings' => $enrollments[2]->have_siblings,
            'is_foreigner' => $enrollments[2]->is_foreigner,
            'conduct' => $enrollments[2]->conduct,
            'token' => $enrollments[2]->token,
            'registration_date' => $enrollments[2]->registration_date,
            'expiry_date' => $enrollments[2]->expiry_date,
            'enrollment_session' => resourceToArray(new EnrollmentSessionResource($enrollments[2]->enrollmentSession)),
            'enrollment_user' => resourceToArray(new EnrollmentUserResource($enrollments[2]->enrollmentUser)),
            'fees_to_be_paid' => null,
            'translations' => $enrollments[2]->translations,
            'billing_documents' => resourceToArray(BillingDocumentResource::collection($enrollments[2]->billingDocuments)),
            'guardians' => resourceToArray(EnrollmentGuardianResource::collection($enrollments[2]->guardians)),
        ]);
});

test('index (FE)', function () {
    $other_enrollments = Enrollment::factory(3)->state(new Sequence(
        [
            'name->en' => 'John Doe',
            'nric' => '*********',
        ],
        [
            'name->en' => 'Jane Smith',
            'nric' => '*********',
        ],
        [
            'name->en' => 'Alice Johnson',
            'nric' => '*********',
        ],
    ))->create();

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollments = Enrollment::factory(3)->state(new Sequence(
        [
            'name->en' => 'Kaka',
            'enrollment_user_id' => $enrollment_user->id,
            'enrollment_status' => EnrollmentStatus::APPROVED->value,
            'payment_status' => EnrollmentPaymentStatus::PAID->value,
        ],
        [
            'name->en' => 'Toro',
            'enrollment_user_id' => $enrollment_user->id,
            'enrollment_status' => EnrollmentStatus::APPROVED->value,
            'payment_status' => EnrollmentPaymentStatus::PAID->value,
        ],
    ))->create();

    $filters = [
        'name' => $enrollments[1]->name,
        'enrollment_status' => EnrollmentStatus::APPROVED->value,
        'payment_status' => EnrollmentPaymentStatus::PAID->value,
        'includes' => [
            'admissionGrade',
            'nationality',
            'race',
            'religion',
            'state',
            'country',
            'enrollmentSession',
            'healthConcern',
            'primarySchool',
            'enrollmentUser',
        ],
    ];

    $this->mock(EnrollmentService::class, function (MockInterface $mock) use ($filters, $enrollments) {
        $mock->shouldReceive('getAllPaginatedEnrollments')
            ->with(array_merge($filters, [
                'enrollment_user_id' => $enrollments[1]->enrollment_user_id,
            ]))
            ->once()
            ->andReturn(new LengthAwarePaginator([
                $enrollments[1]->loadMissing([
                    'admissionGrade',
                    'nationality',
                    'race',
                    'religion',
                    'state',
                    'country',
                    'enrollmentSession',
                    'healthConcern',
                    'primarySchool',
                    'enrollmentUser',
                ])
            ], 1, 1));
    });

    // Authenticate as enrollment user
    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    $response = $this->getJson(
        route('enrollments.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0]['id'])->toEqual($enrollments[1]->id);
});


test('index determine getPaginated per_page is not -1', function () {
    $this->mock(EnrollmentService::class, function (MockInterface $mock) {
        $enrollment = Enrollment::factory()->create();

        $mock->shouldReceive('getAllPaginatedEnrollments')
            ->once()
            ->andReturn(new LengthAwarePaginator([$enrollment], 1, 1));
    });

    $response = $this->getJson(route($this->routeName . '.admin-index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(EnrollmentService::class, function (MockInterface $mock) {
        $enrollments = Enrollment::factory(2)->create();

        $mock->shouldReceive('getAllEnrollments')->once()->andReturn($enrollments);
    });

    $response = $this->getJson(route($this->routeName . '.admin-index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('index (ADMIN) GET BILLING DOCUMENTS FOR ENROLLMENTS', function () {
    $enrollment = Enrollment::factory()->create([
        'name->en' => 'John Doe',
        'nric' => '*********',
    ]);

    $paid_billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'paid_at' => now()->toDateTimeString(),
    ]);

    $unpaid_billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $payment = Payment::factory()->create([
        'billing_document_id' => $paid_billing_document->id,
        'payment_method_id' => PaymentMethod::factory()->create()->id,
        'payment_reference_no' => 'PAYEX-123456',
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$paid_billing_document->bill_to_type],
        'bill_to_id' => $paid_billing_document->bill_to_id,
        'id' => $paid_billing_document->id,
        'status' => $paid_billing_document->status,
        'payment_status' => $paid_billing_document->payment_status,
        'type' => $paid_billing_document->type,
        'sub_type' => $paid_billing_document->sub_type,
        'reference_no' => $paid_billing_document->reference_no,
        'payment_reference_no' => 'PAYEX-123456',
        'includes' => [
            'billTo',
        ],
    ];

    $this->mock(BillingDocumentService::class, function (MockInterface $mock) use ($filters, $paid_billing_document) {
        $mock->shouldReceive('getAllPaginatedBillingDocuments')
            ->with(array_merge($filters, ['bill_to_type' => Userable::USERABLE_MAPPING[$filters['bill_to_type']]]))
            ->once()
            ->andReturn(new LengthAwarePaginator([
                $paid_billing_document->loadMissing([
                    'billTo',
                ])
            ], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeName . '.billing-document-index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $paid_billing_document->id,
            'reference_number' => $paid_billing_document->reference_no,
            'document_date' => $paid_billing_document->document_date,
            'paid_at' => $paid_billing_document->paid_at->tz(config('school.timezone'))->toIso8601String(),
            'type' => $paid_billing_document->type,
            'sub_type' => $paid_billing_document->sub_type,
            'status' => $paid_billing_document->status,
            'payment_status' => $paid_billing_document->payment_status,
            'legal_entity_name' => $paid_billing_document->legal_entity_name,
            'legal_entity_address' => $paid_billing_document->legal_entity_address,
            'bill_to_name' => $paid_billing_document->bill_to_name,
            'bill_to_address' => $paid_billing_document->bill_to_address,
            'bill_to_reference_number' => $paid_billing_document->bill_to_reference_number,
            'bill_to' => resourceToArray(new BillToResource($paid_billing_document->billTo)),
            'tax_code' => $paid_billing_document->tax_code,
            'tax_description' => $paid_billing_document->tax_description,
            'tax_percentage' => $paid_billing_document->tax_percentage,
            'payment_due_date' => $paid_billing_document->payment_due_date,
            'remit_to_account_number' => $paid_billing_document->remit_to_account_number,
            'remit_to_account_name' => $paid_billing_document->remit_to_account_name,
            'remit_to_bank_name' => $paid_billing_document->remit_to_bank_name,
            'remit_to_bank_address' => $paid_billing_document->remit_to_bank_address,
            'remit_to_swift_code' => $paid_billing_document->remit_to_swift_code,
            'currency_code' => $paid_billing_document->currency_code,
            'amount_before_tax' => $paid_billing_document->amount_before_tax,
            'amount_before_tax_after_less_advance' => $paid_billing_document->amount_before_tax_after_less_advance,
            'tax_amount' => $paid_billing_document->tax_amount,
            'amount_after_tax' => $paid_billing_document->amount_after_tax,
            'receipt_url' => $paid_billing_document->receipt_url,
        ]);
});

test('manualPayment (ADMIN) for enrollment\'s billing document failed because of validation', function () {
    /**
     * no payments
     */
    $billing_document = BillingDocument::factory()->create();

    $payload = [
        'remarks' => 'Paid by guardian',
        'payments' => [],
    ];

    $response = $this->postJson(route($this->routeName . '.manual-payment', ['billing_document' => $billing_document->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'payments' => [
                'The payments field is required.',
            ],
        ]);


    /**
     * invalid payment load
     */

    $payload = [
        'remarks' => 'Paid by guardian',
        'payments' => [
            [
                'payment_method_code' => 'INVALID',
                'bank_id' => 111111,
                'payment_reference_no' => '123',
                'amount_received' => '100.00',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ]
        ],
    ];

    $response = $this->postJson(route($this->routeName . '.manual-payment', ['billing_document' => $billing_document->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'payments.0.payment_method_code' => [
                'The selected payment method code is invalid.',
            ],
            'payments.0.bank_id' => [
                'The selected bank is invalid.',
            ],
        ]);


    /**
     * bank_id is required when payment_method_code is BANK_TRANSFER
     * payment_reference_no is required when payment_method_code is not BANK_TRANSFER or FPX
     */

    $payload = [
        'remarks' => 'Paid by guardian',
        'payments' => [
            [
                'payment_method_code' => PaymentMethod::CODE_BANK_TRANSFER,
                'bank_id' => null, // no bank
                'payment_reference_no' => '123',
                'amount_received' => '100.00',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ],
            [
                'payment_method_code' => PaymentMethod::CODE_CHEQUE,
                'bank_id' => null,
                'payment_reference_no' => null, // no payment_reference_no
                'amount_received' => '100.00',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ],
        ],
    ];

    $response = $this->postJson(route($this->routeName . '.manual-payment', ['billing_document' => $billing_document->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'payments.0.bank_id' => [
                'The bank field is required when payment method code is BANK.',
            ],
            'payments.1.payment_reference_no' => [
                'The payment reference no field is required when payment method code is CHEQUE.',
            ],
        ]);
});

test('manualPayment (ADMIN) for enrollment\'s billing document success', function () {
    $enrollment = Enrollment::factory()->create([
        'name->en' => 'John Doe',
        'nric' => '*********',
    ]);

    $invoice = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $enrollment->getBillToType(),
        'bill_to_id' => $enrollment->getBillToId(),
        'bill_to_name' => $enrollment->getBillToName(),
        'bill_to_reference_number' => $enrollment->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => null,

    ]);

    $bank = Bank::first();

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice,
        'amount_before_tax' => 100,
        'gl_account_code' => 'ENROLL00000001',
        'currency_code' => 'MYR',
        'description' => 'Enrollment Fees Jan 2024',
    ]);

    $payload = [
        'remarks' => 'Paid invoice for Enrollment Fees',
        'payments' => [
            [
                'payment_method_code' => PaymentMethod::CODE_BANK_TRANSFER,
                'bank_id' => $bank->id,
                'payment_reference_no' => 'INV1113',
                'amount_received' => '40.50',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ],
            [
                'payment_method_code' => PaymentMethod::CODE_CASH,
                'amount_received' => '59.50',
            ],
        ],
    ];

    $this->mock(AccountingService::class, function (MockInterface $mock) use ($payload, $invoice) {
        $mock->shouldReceive('setUser')->once()->with($this->user)->andReturnSelf();
        $mock->shouldReceive('setBillingDocument')->once()->withArgs(function ($arg) use ($invoice) {
            return $arg instanceof BillingDocument && $arg->id === $invoice->id;
        })->andReturnSelf();
        $mock->shouldReceive('processManualPayment')->once()->with($payload)->andReturnTrue();
    });

    $response = $this->postJson(route($this->routeName . '.manual-payment', ['billing_document' => $invoice->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('downloadPrePaymentTemplate', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $filename = 'enrollment-template';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeName . '.download-pre-payment-template', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
})->with('enrollment session');

test('downloadPostPaymentTemplate', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $filename = 'enrollment-post-payment-template';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeName . '.download-post-payment-template', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
})->with('enrollment session');

test('downloadPrePaymentTemplate - test excel content', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $report_data = app()->make(EnrollmentService::class)->getTemplateData($payload['enrollment_session_id'], false);

    $report_view_name = 'templates.enrollment-template';
    $file_name = 'enrollment-template';

    $export_type = ExportType::EXCEL;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $expected_headers = [
                'No',
                '考生编号 / Exam Slip',
                'Student Name',
                '学生姓名',
                '身份证号码 / IC Number',
                'Passport Number',
                'Religion',
                '性别 / Gender (MALE, FEMALE)',
                '监护人电话号码 / Guardian Phone Number',
                'Guardian Email',
                '监护人姓名 / Guardian Name',
                '监护人类别 / Guardian Type (GUARDIAN, FATHER, MOTHER)',
                '总平均 / Total Average',
                'Status (APPROVED, REJECTED, SHORLISTED)',
                '地址 / Address',
                '就读小学 / Primary School',
                '宿舍 / Hostel (TRUE, FALSE)',
                '兄弟姐妹 / Have Siblings (TRUE, FALSE)',
                '膳食 / Dietary Restrictions (NONE, VEGETARIAN)',
                '健康问题 / Health Concern',
                '外藉生/Foreigner (TRUE, FALSE)',
                '操行 / Conduct',
                '备注 /Remarks',
                '注册日期 / Register Date',
                '过期日期 / Expiry Date',
            ];

            expect($report_data['subject_lists'])->toHaveCount(4);

            foreach ($report_data['subject_lists'] as $code => $s) {
                $expected_headers[] = $s->getTranslation('name', 'zh') . ' / ' . $s->getTranslation('name', 'en');
            }

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($report_data['data'][0]['number']);
            $view->assertSee($report_data['data'][0]['exam_slip_number']);
            $view->assertSee($report_data['data'][0]['student_name_en']);
            $view->assertSee($report_data['data'][0]['student_name_zh']);
            $view->assertSee($report_data['data'][0]['nric']);
            $view->assertSee($report_data['data'][0]['passport_number']);
            $view->assertSee($report_data['data'][0]['religion']);
            $view->assertSee($report_data['data'][0]['gender']);
            $view->assertSee($report_data['data'][0]['guardian_phone_number']);
            $view->assertSee($report_data['data'][0]['guardian_email']);
            $view->assertSee($report_data['data'][0]['guardian_name']);
            $view->assertSee($report_data['data'][0]['guardian_type']);
            $view->assertSee(number_format($report_data['data'][0]['total_average']), 2);
            $view->assertSee($report_data['data'][0]['status']);
            $view->assertSee($report_data['data'][0]['address']);
            $view->assertSee($report_data['data'][0]['primary_school']);
            $view->assertSee($report_data['data'][0]['hostel']);
            $view->assertSee($report_data['data'][0]['have_siblings']);
            $view->assertSee($report_data['data'][0]['dietary_restriction']);
            $view->assertSee($report_data['data'][0]['health_concern']);
            $view->assertSee($report_data['data'][0]['foreigner']);
            $view->assertSee($report_data['data'][0]['conduct']);
            $view->assertSee($report_data['data'][0]['remarks']);
            $view->assertSee($report_data['data'][0]['register_date']);
            $view->assertSee($report_data['data'][0]['expiry_date']);

            foreach ($report_data['subject_lists'] as $code => $s) {
                $view->assertSee($report_data['data'][0][$code]);
            }

            return true;
        }
    );
})->with('enrollment session');

test('downloadPostPaymentTemplate - test excel content', function ($enrollment_session) {
    Excel::fake();

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
    ];

    $report_data = app()->make(EnrollmentService::class)->getTemplateData($payload['enrollment_session_id'], true);

    $report_view_name = 'templates.enrollment-template-post-payment';
    $file_name = 'enrollment-template';

    $export_type = ExportType::EXCEL;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $expected_headers = [
                'No',
                '考生编号 / Exam Slip',
                'Student Name',
                '身份证号码 / IC Number',
                'Passport Number',
                '宿舍 / Hostel (TRUE, FALSE)',
                '总平均 / Total Average',
                'Status (APPROVED, REJECTED)',
            ];

            foreach ($report_data['subject_lists'] as $code => $s) {
                $expected_headers[] = $s->getTranslation('name', 'zh') . ' / ' . $s->getTranslation('name', 'en');
            }

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($report_data['data'][0]['number']);
            $view->assertSee($report_data['data'][0]['exam_slip_number']);
            $view->assertSee($report_data['data'][0]['student_name_en']);
            $view->assertSee($report_data['data'][0]['nric']);
            $view->assertSee($report_data['data'][0]['passport_number']);
            $view->assertSee($report_data['data'][0]['hostel']);
            $view->assertSee(number_format($report_data['data'][0]['total_average']), 2);
            $view->assertSee($report_data['data'][0]['status']);

            foreach ($report_data['subject_lists'] as $code => $s) {
                $view->assertSee($report_data['data'][0][$code]);
            }

            return true;
        }
    );
})->with('enrollment session');


test('importTemplateValidation success', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $subjects = Subject::factory(2)->state(new Sequence(
        [
            'code' => '001',
            'name->en' => 'English',
        ],
        [
            'code' => '002',
            'name->en' => 'Mathematics',
        ],
    ))->create();

    $enrollment_session->examSubjects()->attach($subjects);

    // convert Enrollment Template Excel to collection
    $header = [
        'No.',
        '考生编号 / Exam Slip',
        'Status',
        'Expiry Date',
        'Register Date',
        '学生姓名',
        'Student Name',
        '性别 / Gender',
        '身份证号码 / IC Number',
        'Passport Number',
        '外藉生/Foreigner (True, False)',
        'Religion',
        '地址 / Address',
        '宿舍 / Hostel (True, False)',
        '膳食 / Dietary Restrictions',
        '健康问题 / Health Concern',
        '兄弟姐妹 / Have Siblings (True, False)',
        '就读小学 / Primary School',
        '总平均 / Total Average',
        '短文 / Short Essay',
        '华 / CN',
        '操行 / Conduct',
        '监护人类别 / Guardian Type',
        '监护人姓名 / Guardian Name',
        '监护人电话号码 / Guardian Phone Number',
        'Guardian Email',
        '备注 / Remarks',
    ];

    $row1 = [
        '1',
        'R6566',
        'INVALID_STATUS',
        Date::stringToExcel('2025-01-20'),
        Date::stringToExcel('2025-01-01'),
        '约翰·多',
        'John Doe',
        'MALE',
        '110113101982',
        'A1234567',
        'TRUE',
        'Christian',
        '123 Sample Street',
        'TRUE',
        'VEGETARIAN',
        'Adhd',
        'TRUE',
        'Pin Hwa',
        '82.05',
        '80.05',
        '81.05',
        'A+',
        'FATHER',
        'Frank Reynold',
        '60*********',
        '<EMAIL>',
        'remarks',
    ];

    $row2 = [
        '2',
        'R6566',
        'APPROVED',
        Date::stringToExcel('2025-01-20'),
        Date::stringToExcel('2025-01-01'),
        '约翰·多',
        'John Cena',
        'MALE',
        '110113101982',
        'A1234568',
        'TRUE',
        'Buddhism',
        '555 Sample Street',
        'TRUE',
        'VEGETARIAN',
        'Adhd',
        'TRUE',
        'SK KB',
        '83.05',
        '81.05',
        '82.05',
        'B',
        'MOTHER',
        'Dee Reynold',
        '60123456790',
        '<EMAIL>',
        'yohoo',
    ];

    $test_excel = createFakeExcelFileWithData([
        $header,
        $row1,
        $row2,
    ]);

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'file' => $test_excel,
    ];

    $response = $this->postJson(route($this->routeName . '.import-template-validation'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['data'])->toHaveCount(2)
        ->and($response['data']['data'])->toEqual([
            [
                'number' => '1',
                'exam_slip_number' => 'R6566',
                'student_name_en' => 'JOHN DOE',
                'student_name_zh' => '约翰·多',
                'nric' => '110113101982',
                'passport_number' => 'A1234567',
                'religion' => 'Christian',
                'gender' => 'MALE',
                'guardian_phone_number' => '+60*********',
                'guardian_email' => '<EMAIL>',
                'guardian_name' => 'FRANK REYNOLD',
                'guardian_type' => 'FATHER',
                'total_average' => '82.05',
                '001' => '80.05',
                '002' => '81.05',
                'status' => 'INVALID_STATUS',
                'address' => '123 SAMPLE STREET',
                'primary_school' => 'Pin Hwa',
                'hostel' => true,
                'have_siblings' => true,
                'dietary_restriction' => 'VEGETARIAN',
                'health_concern' => 'Adhd',
                'foreigner' => true,
                'conduct' => 'A+',
                'remarks' => 'remarks',
                'register_date' => '2025-01-01',
                'expiry_date' => '2025-01-20',
                'errors' => [
                    'status' => [
                        'Status must be APPROVED.'
                    ]
                ],
                'warnings' => [
                    'religion' => [
                        'Religion Christian does not exist in the database. It will be created.'
                    ],
                    'primary_school' => [
                        'School Pin Hwa does not exist in the database. It will be created.'
                    ],
                    'health_concern' => [
                        'Health concern Adhd does not exist in the database. It will be created.'
                    ]
                ]
            ],
            [
                'number' => '2',
                'exam_slip_number' => 'R6566',
                'student_name_en' => 'JOHN CENA',
                'student_name_zh' => '约翰·多',
                'nric' => '110113101982',
                'passport_number' => 'A1234568',
                'religion' => 'Buddhism',
                'gender' => 'MALE',
                'guardian_phone_number' => '+60123456790',
                'guardian_email' => '<EMAIL>',
                'guardian_name' => 'DEE REYNOLD',
                'guardian_type' => 'MOTHER',
                'total_average' => '83.05',
                '001' => '81.05',
                '002' => '82.05',
                'status' => 'APPROVED',
                'address' => '555 SAMPLE STREET',
                'primary_school' => 'SK KB',
                'hostel' => true,
                'have_siblings' => true,
                'dietary_restriction' => 'VEGETARIAN',
                'health_concern' => 'Adhd',
                'foreigner' => true,
                'conduct' => 'B',
                'remarks' => 'yohoo',
                'register_date' => '2025-01-01',
                'expiry_date' => '2025-01-20',
                'errors' => [
                    'nric' => [
                        'NRIC is duplicated.'
                    ],
                    'exam_slip_number' => [
                        'Exam slip number is duplicated.'
                    ]
                ],
                'warnings' => [
                    'religion' => [
                        'Religion Buddhism does not exist in the database. It will be created.'
                    ],
                    'primary_school' => [
                        'School SK KB does not exist in the database. It will be created.'
                    ],
                    'health_concern' => [
                        'Health concern Adhd does not exist in the database. It will be created.'
                    ]
                ]
            ],
        ])
        ->and($response['data']['subject_lists'])->toHaveCount(2)
        ->and($response['data']['subject_lists'])->toHaveKey('001')
        ->and($response['data']['subject_lists'])->toHaveKey('002')
        ->and($response['data']['error_count'])->toBe(3)
        ->and($response['data']['warning_count'])->toBe(6);
});

test('importTemplateValidation failed because validation error', function () {
    /**
     * NO PAYLOAD
     */
    $payload = [];

    $response = $this->postJson(route($this->routeName . '.import-template-validation'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'file' => [
                'The file field is required.'
            ],
            'enrollment_session_id' => [
                'The enrollment session id field is required.'
            ],
        ],
        'data' => null
    ]);

    /**
     * INVALID FILE TYPE NOT EXCEL && INVALID ENROLLMENT SESSION ID
     */
    $payload = [
        'enrollment_session_id' => 99999999,
        'file' => UploadedFile::fake()->create('file.png', 500),
    ];

    $response = $this->postJson(route($this->routeName . '.import-template-validation'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'file' => [
                'The file field must be a file of type: xlsx.'
            ],
            'enrollment_session_id' => [
                'The selected enrollment session id is invalid.'
            ],
        ],
        'data' => null
    ]);
});


test('bulkSaveImportedData', function () {
    // Arrange
    $enrollment_session = EnrollmentSession::factory()->create();
    $subject = Subject::factory()->create(['code' => 'MATH']);
    $enrollment_session->examSubjects()->attach([$subject->id]);

    $dummy_enrollments = [
        [
            'student_name_en' => 'John Smith',
            'student_name_zh' => 'John Smith',
            'nric' => '990101123456',
            'passport_number' => '',
            'gender' => 'MALE',
            'address' => 'Test address',
            'hostel' => false,
            'have_siblings' => false,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
            'conduct' => 'A',
            'status' => 'APPROVED',
            'religion' => 'Buddhism',
            'health_concern' => 'Asthma',
            'primary_school' => 'PRIMARY SCHOOL',
            'guardian_name' => 'Test Guardian',
            'guardian_phone_number' => '*********',
            'guardian_email' => '<EMAIL>',
            'guardian_type' => 'GUARDIAN',
            'exam_slip_number' => 'TEST123',
            'total_average' => '80.05',
            'MATH' => '80.05',
            'register_date' => '2025-01-01',
            'expiry_date' => '2025-01-20',
        ]
    ];

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'enrollments' => $dummy_enrollments
    ];

    $response = $this->postJson(route($this->routeName . '.bulk-save-imported-data'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->enrollmentTableName, 1);
    $this->assertDatabaseCount('enrollment_users', 1);
    $this->assertDatabaseCount('enrollment_exams', 1);
    $this->assertDatabaseCount('enrollment_exam_marks', 1);
});

test('bulkSaveImportedData : error validation, nothing created', function () {
    // Arrange
    $enrollment_session = EnrollmentSession::factory()->create();

    $dummy_enrollments = [
        [
            'student_name_en' => 'John Smith',
            'student_name_zh' => 'John Smith',
            'nric' => '990101123456',
            'passport_number' => '',
            'gender' => 'MALEXX',
            'address' => 'Test address',
            'hostel' => false,
            'have_siblings' => false,
            'dietary_restriction' => 'NONEs',
            'foreigner' => false,
            'conduct' => 'A',
            'status' => 'APPROVED',
            'religion' => 'Buddhism',
            'health_concern' => 'Asthma',
            'primary_school' => 'PRIMARY SCHOOL',
            'guardian_name' => 'Test Guardian',
            'guardian_phone_number' => '*********',
            'guardian_email' => '<EMAIL>',
            'guardian_type' => 'GUARDIAN',
            'exam_slip_number' => 'TEST123',
            'total_average' => '80.05',
            'MATH' => '80.05',
            'register_date' => '2021-01-01',
            'expiry_date' => '2021-01-20',
        ]
    ];

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'enrollments' => $dummy_enrollments
    ];

    $response = $this->postJson(route($this->routeName . '.bulk-save-imported-data'), $payload)->json();

    expect($response['code'])->toEqual(422)
        ->and($response['data']['data'])->toHaveCount(1)
        ->and($response['data']['data'][0]['errors'])->toHaveCount(1)
        ->and($response['data']['data'][0]['warnings'])->toHaveCount(3);

    $this->assertDatabaseCount($this->enrollmentTableName, 0);
    $this->assertDatabaseCount('guardians', 0);
    $this->assertDatabaseCount('guardian_student', 0);
    $this->assertDatabaseCount('enrollment_exams', 0);
    $this->assertDatabaseCount('enrollment_exam_marks', 0);
});

test('destroy', function () {
    $first_enrollment = Enrollment::factory()->create();
    $other_enrollments = Enrollment::factory(3)->create();

    $this->assertDatabaseCount($this->enrollmentTableName, 4);

    //id not exist
    $response = $this->deleteJson(route($this->routeName . '.destroy', ['enrollment' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->enrollmentTableName, 4);

    //delete success
    $response = $this->deleteJson(route($this->routeName . '.destroy', ['enrollment' => $first_enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->enrollmentTableName, 3);

    $this->assertDatabaseMissing($this->enrollmentTableName, ['id' => $first_enrollment->id]);

    foreach ($other_enrollments as $other_enrollment) {
        $this->assertDatabaseHas($this->enrollmentTableName, ['id' => $other_enrollment->id]);
    }
});

test('show (ADMIN) enrollment with fees_to_be_paid', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $product_2 = Product::factory()->create([
        'name->en' => 'Hostel Fee',
        'unit_price' => 2000,
    ]);

    $temp_fee_assignment = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => $product_1->id,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $malaysia->id,
                ],
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $product_2->id,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => $temp_fee_assignment,
    ]);

    // MALAYSIAN NON HOSTEL STUDENT
    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'is_foreigner' => false,
        'is_hostel' => false,
    ]);

    $response = $this->getJson(route($this->routeName . '.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $enrollment->id,
            'name' => $enrollment->name,
            'email' => $enrollment->email,
            'phone_number' => $enrollment->phone_number,
            'phone_number_2' => $enrollment->phone_number_2,
            'nric' => $enrollment->nric,
            'passport_number' => $enrollment->passport_number,
            'admission_year' => $enrollment->admission_year,
            'admission_grade' => resourceToArray(new GradeResource($enrollment->admissionGrade)),
            'join_date' => $enrollment->join_date,
            'leave_date' => $enrollment->leave_date,
            'leave_status' => $enrollment->leave_status,
            'student_number' => $enrollment->student_number,
            'birthplace' => $enrollment->birthplace,
            'nationality' => resourceToArray(new CountryResource($enrollment->nationality)),
            'date_of_birth' => $enrollment->date_of_birth,
            'gender' => $enrollment->gender->value,
            'birth_cert_number' => $enrollment->birth_cert_number,
            'race' => resourceToArray(new RaceResource($enrollment->race)),
            'religion' => resourceToArray(new ReligionResource($enrollment->religion)),
            'address' => $enrollment->address,
            'postal_code' => $enrollment->postal_code,
            'city' => $enrollment->city,
            'state' => resourceToArray(new StateResource($enrollment->state)),
            'country' => resourceToArray(new CountryResource($enrollment->country)),
            'address_2' => $enrollment->address_2,
            'is_hostel' => $enrollment->is_hostel,
            'is_active' => $enrollment->is_active,
            'remarks' => $enrollment->remarks,
            'custom_field' => $enrollment->custom_field,
            'dietary_restriction' => $enrollment->dietary_restriction,
            'health_concern' => resourceToArray(new HealthConcernResource($enrollment->healthConcern)),
            'primary_school' => resourceToArray(new SchoolResource($enrollment->primarySchool)),
            'admission_type' => $enrollment->admission_type->value,
            'enrollment_status' => $enrollment->enrollment_status,
            'payment_status' => $enrollment->payment_status->value,
            'have_siblings' => $enrollment->have_siblings,
            'is_foreigner' => $enrollment->is_foreigner,
            'conduct' => $enrollment->conduct,
            'token' => $enrollment->token,
            'registration_date' => $enrollment->registration_date,
            'expiry_date' => $enrollment->expiry_date,
            'enrollment_session' => resourceToArray(new EnrollmentSessionResource($enrollment->enrollmentSession)),
            'fees_to_be_paid' => [
                [
                    // only product_1 is applicable
                    'product' => $product_1->toArray(),
                    'amount' => 1000,
                    'period' => '2024-01-01',
                    'product_id' => $product_1->id,
                ],
            ],
            'translations' => $enrollment->translations,
            'billing_documents' => [],
            'guardians' => [],
            'enrollment_exams' => [],
        ]);

    // MALAYSIAN HOSTEL STUDENT
    $enrollment->update([
        'nationality_id' => $malaysia->id,
        'is_hostel' => true,
    ]);

    $response = $this->getJson(route($this->routeName . '.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toEqual([
            [
                'product' => $product_1->toArray(),
                'amount' => 1000,
                'period' => '2024-01-01',
                'product_id' => $product_1->id,
            ],
            [
                'product' => $product_2->toArray(),
                'amount' => 2000,
                'period' => '2024-02-01',
                'product_id' => $product_2->id,
            ]
        ]);


    // no fee to be paid
    $enrollment_session->update([
        'fee_assignment_settings' => null,
    ]);

    $response = $this->getJson(route($this->routeName . '.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toEqual([]);
});

test('show (USER) enrollment with authorization', function () {
    // Create enrollment user (guardian/parent)
    $enrollment_user = EnrollmentUser::factory()->create([
        'name' => ['en' => 'Guardian User', 'zh' => '监护人'],
        'email' => '<EMAIL>',
        'phone_number' => '0*********',
        'nric' => '990101123456'
    ]);

    // Create another enrollment user (unauthorized)
    $unauthorized_user = EnrollmentUser::factory()->create([
        'name' => ['en' => 'Other Guardian', 'zh' => '其他监护人'],
        'email' => '<EMAIL>',
        'phone_number' => '0987654321',
        'nric' => '880202234567'
    ]);

    // Create enrollment session
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Test Session 2024',
        'from_date' => '2024-01-01',
        'to_date' => '2024-12-31',
        'code' => 'TEST2024',
        'is_active' => true,
    ]);

    // Create enrollment belonging to the first user
    $enrollment = Enrollment::factory()->create([
        'enrollment_user_id' => $enrollment_user->id,
        'enrollment_session_id' => $enrollment_session->id,
        'name' => ['en' => 'Student Name', 'zh' => '学生姓名'],
        'email' => '<EMAIL>',
        'nric' => '050101123456',
    ]);

    // Test 1: Successful access by authorized user
    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveKey('id', $enrollment->id)
        ->and($response['data'])->toHaveKey('name', $enrollment->name)
        ->and($response['data'])->toHaveKey('email', $enrollment->email)
        ->and($response['data'])->toHaveKey('nric', $enrollment->nric)
        ->and($response['data'])->toHaveKey('enrollment_session')
        ->and($response['data'])->toHaveKey('fees_to_be_paid');

    // Test 2: Unauthorized access by different user
    Sanctum::actingAs($unauthorized_user, [], 'enrollment');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(403)
        ->and($response['error'])->toBe(__('api.common.unauthorized'));

    // Test 3: Non-existent enrollment
    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => 99999]));

    expect($response->status())->toBe(404);
});

test('show (USER) enrollment with fees calculation', function () {
    // Create enrollment user
    $enrollment_user = EnrollmentUser::factory()->create([
        'name' => ['en' => 'Guardian User', 'zh' => '监护人'],
        'email' => '<EMAIL>',
        'phone_number' => '0*********',
        'nric' => '990101123456'
    ]);

    // Create products for fee calculation
    $general_fee = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1500,
    ]);

    $hostel_fee = Product::factory()->create([
        'name->en' => 'Hostel Fee',
        'unit_price' => 800,
    ]);

    // Create country for nationality condition
    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    // Create fee assignment settings
    $fee_assignment_settings = [
        [
            'conditions' => null, // Apply to all students
            'outcome' => [
                'product_id' => $general_fee->id,
                'amount' => 1500,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $malaysia->id,
                ],
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $hostel_fee->id,
                'amount' => 800,
                'period' => '2024-02-01',
            ]
        ]
    ];

    // Create enrollment session with fee settings
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Test Session 2024',
        'from_date' => '2024-01-01',
        'to_date' => '2024-12-31',
        'code' => 'TEST2024',
        'is_active' => true,
        'fee_assignment_settings' => $fee_assignment_settings,
    ]);

    // Create enrollment - Malaysian hostel student
    $enrollment = Enrollment::factory()->create([
        'enrollment_user_id' => $enrollment_user->id,
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'is_hostel' => true,
        'name' => ['en' => 'Malaysian Student', 'zh' => '马来西亚学生'],
        'email' => '<EMAIL>',
        'nric' => '050101123456',
    ]);

    // Authenticate as enrollment user
    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toHaveCount(2)
        ->and($response['data']['fees_to_be_paid'][0])->toMatchArray([
            'product_id' => $general_fee->id,
            'amount' => 1500,
            'period' => '2024-01-01',
        ])
        ->and($response['data']['fees_to_be_paid'][1])->toMatchArray([
            'product_id' => $hostel_fee->id,
            'amount' => 800,
            'period' => '2024-02-01',
        ]);

    // Test non-hostel student (should only get general fee)
    $enrollment->update(['is_hostel' => false]);

    $response = $this->getJson(route('enrollments.show', ['enrollment' => $enrollment->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['fees_to_be_paid'])->toHaveCount(1)
        ->and($response['data']['fees_to_be_paid'][0])->toMatchArray([
            'product_id' => $general_fee->id,
            'amount' => 1500,
            'period' => '2024-01-01',
        ]);
});

test('extendExpiry', function () {
    $enrollment = Enrollment::factory()->create([
        'expiry_date' => '2023-01-01',
    ]);

    $payload = [
        'expiry_date' => '2027-01-01',
    ];

    $response = $this->postJson(
        route($this->routeName . '.extend-expiry', ['enrollment' => $enrollment->id]),
        $payload,
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['expiry_date'])->toBe('2027-01-01');
});

test('makePayment (FE) [FPX_PAYMENT]', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
            [
                'conditions' => null,
                'outcome' => [
                    'product_id' => $product_1->id,
                    'amount' => 1000,
                    'period' => '2024-01-01',
                ]
            ],
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    createPayexConfig();
    mockPayex();

    $payload = [
        'is_manual_payment_requested' => false,
    ];

    $response = $this->postJson(route('enrollments.make-payment', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'currency' => 'MYR',
            'amount' => 1000,
            'status' => PaymentStatus::PENDING->value,
            'payment_url' => 'VALID_PAYMENT_URL',
            'payment_required' => true,
        ])->toHaveKey('order_id');

    $this->assertDatabaseCount('unpaid_items', 1);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1);

    $this->assertDatabaseHas('unpaid_items', [
        'currency_code' => 'MYR',
        'created_by_employee_id' => SystemHelper::getSystemEmployee()->id,
        'product_id' => $product_1->id,
        'unit_price' => $product_1->unit_price,
        'quantity' => 1,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'period' => '2024-01-01',
        'description' => 'General Fee (2024-01-01)',
        'gl_account_code' => $product_1->gl_account_code,
        'amount_before_tax' => 1000,
        'paid_at' => null,
    ]);

    $this->assertDatabaseHas('billing_documents', [
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'classification' => 'AR',
        'document_date' => now()->toDateString(),
        'posting_date' => null,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'legal_entity_id' => SystemHelper::getDefaultLegalEntity()->id,
        'legal_entity_name' => SystemHelper::getDefaultLegalEntity()->name,
        'legal_entity_address' => SystemHelper::getDefaultLegalEntity()->address,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'bill_to_name' => 'John Doe',
        'bill_to_reference_number' => $enrollment->getBillToReferenceNumber(),
        'bill_to_address' => $enrollment->address,
        'receipt_url' => null,
        'currency_code' => 'MYR',
        'amount_before_tax' => 1000,
        'amount_before_tax_after_less_advance' => 1000,
        'tax_amount' => 0,
        'amount_after_tax' => 1000,
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $enrollment_user->id,
        'requested_by_type' => get_class($enrollment_user),
        'transaction_loggable_type' => Enrollment::class,
        'transaction_loggable_id' => $enrollment->id,
        'type' => PaymentType::ENROLLMENT_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'amount' => 1000,
        'status' => PaymentStatus::PENDING,
        'description' => 'Pay Enrollment Fee',
        'payment_url' => 'VALID_PAYMENT_URL'
    ]);

    $enrollment->refresh();

    $created_billing_document = $enrollment->billingDocuments->first();

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'payment_status' => EnrollmentPaymentStatus::PENDING->value,
        'billing_document_id' => $created_billing_document->id,
    ]);
});

test('makePayment (FE), no fees to be paid [FPX_PAYMENT]', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    createPayexConfig();
    mockPayex();

    $payload = [
        'is_manual_payment_requested' => false,
    ];

    $response = $this->postJson(route('enrollments.make-payment', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toBe(['payment_required' => false]);

    $this->assertDatabaseCount('unpaid_items', 0);
    $this->assertDatabaseCount('billing_documents', 0);
    $this->assertDatabaseCount('billing_document_line_items', 0);
    $this->assertDatabaseCount('payment_gateway_logs', 0);

    $enrollment->refresh();

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'payment_status' => EnrollmentPaymentStatus::NOT_REQUIRED->value,
        'billing_document_id' => null
    ]);
});

test('makePayment (FE), BO assign new fees to pay before user create initial billing_document [FPX_PAYMENT]', function () {

    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
            [
                'conditions' => null,
                'outcome' => [
                    'product_id' => $product_1->id,
                    'amount' => 1000,
                    'period' => '2024-01-01',
                ]
            ],
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    // ASSIGN new billing document to enrollment before user make payment
    $extra_billing_document = BillingDocument::factory()->create([
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'classification' => 'AR',
        'document_date' => now()->toDateString(),
        'posting_date' => null,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'legal_entity_id' => SystemHelper::getDefaultLegalEntity()->id,
        'legal_entity_name' => SystemHelper::getDefaultLegalEntity()->name,
        'legal_entity_address' => SystemHelper::getDefaultLegalEntity()->address,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'bill_to_name' => $enrollment->name,
        'bill_to_address' => $enrollment->address,
        'currency_code' => 'MYR',
        'amount_before_tax' => 200,
        'amount_before_tax_after_less_advance' => 200,
        'tax_amount' => 0,
        'amount_after_tax' => 200,
    ]);

    $enrollment->refresh();

    expect($enrollment->billingDocuments)->toHaveCount(1);

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    createPayexConfig();
    mockPayex();

    $payload = [
        'is_manual_payment_requested' => false,
    ];

    $response = $this->postJson(route('enrollments.make-payment', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'currency' => 'MYR',
            'amount' => 1000,
            'status' => PaymentStatus::PENDING->value,
            'payment_url' => 'VALID_PAYMENT_URL',
            'payment_required' => true,
        ])->toHaveKey('order_id');

    $this->assertDatabaseCount('unpaid_items', 1);
    $this->assertDatabaseCount('billing_documents', 2);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1);

    $this->assertDatabaseHas('billing_documents', [
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'classification' => 'AR',
        'document_date' => now()->toDateString(),
        'posting_date' => null,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'legal_entity_id' => SystemHelper::getDefaultLegalEntity()->id,
        'legal_entity_name' => SystemHelper::getDefaultLegalEntity()->name,
        'legal_entity_address' => SystemHelper::getDefaultLegalEntity()->address,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'bill_to_name' => 'John Doe',
        'bill_to_reference_number' => $enrollment->getBillToReferenceNumber(),
        'bill_to_address' => $enrollment->address,
        'receipt_url' => null,
        'currency_code' => 'MYR',
        'amount_before_tax' => 1000,
        'amount_before_tax_after_less_advance' => 1000,
        'tax_amount' => 0,
        'amount_after_tax' => 1000,
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $enrollment_user->id,
        'requested_by_type' => get_class($enrollment_user),
        'transaction_loggable_type' => Enrollment::class,
        'transaction_loggable_id' => $enrollment->id,
        'type' => PaymentType::ENROLLMENT_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'amount' => 1000,
        'status' => PaymentStatus::PENDING,
        'description' => 'Pay Enrollment Fee',
        'payment_url' => 'VALID_PAYMENT_URL'
    ]);

    $enrollment->refresh();

    expect($enrollment->billingDocuments)->toHaveCount(2);

    $billing_documents = $enrollment->billingDocuments->sortByDesc('id');

    $created_billing_document = $billing_documents->first();
    $old_billing_document = $billing_documents->last();

    expect($old_billing_document->id)->toEqual($extra_billing_document->id);

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'payment_status' => EnrollmentPaymentStatus::PENDING->value,
        'billing_document_id' => $created_billing_document->id, // enrollment is tied to newly created billing document
    ]);
});

test('makePayment (FE) failed because of Payex [FPX_PAYMENT]', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
            [
                'conditions' => null,
                'outcome' => [
                    'product_id' => $product_1->id,
                    'amount' => 1000,
                    'period' => '2024-01-01',
                ]
            ],
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
    ]);

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    createPayexConfig();
    mockFailedPayex();

    $payload = [
        'is_manual_payment_requested' => false,
    ];

    $response = $this->postJson(route('enrollments.make-payment', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response['code'])->toBe(500)
        ->and($response['error'])->toBe('INVALID PAYMENT');
});

test('makePayment (FE) : [MANUAL_PAYMENT]', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
            [
                'conditions' => null,
                'outcome' => [
                    'product_id' => $product_1->id,
                    'amount' => 1000,
                    'period' => '2024-01-01',
                ]
            ],
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    createPayexConfig();
    mockPayex();

    $payload = [
        'is_manual_payment_requested' => true,
    ];

    $response = $this->postJson(route('enrollments.make-payment', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'currency' => 'MYR',
            'amount' => 1000,
            'status' => null,
            'payment_url' => null,
            'payment_required' => false,
            'is_manual_payment' => true,
        ]);

    $this->assertDatabaseCount('unpaid_items', 1);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 0); // no payment gateway log created for manual payment

    $this->assertDatabaseHas('unpaid_items', [
        'currency_code' => 'MYR',
        'created_by_employee_id' => SystemHelper::getSystemEmployee()->id,
        'product_id' => $product_1->id,
        'unit_price' => $product_1->unit_price,
        'quantity' => 1,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'period' => '2024-01-01',
        'description' => 'General Fee (2024-01-01)',
        'gl_account_code' => $product_1->gl_account_code,
        'amount_before_tax' => 1000,
        'paid_at' => null,
    ]);

    $this->assertDatabaseHas('billing_documents', [
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'classification' => 'AR',
        'document_date' => now()->toDateString(),
        'posting_date' => null,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
        'legal_entity_id' => SystemHelper::getDefaultLegalEntity()->id,
        'legal_entity_name' => SystemHelper::getDefaultLegalEntity()->name,
        'legal_entity_address' => SystemHelper::getDefaultLegalEntity()->address,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'bill_to_name' => 'John Doe',
        'bill_to_reference_number' => $enrollment->getBillToReferenceNumber(),
        'bill_to_address' => $enrollment->address,
        'receipt_url' => null,
        'currency_code' => 'MYR',
        'amount_before_tax' => 1000,
        'amount_before_tax_after_less_advance' => 1000,
        'tax_amount' => 0,
        'amount_after_tax' => 1000,
    ]);

    $enrollment->refresh();

    $created_billing_document = $enrollment->billingDocuments->first();

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'payment_status' => EnrollmentPaymentStatus::PENDING->value,
        'billing_document_id' => $created_billing_document->id,
    ]);
});

test('makePayment (FE) failed because of expiry', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
            [
                'conditions' => null,
                'outcome' => [
                    'product_id' => $product_1->id,
                    'amount' => 1000,
                    'period' => '2024-01-01',
                ]
            ],
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'expiry_date' => now()->subDays(1), // expiry date to yesterday
    ]);

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    $response = $this->postJson(route('enrollments.make-payment', ['enrollment' => $enrollment->id]))->json();

    expect($response['code'])->toBe(5029)
        ->and($response['error'])->toBe(__('system_error.5029'));
});

test('update (FE)', function () {
    $enrollment_user = EnrollmentUser::factory()->create();

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    $enrollment = Enrollment::factory()->create([
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $religion = Religion::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $school = School::factory()->create(['level' => SchoolLevel::PRIMARY]);

    $payload = [
        'email' => '<EMAIL>',
        'phone_number' => '+60*********',
        'phone_number_2' => '+60198765432',
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => $country->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => '123 John Updated St, John City',
        'postal_code' => '50000',
        'city' => 'John City',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'address_2' => 'Apt 101, John Tower',
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $school->id,
        'guardians' => [
            [
                'name' => ['en' => 'John'],
                'email' => '<EMAIL>',
                'phone_number' => '+60*********',
                'nric' => '900101101234',
                'passport_number' => 'A12345678',
                'nationality_id' => $country->id,
                'race_id' => $race->id,
                'religion_id' => $religion->id,
                'education_id' => null,
                'married_status' => MarriedStatus::MARRIED->value,
                'live_status' => LiveStatus::NORMAL->value,
                'occupation' => 'Engineer',
                'occupation_description' => null,
                'guardian_type' => GuardianType::FATHER->value,
            ],
            [
                'name' => ['en' => 'Jane'],
                'email' => '<EMAIL>',
                'phone_number' => '+60123456777',
                'guardian_type' => GuardianType::MOTHER->value,
            ],
            [
                'name' => ['en' => 'Guardian'],
                'email' => '<EMAIL>',
                'phone_number' => '+60123456790',
                'guardian_type' => GuardianType::GUARDIAN->value,
            ],
        ],
    ];

    $response = $this->putJson(route('enrollments.update', ['enrollment' => $enrollment->id]), $payload)->json();

    $enrollment->refresh();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $enrollment->id,
            'name' => $enrollment->name,
            'email' => $payload['email'],
            'phone_number' => $payload['phone_number'],
            'phone_number_2' => $payload['phone_number_2'],
            'nric' => $enrollment->nric,
            'passport_number' => $enrollment->passport_number,
            'birthplace' => $payload['birthplace'],
            'nationality' => resourceToArray(new CountryResource($country)),
            'date_of_birth' => $payload['date_of_birth'],
            'gender' => $payload['gender'],
            'birth_cert_number' => $payload['birth_cert_number'],
            'race' => resourceToArray(new RaceResource($race)),
            'religion' => resourceToArray(new ReligionResource($religion)),
            'address' => $payload['address'],
            'postal_code' => $payload['postal_code'],
            'city' => $payload['city'],
            'state' => resourceToArray(new StateResource($state)),
            'country' => resourceToArray(new CountryResource($country)),
            'address_2' => $payload['address_2'],
            'dietary_restriction' => $payload['dietary_restriction'],
            'health_concern' => resourceToArray(new HealthConcernResource($health_concern)),
            'primary_school' => resourceToArray(new SchoolResource($school)),
            'guardians' => resourceToArray(EnrollmentGuardianResource::collection($enrollment->guardians)),
        ]);
});

test('update (FE) validation error', function () {
    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment_user2 = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    Sanctum::actingAs($enrollment_user2, [], 'enrollment');

    $response = $this->putJson(route('enrollments.update', ['enrollment' => $enrollment->id]), []);
    expect($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toBe("This action is unauthorized.");

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    expect(Enrollment::count())->toBe(1);

    $response = $this->putJson(route('enrollments.update', ['enrollment' => $enrollment->id]), []);

    $response->assertStatus(422);

    // Expect validation error
    expect($response->json()['error'])->toBe([
        'birthplace' => [
            'The birthplace field is required.'
        ],
        'nationality_id' => [
            'The nationality id field is required.'
        ],
        'date_of_birth' => [
            'The date of birth field is required.'
        ],
        'gender' => [
            'The gender field is required.'
        ],
        'birth_cert_number' => [
            'The birth cert number field is required.'
        ],
        'race_id' => [
            'The race id field is required.'
        ],
        'religion_id' => [
            'The religion id field is required.'
        ],
        'address' => [
            'The address field is required.'
        ],
        'postal_code' => [
            'The postal code field is required.'
        ],
        'city' => [
            'The city field is required.'
        ],
        'state_id' => [
            'The state id field is required.'
        ],
        'country_id' => [
            'The country id field is required.'
        ],
        'primary_school_id' => [
            'The primary school id field is required.'
        ],
        'guardians' => [
            'The guardians field is required.'
        ],
    ]);

    // failed because missing FATHER and MOTHER

    $payload = [
        'email' => '<EMAIL>',
        'phone_number' => '+60*********',
        'phone_number_2' => '+60198765432',
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => Country::factory()->create()->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => Race::factory()->create()->id,
        'religion_id' => Religion::factory()->create()->id,
        'address' => '123 John Updated St, John City',
        'postal_code' => '50000',
        'city' => 'John City',
        'state_id' => State::factory()->create()->id,
        'country_id' => Country::factory()->create()->id,
        'address_2' => 'Apt 101, John Tower',
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => HealthConcern::factory()->create()->id,
        'primary_school_id' => School::factory()->create()->id,
        'guardians' => [
            [
                'name' => ['en' => 'John'],
                'email' => '<EMAIL>',
                'phone_number' => '+60*********',
                'guardian_type' => GuardianType::GUARDIAN->value,
            ],
        ],
    ];

    $response = $this->putJson(route('enrollments.update', ['enrollment' => $enrollment->id]), $payload);

    $response->assertStatus(422);

    expect($response->json()['error'])->toBe([
        'guardians' => [
            'Both Mother and Father information are required.'
        ],
    ]);


    // failed because expired enrollment

    $enrollment->update(['expiry_date' => now()->subDays(1)]);

    $payload = [
        'email' => '<EMAIL>',
        'phone_number' => '+60*********',
        'phone_number_2' => '+60198765432',
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => Country::factory()->create()->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => Race::factory()->create()->id,
        'religion_id' => Religion::factory()->create()->id,
        'address' => '123 John Updated St, John City',
        'postal_code' => '50000',
        'city' => 'John City',
        'state_id' => State::factory()->create()->id,
        'country_id' => Country::factory()->create()->id,
        'address_2' => 'Apt 101, John Tower',
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => HealthConcern::factory()->create()->id,
        'primary_school_id' => School::factory()->create()->id,
        'guardians' => [
            [
                'name' => ['en' => 'John'],
                'email' => '<EMAIL>',
                'phone_number' => '+60*********',
                'guardian_type' => GuardianType::FATHER->value,
            ],
            [
                'name' => ['en' => 'Jane'],
                'email' => '<EMAIL>',
                'phone_number' => '+60123456777',
                'guardian_type' => GuardianType::MOTHER->value,
            ],
        ],
    ];

    $response = $this->putJson(route('enrollments.update', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response['code'])->toBe(5029)
        ->and($response['error'])->toBe(__('system_error.5029'));
});

test('update (ADMIN)', function () {
    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    // with exam + marks
    $exam = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment->id,
        'total_average' => 85.05,
    ]);

    $marks = EnrollmentExamMark::factory(2)->create([
        'enrollment_exam_id' => $exam->id,
        'mark' => 85.05,
    ]);

    $grade = Grade::factory()->create();
    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $religion = Religion::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $school = School::factory()->create(['level' => SchoolLevel::PRIMARY]);

    $payload = [
        'name' => ['en' => 'Admin Updated Name', 'zh' => '管理员更新名称'],
        'email' => '<EMAIL>',
        'phone_number' => '+60*********',
        'phone_number_2' => '+60198765432',
        'nric' => '900101101234',
        'passport_number' => 'A12345678',
        'admission_year' => 2025,
        'admission_grade_id' => $grade->id,
        'join_date' => '2025-01-15',
        'leave_date' => null,
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => $country->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => '123 Admin Updated St, Admin City',
        'postal_code' => '50000',
        'city' => 'Admin City',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'address_2' => 'Apt 101, Admin Tower',
        'is_hostel' => true,
        'is_active' => true,
        'remarks' => 'Updated by admin with new details.',
        'custom_field' => "{\"key1\":\"value1\",\"key2\":\"value2\"}",
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $school->id,
        'admission_type' => 'NEW',
        'have_siblings' => true,
        'is_foreigner' => false,
        'conduct' => 'A+',
        'enrollment_status' => EnrollmentStatus::APPROVED->value,
        'guardians' => [
            [
                'name' => ['en' => 'John'],
                'email' => '<EMAIL>',
                'phone_number' => '+60*********',
                'nric' => '900101101234',
                'passport_number' => 'A12345678',
                'nationality_id' => $country->id,
                'race_id' => $race->id,
                'religion_id' => $religion->id,
                'education_id' => null,
                'married_status' => MarriedStatus::MARRIED->value,
                'live_status' => LiveStatus::NORMAL->value,
                'occupation' => 'Engineer',
                'occupation_description' => null,
            ],
        ],
        'enrollment_exams' => [
            [
                'exam_id' => $exam->id,
                'total_average' => 90.07,
                'marks' => [
                    [
                        'exam_mark_id' => $marks[0]->id,
                        'mark' => 90.07,
                    ],
                    [
                        'exam_mark_id' => $marks[1]->id,
                        'mark' => 90.07,
                    ],
                ],
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeName . '.show', ['enrollment' => $enrollment->id]), $payload)->json();

    $enrollment->refresh();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $enrollment->id,
            'name' => $payload['name']['en'],
            'email' => $payload['email'],
            'phone_number' => $payload['phone_number'],
            'phone_number_2' => $payload['phone_number_2'],
            'nric' => $payload['nric'],
            'passport_number' => $payload['passport_number'],
            'admission_year' => $payload['admission_year'],
            'join_date' => $payload['join_date'],
            'leave_date' => $payload['leave_date'],
            'birthplace' => $payload['birthplace'],
            'date_of_birth' => $payload['date_of_birth'],
            'gender' => $payload['gender'],
            'birth_cert_number' => $payload['birth_cert_number'],
            'address' => $payload['address'],
            'postal_code' => $payload['postal_code'],
            'city' => $payload['city'],
            'address_2' => $payload['address_2'],
            'is_hostel' => $payload['is_hostel'],
            'is_active' => $payload['is_active'],
            'remarks' => $payload['remarks'],
            'custom_field' => $payload['custom_field'],
            'dietary_restriction' => $payload['dietary_restriction'],
            'admission_type' => $payload['admission_type'],
            'have_siblings' => $payload['have_siblings'],
            'is_foreigner' => $payload['is_foreigner'],
            'conduct' => $payload['conduct'],
            'enrollment_status' => $payload['enrollment_status'],
            'admission_grade' => resourceToArray(new GradeResource($grade)),
            'nationality' => resourceToArray(new CountryResource($country)),
            'race' => resourceToArray(new RaceResource($race)),
            'religion' => resourceToArray(new ReligionResource($religion)),
            'state' => resourceToArray(new StateResource($state)),
            'country' => resourceToArray(new CountryResource($country)),
            'health_concern' => resourceToArray(new HealthConcernResource($health_concern)),
            'primary_school' => resourceToArray(new SchoolResource($school)),
            'guardians' => resourceToArray(EnrollmentGuardianResource::collection($enrollment->guardians->loadMissing(['religion', 'nationality', 'race', 'education', 'religion']))),
            'enrollment_exams' => resourceToArray(EnrollmentExamResource::collection($enrollment->enrollmentExams->loadMissing('examMarks.subject'))),
        ]);
});

test('update (ADMIN) validation error', function () {
    $enrollment = Enrollment::factory()->create([
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'billing_document_id' => null,
    ]);

    $secondary_school = School::factory()->create([
        'level' => SchoolLevel::SECONDARY,
    ]);

    $payload = [
        'primary_school_id' => $secondary_school->id,
    ];

    expect(Enrollment::count())->toBe(1);

    $response = $this->putJson(route($this->routeName . '.update', ['enrollment' => $enrollment->id]), $payload);

    $response->assertStatus(422);

    // Test 1 : required fields
    expect($response->json()['error'])->toBe([
        'name' => [
            'The name field is required.'
        ],
        'nric' => [
            'The nric field is required when passport number is not present.'
        ],
        'passport_number' => [
            'The passport number field is required when nric is not present.'
        ],
        'birthplace' => [
            'The birthplace field is required.'
        ],
        'nationality_id' => [
            'The nationality id field is required.'
        ],
        'date_of_birth' => [
            'The date of birth field is required.'
        ],
        'gender' => [
            'The gender field is required.'
        ],
        'birth_cert_number' => [
            'The birth cert number field is required.'
        ],
        'race_id' => [
            'The race id field is required.'
        ],
        'religion_id' => [
            'The religion id field is required.'
        ],
        'address' => [
            'The address field is required.'
        ],
        'postal_code' => [
            'The postal code field is required.'
        ],
        'city' => [
            'The city field is required.'
        ],
        'state_id' => [
            'The state id field is required.'
        ],
        'country_id' => [
            'The country id field is required.'
        ],
        'primary_school_id' => [
            'The selected primary school id is invalid.'
        ],
        'enrollment_status' => [
            'The enrollment status field is required.'
        ],
        'guardians' => [
            'The guardians field is required.'
        ],
    ]);

    Student::factory()->create([
        'nric' => '*********012',
        'passport_number' => '1122334455',
        'student_number' => 'S1001',
        'birth_cert_number' => 'BC123456',
    ]);

    $enrollment_1 = Enrollment::factory()->create();

    $response = $this->putJson(route($this->routeName . '.update', $enrollment_1->id), [
        'nric' => '*********012',
        'passport_number' => '1122334455',
        'student_number' => 'S1001',
        'birth_cert_number' => 'BC123456',
    ]);

    // Test 2 : nric, passport, student_number, birth_cert_number must be unique
    expect(Enrollment::count())->toBe(2)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'nric' => [
                'The nric has already been taken.'
            ],
            'passport_number' => [
                'The passport number has already been taken.'
            ],
            'birth_cert_number' => [
                'The birth cert number has already been taken.'
            ],
        ]);

    // Test 3 : validate ENUMs
    $enrollment_2 = Enrollment::factory()->create();

    $response = $this->putJson(route($this->routeName . '.update', $enrollment_2->id), [
        'leave_status' => 'INVALID_STATUS',
        'gender' => 'INVALID_GENDER',
        'dietary_restriction' => 'INVALID_DIETARY_RESTRICTION',
        'admission_type' => 'INVALID_ADMISSION_TYPE',
        'enrollment_status' => 'INVALID_ENROLLMENT_STATUS',
    ]);

    expect(Enrollment::count())->toBe(3)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'leave_status' => [
                'The selected leave status is invalid.',
            ],
            'gender' => [
                'The selected gender is invalid.',
            ],
            'dietary_restriction' => [
                'The selected dietary restriction is invalid.',
            ],
            'admission_type' => [
                'The selected admission type is invalid.',
            ],
            'enrollment_status' => [
                'The selected enrollment status is invalid.',
            ],
        ]);


    // Test 4 : validate guardians required fields
    $enrollment_3 = Enrollment::factory()->create();

    $response = $this->putJson(route($this->routeName . '.update', $enrollment_3->id), [
        'guardians' => [
            [
                // 'name' => ['en' => 'Guardian Name'], // required
                // 'phone_number' => '+60*********', // required
                'nric' => '900101101234',
            ]
        ],
    ]);

    expect(Enrollment::count())->toBe(4)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'guardians.0.name' => [
                'The guardians.0.name field is required.',
            ],
            'guardians.0.phone_number' => [
                'The guardians.0.phone_number field is required.',
            ],
        ]);


    // Test 5 : validate nationality, race, religion, education
    $enrollment_4 = Enrollment::factory()->create();

    $response = $this->putJson(route($this->routeName . '.update', $enrollment_4->id), [
        'guardians' => [
            [
                'name' => ['en' => 'Guardian Name'],
                'phone_number' => '+60*********',
                'nationality_id' => 222222,
                'race_id' => 2222222,
                'religion_id' => 222222,
                'education_id' => 222222,
            ]
        ],
    ]);

    expect(Enrollment::count())->toBe(5)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'guardians.0.nationality_id' => [
                'The selected guardians.0.nationality_id is invalid.',
            ],
            'guardians.0.race_id' => [
                'The selected guardians.0.race_id is invalid.',
            ],
            'guardians.0.religion_id' => [
                'The selected guardians.0.religion_id is invalid.',
            ],
            'guardians.0.education_id' => [
                'The selected guardians.0.education_id is invalid.',
            ],
        ]);

    // Test 6 : validate enrollment exams required fields
    $response = $this->putJson(route($this->routeName . '.update', $enrollment_4->id), [
        'enrollment_exams' => [
            [
                'total_average' => 90.07,
                'marks' => 'NOT AN ARRAY', // marks should be an array
            ]
        ],
    ]);

    expect(Enrollment::count())->toBe(5)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'enrollment_exams.0.exam_id' => [
                'The enrollment_exams.0.exam_id field is required.',
            ],
            'enrollment_exams.0.marks' => [
                'The enrollment_exams.0.marks field must be an array.',
            ],
        ]);

    // Test 7 : validate enrollment exams exam_id, total_average, marks
    $enrollment_5 = Enrollment::factory()->create();

    $response = $this->putJson(route($this->routeName . '.update', $enrollment_5->id), [
        'enrollment_exams' => [
            [
                'exam_id' => 999999, // invalid exam_id
                'total_average' => 90.07,
                'marks' => [
                    [
                        'exam_mark_id' => 1, // invalid exam_mark_id
                        'mark' => 90.07,
                    ]
                ],
            ]
        ],
    ]);

    expect(Enrollment::count())->toBe(6)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'enrollment_exams.0.exam_id' => [
                'The selected enrollment_exams.0.exam_id is invalid.',
            ],
            'enrollment_exams.0.marks.0.exam_mark_id' => [
                'The selected enrollment_exams.0.marks.0.exam_mark_id is invalid.',
            ],
        ]);
});


test('importPostPaymentTemplateValidation success', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $math_subject = Subject::factory()->create(['code' => 'MATH001']);
    $eng_subject = Subject::factory()->create(['code' => 'ENG001']);

    $enrollment_session->examSubjects()->attach([
        $math_subject->id,
        $eng_subject->id,
    ]);

    $enrollment1 = Enrollment::factory()->create([
        'name' => ['en' => 'John Doe'],
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $enrollment2 = Enrollment::factory()->create([
        'name' => ['en' => 'John Cena'],
        'nric' => null,
        'passport_number' => 'A1234567',
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::REJECTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $enrollment_exam1 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => 'SLIP_EXAM_001',
        'total_average' => 75.0,
    ]);

    $enrollment_exam2 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment2->id,
        'exam_slip_number' => 'SLIP_EXAM_002',
        'total_average' => 80.0,
    ]);

    // enrollment exam 1
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $math_subject->id,
        'mark' => 75.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $eng_subject->id,
        'mark' => 75.0,
    ]);

    // enrollment exam 2
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam2->id,
        'subject_id' => $math_subject->id,
        'mark' => 80.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam2->id,
        'subject_id' => $eng_subject->id,
        'mark' => 80.0,
    ]);

    // convert Enrollment Template Excel to collection
    $row1 = ['1', 'SLIP_EXAM_001', 'John Doe', '990101123456', null, 'TRUEs', '99.05', '99.05', '99.05', 'APPROVEDs'];

    $row2 = ['2', 'SLIP_EXAM_002', 'John Cena', null, 'A1234567', 'TRUE', '88.05', '88.05', '88.05', 'REJECTED'];

    $header = [
        'No',
        '考生编号/Exam Slip',
        'Student Name',
        '身份证号码 / IC Number',
        'Passport Number',
        '宿舍 / Hostel (TRUE, FALSE)',
        '总平均 / Total Average',
        'English Language',
        'Mathematics',
        'Status (APPROVED, REJECTED, SHORLISTED)'
    ];

    $test_excel = createFakeExcelFileWithData([
        $header,
        $row1,
        $row2,
    ]);

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'file' => $test_excel,
    ];

    $response = $this->postJson(route($this->routeName . '.import-post-payment-template-validation'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['data'])->toHaveCount(2)
        ->and($response['data']['data'])->toEqual([
            [
                'number' => '1',
                'exam_slip_number' => 'SLIP_EXAM_001',
                'student_name_en' => 'JOHN DOE',
                'nric' => '990101123456',
                'passport_number' => null,
                'hostel' => null,
                'total_average' => '99.05',
                'ENG001' => '99.05',
                'MATH001' => '99.05',
                'status' => 'APPROVEDs',
                'errors' => [
                    'status' => [
                        'Status must be REJECTED, APPROVED.',
                    ],
                    'hostel' => [
                        'Hostel must be boolean.',
                    ],
                ],
            ],
            [
                'number' => '2',
                'exam_slip_number' => 'SLIP_EXAM_002',
                'student_name_en' => 'JOHN CENA',
                'nric' => null,
                'passport_number' => 'A1234567',
                'hostel' => true,
                'total_average' => '88.05',
                'ENG001' => '88.05',
                'MATH001' => '88.05',
                'status' => 'REJECTED',
            ],
        ])
        ->and($response['data']['subject_lists'])->toHaveCount(2)
        ->and($response['data']['subject_lists'])->toHaveKey('ENG001')
        ->and($response['data']['subject_lists'])->toHaveKey('MATH001')
        ->and($response['data']['error_count'])->toBe(2);
});

test('importPostPaymentTemplateValidation failed because validation error', function () {
    /**
     * NO PAYLOAD
     */
    $payload = [];

    $response = $this->postJson(route($this->routeName . '.import-post-payment-template-validation'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'file' => [
                'The file field is required.'
            ],
            'enrollment_session_id' => [
                'The enrollment session id field is required.'
            ],
        ],
        'data' => null
    ]);

    /**
     * INVALID FILE TYPE NOT EXCEL && INVALID ENROLLMENT SESSION ID
     */
    $payload = [
        'enrollment_session_id' => 99999999,
        'file' => UploadedFile::fake()->create('file.png', 500),
    ];

    $response = $this->postJson(route($this->routeName . '.import-post-payment-template-validation'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'file' => [
                'The file field must be a file of type: xlsx.'
            ],
            'enrollment_session_id' => [
                'The selected enrollment session id is invalid.'
            ],
        ],
        'data' => null
    ]);
});


test('bulkSavePostPaymentImportedData', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $math_subject = Subject::factory()->create(['code' => 'MATH001']);
    $eng_subject = Subject::factory()->create(['code' => 'ENG001']);

    $enrollment_session->examSubjects()->attach([
        $math_subject->id,
        $eng_subject->id,
    ]);

    $enrollment1 = Enrollment::factory()->create([
        'name' => ['en' => 'John Doe'],
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $enrollment_exam1 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => 'SLIP_EXAM_001',
        'total_average' => 75.0,
    ]);

    // enrollment exam 1
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $math_subject->id,
        'mark' => 75.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $eng_subject->id,
        'mark' => 75.0,
    ]);

    $dummy_enrollments = [
        [
            'number' => '1',
            'exam_slip_number' => 'SLIP_EXAM_001',
            'student_name_en' => 'John Doe',
            'nric' => '990101123456',
            'passport_number' => null,
            'hostel' => true,
            'total_average' => '100.05',
            'ENG001' => '100.05',
            'MATH001' => '100.05',
            'status' => 'APPROVED',
        ],
    ];

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'enrollments' => $dummy_enrollments
    ];

    $response = $this->postJson(route($this->routeName . '.bulk-save-post-payment-imported-data'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->enrollmentTableName, 1);
    $this->assertDatabaseCount('enrollment_exams', 1);
    $this->assertDatabaseCount('enrollment_exam_marks', 2);
});

test('bulkSavePostPaymentImportedData : error validation, nothing updated', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $math_subject = Subject::factory()->create(['code' => 'MATH001']);
    $eng_subject = Subject::factory()->create(['code' => 'ENG001']);

    $enrollment_session->examSubjects()->attach([
        $math_subject->id,
        $eng_subject->id,
    ]);

    $enrollment1 = Enrollment::factory()->create([
        'name' => ['en' => 'John Doe'],
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $enrollment_exam1 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => 'SLIP_EXAM_001',
        'total_average' => 75.0,
    ]);

    // enrollment exam 1
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $math_subject->id,
        'mark' => 75.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $eng_subject->id,
        'mark' => 75.0,
    ]);

    $dummy_enrollments = [
        [
            'number' => '1',
            'exam_slip_number' => 'SLIP_EXAM_0012',
            'student_name_en' => 'John Doe',
            'nric' => '9901011234562',
            'passport_number' => null,
            'hostel' => false,
            'total_average' => '100.05',
            'ENG001' => '100.05',
            'MATH001' => '100.05',
            'status' => 'APPROVEDs',
        ],
    ];

    $payload = [
        'enrollment_session_id' => $enrollment_session->id,
        'enrollments' => $dummy_enrollments
    ];

    $response = $this->postJson(route($this->routeName . '.bulk-save-post-payment-imported-data'), $payload)->json();

    expect($response['code'])->toEqual(422)
        ->and($response['data']['data'])->toHaveCount(1)
        ->and($response['data']['data'][0]['errors'])->toHaveCount(2); // Status and NRIC


    // still original data
    $this->assertDatabaseHas($this->enrollmentTableName, [
        'id' => $enrollment1->id,
        'name->en' => 'John Doe',
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => 'SLIP_EXAM_001',
        'total_average' => 75.0,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $math_subject->id,
        'mark' => 75.0,

    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $eng_subject->id,
        'mark' => 75.0,
    ]);
});

test('retryPayment : success', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
            [
                'conditions' => null,
                'outcome' => [
                    'product_id' => $product_1->id,
                    'amount' => 1000,
                    'period' => '2024-01-01',
                ]
            ],
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    $billing_document = BillingDocument::factory()->create([
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'classification' => 'AR',
        'document_date' => now()->toDateString(),
        'posting_date' => null,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'legal_entity_id' => SystemHelper::getDefaultLegalEntity()->id,
        'legal_entity_name' => SystemHelper::getDefaultLegalEntity()->name,
        'legal_entity_address' => SystemHelper::getDefaultLegalEntity()->address,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'bill_to_name' => $enrollment->name,
        'bill_to_address' => $enrollment->address,
        'currency_code' => 'MYR',
        'amount_before_tax' => 200,
        'amount_before_tax_after_less_advance' => 200,
        'tax_amount' => 0,
        'amount_after_tax' => 200,
    ]);

    $enrollment->billing_document_id = $billing_document->id;
    $enrollment->save();

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    createPayexConfig();
    mockPayex();

    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 0);

    $response = $this->postJson(route('enrollments.retry-payment', ['billing_document' => $billing_document->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'billing_document_id' => $billing_document->id,
            'amount' => $billing_document->amount_after_tax,
            'status' => PaymentStatus::PENDING->value,
            'payment_url' => 'VALID_PAYMENT_URL',
            'payment_required' => true,
        ])->toHaveKey('order_id');


    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1);
});

test('retryPayment : failed because of PAYEX', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => [
            [
                'conditions' => null,
                'outcome' => [
                    'product_id' => $product_1->id,
                    'amount' => 1000,
                    'period' => '2024-01-01',
                ]
            ],
        ],
    ]);

    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'enrollment_user_id' => $enrollment_user->id,
        'billing_document_id' => null,
    ]);

    $billing_document = BillingDocument::factory()->create([
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'classification' => 'AR',
        'document_date' => now()->toDateString(),
        'posting_date' => null,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'legal_entity_id' => SystemHelper::getDefaultLegalEntity()->id,
        'legal_entity_name' => SystemHelper::getDefaultLegalEntity()->name,
        'legal_entity_address' => SystemHelper::getDefaultLegalEntity()->address,
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'bill_to_name' => $enrollment->name,
        'bill_to_address' => $enrollment->address,
        'currency_code' => 'MYR',
        'amount_before_tax' => 200,
        'amount_before_tax_after_less_advance' => 200,
        'tax_amount' => 0,
        'amount_after_tax' => 200,
    ]);

    $enrollment->billing_document_id = $billing_document->id;
    $enrollment->save();

    Sanctum::actingAs($enrollment_user, [], 'enrollment');

    createPayexConfig();
    mockFailedPayex();

    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 0);

    $response = $this->postJson(route('enrollments.retry-payment', ['billing_document' => $billing_document->id]))->json();

    expect($response['code'])->toBe(500)
        ->and($response['error'])->toBe('INVALID PAYMENT');

    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1); // still created log even if failed

    $this->assertDatabaseHas('payment_gateway_logs', [
        'billing_document_id' => $billing_document->id,
        'status' => PaymentStatus::FAILED->value,
        'remark' => 'INVALID PAYMENT',
    ]);
});

test('updateStatus', function () {
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::PENDING_PAYMENT->value,
    ]);

    $payload = [
        'status' => EnrollmentStatus::APPROVED->value,
    ];

    $response = $this->putJson(route($this->routeName . '.update-status', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::APPROVED->value,
    ]);


    $payload = [
        'status' => EnrollmentStatus::REJECTED->value,
    ];

    $response = $this->putJson(route($this->routeName . '.update-status', ['enrollment' => $enrollment->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::REJECTED->value,
    ]);

});
