<?php

use App\Enums\ClassType;
use App\Http\Resources\TimeslotOverrideResource;
use App\Models\Employee;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\PeriodAttendance;
use App\Models\SemesterClass;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\TimeslotOverride;
use App\Models\User;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);
    app()->setLocale('en');
    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);
    $user->assignRole('Super Admin');
    Sanctum::actingAs($user);

    $this->timeslotOverrideTable = resolve(TimeslotOverride::class)->getTable();
    $this->routeNamePrefix = 'timeslot-override.';
});

test('index', function (int $expected_count, array $filters, array $expected_models, bool $paginated = true) {
    $student = Student::factory()->create(['id' => 1001]);
    $student2 = Student::factory()->create(['id' => 2001]);

    $employee = Employee::factory()->create(['id' => 3001]);
    $employee2 = Employee::factory()->create(['id' => 4001]);

    $timeslot_override = [
        'first_timeslot_override' => TimeslotOverride::factory()->create([
            'student_id' => $student->id,
            'date' => '2025-04-04',
            'period' => 1,
            'employee_id' => $employee->id,
            'inherit_from_school_attendance' => false,
            'class_attendance_required' => false,
            'is_empty' => false,
        ]),
        'second_timeslot_override' => TimeslotOverride::factory()->create([
            'student_id' => $student2->id,
            'date' => '2025-04-04',
            'period' => 1,
            'employee_id' => $employee2->id,
            'inherit_from_school_attendance' => false,
            'class_attendance_required' => false,
            'is_empty' => true,
        ]),
        'third_timeslot_override' => TimeslotOverride::factory()->create([
            'student_id' => $student->id,
            'date' => '2025-04-05',
            'period' => 2,
            'employee_id' => $employee->id,
            'inherit_from_school_attendance' => true,
            'class_attendance_required' => true,
            'is_empty' => false,
        ]),
    ];

    $result = $this->getJson(route($this->routeNamePrefix . 'index', $filters))->json();

    expect(isset($result['pagination']))->toBe($paginated);
    expect($result['data'])->toHaveCount($expected_count);
    foreach ($expected_models as $index => $model) {
        $expected_data = resourceToArray(new TimeslotOverrideResource($timeslot_override[$model]));
        expect($result['data'][$index])->toEqual($expected_data);
    }

})->with([
    'no filter' => [3, [], ['third_timeslot_override', 'second_timeslot_override', 'first_timeslot_override']],
    'filter by student_id' => [2, ['student_id' => 1001], ['third_timeslot_override', 'first_timeslot_override']],
    'filter by date' => [1, ['date' => '2025-04-05'], ['third_timeslot_override']],
    'filter by period' => [2, ['period' => 1], ['second_timeslot_override', 'first_timeslot_override']],
    'filter by employee_id' => [1, ['employee_id' => 4001], ['second_timeslot_override']],
    'filter by inherit_from_school_attendance' => [2, ['inherit_from_school_attendance' => false], ['second_timeslot_override', 'first_timeslot_override']],
    'filter by class_attendance_required' => [1, ['class_attendance_required' => true], ['third_timeslot_override']],
    'filter by is_empty' => [2, ['is_empty' => false], ['third_timeslot_override', 'first_timeslot_override']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first_timeslot_override', 'second_timeslot_override', 'third_timeslot_override']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third_timeslot_override', 'second_timeslot_override', 'first_timeslot_override']],
]);

test('bulkCreate', function () {
    $student = Student::factory()->create();
    $student2 = Student::factory()->create();
    $employee = Employee::factory()->create();
    $homeroom_teacher = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'homeroom_teacher_id' => $homeroom_teacher->id
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student->id,
        'is_active' => true,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student2->id,
        'is_active' => true,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow("2025-04-01 00:00:00");

    // use homeroom teacher = true
    $payload = [
        'date' => '2025-04-04',
        'employee_id' => null,
        'use_homeroom_teacher' => true,
        'student_ids' => [
            $student->id, $student2->id
        ],
        'periods' => [
            [
                'period' => '1',
                'placeholder' => 'sports period 1',
                'attendance_from' => '07:00:00',
                'attendance_to' => '08:00:00',
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => false,
                'is_empty' => false,
            ],
            [
                'period' => '2',
                'placeholder' => 'sports period 2',
                'attendance_from' => '08:00:00',
                'attendance_to' => '09:00:00',
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => false,
                'is_empty' => false,
            ]
        ],
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'bulk-create'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->timeslotOverrideTable, 4);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student->id,
        'date' => '2025-04-04',
        'period' => '1',
        'placeholder' => 'sports period 1',
        'attendance_from' => '07:00:00',
        'attendance_to' => '08:00:00',
        'employee_id' => $homeroom_teacher->id,
        'inherit_from_school_attendance' => true,
        'class_attendance_required' => false,
        'is_empty' => false,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student2->id,
        'date' => '2025-04-04',
        'period' => '1',
        'placeholder' => 'sports period 1',
        'attendance_from' => '07:00:00',
        'attendance_to' => '08:00:00',
        'employee_id' => $homeroom_teacher->id,
        'inherit_from_school_attendance' => true,
        'class_attendance_required' => false,
        'is_empty' => false,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student->id,
        'date' => '2025-04-04',
        'period' => '2',
        'placeholder' => 'sports period 2',
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'employee_id' => $homeroom_teacher->id,
        'inherit_from_school_attendance' => true,
        'class_attendance_required' => false,
        'is_empty' => false,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student2->id,
        'date' => '2025-04-04',
        'period' => '2',
        'placeholder' => 'sports period 2',
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'employee_id' => $homeroom_teacher->id,
        'inherit_from_school_attendance' => true,
        'class_attendance_required' => false,
        'is_empty' => false,
    ]);

    TimeslotOverride::truncate();

    // use homeroom teacher = false
    $payload = [
        'date' => '2025-04-05',
        'employee_id' => $employee->id,
        'use_homeroom_teacher' => false,
        'student_ids' => [
            $student->id, $student2->id
        ],
        'periods' => [
            [
                'period' => '1',
                'placeholder' => 'math period 1',
                'attendance_from' => '07:00:00',
                'attendance_to' => '08:00:00',
                'inherit_from_school_attendance' => false,
                'class_attendance_required' => true,
                'is_empty' => false,
            ],
            [
                'period' => '2',
                'placeholder' => 'math period 2',
                'attendance_from' => '08:00:00',
                'attendance_to' => '09:00:00',
                'inherit_from_school_attendance' => false,
                'class_attendance_required' => true,
                'is_empty' => false,
            ],
            [
                'period' => '3',
                'placeholder' => 'is empty',
                'attendance_from' => '09:00:00',
                'attendance_to' => '10:00:00',
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => true,
                'is_empty' => true,
            ],
            [
                'period' => '4',
                'placeholder' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => true,
                'is_empty' => true,
            ],
            [
                'period' => '5',
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => true,
                'is_empty' => true,
            ],
        ],
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'bulk-create'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->timeslotOverrideTable, 10);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student->id,
        'date' => '2025-04-05',
        'period' => '1',
        'placeholder' => 'math period 1',
        'attendance_from' => '07:00:00',
        'attendance_to' => '08:00:00',
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student2->id,
        'date' => '2025-04-05',
        'period' => '1',
        'placeholder' => 'math period 1',
        'attendance_from' => '07:00:00',
        'attendance_to' => '08:00:00',
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student->id,
        'date' => '2025-04-05',
        'period' => '2',
        'placeholder' => 'math period 2',
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student2->id,
        'date' => '2025-04-05',
        'period' => '2',
        'placeholder' => 'math period 2',
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student->id,
        'date' => '2025-04-05',
        'period' => '3',
        'placeholder' => null,
        'attendance_from' => null,
        'attendance_to' => null,
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false, // if is_empty = true, set to false
        'class_attendance_required' => false, // if is_empty = true, set to false
        'is_empty' => true,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student2->id,
        'date' => '2025-04-05',
        'period' => '3',
        'placeholder' => null,
        'attendance_from' => null,
        'attendance_to' => null,
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false, // if is_empty = true, set to false
        'class_attendance_required' => false, // if is_empty = true, set to false
        'is_empty' => true,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student->id,
        'date' => '2025-04-05',
        'period' => '4',
        'placeholder' => null,
        'attendance_from' => null,
        'attendance_to' => null,
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false, // if is_empty = true, set to false
        'class_attendance_required' => false, // if is_empty = true, set to false
        'is_empty' => true,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student2->id,
        'date' => '2025-04-05',
        'period' => '4',
        'placeholder' => null,
        'attendance_from' => null,
        'attendance_to' => null,
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false, // if is_empty = true, set to false
        'class_attendance_required' => false, // if is_empty = true, set to false
        'is_empty' => true,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student->id,
        'date' => '2025-04-05',
        'period' => '5',
        'placeholder' => null,
        'attendance_from' => null,
        'attendance_to' => null,
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false, // if is_empty = true, set to false
        'class_attendance_required' => false, // if is_empty = true, set to false
        'is_empty' => true,
    ]);
    $this->assertDatabaseHas($this->timeslotOverrideTable, [
        'student_id' => $student2->id,
        'date' => '2025-04-05',
        'period' => '5',
        'placeholder' => null,
        'attendance_from' => null,
        'attendance_to' => null,
        'employee_id' => $employee->id,
        'inherit_from_school_attendance' => false, // if is_empty = true, set to false
        'class_attendance_required' => false, // if is_empty = true, set to false
        'is_empty' => true,
    ]);
});

test('bulkCreate - throw error', function () {
    $payload = [
        'employee_id' => null,
        'use_homeroom_teacher' => false,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'bulk-create'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            "date" => [
                "The date field is required."
            ],
            "employee_id" => [
                "The employee id field is required when use homeroom teacher is false."
            ],
            "student_ids" => [
                "The student ids field is required."
            ],
            "periods" => [
                "The periods field is required."
            ],
        ]);


    $student = Student::factory()->create();
    $student2 = Student::factory()->create();
    $employee = Employee::factory()->create();

    // placeholder, attendance_from and attendance_to is required if is_empty = false
    $payload = [
        'date' => '2025-04-04',
        'employee_id' => null,
        'use_homeroom_teacher' => true,
        'student_ids' => [
            $student->id, $student2->id
        ],
        'periods' => [
            [
                'period' => '1',
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => false,
                'is_empty' => true,
            ],
            [
                'period' => '2',
                'placeholder' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => false,
                'is_empty' => true,
            ],
            [
                'period' => '2',
                'placeholder' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                'inherit_from_school_attendance' => true,
                'class_attendance_required' => false,
                'is_empty' => false,
            ]
        ],
    ];
    $response = $this->postJson(route($this->routeNamePrefix . 'bulk-create'), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            "periods.2.placeholder" => [
                "The periods.2.placeholder field is required when periods.2.is_empty is false."
            ],
            "periods.2.attendance_from" => [
                "The periods.2.attendance_from field is required when periods.2.is_empty is false."
            ],
            "periods.2.attendance_to" => [
                "The periods.2.attendance_to field is required when periods.2.is_empty is false."
            ],
        ]);

    // only allowed to create timeslot override for future dates
    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow("2025-04-04 00:00:00");
    $payload = [
        'date' => '2025-04-04',
        'employee_id' => $employee->id,
        'use_homeroom_teacher' => false,
        'student_ids' => [
            $student->id, $student2->id
        ],
        'periods' => [
            [
                'period' => '1',
                'placeholder' => 'math period 1',
                'attendance_from' => '07:00:00',
                'attendance_to' => '08:00:00',
                'inherit_from_school_attendance' => false,
                'class_attendance_required' => true,
                'is_empty' => false,
            ],
        ],
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'bulk-create'), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => 52003,
            'error' => 'Only allowed to create timeslot overrides for future dates.'
        ]);

    // duplicate timeslot overrides
    TimeslotOverride::factory()->create([
        'student_id' => $student->id,
        'date' => '2025-04-05',
        'period' => 2,
    ]);
    TimeslotOverride::factory()->create([
        'student_id' => $student->id,
        'date' => '2025-04-05',
        'period' => 3,
    ]);
    TimeslotOverride::factory()->create([
        'student_id' => $student2->id,
        'date' => '2025-04-05',
        'period' => 1,
    ]);
    $payload = [
        'date' => '2025-04-05',
        'employee_id' => $employee->id,
        'use_homeroom_teacher' => false,
        'student_ids' => [
            $student->id, $student2->id
        ],
        'periods' => [
            [
                'period' => '1',
                'placeholder' => 'math period 1',
                'attendance_from' => '07:00:00',
                'attendance_to' => '08:00:00',
                'inherit_from_school_attendance' => false,
                'class_attendance_required' => true,
                'is_empty' => false,
            ],
            [
                'period' => '2',
                'placeholder' => 'math period 2',
                'attendance_from' => '08:00:00',
                'attendance_to' => '09:00:00',
                'inherit_from_school_attendance' => false,
                'class_attendance_required' => true,
                'is_empty' => false,
            ],
            [
                'period' => '3',
                'placeholder' => 'math period 3',
                'attendance_from' => '09:00:00',
                'attendance_to' => '10:00:00',
                'inherit_from_school_attendance' => false,
                'class_attendance_required' => true,
                'is_empty' => false,
            ]
        ],
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'bulk-create'), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => 52002,
            'error' => 'Duplicate timeslot overrides for the same period on the same date are not allowed. \n' . $student2->student_number . ' (' . $student2->name . '), period - 1\n' . $student->student_number . ' (' . $student->name . '), period - 2, 3'
        ]);
});

test('destroy', function () {
    $timeslot_override = TimeslotOverride::factory()->create();

    $timeslot_override2 = TimeslotOverride::factory()->create();
    PeriodAttendance::factory()->create([
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override2->id,
    ]);
    $this->assertDatabaseCount($this->timeslotOverrideTable, 2);

    $response = $this->deleteJson(route($this->routeNamePrefix . 'destroy', ['timeslot_override' => $timeslot_override->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->timeslotOverrideTable, 1);

    $response = $this->deleteJson(route($this->routeNamePrefix . 'destroy', ['timeslot_override' => $timeslot_override2->id]))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => 52001,
            'error' => 'Timeslot override cannot be deleted because class attendance has already been taken.'
        ]);
});

test('batchDelete', function () {
    $timeslot_override = TimeslotOverride::factory()->create();
    $timeslot_override2 = TimeslotOverride::factory()->create();
    $timeslot_override3 = TimeslotOverride::factory()->create();
    PeriodAttendance::factory()->create([
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override3->id,
    ]);
    $this->assertDatabaseCount($this->timeslotOverrideTable, 3);

    // error
    $payload = [
        'ids' => [$timeslot_override->id, $timeslot_override3->id],
    ];
    $response = $this->postJson(route($this->routeNamePrefix . 'batch-delete'), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => 52001,
            'error' => 'Timeslot override cannot be deleted because class attendance has already been taken.'
        ]);
    $this->assertDatabaseCount($this->timeslotOverrideTable, 3);

    // success
    $payload = [
        'ids' => [$timeslot_override->id, $timeslot_override2->id],
    ];
    $response = $this->postJson(route($this->routeNamePrefix . 'batch-delete'), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();
    $this->assertDatabaseCount($this->timeslotOverrideTable, 1);
});
