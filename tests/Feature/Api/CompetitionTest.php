<?php

use App\Enums\ClassType;
use App\Enums\CompetitionBonusType;
use App\Http\Resources\AwardResource;
use App\Http\Resources\DepartmentResource;
use App\Http\Resources\SemesterClassResource;
use App\Http\Resources\SimpleStudentResource;
use App\Models\Award;
use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\Department;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\CompetitionService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Pagination\LengthAwarePaginator;
use Laravel\Sanctum\Sanctum;
use Mo<PERSON>y\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    $this->user = User::factory()->create();

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->baseUrl = 'competitions.';

    $this->tableName = resolve(Competition::class)->getTable();
});

test('index without any params', function () {
    $first_competition = Competition::factory()->create();
    $second_competition = Competition::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'order_by' => ['id'],
        'includes' => ['department']
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_competition->id,
                'name' => $first_competition->name,
                'department' => resourceToArray(new DepartmentResource($first_competition->department)),
                'date' => $first_competition->date,
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_competition->id,
                'name' => $second_competition->name,
                'department' => resourceToArray(new DepartmentResource($second_competition->department)),
                'date' => $first_competition->date,
            ]),
        );
});

test('index: test service accepting params', function () {
    $competition = Competition::factory()->create();
    $record1 = CompetitionRecord::factory()->create([
        'competition_id' => $competition->id,
    ]);

    $filters = [
        'name' => $competition->name,
        'department_id' => $competition->department_id,
        'date' => $competition->date,
        'includes' => ['department', 'records.student', 'records.award'],
        'order_by' => ['id' => 'asc'],
    ];

    $this->mock(CompetitionService::class, function (MockInterface $mock) use ($filters, $competition) {
        $mock->shouldReceive('getAllPaginatedCompetitions')->once()->with($filters)->andReturn(new LengthAwarePaginator([$competition->loadMissing(['department', 'records.student', 'records.award'])], 1, 1));
    });

    $response = $this->getJson(route($this->baseUrl . 'index', $filters))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $competition->id,
            'name' => $competition->name,
            'department' => resourceToArray(new DepartmentResource($competition->department)),
            'date' => $competition->date,
            'records' => [
                [
                    'id' => $record1->id,
                    'student' => resourceToArray(new SimpleStudentResource($record1->student)),
                    'award' => resourceToArray(new AwardResource($record1->award)),
                    'type_of_bonus' => $record1->type_of_bonus->value,
                    'mark' => $record1->mark,
                ],
            ],
        ]);
});

test('show success', function () {
    $first_competition = Competition::factory()->create();
    $second_competition = Competition::factory()->create();

    $record1 = CompetitionRecord::factory()->create([
        'competition_id' => $first_competition->id,
    ]);

    $response = $this->getJson(route($this->baseUrl . 'show', $first_competition->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toEqual([
            'id' => $first_competition->id,
            'name' => $first_competition->name,
            'department' => resourceToArray(new DepartmentResource($first_competition->department)),
            'date' => $first_competition->date,
            'records' => [
                [
                    'id' => $record1->id,
                    'student' => resourceToArray(new SimpleStudentResource($record1->student)),
                    'award' => resourceToArray(new AwardResource($record1->award)),
                    'type_of_bonus' => $record1->type_of_bonus->value,
                    'mark' => $record1->mark,
                    'semester_class' => resourceToArray(new SemesterClassResource($record1->semesterClass))
                ],
            ],
        ]);

    $response = $this->getJson(route($this->baseUrl . 'show', $second_competition->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toEqual([
            'id' => $second_competition->id,
            'name' => $second_competition->name,
            'department' => resourceToArray(new DepartmentResource($second_competition->department)),
            'date' => $second_competition->date,
            'records' => [],
        ]);
});

test('create success', function () {
    /**
     * create with records
     */
    $this->assertDatabaseCount($this->tableName, 0);

    $department = Department::factory()->create();
    $student1 = Student::factory()->create();
    $student_class_10_02 = StudentClass::factory()->create([
        'student_id' => $student1->id,
        'class_enter_date' => '2024-10-02',
        'is_active' => true,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    $student_class_10_10 = StudentClass::factory()->create([
        'student_id' => $student1->id,
        'class_enter_date' => '2024-10-10',
        'is_active' => true,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    $student_class_10_13 = StudentClass::factory()->create([
        'student_id' => $student1->id,
        'class_enter_date' => '2024-10-13',
        'is_active' => true,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    $award = Award::factory()->create();

    $payload = [
        'name' => 'Lompat Galah',
        'department_id' => $department->id,
        'date' => '2024-10-10',
        'records' => [
            [
                'student_id' => $student1->id,
                'award_id' => $award->id,
                'type_of_bonus' => CompetitionBonusType::PERFORMANCE->value,
                'mark' => 1.1,
            ],
        ],
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    $created_data = Competition::first();
    $record1 = $created_data->records->first();

    expect($created_data->records)->toHaveCount(1);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toBe([
            'id' => $created_data->id,
            'name' => $created_data->name,
            'department' => resourceToArray(new DepartmentResource($created_data->department)),
            'date' => $created_data->date,
            'records' => [
                [
                    'id' => $record1->id,
                    'student' => resourceToArray(new SimpleStudentResource($record1->student)),
                    'award' => resourceToArray(new AwardResource($record1->award)),
                    'type_of_bonus' => $record1->type_of_bonus->value,
                    'mark' => (float) $record1->mark,
                    'semester_class' => resourceToArray(new SemesterClassResource($record1->semesterClass)),
                ],
            ],
        ]);

    $this->assertDatabaseCount($this->tableName, 1);

    $this->assertDatabaseHas($this->tableName, [
        'name' => $payload['name'],
        'department_id' => $payload['department_id'],
        'date' => $payload['date'],
    ]);

    $this->assertDatabaseCount('competition_records', 1);

    $this->assertDatabaseHas('competition_records', [
        'competition_id' => $created_data->id,
        'student_id' => $payload['records'][0]['student_id'],
        'award_id' => $payload['records'][0]['award_id'],
        'type_of_bonus' => $payload['records'][0]['type_of_bonus'],
        'mark' => $payload['records'][0]['mark'],
        'semester_class_id' => $student_class_10_10->semester_class_id,
    ]);


    /**
     * create without records
     */
    $this->assertDatabaseCount($this->tableName, 1);

    $department = Department::factory()->create();

    $new_payload = [
        'name' => 'Lompat Galah 24',
        'department_id' => $department->id,
        'date' => '2024-10-10',
        'records' => [],
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $new_payload);

    $response->assertStatus(200);

    $created_data = Competition::where('name', 'Lompat Galah 24')->first();

    expect($created_data->records)->toHaveCount(0);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toBe([
            'id' => $created_data->id,
            'name' => $created_data->name,
            'department' => resourceToArray(new DepartmentResource($created_data->department)),
            'date' => $created_data->date,
            'records' => [],
        ]);

    $this->assertDatabaseCount($this->tableName, 2);

    $this->assertDatabaseHas($this->tableName, [
        'name' => $new_payload['name'],
        'department_id' => $new_payload['department_id'],
        'date' => $new_payload['date'],
    ]);

    $this->assertDatabaseCount('competition_records', 1);
});

test('create validation error', function () {
    $this->assertDatabaseCount($this->tableName, 0);

    /**
     * no payload
     */
    $response = $this->postJson(route($this->baseUrl . 'create'), []);

    $response->assertStatus(422);

    $this->assertDatabaseCount($this->tableName, 0);

    expect($response->json())
        ->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'name' => [
                    'The name field is required.'
                ],
                'department_id' => [
                    'The department id field is required.'
                ],
                'date' => [
                    'The date field is required.'
                ],
            ],
            'data' => null
        ]);

    /**
     * invalid records payload
     */
    $department = Department::factory()->create();

    $payload = [
        'name' => 'Lompat Galah',
        'department_id' => $department->id,
        'date' => '2024-10-10',
        'records' => [
            [
                'student_id' => 1,
                'award_id' => 1,
                'type_of_bonus' => 'Invalid',
                'mark' => 1.1,
            ],
        ],
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(422);

    $this->assertDatabaseCount($this->tableName, 0);

    expect($response->json())
        ->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'records.0.student_id' => [
                    'The selected record student is invalid.',
                ],
                'records.0.award_id' => [
                    'The selected record award is invalid.',
                ],
                'records.0.type_of_bonus' => [
                    'The selected record type of bonus is invalid.',
                ],
            ],
            'data' => null
        ]);
});

test('update success', function () {
    $competition = Competition::factory()->create();

    $department = Department::factory()->create();
    $student1 = Student::factory()->create();
    $student_class_10_02 = StudentClass::factory()->create([
        'student_id' => $student1->id,
        'class_enter_date' => '2024-10-02',
        'is_active' => true,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    $award = Award::factory()->create();

    expect($competition->records)->toHaveCount(0);

    $payload = [
        'name' => 'Updated Lompat Galah',
        'department_id' => $department->id,
        'date' => '2024-10-10',
        'records' => [
            [
                'student_id' => $student1->id,
                'award_id' => $award->id,
                'type_of_bonus' => CompetitionBonusType::PERFORMANCE->value,
                'mark' => 1.1,
            ],
        ],
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $competition->id), $payload);
    $response->assertStatus(200);

    $competition->refresh();

    expect($competition->records)->toHaveCount(1);
    $record1 = $competition->records->first();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toBe([
            'id' => $competition->id,
            'name' => $competition->name,
            'department' => resourceToArray(new DepartmentResource($competition->department)),
            'date' => $competition->date,
            'records' => [
                [
                    'id' => $record1->id,
                    'student' => resourceToArray(new SimpleStudentResource($record1->student)),
                    'award' => resourceToArray(new AwardResource($record1->award)),
                    'type_of_bonus' => $record1->type_of_bonus->value,
                    'mark' => (float) $record1->mark,
                    'semester_class' => resourceToArray(new SemesterClassResource($record1->semesterClass)),
                ],
            ],
        ]);


    $this->assertDatabaseCount($this->tableName, 1);

    $this->assertDatabaseHas($this->tableName, [
        'name' => $payload['name'],
        'department_id' => $payload['department_id'],
        'date' => $payload['date'],
    ]);


    $this->assertDatabaseCount('competition_records', 1);

    $this->assertDatabaseHas('competition_records', [
        'competition_id' => $competition->id,
        'student_id' => $payload['records'][0]['student_id'],
        'award_id' => $payload['records'][0]['award_id'],
        'type_of_bonus' => $payload['records'][0]['type_of_bonus'],
        'mark' => $payload['records'][0]['mark'],
        'semester_class_id' => $student_class_10_02->semester_class_id,
    ]);

    // update competition record's semester class id
    $student_class_10_08 = StudentClass::factory()->create([
        'student_id' => $student1->id,
        'class_enter_date' => '2024-10-08',
        'is_active' => true,
        'class_type' => ClassType::PRIMARY->value,
    ]);

    $response = $this->putJson(route($this->baseUrl . 'update', $competition->id), $payload);
    $response->assertStatus(200);
    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('competition_records', 1);

    $this->assertDatabaseHas('competition_records', [
        'competition_id' => $competition->id,
        'student_id' => $payload['records'][0]['student_id'],
        'award_id' => $payload['records'][0]['award_id'],
        'type_of_bonus' => $payload['records'][0]['type_of_bonus'],
        'mark' => $payload['records'][0]['mark'],
        'semester_class_id' => $student_class_10_08->semester_class_id,
    ]);

});

test('update validation error', function () {
    $competition = Competition::factory()->create();

    $this->assertDatabaseCount($this->tableName, 1);

    /**
     * no payload
     */
    $response = $this->putJson(route($this->baseUrl . 'update', $competition->id), []);

    $response->assertStatus(422);

    $this->assertDatabaseCount($this->tableName, 1);

    expect($response->json())
        ->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'name' => [
                    'The name field is required.'
                ],
                'department_id' => [
                    'The department id field is required.'
                ],
                'date' => [
                    'The date field is required.'
                ],
            ],
            'data' => null
        ]);

    /**
     * invalid records payload
     */
    $department = Department::factory()->create();

    $payload = [
        'name' => 'Updated Lompat Galah',
        'department_id' => $department->id,
        'date' => '2024-10-10',
        'records' => [
            [
                'student_id' => 1,
                'award_id' => 1,
                'type_of_bonus' => 'Invalid',
                'mark' => 1.1,
            ],
        ],
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $competition->id), $payload);

    $response->assertStatus(422);

    $this->assertDatabaseCount($this->tableName, 1);

    expect($response->json())
        ->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'records.0.student_id' => [
                    'The selected record student is invalid.',
                ],
                'records.0.award_id' => [
                    'The selected record award is invalid.',
                ],
                'records.0.type_of_bonus' => [
                    'The selected record type of bonus is invalid.',
                ],
            ],
            'data' => null
        ]);
});

test('delete success', function () {
    $competition = Competition::factory()->create();
    $other_competitions = Competition::factory(3)->create();

    $this->assertDatabaseCount($this->tableName, 4);

    //id not exist
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['competition' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->tableName, 4);

    //delete success
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['competition' => $competition->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->tableName, 3);

    $this->assertDatabaseMissing($this->tableName, ['id' => $competition->id]);

    foreach ($other_competitions as $other_competition) {
        $this->assertDatabaseHas($this->tableName, ['id' => $other_competition->id]);
    }
});
