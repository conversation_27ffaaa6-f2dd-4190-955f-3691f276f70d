<?php

use App\Enums\EcommerceOrderPaymentStatus;
use App\Enums\EcommerceOrderStatus;
use App\Enums\ExportType;
use App\Enums\MerchantType;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\ClassModel;
use App\Models\EcommerceOrder;
use App\Models\EcommerceOrderItem;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\Merchant;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\Role;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {

    $this->seedAccountingData();
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        PaymentMethodSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->student = Student::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->table = resolve(EcommerceOrder::class)->getTable();
    $this->orderItemTable = resolve(EcommerceOrderItem::class)->getTable();
    $this->walletTable = resolve(Wallet::class)->getTable();
    $this->walletTransactionTable = resolve(WalletTransaction::class)->getTable();

    $this->routeNamePrefix = 'reports.ecommerce.bookshops';

    //prepare data
    $this->payment_methods = PaymentMethod::all();
    $this->grades = Grade::factory(2)->create();
    $this->students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Student 1',
        ],
        [
            'name->en' => 'Student 2',
        ],
    ))->create();
    $this->semester_settings = SemesterSetting::factory(2)->state(new Sequence(
        [
            'name' => 'Semester 1',
        ],
        [
            'name' => 'Semester 2',
        ]
    ))->create();
    $this->classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name' => 'Class 1',
            'grade_id' => $this->grades[0]->id,
        ],
        [
            'name' => 'Class 2',
            'grade_id' => $this->grades[1]->id,
        ],
    ))->create();
    $this->semester_classes = SemesterClass::factory(2)
        ->state(new Sequence(
            [
                'semester_setting_id' => $this->semester_settings[0]->id,
                'class_id' => $this->classes[0]->id,
            ],
            [
                'semester_setting_id' => $this->semester_settings[1]->id,
                'class_id' => $this->classes[1]->id,
            ],
        ))
        ->create();

    $this->student_classes = StudentClass::factory(2)->state(new Sequence(
        [
            'student_id' => $this->students[0]->id,
            'semester_class_id' => $this->semester_classes[0]->id,
            'semester_setting_id' => $this->semester_settings[0]->id,
        ],
        [
            'student_id' => $this->students[1]->id,
            'semester_class_id' => $this->semester_classes[1]->id,
            'semester_setting_id' => $this->semester_settings[1]->id,
        ]
    ))->create();

    $this->orders = EcommerceOrder::factory(6)->state(new Sequence(
        [
            'merchant_type' => MerchantType::BOOKSHOP,
            'order_reference_number' => '123',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[0]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PENDING,
            'recipient_student_class_id' => $this->student_classes[0]->id,
            'created_at' => Carbon::parse('2024-01-01'),
        ],
        [
            'merchant_type' => MerchantType::CANTEEN,
            'order_reference_number' => '456',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[0]->id,
            'status' => EcommerceOrderStatus::PROCESSING,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[0]->id,
            'created_at' => Carbon::parse('2024-01-01'),
        ],
        [
            'merchant_type' => MerchantType::CANTEEN,
            'order_reference_number' => '789',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[1]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[1]->id,
            'created_at' => Carbon::parse('2024-01-01'),
        ],
        [
            'merchant_type' => MerchantType::BOOKSHOP,
            'order_reference_number' => '7891',
            'buyer_userable_type' => Guardian::class,
            'buyer_userable_id' => 3,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => null,
            'created_at' => Carbon::parse('2024-02-01'),
        ],
        [
            'merchant_type' => MerchantType::BOOKSHOP,
            'order_reference_number' => '7892',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[1]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[1]->id,
            'created_at' => Carbon::parse('2024-02-01'),
        ],
        [
            'merchant_type' => MerchantType::BOOKSHOP,
            'order_reference_number' => '7893',
            'buyer_userable_type' => Student::class,
            'buyer_userable_id' => $this->students[1]->id,
            'status' => EcommerceOrderStatus::COMPLETED,
            'payment_status' => EcommerceOrderPaymentStatus::PAID,
            'recipient_student_class_id' => $this->student_classes[1]->id,
            'created_at' => Carbon::parse('2024-02-01'),
        ],
    ))->create();

    $this->merchant_user = User::factory()->create();

    $this->merchants = Merchant::factory(3)->state(new Sequence(
        [
            'type' => MerchantType::BOOKSHOP,
            'user_id' => $this->merchant_user->id,
        ],
        [
            'type' => MerchantType::CANTEEN
        ],
        [
            'type' => MerchantType::BOOKSHOP,
        ],
    ))->create();

    $merchant_role = Role::create(['guard_name' => 'api', 'name' => 'Merchant']);

    $merchant_role->givePermissionTo(PermissionSeeder::REPORT_PERMISSIONS['ecommerce']);

    $this->merchant_user->assignRole($merchant_role);

    $this->order_items = EcommerceOrderItem::factory(7)->state(new Sequence(
        [
            'product_name' => 'product 1',
            'product_unit_price' => 1,
            'quantity' => 1,
            'amount_before_tax' => 1,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[0]->id,
            'order_id' => $this->orders[0]->id,
            'product_delivery_date' => Carbon::parse('2024-01-01 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 2,
            'amount_before_tax' => 4,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[0]->id,
            'order_id' => $this->orders[0]->id,
            'product_delivery_date' => Carbon::parse('2024-01-01 00:00:00')
        ],
        [
            'product_name' => 'product 1',
            'product_unit_price' => 1,
            'quantity' => 1,
            'amount_before_tax' => 1,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[1]->id,
            'product_delivery_date' => Carbon::parse('2024-01-02 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 3,
            'amount_before_tax' => 6,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[2]->id,
            'product_delivery_date' => Carbon::parse('2024-01-03 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 5,
            'amount_before_tax' => 10,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[3]->id,
            'product_delivery_date' => Carbon::parse('2024-01-03 00:00:00')
        ],
        [
            'product_name' => 'product 1',
            'product_unit_price' => 1,
            'quantity' => 5,
            'amount_before_tax' => 5,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[0]->id,
            'order_id' => $this->orders[4]->id,
            'product_delivery_date' => Carbon::parse('2024-01-01 00:00:00')
        ],
        [
            'product_name' => 'product 1',
            'product_unit_price' => 1,
            'quantity' => 5,
            'amount_before_tax' => 5,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[2]->id,
            'order_id' => $this->orders[5]->id,
            'product_delivery_date' => Carbon::parse('2024-01-01 00:00:00')
        ],
        [
            'product_name' => 'product 2',
            'product_unit_price' => 2,
            'quantity' => 3,
            'amount_before_tax' => 6,
            'tax_amount' => 0,
            'merchant_id' => $this->merchants[1]->id,
            'order_id' => $this->orders[5]->id,
            'product_delivery_date' => Carbon::parse('2024-01-03 00:00:00')
        ],
    ))->create();

    foreach ($this->orders as $order) {
        $billing_document = BillingDocument::factory()->create();

        Payment::factory()->create([
            'billing_document_id' => $billing_document->id,
            'payment_method_id' => $this->payment_methods[0]->id,
        ]);

        foreach ($order->items as $order_item) {
            BillingDocumentLineItem::factory()->create([
                'billing_document_id' => $billing_document->id,
                'billable_item_type' => EcommerceOrderItem::class,
                'billable_item_id' => $order_item->id,
            ]);
        }
    }

    SnappyPdf::fake();
    Excel::fake();
});

test('reportByClasses return data', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[1]->id,
        'date' => '2024-02-01'
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.classes', $filters)
    )->json();

    $expected_items = [];

    foreach ($this->orders[4]->items as $item) {
        $expected_items[] = [
            'product_name' => $item->product_name,
            'product_unit_price' => number_format($item->product_unit_price, 2),
            'quantity' => (int)$item->quantity,
            'total' => bcadd($item->amount_before_tax, $item->tax_amount, 2)
        ];
    }

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($expected_items) {
                return $data->toMatchArray(
                    [
                        'order_reference_number' => $this->orders[4]->order_reference_number,
                        'billing_document_reference_number' => $this->orders[4]->items[0]->billingDocumentLineItem?->billingDocument->reference_no,
                        'student_name' => $this->orders[4]->recipientStudentClass->student->getFormattedTranslations('name'),
                        'student_number' => $this->orders[4]->recipientStudentClass->student->student_number,
                        'created_at' => Carbon::parse($this->orders[4]->created_at)->format('Y-m-d\TH:i:s.u\Z'),
                        'student_class_name' => $this->orders[4]->recipientStudentClass->semesterClass->classModel->name,
                        'student_semester_name' => $this->orders[4]->recipientStudentClass->semesterClass->semesterSetting->name,
                        'currency_code' => $this->orders[4]->currency_code,
                        'total' => $this->orders[4]->amount_after_tax,
                        'items' => $expected_items
                    ]
                );
            },
            function ($data) use ($expected_items) {
                return $data->toMatchArray(
                    [
                        'order_reference_number' => $this->orders[5]->order_reference_number,
                        'billing_document_reference_number' => $this->orders[5]->items[0]->billingDocumentLineItem?->billingDocument->reference_no,
                        'student_name' => $this->orders[5]->recipientStudentClass->student->getFormattedTranslations('name'),
                        'student_number' => $this->orders[5]->recipientStudentClass->student->student_number,
                        'created_at' => Carbon::parse($this->orders[5]->created_at)->format('Y-m-d\TH:i:s.u\Z'),
                        'student_class_name' => $this->orders[5]->recipientStudentClass->semesterClass->classModel->name,
                        'student_semester_name' => $this->orders[5]->recipientStudentClass->semesterClass->semesterSetting->name,
                        'currency_code' => $this->orders[5]->currency_code,
                        'total' => $this->orders[5]->amount_after_tax,
                        'items' => $expected_items
                    ]
                );
            }
        );
});

test('reportByClasses return data - merchant', function () {
    //Only merchant order will be selected
    Sanctum::actingAs($this->merchant_user);

    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[1]->id,
        'date' => '2024-02-01'
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.classes', $filters)
    )->json();

    $expected_items = [];

    foreach ($this->orders[4]->items as $item) {
        $expected_items[] = [
            'product_name' => $item->product_name,
            'product_unit_price' => number_format($item->product_unit_price, 2),
            'quantity' => (int)$item->quantity,
            'total' => bcadd($item->amount_before_tax, $item->tax_amount, 2)
        ];
    }

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($expected_items) {
                return $data->toMatchArray(
                    [
                        'order_reference_number' => $this->orders[4]->order_reference_number,
                        'billing_document_reference_number' => $this->orders[4]->items[0]->billingDocumentLineItem?->billingDocument->reference_no,
                        'student_name' => $this->orders[4]->recipientStudentClass->student->getFormattedTranslations('name'),
                        'student_number' => $this->orders[4]->recipientStudentClass->student->student_number,
                        'created_at' => Carbon::parse($this->orders[4]->created_at)->format('Y-m-d\TH:i:s.u\Z'),
                        'student_class_name' => $this->orders[4]->recipientStudentClass->semesterClass->classModel->name,
                        'student_semester_name' => $this->orders[4]->recipientStudentClass->semesterClass->semesterSetting->name,
                        'currency_code' => $this->orders[4]->currency_code,
                        'total' => $this->orders[4]->amount_after_tax,
                        'items' => $expected_items
                    ]
                );
            }
        );
});

test('reportByClasses return excel', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[1]->id,
        'date' => '2024-02-01',
        'export_type' => ExportType::EXCEL->value
    ];

    $filename = 'ecommerce-bookshop-report-by-classes';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.classes', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportByClasses return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[1]->id,
        'date' => '2024-02-01',
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'ecommerce-bookshop-report-by-classes';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.classes', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportByStudents return data', function () {
    $filters = [
        'report_language' => 'en',
        'student_ids' => [$this->students[1]->id],
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-02',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.students', $filters)
    )->json();

    $expected_items = [];

    foreach ($this->orders[4]->items as $item) {
        $expected_items[] = [
            'product_name' => $item->product_name,
            'product_unit_price' => number_format($item->product_unit_price, 2),
            'quantity' => (int)$item->quantity,
            'total' => bcadd($item->amount_before_tax, $item->tax_amount, 2)
        ];
    }

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            function ($data) use ($expected_items) {
                return $data->toMatchArray(
                    [
                        'order_reference_number' => $this->orders[4]->order_reference_number,
                        'billing_document_reference_number' => $this->orders[4]->items[0]->billingDocumentLineItem?->billingDocument->reference_no,
                        'student_name' => $this->orders[4]->recipientStudentClass->student->getFormattedTranslations('name'),
                        'student_number' => $this->orders[4]->recipientStudentClass->student->student_number,
                        'created_at' => Carbon::parse($this->orders[4]->created_at)->format('Y-m-d\TH:i:s.u\Z'),
                        'student_class_name' => $this->orders[4]->recipientStudentClass->semesterClass->classModel->name,
                        'student_semester_name' => $this->orders[4]->recipientStudentClass->semesterClass->semesterSetting->name,
                        'currency_code' => $this->orders[4]->currency_code,
                        'total' => $this->orders[4]->amount_after_tax,
                        'items' => $expected_items
                    ]
                );
            },
            function ($data) use ($expected_items) {
                return $data->toMatchArray(
                    [
                        'order_reference_number' => $this->orders[5]->order_reference_number,
                        'billing_document_reference_number' => $this->orders[5]->items[0]->billingDocumentLineItem?->billingDocument->reference_no,
                        'student_name' => $this->orders[5]->recipientStudentClass->student->getFormattedTranslations('name'),
                        'student_number' => $this->orders[5]->recipientStudentClass->student->student_number,
                        'created_at' => Carbon::parse($this->orders[5]->created_at)->format('Y-m-d\TH:i:s.u\Z'),
                        'student_class_name' => $this->orders[5]->recipientStudentClass->semesterClass->classModel->name,
                        'student_semester_name' => $this->orders[5]->recipientStudentClass->semesterClass->semesterSetting->name,
                        'currency_code' => $this->orders[5]->currency_code,
                        'total' => $this->orders[5]->amount_after_tax,
                        'items' => $expected_items
                    ],
                );
            }
        );
});

test('reportByStudents return data - merchant', function () {
    //Only merchant order will be selected
    Sanctum::actingAs($this->merchant_user);

    $filters = [
        'report_language' => 'en',
        'student_ids' => [$this->students[1]->id],
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-02',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.students', $filters)
    )->json();

    $expected_items = [];

    foreach ($this->orders[4]->items as $item) {
        $expected_items[] = [
            'product_name' => $item->product_name,
            'product_unit_price' => number_format($item->product_unit_price, 2),
            'quantity' => (int)$item->quantity,
            'total' => bcadd($item->amount_before_tax, $item->tax_amount, 2)
        ];
    }

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            function ($data) use ($expected_items) {
                return $data->toMatchArray(
                    [
                        'order_reference_number' => $this->orders[4]->order_reference_number,
                        'billing_document_reference_number' => $this->orders[4]->items[0]->billingDocumentLineItem?->billingDocument->reference_no,
                        'student_name' => $this->orders[4]->recipientStudentClass->student->getFormattedTranslations('name'),
                        'student_number' => $this->orders[4]->recipientStudentClass->student->student_number,
                        'created_at' => Carbon::parse($this->orders[4]->created_at)->format('Y-m-d\TH:i:s.u\Z'),
                        'student_class_name' => $this->orders[4]->recipientStudentClass->semesterClass->classModel->name,
                        'student_semester_name' => $this->orders[4]->recipientStudentClass->semesterClass->semesterSetting->name,
                        'currency_code' => $this->orders[4]->currency_code,
                        'total' => $this->orders[4]->amount_after_tax,
                        'items' => $expected_items
                    ]
                );
            }
        );
});

test('reportByStudents return excel', function () {
    $filters = [
        'report_language' => 'en',
        'student_ids' => [$this->students[1]->id],
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-02',
        'export_type' => ExportType::EXCEL->value
    ];

    $filename = 'ecommerce-bookshop-report-by-students';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.students', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportByStudents return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'student_ids' => [$this->students[1]->id],
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-02',
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'ecommerce-bookshop-report-by-students';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.students', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportOrderItems return data', function () {
    $filters = [
        'report_language' => 'en',
        'grade_id' => $this->grades[1]->id,
        'semester_setting_id' => $this->semester_settings[1]->id,
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-05',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.order-items', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toMatchArray(
            [
                'total' => '10.00',
                'currency_code' => 'MYR',
                'items' => [
                    [
                        'product_name' => 'product 1',
                        'product_unit_price' => '1.00',
                        'total_quantity' => 5,
                        'total' => '5.00',
                        'product_id' => $this->order_items[5]->product_id,
                        'currency_code' => 'MYR',
                    ],
                    [
                        'product_name' => 'product 1',
                        'product_unit_price' => '1.00',
                        'total_quantity' => 5,
                        'total' => '5.00',
                        'product_id' => $this->order_items[6]->product_id,
                        'currency_code' => 'MYR',
                    ]
                ]
            ]
        );
});

test('reportOrderItems return data - merchant', function () {
    Sanctum::actingAs($this->merchant_user);

    $filters = [
        'report_language' => 'en',
        'grade_id' => $this->grades[1]->id,
        'semester_setting_id' => $this->semester_settings[1]->id,
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-05',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.order-items', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toMatchArray(
            [
                'total' => '5.00',
                'currency_code' => 'MYR',
                'items' => [
                    [
                        'product_name' => 'product 1',
                        'product_unit_price' => '1.00',
                        'total_quantity' => 5,
                        'total' => '5.00',
                        'product_id' => $this->order_items[5]->product_id,
                        'currency_code' => 'MYR',
                    ]
                ]
            ]
        );
});

test('reportOrderItems return excel', function () {
    $filters = [
        'report_language' => 'en',
        'grade_id' => $this->grades[1]->id,
        'semester_setting_id' => $this->semester_settings[1]->id,
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-05',
        'export_type' => ExportType::EXCEL->value
    ];

    $filename = 'ecommerce-bookshop-report-by-order-items';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.order-items', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportOrderItems return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'grade_id' => $this->grades[1]->id,
        'semester_setting_id' => $this->semester_settings[1]->id,
        'start_date' => '2024-02-01',
        'end_date' => '2024-02-05',
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'ecommerce-bookshop-report-by-order-items';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.order-items', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});
