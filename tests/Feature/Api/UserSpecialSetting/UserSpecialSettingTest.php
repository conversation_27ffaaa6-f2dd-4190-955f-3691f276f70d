<?php

use App\Enums\UserSpecialSettingModule;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\UserSpecialSetting;
use App\Services\UserSpecialSettingService;
use Database\Seeders\PermissionSeeder;
use <PERSON>vel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        PermissionSeeder::class
    ]);

    $this->user = User::factory()->create();

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->baseUrl = 'user-special-settings.';

    $this->userSpecialSettingTableName = resolve(UserSpecialSetting::class)->getTable();
});

test('index without any params', function () {
    $first_user_special_setting = UserSpecialSetting::factory()->create();
    $second_user_special_setting = UserSpecialSetting::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'index', ['order_by' => ['id']]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn ($response) => $response->toMatchArray([
                'id' => $first_user_special_setting->id,
                'module' => $first_user_special_setting->module->value,
                'submodule' => $first_user_special_setting->submodule,
                'user' => resourceToArray(new UserResource($first_user_special_setting->user)),
                'custom_permissions' => $first_user_special_setting->custom_permissions,
            ]),
            fn ($response) => $response->toMatchArray([
                'id' => $second_user_special_setting->id,
                'module' => $second_user_special_setting->module->value,
                'submodule' => $second_user_special_setting->submodule,
                'user' => resourceToArray(new UserResource($second_user_special_setting->user)),
                'custom_permissions' => $second_user_special_setting->custom_permissions,
            ]),
        );
});

test('index with params filters', function () {
    $user = User::factory()->create();
    $first_user_special_setting = UserSpecialSetting::factory()->create([
        'module' => UserSpecialSettingModule::HOSTEL,
        'submodule' => 'HOSTEL_PIC',
        'user_id' => $user->id,
    ]);

    UserSpecialSetting::factory()->create();

    expect(UserSpecialSetting::count())->toBe(2);

    $payload = [
        'module' => 'HOSTEL',
        'submodule' => 'HOSTEL_PIC',
        'user_id' => $user->id,
        'order_by' => ['id'],
    ];

    $this->mock(UserSpecialSettingService::class)
        ->shouldReceive('getAllPaginatedUserSpecialSettings')
        ->once()
        ->with($payload)
        ->andReturn(UserSpecialSetting::where(collect($payload)->except(['order_by'])->toArray())->paginate());

    // filter by submodule asc
    $response = $this->getJson(route($this->baseUrl . 'index', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->toHaveKey('0', [
            'id' => $first_user_special_setting->id,
            'module' => $first_user_special_setting->module->value,
            'submodule' => $first_user_special_setting->submodule,
            'user' => resourceToArray(new UserResource($first_user_special_setting->user)),
            'custom_permissions' => $first_user_special_setting->custom_permissions
        ]);
});

test('create success', function () {
    expect(UserSpecialSetting::count())->toBe(0);

    $user = User::factory()->create();

    $payload = [
        'module' => 'HOSTEL',
        'submodule' => 'HOSTEL_PIC',
        'user_id' => $user->id,
        'custom_permissions' => [],
    ];

    $this->mock(UserSpecialSettingService::class)
        ->shouldReceive('createUserSpecialSetting')
        ->once()
        ->with($payload)
        ->andReturn(UserSpecialSetting::create($payload));

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    // Expect only one record is created
    expect(UserSpecialSetting::count())->toBe(1);

    $created_data = UserSpecialSetting::first();

    $payload['id'] = $created_data->id;
    $payload['user'] = resourceToArray(new UserResource($user));
    unset($payload['user_id']);

    expect($response->json())->toMatchArray([
        'status' => 'OK',
        'code' => 200,
        'message' => 'Success.',
    ])
        ->and($response->json()['data'])
        ->toEqual($payload);
});

test('create validation error', function () {
    expect(UserSpecialSetting::count())->toBe(0);

    $response = $this->postJson(route($this->baseUrl . 'create'), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->userSpecialSettingTableName, 0);

    expect(UserSpecialSetting::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'module' =>  [
                    'The module field is required.'
                ],
                'user_id' =>  [
                    'The user id field is required.'
                ],
            ],
            'data' => null
        ]);
});

test('update success', function () {
    $user_special_setting = UserSpecialSetting::factory()->create();

    expect(UserSpecialSetting::count())->toBe(1);

    $new_user = User::factory()->create();

    $payload = [
        'module' => 'EXAM',
        'submodule' => null,
        'user_id' => $new_user->id,
        'custom_permissions' => [
            'test' => true
        ],
    ];

    $this->mock(UserSpecialSettingService::class)
        ->shouldReceive('updateUserSpecialSetting')
        ->once()
        ->withArgs(function ($func_user_special_setting, $func_payload) use ($user_special_setting, $payload) {
            return $func_user_special_setting->is($user_special_setting) && $func_payload == $payload;
        })
        ->andReturnUsing(function () use ($user_special_setting, $payload) {
            $user_special_setting->update($payload);
            return $user_special_setting->refresh();
        });

    $response = $this->putJson(route($this->baseUrl . 'update', $user_special_setting->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->userSpecialSettingTableName, 1);

    expect(UserSpecialSetting::count())->toBe(1)
        ->and($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $user_special_setting->id,
            'module' => $payload['module'],
            'submodule' => $payload['submodule'],
            'user' => resourceToArray(new UserResource($new_user)),
        ]);

    $this->assertDatabaseHas($this->userSpecialSettingTableName, [
        'id' => $user_special_setting->id,
        'module' => $payload['module'],
        'submodule' => $payload['submodule'],
        'user_id' => $payload['user_id'],
    ]);
});

test('update validation error', function () {
    $user_special_setting = UserSpecialSetting::factory()->create();

    expect(UserSpecialSetting::count())->toBe(1);

    $response = $this->putJson(route($this->baseUrl . 'update', $user_special_setting->id), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(UserSpecialSetting::count())->toBe(1)
        ->and($response->json())->toMatchArray([
            'error' => [
                'module' =>  [
                    'The module field is required.'
                ],
                'user_id' =>  [
                    'The user id field is required.'
                ],
            ],
            'data' => null
        ]);
});

test('show single record success', function () {
    $first_user_special_setting = UserSpecialSetting::factory()->create();
    $second_user_special_setting = UserSpecialSetting::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'show', $first_user_special_setting->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $first_user_special_setting->id,
            'module' => $first_user_special_setting->module->value,
            'submodule' => $first_user_special_setting->submodule,
            'user' => resourceToArray(new UserResource($first_user_special_setting->user)),
            'custom_permissions' => $first_user_special_setting->custom_permissions,
        ]);

    $response = $this->getJson(route($this->baseUrl . 'show', $second_user_special_setting->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $second_user_special_setting->id,
            'module' => $second_user_special_setting->module->value,
            'submodule' => $second_user_special_setting->submodule,
            'user' => resourceToArray(new UserResource($second_user_special_setting->user)),
            'custom_permissions' => $second_user_special_setting->custom_permissions,
        ]);
});

test('show not existing record error', function () {
    expect(UserSpecialSetting::count())->toBe(0);

    $response = $this->getJson(route($this->baseUrl . 'show', 1));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('delete success', function () {
    $first_user_special_setting = UserSpecialSetting::factory()->create();
    $other_user_special_settings = UserSpecialSetting::factory()->count(3)->create();

    $this->assertDatabaseCount($this->userSpecialSettingTableName, 4);

    //id not exist
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['user_special_setting' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->userSpecialSettingTableName, 4);

    //delete success
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['user_special_setting' => $first_user_special_setting->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->userSpecialSettingTableName, 3);
    $this->assertDatabaseMissing($this->userSpecialSettingTableName, ['id' => $first_user_special_setting->id]);

    foreach ($other_user_special_settings as $other_user_special_setting) {
        $this->assertDatabaseHas($this->userSpecialSettingTableName, ['id' => $other_user_special_setting->id]);
    }
});
