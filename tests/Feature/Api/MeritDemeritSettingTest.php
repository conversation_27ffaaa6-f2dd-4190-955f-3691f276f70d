<?php

use App\Enums\MeritDemeritType;
use App\Models\MeritDemeritSetting;
use App\Models\User;
use App\Services\MeritDemeritSettingService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Pagination\LengthAwarePaginator;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    $this->user = User::factory()->create();

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->baseUrl = 'merit-demerit-settings.';

    $this->meritDemeritSettingTableName = resolve(MeritDemeritSetting::class)->getTable();
});

test('index without any params', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create();
    $second_merit_demerit_setting = MeritDemeritSetting::factory()->create();

    $response = $this->getJson(route($this->baseUrl.'index', ['order_by' => ['id']]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_merit_demerit_setting->id,
                'name' => $first_merit_demerit_setting->name,
                'type' => $first_merit_demerit_setting->type,
                'average_exam_marks' => $first_merit_demerit_setting->average_exam_marks,
                'conduct_marks' => $first_merit_demerit_setting->conduct_marks,
                'translations' => $first_merit_demerit_setting->translations
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_merit_demerit_setting->id,
                'name' => $second_merit_demerit_setting->name,
                'type' => $second_merit_demerit_setting->type,
                'average_exam_marks' => $second_merit_demerit_setting->average_exam_marks,
                'conduct_marks' => $second_merit_demerit_setting->conduct_marks,
                'translations' => $second_merit_demerit_setting->translations
            ]),
        );
});

test('index with params filter by name', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Bring Phone',
        'name->zh' => 'zh Bring Phone',
    ]);

    MeritDemeritSetting::factory()->create();

    expect(MeritDemeritSetting::count())->toBe(2);

    $response = $this->getJson(route($this->baseUrl.'index', [
        'name' => 'Bring Phone',
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->toHaveKey('0.name', $first_merit_demerit_setting->name);
});

test('index with params filter by type', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'type' => MeritDemeritType::MERIT->value,
    ]);

    MeritDemeritSetting::factory()->create([
        'type' => MeritDemeritType::DEMERIT->value,
    ]);

    expect(MeritDemeritSetting::count())->toBe(2);

    $response = $this->getJson(route($this->baseUrl.'index', [
        'type' => MeritDemeritType::MERIT->value,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->toHaveKey('0.type', $first_merit_demerit_setting->type);
});

test('index with params order by id', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create();
    $second_merit_demerit_setting = MeritDemeritSetting::factory()->create();

    expect(MeritDemeritSetting::count())->toBe(2);

    // Sort by id asc
    $response = $this->getJson(route($this->baseUrl.'index', [
        'order_by' => ['id' => 'asc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.id', $first_merit_demerit_setting->id)
        ->toHaveKey('1.id', $second_merit_demerit_setting->id);

    // Sort by id desc
    $response = $this->getJson(route($this->baseUrl.'index', [
        'order_by' => ['id' => 'desc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.id', $second_merit_demerit_setting->id)
        ->toHaveKey('1.id', $first_merit_demerit_setting->id);
});

test('index with params order by name', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Bring Phone',
        'name->zh' => 'zh Bring Phone',
    ]);

    $second_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Loiter',
        'name->zh' => 'zh Loiter',
    ]);

    expect(MeritDemeritSetting::count())->toBe(2);

    // Sort by name asc
    $response = $this->getJson(route($this->baseUrl.'index', [
        'order_by' => ['name' => 'asc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.name', $first_merit_demerit_setting->name)
        ->toHaveKey('1.name', $second_merit_demerit_setting->name);

    // Sort by name desc
    $response = $this->getJson(route($this->baseUrl.'index', [
        'order_by' => ['name' => 'desc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.name', $second_merit_demerit_setting->name)
        ->toHaveKey('1.name', $first_merit_demerit_setting->name);
});

test('index - getAll', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create();
    $second_merit_demerit_setting = MeritDemeritSetting::factory()->create();

    $payload = [
        'order_by' => 'id',
        'per_page' => -1
    ];

    $response = $this->getJson(route($this->baseUrl.'index', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_merit_demerit_setting->id,
                'name' => $first_merit_demerit_setting->name,
                'type' => $first_merit_demerit_setting->type,
                'average_exam_marks' => $first_merit_demerit_setting->average_exam_marks,
                'conduct_marks' => $first_merit_demerit_setting->conduct_marks,
                'translations' => $first_merit_demerit_setting->translations
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_merit_demerit_setting->id,
                'name' => $second_merit_demerit_setting->name,
                'type' => $second_merit_demerit_setting->type,
                'average_exam_marks' => $second_merit_demerit_setting->average_exam_marks,
                'conduct_marks' => $second_merit_demerit_setting->conduct_marks,
                'translations' => $second_merit_demerit_setting->translations
            ]),
        );
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(MeritDemeritSettingService::class, function (MockInterface $mock) {
        $setting = MeritDemeritSetting::factory()->create();

        $mock->shouldReceive('getAllPaginatedMeritDemeritSettings')
            ->once()
            ->andReturn(new LengthAwarePaginator([$setting], 1, 1));
    });

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(MeritDemeritSettingService::class, function (MockInterface $mock) {
        $settings = MeritDemeritSetting::factory(2)->create();

        $mock->shouldReceive('getAllMeritDemeritSettings')->once()->andReturn($settings);
    });

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});


test('create success', function () {
    expect(MeritDemeritSetting::count())->toBe(0);

    $payload = [
        'name' => [
            'en' => 'Bring Phone',
            'zh' => 'zh Bring Phone',
        ],
        'type' => MeritDemeritType::DEMERIT->value,
        'average_exam_marks' => -10,
        'conduct_marks' => -5,
    ];

    $response = $this->postJson(route($this->baseUrl.'create'), $payload);

    $response->assertStatus(200);

    // Expect only one record is created
    expect(MeritDemeritSetting::count())->toBe(1);

    $created_data = MeritDemeritSetting::first();

    $payload['id'] = $created_data->id;
    $payload['translations']['name'] = $payload['name'];
    $payload['name'] = $payload['name']['en'];

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual($payload);
});

test('create validation error', function () {
    expect(MeritDemeritSetting::count())->toBe(0);

    $response = $this->postJson(route($this->baseUrl.'create'), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->meritDemeritSettingTableName, 0);

    expect(MeritDemeritSetting::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'name' => [
                    'The name field is required.'
                ],
                'type' => [
                    'The type field is required.'
                ],
                'average_exam_marks' => [
                    'The average exam marks field is required.'
                ],
                'conduct_marks' => [
                    'The conduct marks field is required.'
                ],
            ],
            'data' => null
        ]);
});

test('update success', function () {
    $merit_demerit_setting = MeritDemeritSetting::factory()->create();

    expect(MeritDemeritSetting::count())->toBe(1);

    $payload = [
        'name' => [
            'en' => 'updated Bring Phone',
            'zh' => 'updated zh Bring Phone',
        ],
        'type' => MeritDemeritType::DEMERIT->value,
        'average_exam_marks' => -100,
        'conduct_marks' => -50,
    ];

    $response = $this->putJson(route($this->baseUrl.'update', $merit_demerit_setting->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->meritDemeritSettingTableName, 1);

    expect(MeritDemeritSetting::count())->toBe(1)
        ->and($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $merit_demerit_setting->id,
            'name' => $payload['name']['en'],
            'type' => $payload['type'],
            'average_exam_marks' => $payload['average_exam_marks'],
            'conduct_marks' => $payload['conduct_marks'],
            'translations' => $merit_demerit_setting->refresh()->translations
        ]);

    $this->assertDatabaseHas($this->meritDemeritSettingTableName, [
        'id' => $merit_demerit_setting->id,
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'type' => $payload['type'],
        'average_exam_marks' => $payload['average_exam_marks'],
        'conduct_marks' => $payload['conduct_marks'],
    ]);
});

test('update validation error', function () {
    $merit_demerit_setting = MeritDemeritSetting::factory()->create();

    expect(MeritDemeritSetting::count())->toBe(1);

    $response = $this->putJson(route($this->baseUrl.'update', $merit_demerit_setting->id), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(MeritDemeritSetting::count())->toBe(1)
        ->and($response->json())->toMatchArray([
            'error' => [
                'name' => [
                    'The name field is required.'
                ],
                'type' => [
                    'The type field is required.'
                ],
                'average_exam_marks' => [
                    'The average exam marks field is required.'
                ],
                'conduct_marks' => [
                    'The conduct marks field is required.'
                ],
            ],
            'data' => null
        ]);

    $this->assertDatabaseHas($this->meritDemeritSettingTableName, [
        'id' => $merit_demerit_setting->id,
        'name->en' => $merit_demerit_setting->getTranslation('name', 'en'),
        'name->zh' => $merit_demerit_setting->getTranslation('name', 'zh'),
    ]);
});

test('show single record success', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create();
    $second_merit_demerit_setting = MeritDemeritSetting::factory()->create();

    $response = $this->getJson(route($this->baseUrl.'show', $first_merit_demerit_setting->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $first_merit_demerit_setting->id,
            'name' => $first_merit_demerit_setting->name,
            'type' => $first_merit_demerit_setting->type,
            'average_exam_marks' => $first_merit_demerit_setting->average_exam_marks,
            'conduct_marks' => $first_merit_demerit_setting->conduct_marks,
            'translations' => $first_merit_demerit_setting->translations
        ]);

    $response = $this->getJson(route($this->baseUrl.'show', $second_merit_demerit_setting->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $second_merit_demerit_setting->id,
            'name' => $second_merit_demerit_setting->name,
            'type' => $second_merit_demerit_setting->type,
            'average_exam_marks' => $second_merit_demerit_setting->average_exam_marks,
            'conduct_marks' => $second_merit_demerit_setting->conduct_marks,
            'translations' => $second_merit_demerit_setting->translations
        ]);
});

test('show not existing record error', function () {
    expect(MeritDemeritSetting::count())->toBe(0);

    $response = $this->getJson(route($this->baseUrl.'show', 1));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('delete success', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create();
    $other_merit_demerit_settings = MeritDemeritSetting::factory()->count(3)->create();

    $this->assertDatabaseCount($this->meritDemeritSettingTableName, 4);

    //id not exist
    $response = $this->deleteJson(route($this->baseUrl.'destroy', ['merit_demerit_setting' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->meritDemeritSettingTableName, 4);

    //delete success
    $response = $this->deleteJson(route($this->baseUrl.'destroy', ['merit_demerit_setting' => $first_merit_demerit_setting->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    foreach ($other_merit_demerit_settings as $other_merit_demerit_setting) {
        $this->assertDatabaseHas($this->meritDemeritSettingTableName, ['id' => $other_merit_demerit_setting->id]);
    }
});
