<?php

use App\Enums\MerchantType;

test('getLabel()', function () {
    expect(MerchantType::getLabel(MerchantType::BOOKSHOP))->toBe('Bookshop')
        ->and(MerchantType::getLabel(MerchantType::CANTEEN))->toBe('Canteen')
        ->and(MerchantType::getLabel(MerchantType::OTHER))->toBe('Other');
});

test('values()', function () {
    expect(MerchantType::values())->toBe(['BOOKSHOP', 'CANTEEN', 'OTHER']);
});

test('options()', function () {
    expect(MerchantType::options())->toEqual([
        ['value' => 'BOOKSHOP', 'name' => 'Bookshop'],
        ['value' => 'CANTEEN', 'name' => 'Canteen'],
        ['value' => 'OTHER', 'name' => 'Other'],
    ]);
});
