<?php

use App\Enums\CardType;

test('getLabel()', function () {
    expect(CardType::getLabel(CardType::NFC))->toBe('Nfc')
        ->and(CardType::getLabel(CardType::PROXIMITY))->toBe('Proximity');
});

test('values()', function() {
    expect(CardType::values())->toBe(['NFC', 'PROXIMITY']);
});

test('options()', function() {
    expect(CardType::options())->toEqual([
        ['value' => 'NFC', 'name' => 'Nfc'],
        ['value' => 'PROXIMITY', 'name' => 'Proximity'],
    ]);
});
