<?php

use App\Enums\AttendanceStatus;

test('getLabel()', function () {
    expect(AttendanceStatus::getLabel(AttendanceStatus::PRESENT))->toBe('Present')
        ->and(AttendanceStatus::getLabel(AttendanceStatus::ABSENT))->toBe('Absent');
});

test('values()', function () {
    expect(AttendanceStatus::values())->toBe(['PRESENT', 'ABSENT']);
});

test('options()', function () {
    expect(AttendanceStatus::options())->toEqual([
        ['value' => 'PRESENT', 'name' => 'Present'],
        ['value' => 'ABSENT', 'name' => 'Absent'],
    ]);
});
