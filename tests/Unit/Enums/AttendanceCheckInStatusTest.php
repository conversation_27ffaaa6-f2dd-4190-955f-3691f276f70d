<?php

use App\Enums\AttendanceCheckInStatus;

test('getLabel()', function () {
    expect(AttendanceCheckInStatus::getLabel(AttendanceCheckInStatus::ON_TIME))->toBe('On Time')
        ->and(AttendanceCheckInStatus::getLabel(AttendanceCheckInStatus::LATE))->toBe('Late');
});

test('values()', function () {
    expect(AttendanceCheckInStatus::values())->toBe(['ON_TIME', 'LATE']);
});

test('options()', function () {
    expect(AttendanceCheckInStatus::options())->toEqual([
        ['value' => 'ON_TIME', 'name' => 'On Time'],
        ['value' => 'LATE', 'name' => 'Late'],
    ]);
});
