<?php

use App\Enums\HostelBlockType;

test('getLabel()', function () {
    expect(HostelBlockType::getLabel(HostelBlockType::EMPLOYEE))->toBe('Employee')
        ->and(HostelBlockType::getLabel(HostelBlockType::STUDENT))->toBe('Student');
});

test('values()', function() {
    expect(HostelBlockType::values())->toBe(['EMPLOYEE', 'STUDENT']);
});

test('options()', function() {
    expect(HostelBlockType::options())->toBe([
        ['value' => 'EMPLOYEE', 'name' => 'Employee'],
        ['value' => 'STUDENT', 'name' => 'Student'],
    ]);
});
