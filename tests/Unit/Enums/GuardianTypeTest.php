<?php

use App\Enums\GuardianType;

test('getLabel()', function () {
    expect(GuardianType::getLabel(GuardianType::FATHER))->toBe('Father')
        ->and(GuardianType::getLabel(GuardianType::MOTHER))->toBe('Mother')
        ->and(GuardianType::getLabel(GuardianType::GUARDIAN))->toBe('Guardian');
});

test('values()', function () {
    expect(GuardianType::values())->toBe([
        'FATHER',
        'MOTHER',
        'GUARDIAN',
    ]);
});

test('options()', function () {
    expect(GuardianType::options())->toEqual([
        ['value' => 'FATHER', 'name' => 'Father'],
        ['value' => 'MOTHER', 'name' => 'Mother'],
        ['value' => 'GUAR<PERSON>AN', 'name' => 'Guardian'],
    ]);
});
