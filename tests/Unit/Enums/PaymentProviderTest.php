<?php

use App\Enums\PaymentProvider;

test('getLabel()', function () {
    expect(PaymentProvider::getLabel(PaymentProvider::PAYEX))->toBe('Payex');
});

test('values()', function() {
    expect(PaymentProvider::values())->toBe(['PAYEX']);
});

test('options()', function() {
    expect(PaymentProvider::options())->toEqual([
        ['value' => 'PAYEX', 'name' => 'Payex']
    ]);
});

test('getEnum()', function() {
    expect(PaymentProvider::getEnum('PAYEX'))->toBe(PaymentProvider::PAYEX);

    $this->expectExceptionMessage('Payment provider not found.');
    PaymentProvider::getEnum('NON_EXIST_PROVIDER');
});
