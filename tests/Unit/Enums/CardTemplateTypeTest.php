<?php

use App\Enums\CardTemplateType;

test('getLabel()', function () {
    expect(CardTemplateType::getLabel(CardTemplateType::EMPLOYEE))->toBe('Employee')
        ->and(CardTemplateType::getLabel(CardTemplateType::STUDENT))->toBe('Student')
        ->and(CardTemplateType::getLabel(CardTemplateType::CONTRACTOR))->toBe('Contractor')
        ;
});

test('values()', function () {
    expect(CardTemplateType::values())->toBe(['EMPLOYEE', 'STUDENT', 'CONTRACTOR']);
});

test('options()', function () {
    expect(CardTemplateType::options())->toEqual([
        ['value' => 'EMPLOYEE', 'name' => 'Employee'],
        ['value' => 'STUDENT', 'name' => 'Student'],
        ['value' => 'CONTRACTOR', 'name' => 'Contractor'],
    ]);
});
