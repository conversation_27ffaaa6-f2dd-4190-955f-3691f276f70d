<?php

use App\Enums\ConductRecordStatus;

test('getLabel()', function () {
    expect(ConductRecordStatus::getLabel(ConductRecordStatus::DRAFT))->toBe('Draft')
        ->and(ConductRecordStatus::getLabel(ConductRecordStatus::POSTED))->toBe('Posted');
});

test('values()', function() {
    expect(ConductRecordStatus::values())->toBe(['DRAFT', 'POSTED']);
});

test('options()', function() {
    expect(ConductRecordStatus::options())->toEqual([
        ['value' => 'DRAFT', 'name' => 'Draft'],
        ['value' => 'POSTED', 'name' => 'Posted'],
    ]);
});
