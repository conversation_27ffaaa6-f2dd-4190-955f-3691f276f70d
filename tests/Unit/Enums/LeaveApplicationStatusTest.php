<?php

use App\Enums\LeaveApplicationStatus;

test('getLabel()', function () {
    expect(LeaveApplicationStatus::getLabel(LeaveApplicationStatus::PENDING))->toBe('Pending')
        ->and(LeaveApplicationStatus::getLabel(LeaveApplicationStatus::APPROVED))->toBe('Approved')
        ->and(LeaveApplicationStatus::getLabel(LeaveApplicationStatus::REJECTED))->toBe('Rejected');
});

test('values()', function () {
    expect(LeaveApplicationStatus::values())->toBe(['PENDING', 'APPROVED', 'REJECTED']);
});

test('options()', function () {
    expect(LeaveApplicationStatus::options())->toBe([
        ['value' => 'PENDING', 'name' => 'Pending'],
        ['value' => 'APPROVED', 'name' => 'Approved'],
        ['value' => 'REJECTED', 'name' => 'Rejected'],
    ]);
});
