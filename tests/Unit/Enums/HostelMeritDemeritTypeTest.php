<?php

use App\Enums\HostelMeritDemeritType;

test('getLabel()', function () {
    expect(HostelMeritDemeritType::getLabel(HostelMeritDemeritType::MERIT))->toBe('MERIT')
        ->and(HostelMeritDemeritType::getLabel(HostelMeritDemeritType::DEMERIT))->toBe('DEMERIT');
});

test('values()', function () {
    expect(HostelMeritDemeritType::values())->toBe(['MERIT', 'DEMERIT']);
});

test('options()', function () {
    expect(HostelMeritDemeritType::options())->toEqual([
        ['value' => 'MERIT', 'name' => 'MERIT'],
        ['value' => 'DEMERIT', 'name' => 'DEMERIT'],
    ]);
});
