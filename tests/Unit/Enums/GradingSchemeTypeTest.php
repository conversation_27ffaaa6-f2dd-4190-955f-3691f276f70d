<?php

use App\Enums\GradingSchemeType;

test('getLabel()', function () {
    expect(GradingSchemeType::getLabel(GradingSchemeType::CONDUCT))->toBe('Conduct')
        ->and(GradingSchemeType::getLabel(GradingSchemeType::EXAM))->toBe('Exam');
});

test('values()', function() {
    expect(GradingSchemeType::values())->toBe(['CONDUCT', 'EXAM']);
});

test('options()', function() {
    expect(GradingSchemeType::options())->toEqual([
        ['value' => 'CONDUCT', 'name' => 'Conduct'],
        ['value' => 'EXAM', 'name' => 'Exam'],
    ]);
});
