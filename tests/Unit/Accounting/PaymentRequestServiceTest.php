<?php


use App\Models\Bank;
use App\Models\BillingDocument;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\PaymentMethod;
use App\Models\Student;
use App\Models\PaymentRequest;
use App\Services\Billing\PaymentRequestService;
use Carbon\Carbon;

beforeEach(function () {
    $this->student = Student::factory()->create();
    $this->guardian = Guardian::factory()->create();
    $this->employee = Employee::factory()->create();
});

test('create payment request success', function() {
    /** @var PaymentRequestService */
    $payment_request_service = resolve(PaymentRequestService::class);

    $invoice = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_reference_number' => $this->student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => null,
    ]);

    $fpx_payment_method = PaymentMethod::factory()->create([
        'name' => 'Fpx',
        'code' => 'FPX',
    ]);

    $bank = Bank::factory()->create([
        'name->en' => 'MAYBANK',
    ]);

    $payment_request = $payment_request_service
        ->setUserable($this->guardian)
        ->setBillingDocument($invoice)
        ->setPaymentMethod($fpx_payment_method)
        ->setStatus(PaymentRequest::STATUS_APPROVED)
        ->setAmount(100.5)
        ->setBankId($bank->id)
        ->setPaymentReferenceNo('FPX123456')
        ->setApprovedAt(Carbon::now())
        ->setApprovedByEmployee($this->employee)
        ->create()
        ->getPaymentRequest();

    expect($payment_request->userable_id)->toBe($this->guardian->id)
        ->and($payment_request->userable_type)->toBe(Guardian::class)
        ->and($payment_request->billing_document_id)->toBe($invoice->id)
        ->and($payment_request->payment_method_id)->toBe($fpx_payment_method->id)
        ->and($payment_request->status)->toBe(PaymentRequest::STATUS_APPROVED)
        ->and($payment_request->amount)->toBe(100.5)
        ->and($payment_request->bank_id)->toBe($bank->id)
        ->and($payment_request->payment_reference_no)->toBe('FPX123456')
        ->and($payment_request->approved_at)->toBe(now()->toDateTimeString())
        ->and($payment_request->approved_by_employee_id)->toBe($this->employee->id);

    $this->assertDatabaseHas('payment_requests', [
        'userable_id' => $this->guardian->id,
        'userable_type' => Guardian::class,
        'billing_document_id' => $invoice->id,
        'payment_method_id' => $fpx_payment_method->id,
        'status' => PaymentRequest::STATUS_APPROVED,
        'amount' => 100.5,
        'bank_id' => $bank->id,
        'payment_reference_no' => 'FPX123456',
        'approved_at' => now()->toDateTimeString(),
        'approved_by_employee_id' => $this->employee->id,
    ]);
});