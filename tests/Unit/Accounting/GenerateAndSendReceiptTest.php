<?php

use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\Payment;
use App\Models\PaymentTerm;
use App\Models\Product;
use App\Models\Student;
use App\Models\Tax;
use App\Models\User;
use App\Services\DocumentPrintService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;

uses(RefreshDatabase::class);

beforeEach(function () {

    $this->legalEntity = LegalEntity::factory()->create();
    $this->student = Student::factory()->has(User::factory()->state([]))->create([]);

    $this->system = Employee::factory()->create([
        'employee_number' => 'SYSTEM',
    ]);
    $this->employee = Employee::factory()->create();

    $this->bank = Bank::factory()->create([
        'name' => 'MAYBANK',
    ]);
    $this->bankAccount = BankAccount::factory()->create([
        'bank_id' => $this->bank->id,
        'bankable_id' => $this->legalEntity->id,
        'bankable_type' => LegalEntity::class,
    ]);
    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);
    $this->product = Product::factory()->create([
        'gl_account_code' => 'PRO0000001'
    ]);

});

test('test generation of receipt and email receipt', function () {

    $invoice1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'paid_at' => '2024-07-25 04:30:00'
    ]);


    $payment_method = \App\Models\PaymentMethod::factory()->create([
        'name' => 'FPX'
    ]);

    $payment = Payment::factory()->create([
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $payment_method->id,
        'payment_reference_no' => 'FPX03949324',
        'amount_received' => $invoice1->amount_after_tax,
        'paid_at' => '2024-09-23 04:30:00',
    ]);

    $this->mock(DocumentPrintService::class, function ($mock) use (&$invoice1) {
        $mock->shouldReceive('setPrintable')->once()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andReturnSelf();
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('https://domain.com/file.pdf');
    });

    Mail::fake();

    // trigger event listener
    $event = new \App\Events\PaymentCompletedEvent($payment);
    $listener = new \App\Listeners\GenerateAndSendReceipt();

    $listener->handle($event);

    // 2025-03-21 Lucas
    // Don't need to send email to user anymore from client's feedback
//    Mail::assertSent(\App\Mail\PaymentReceiptMail::class, function ($mail) use (&$invoice1) {
//        return $mail->hasTo($this->student->email) &&
//            $mail->hasSubject(config('app.name') . ': Official Receipt') &&
//            $mail->assertSeeInHtml('https://domain.com/file.pdf') &&
//            $mail->assertSeeInHtml(number_format($invoice1->amount_after_tax, 2));
//    });

    //check receipt_url is save
    $this->assertDatabaseHas('billing_documents', [
        'id' => $invoice1->id,
        'receipt_url' => "https://domain.com/file.pdf"
    ]);
});


