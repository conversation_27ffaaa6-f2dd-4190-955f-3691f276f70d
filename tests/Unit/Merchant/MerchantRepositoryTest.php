<?php

use App\Enums\MerchantType;
use App\Models\Merchant;
use App\Repositories\MerchantRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->merchantRepository = app(MerchantRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Merchant::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->merchantRepository->getModelClass();

    expect($response)->toEqual(Merchant::class);
});

test('getAll()', function () {
    $merchants = Merchant::factory(3)->state(new Sequence(
        [
            'name->en' => 'English',
            'label' => 'en1',
            'type' => MerchantType::CANTEEN,
            'email' => null,
            'phone_number' => null,
        ],
        [
            'name->en' => 'English 2',
            'label' => 'en2',
            'type' => MerchantType::BOOKSHOP,
            'email' => null,
            'phone_number' => null,
        ],
        [
            'name->en' => 'Tamil',
            'label' => 'tm1',
            'type' => MerchantType::OTHER,
            'email' => null,
            'phone_number' => null,
        ]
    ))->create(['user_id' => null]);

    $response = $this->merchantRepository->getAll(['order_by' => 'id'])->toArray();

    expect($response)->toEqual($merchants->toArray());
});

test('getAllPaginated()', function () {
    $merchants = Merchant::factory(3)->state(new Sequence(
        [
            'name->en' => 'English',
            'label' => 'en1',
            'type' => MerchantType::CANTEEN,
            'is_active' => true,
            'email' => null,
            'phone_number' => null,
        ],
        [
            'name->en' => 'English 2',
            'label' => 'en2',
            'type' => MerchantType::BOOKSHOP,
            'is_active' => true,
            'email' => null,
            'phone_number' => null,
        ],
        [
            'name->en' => 'Tamil',
            'label' => 'tm1',
            'type' => MerchantType::OTHER,
            'is_active' => false,
            'email' => null,
            'phone_number' => null,
        ]
    ))->create(['user_id' => null]);

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.name.en", 'English 2');

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.en", 'English'),
            fn($data) => $data->toHaveKey("name.en", 'English 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by label = en1
    $payload = [
        'label' => 'en1'
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.label", 'en1');

    //Filter by partial label = en
    $payload = [
        'label' => 'en'
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("label", 'en1'),
            fn($data) => $data->toHaveKey("label", 'en2')
        );

    //Filter non-existing label = Not Exist
    $payload = [
        'label' => 'Not Exist'
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by type = CANTEEN
    $payload = [
        'type' => MerchantType::CANTEEN->value
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.type", MerchantType::CANTEEN->value);

    //Filter non-existing type = Not Exist
    $payload = [
        'type' => 'Not Exist'
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by is_active = true
    $payload = [
        'is_active' => true
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("is_active", true),
            fn($data) => $data->toHaveKey("is_active", true),
        );

    //Filter by is_active = false
    $payload = [
        'is_active' => false
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("is_active", false),
        );

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.en", 'English'),
            fn($data) => $data->toHaveKey("name.en", 'English 2'),
            fn($data) => $data->toHaveKey("name.en", 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.en", 'Tamil'),
            fn($data) => $data->toHaveKey("name.en", 'English 2'),
            fn($data) => $data->toHaveKey("name.en", 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $merchants[0]->id),
            fn($data) => $data->toHaveKey('id', $merchants[1]->id),
            fn($data) => $data->toHaveKey('id', $merchants[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->merchantRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $merchants[2]->id),
            fn($data) => $data->toHaveKey('id', $merchants[1]->id),
            fn($data) => $data->toHaveKey('id', $merchants[0]->id),
        );
});
