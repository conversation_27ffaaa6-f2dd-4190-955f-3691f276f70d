<?php

use App\Enums\GradingSchemeType;
use App\Models\GradingScheme;
use App\Repositories\GradingSchemeRepository;

beforeEach(function () {
    $this->gradingSchemeRepository = resolve(GradingSchemeRepository::class);
});

test('getModelClass()', function () {
    $response = $this->gradingSchemeRepository->getModelClass();

    expect($response)->toEqual(GradingScheme::class);
});

test('getAll()', function () {
    $first_grading_scheme = GradingScheme::factory()->create();
    $second_grading_scheme = GradingScheme::factory()->create();
    $third_grading_scheme = GradingScheme::factory()->create();

    $response = $this->gradingSchemeRepository->getAll(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->sequence(
        fn ($response) => $response->toEqual($first_grading_scheme->toArray()),
        fn ($response) => $response->toEqual($second_grading_scheme->toArray()),
        fn ($response) => $response->toEqual($third_grading_scheme->toArray()),
    );
});

test('getAllPaginated()', function () {
    $type = GradingSchemeType::CONDUCT->value;
    $first_grading_scheme = GradingScheme::factory()->create([
        'name' => 'AAAA Scheme',
        'type' => $type,
    ]);

    // Filter by type = $type
    $response = $this->gradingSchemeRepository->getAllPaginated(['type' => $type])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_grading_scheme->toArray()]);

    // ----------------------------------------------------------------------------------------------
    // ----------------------------------------------------------------------------------------------

    $second_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Sem 1 2024 Class E',
    ]);

    $partial_same_name_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Sem 1',
    ]);

    // Filter by 'name' => 'Sem 1 2024 Class E'
    $response = $this->gradingSchemeRepository->getAllPaginated(['name' => 'Sem 1 2024 Class E'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$second_grading_scheme->toArray()]);

    // Filter by 'name' => 'Sem 1'
    $response = $this->gradingSchemeRepository->getAllPaginated(['name' => 'Sem 1'])->toArray();

    expect($response['data'])
        ->toHaveCount(2)
        ->toEqual([
            $second_grading_scheme->toArray(),
            $partial_same_name_grading_scheme->toArray(),
        ]);

    // ----------------------------------------------------------------------------------------------
    // ----------------------------------------------------------------------------------------------

    $inactive_grading_scheme = GradingScheme::factory()->create([
        'name' => 'ZZZZ Scheme',
        'is_active' => false,
    ]);

    // Filter by 'is_active' => false
    $response = $this->gradingSchemeRepository->getAllPaginated(['is_active' => false])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$inactive_grading_scheme->toArray()]);

    // Filter by 'is_active' => true
    $response = $this->gradingSchemeRepository->getAllPaginated(['is_active' => true])->toArray();

    expect($response['data'])
        ->toHaveCount(3)
        ->toEqual([
            $first_grading_scheme->toArray(),
            $second_grading_scheme->toArray(),
            $partial_same_name_grading_scheme->toArray(),
        ]);

    // ----------------------------------------------------------------------------------------------
    // ----------------------------------------------------------------------------------------------

    // Sort by name asc
    $response = $this->gradingSchemeRepository->getAllPaginated([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn ($response) => $response->toHaveKey('name', 'AAAA Scheme'),
        fn ($response) => $response->toHaveKey('name', 'Sem 1'),
        fn ($response) => $response->toHaveKey('name', 'Sem 1 2024 Class E'),
        fn ($response) => $response->toHaveKey('name', 'ZZZZ Scheme'),
    );

    // Sort by name desc
    $response = $this->gradingSchemeRepository->getAllPaginated([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn ($response) => $response->toHaveKey('name', 'ZZZZ Scheme'),
        fn ($response) => $response->toHaveKey('name', 'Sem 1 2024 Class E'),
        fn ($response) => $response->toHaveKey('name', 'Sem 1'),
        fn ($response) => $response->toHaveKey('name', 'AAAA Scheme'),
    );

    // ----------------------------------------------------------------------------------------------
    // ----------------------------------------------------------------------------------------------

    // Sort by id asc
    $response = $this->gradingSchemeRepository->getAllPaginated([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn ($response) => $response->toHaveKey('id', $first_grading_scheme->id),
        fn ($response) => $response->toHaveKey('id', $second_grading_scheme->id),
        fn ($response) => $response->toHaveKey('id', $partial_same_name_grading_scheme->id),
        fn ($response) => $response->toHaveKey('id', $inactive_grading_scheme->id),
    );

    // Sort by id desc
    $response = $this->gradingSchemeRepository->getAllPaginated([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn ($response) => $response->toHaveKey('id', $inactive_grading_scheme->id),
        fn ($response) => $response->toHaveKey('id', $partial_same_name_grading_scheme->id),
        fn ($response) => $response->toHaveKey('id', $second_grading_scheme->id),
        fn ($response) => $response->toHaveKey('id', $first_grading_scheme->id),
    );
});
