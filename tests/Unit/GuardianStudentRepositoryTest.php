<?php

use App\Models\Enrollment;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\Student;
use App\Repositories\GuardianStudentRepository;

beforeEach(function () {
    $this->guardianStudentRepository = resolve(GuardianStudentRepository::class);

    app()->setLocale('en');

    $this->locale = app()->getLocale();
    $this->table = resolve(GuardianStudent::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->guardianStudentRepository->getModelClass();

    expect($response)->toEqual(GuardianStudent::class);
});

test('getAll()', function () {
    $student = Student::factory()->create();
    $enrollment = Enrollment::factory()->create();
    $first_guardian = Guardian::factory()->create();
    $second_guardian = Guardian::factory()->create();
    $first_guardian_student = GuardianStudent::factory()->create([
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'guardian_id' => $first_guardian->id,
    ]);
    $second_guardian_student = GuardianStudent::factory()->create([
        'studenable_id' => $enrollment->id,
        'studenable_type' => Enrollment::class,
        'guardian_id' => $second_guardian->id,
    ]);

    //filter by studenable
    $filters = [
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ];
    $response = $this->guardianStudentRepository->getAll($filters)->toArray();

    expect($response)->toEqual([
        $first_guardian_student->toArray(),
    ]);
});

test('getAllPaginated()', function () {
    $student = Student::factory()->create();
    $enrollment = Enrollment::factory()->create();
    $first_guardian_student = GuardianStudent::factory()->create([
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);
    $second_guardian_student = GuardianStudent::factory()->create([
        'studenable_id' => $enrollment->id,
        'studenable_type' => Enrollment::class,
    ]);

    $response = $this->guardianStudentRepository->getAllPaginated()->toArray();

    expect($response['data'])->toEqual([$first_guardian_student->toArray(), $second_guardian_student->toArray()]);
});

test('deleteByStudenable()', function () {
    $first_student = Student::factory()->create();
    GuardianStudent::factory()->create([
        'studenable_id' => $first_student->id,
        'studenable_type' => Student::class,
    ]);
    $second_student = Student::factory()->create();
    GuardianStudent::factory()->create([
        'studenable_id' => $second_student->id,
        'studenable_type' => Student::class,
    ]);

    $this->guardianStudentRepository->deleteByStudenable($first_student);

    $this->assertDatabaseMissing($this->table, [
        'studenable_id' => $first_student->id,
        'studenable_type' => Student::class,
    ]);

    $this->assertDatabaseHas($this->table, [
        'studenable_id' => $second_student->id,
        'studenable_type' => Student::class,
    ]);

});
