<?php

use App\Models\Author;
use App\Services\AuthorService;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->authorService = app(AuthorService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Author::class)->getTable();
});

test('getAllPaginatedAuthors()', function () {
    $authors = Author::factory(3)->state(new Sequence(
        [
            'name' => 'English'
        ],
        [
            'name' => 'English 2'
        ],
        [
            'name' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->authorService->getAllPaginatedAuthors($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English 2')
        );

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->authorService->getAllPaginatedAuthors($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English'),
            fn($data) => $data->toHaveKey("name", 'English 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->authorService->getAllPaginatedAuthors($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->authorService->getAllPaginatedAuthors($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'English'),
            fn($data) => $data->toHaveKey("name", 'English 2'),
            fn($data) => $data->toHaveKey("name", 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->authorService->getAllPaginatedAuthors($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name", 'Tamil'),
            fn($data) => $data->toHaveKey("name", 'English 2'),
            fn($data) => $data->toHaveKey("name", 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->authorService->getAllPaginatedAuthors($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $authors[0]->id),
            fn($data) => $data->toHaveKey('id', $authors[1]->id),
            fn($data) => $data->toHaveKey('id', $authors[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->authorService->getAllPaginatedAuthors($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $authors[2]->id),
            fn($data) => $data->toHaveKey('id', $authors[1]->id),
            fn($data) => $data->toHaveKey('id', $authors[0]->id),
        );
});

test('createAuthor()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => fake()->name,
    ];

    $response = $this->authorService->createAuthor($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name']
    ]);
});

test('updateAuthor()', function () {
    $author = Author::factory()->create([
        'name' => 'Test EN'
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => 'Test 2',
    ];

    $response = $this->authorService->updateAuthor($author->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $author->id,
        'name' => $payload['name']
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name']
    ]);

    //update with id not exist
    $payload = [
        'name' => 'Test 3',
    ];

    $this->expectException(ModelNotFoundException::class);
    $this->authorService->updateAuthor(9999, $payload)->toArray();
});

test('deleteAuthor()', function () {
    $author = Author::factory()->create();
    $other_authors = Author::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->authorService->deleteAuthor($author->id);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $author->id]);

    foreach ($other_authors as $other_author) {
        $this->assertDatabaseHas($this->table, ['id' => $other_author->id]);
    }

    //id not exist
    $this->expectException(ModelNotFoundException::class);
    $this->authorService->deleteAuthor(9999);
});
