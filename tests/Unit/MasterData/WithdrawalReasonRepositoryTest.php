<?php

use App\Models\WithdrawalReason;
use App\Repositories\WithdrawalReasonRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->withdrawalReasonRepository = app(WithdrawalReasonRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(WithdrawalReason::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->withdrawalReasonRepository->getModelClass();

    expect($response)->toEqual(WithdrawalReason::class);
});

test('getAll()', function () {
    $withdrawal_reasons = WithdrawalReason::factory(3)->state(new Sequence(
        [
            'name->en' => 'Reason 1',
            'name->zh' => '理由一',
            'is_active' => true,
            'sequence' => 1,
        ],
        [
            'name->en' => 'Reason 2',
            'name->zh' => '理由二',
            'is_active' => true,
            'sequence' => 2,
        ],
        [
            'name->en' => 'Excuse 1',
            'name->zh' => '借口一',
            'is_active' => false,
            'sequence' => 3,
        ]
    ))->create();

    $response = $this->withdrawalReasonRepository->getAll(['order_by' => 'id'])->toArray();
    expect($response)->toEqual($withdrawal_reasons->toArray());
});


test('getAllPaginated() filter by name', function () {
    $withdrawal_reasons = WithdrawalReason::factory(3)->state(new Sequence(
        [
            'name->en' => 'Reason 1',
            'name->zh' => '理由一',
            'is_active' => true,
            'sequence' => 1,
        ],
        [
            'name->en' => 'Reason 2',
            'name->zh' => '理由二',
            'is_active' => true,
            'sequence' => 2,
        ],
        [
            'name->en' => 'Excuse 1',
            'name->zh' => '借口一',
            'is_active' => false,
            'sequence' => 3,
        ]
    ))->create();

    //Filter by name = Reason 1
    $payload = [
        'name' => 'Reason 1'
    ];
    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.name.$this->testLocale", 'Reason 1');

    //Filter by partial name = Reason
    $payload = [
        'name' => 'Reason'
    ];
    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Reason 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Reason 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);
});

test('getAllPaginated() sort', function () {
    $withdrawal_reasons = WithdrawalReason::factory(3)->state(new Sequence(
        [
            'name->en' => 'Reason 1',
            'name->zh' => '理由一',
            'is_active' => true,
            'sequence' => 1,
        ],
        [
            'name->en' => 'Reason 2',
            'name->zh' => '理由二',
            'is_active' => true,
            'sequence' => 2,
        ],
        [
            'name->en' => 'Excuse 1',
            'name->zh' => '借口一',
            'is_active' => false,
            'sequence' => 3,
        ]
    ))->create();

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Excuse 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Reason 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Reason 2'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Reason 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Reason 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Excuse 1'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[0]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[1]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[2]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[1]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[0]->id),
        );

    //sort by sequence asc
    $payload = [
        'order_by' => ['sequence' => 'asc', 'id' => 'asc'],
    ];
    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[0]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[1]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[2]->id),
        );

    //sort by sequence desc
    $payload = [
        'order_by' => ['sequence' => 'desc', 'id' => 'desc'],
    ];
    $response = $this->withdrawalReasonRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[2]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[1]->id),
            fn($data) => $data->toHaveKey('id', $withdrawal_reasons[0]->id),
        );
});
