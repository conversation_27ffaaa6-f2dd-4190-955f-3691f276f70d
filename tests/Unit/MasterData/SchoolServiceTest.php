<?php

use App\Enums\SchoolLevel;
use App\Models\School;
use App\Services\SchoolService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->schoolService = app(SchoolService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(School::class)->getTable();

});

test('getAllPaginatedSchools()', function () {
    $schools = School::factory(2)->state(new Sequence(
        [
            'name' => 'Apple'
        ],
        [
            'name' => 'Orange'
        ],
    ))->create();

    //Filter by name = Apple
    $payload = [
        'name' => 'apple'
    ];
    $response = $this->schoolService->getAllPaginatedSchools($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Apple')
        );

    //Filter by partial name = ange
    $payload = [
        'name' => 'ange'
    ];
    $response = $this->schoolService->getAllPaginatedSchools($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Orange'),
        );

    //Filter non-existing name = Non Exist
    $payload = [
        'name' => 'Non Exist'
    ];
    $response = $this->schoolService->getAllPaginatedSchools($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->schoolService->getAllPaginatedSchools($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Apple'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Orange'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->schoolService->getAllPaginatedSchools($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Orange'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Apple'),
        );

});

test('getAllSchools()', function () {
    $schools = School::factory(2)->state(new Sequence(
        [
            'name' => 'Apple'
        ],
        [
            'name' => 'Orange'
        ],
    ))->create();

    //Filter by name = Apple
    $payload = [
        'name' => 'apple'
    ];
    $response = $this->schoolService->getAllSchools($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Apple')
        );

    //Filter by partial name = ange
    $payload = [
        'name' => 'ange'
    ];
    $response = $this->schoolService->getAllSchools($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Orange'),
        );

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->schoolService->getAllSchools($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Apple'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Orange'),
        );
});

test('createSchool()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'level' => SchoolLevel::SECONDARY,
    ];

    $response = $this->schoolService->createSchool($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'level' => SchoolLevel::SECONDARY->value,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'level' => SchoolLevel::SECONDARY,
    ]);
});

test('updateSchool()', function () {
    $school = School::factory()->create([
        'name->en' => 'Test EN',
        'name->zh' => 'Test ZH',
        'level' => SchoolLevel::SECONDARY,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Test 2',
            'zh' => 'Test 3',
        ],
        'level' => SchoolLevel::PRIMARY,
    ];

    $response = $this->schoolService->updateSchool($school, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $school->id,
        'name' => $payload['name'],
        'level' => SchoolLevel::PRIMARY->value,
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'level' => SchoolLevel::PRIMARY,
    ]);

});

test('deleteSchool success', function () {
    $school = School::factory()->create();
    $other_schools = School::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->schoolService->deleteSchool($school);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $school->id]);

    foreach ($other_schools as $other_school) {
        $this->assertDatabaseHas($this->table, ['id' => $other_school->id]);
    }

});

test('deleteSchool failed validation', function () {

    $this->markTestIncomplete('implement this when school object has dependency that prevents it from being deleted.');

    $school = School::factory()->create();
    $other_schools = School::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    // todo: make sure got other dependency using school

    //delete failed
    $this->schoolService->deleteSchool($school);

    $this->assertDatabaseCount($this->table, 4);

});
