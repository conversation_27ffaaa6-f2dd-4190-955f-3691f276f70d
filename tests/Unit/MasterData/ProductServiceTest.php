<?php

use App\Enums\ProductCategory;
use App\Models\BillingDocumentLineItem;
use App\Models\GlAccount;
use App\Models\Product;
use App\Models\UnpaidItem;
use App\Models\Uom;
use App\Services\ProductService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->productService = app(ProductService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Product::class)->getTable();
});

test('getAllPaginatedProducts()', function () {
    Product::factory(3)->state(new Sequence(
        [
            'name->en' => 'School Fees 2024',
            'code' => 'SCHOOL01',
            'category' => ProductCategory::SCHOOL_FEES->value,
            'uom_code' => Uom::CODE_DEFAULT,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'is_active' => true,
            'unit_price' => 200.00,
        ],
        [
            'name->en' => 'Tuition Fees 2024',
            'code' => 'SCHOOL02',
            'category' => ProductCategory::HOSTEL_FEES->value,
            'uom_code' => Uom::CODE_DEFAULT,
            'gl_account_code' => GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS,
            'is_active' => true,
            'unit_price' => 100.00,
        ],
        [
            'name->en' => 'Cocu Fees 2024',
            'code' => 'SCHOOL03',
            'category' => ProductCategory::OTHERS->value,
            'uom_code' => Uom::CODE_DEFAULT,
            'gl_account_code' => GlAccount::CODE_ANY,
            'is_active' => true,
            'unit_price' => 20.00,
        ],
    ))->create();

    //Filter by code = SCHOOL02
    $payload = [
        'code' => 'SCHOOL02'
    ];

    $response = $this->productService->getAllPaginatedProducts($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('code', 'SCHOOL02')
        )
        ->and(isset($response['current_page']))->toBeTrue();
});

test('getAllProducts()', function () {
    Product::factory(3)->state(new Sequence(
        [
            'name->en' => 'School Fees 2024',
            'code' => 'SCHOOL01',
            'category' => ProductCategory::SCHOOL_FEES->value,
            'uom_code' => Uom::CODE_DEFAULT,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'is_active' => true,
            'unit_price' => 200.00,
        ],
        [
            'name->en' => 'Tuition Fees 2024',
            'code' => 'SCHOOL02',
            'category' => ProductCategory::HOSTEL_FEES->value,
            'uom_code' => Uom::CODE_DEFAULT,
            'gl_account_code' => GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS,
            'is_active' => true,
            'unit_price' => 100.00,
        ],
        [
            'name->en' => 'Cocu Fees 2024',
            'code' => 'SCHOOL03',
            'category' => ProductCategory::OTHERS->value,
            'uom_code' => Uom::CODE_DEFAULT,
            'gl_account_code' => GlAccount::CODE_ANY,
            'is_active' => true,
            'unit_price' => 20.00,
        ],
    ))->create();

    //Filter by code = SCHOOL03
    $payload = [
        'code' => 'SCHOOL03'
    ];

    $response = $this->productService->getAllProducts($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('code', 'SCHOOL03')
        )
        ->and(isset($response['current_page']))->toBeFalse();
});

test('createProduct()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'code' => 'SCHOOL01',
        'name' => [
            'en' => 'School Fees 2024',
            'zh' => 'zh School Fees 2024',
        ],
        'category' => ProductCategory::SCHOOL_FEES->value,
        'sub_category_1' => 'SUB_1',
        'sub_category_2' => 'SUB_2',
        'uom_code' => Uom::CODE_DEFAULT,
        'unit_price' => 200.00,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'description' => 'DESCRIPTION',
        'is_active' => true,
    ];

    $response = $this->productService->createProduct($payload)->toArray();

    expect($response)->toMatchArray([
        'code' => $payload['code'],
        'name' => $payload['name'],
        'category' => $payload['category'],
        'sub_category_1' => $payload['sub_category_1'],
        'sub_category_2' => $payload['sub_category_2'],
        'uom_code' => $payload['uom_code'],
        'unit_price' => $payload['unit_price'],
        'gl_account_code' => $payload['gl_account_code'],
        'description' => $payload['description'],
        'is_active' => $payload['is_active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'category' => $payload['category'],
        'sub_category_1' => $payload['sub_category_1'],
        'sub_category_2' => $payload['sub_category_2'],
        'uom_code' => $payload['uom_code'],
        'unit_price' => $payload['unit_price'],
        'gl_account_code' => $payload['gl_account_code'],
        'description' => $payload['description'],
        'is_active' => $payload['is_active'],
    ]);
});

test('updateProduct()', function () {
    $product = Product::factory()->create([
        'code' => 'TEST',
        'name' => [
            'en' => 'TEST',
            'zh' => 'zh TEST',
        ],
        'category' => ProductCategory::SCHOOL_FEES->value,
        'sub_category_1' => 'TEST',
        'sub_category_2' => 'TEST',
        'uom_code' => Uom::CODE_DEFAULT,
        'unit_price' => 200.00,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'description' => 'TEST',
        'is_active' => true,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'code' => 'SCHOOL01',
        'name' => [
            'en' => 'School Fees 2024',
            'zh' => 'zh School Fees 2024',
        ],
        'category' => ProductCategory::SCHOOL_FEES->value,
        'sub_category_1' => 'SUB_1',
        'sub_category_2' => 'SUB_2',
        'uom_code' => Uom::CODE_DEFAULT,
        'unit_price' => 200.00,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'description' => 'DESCRIPTION',
        'is_active' => true,
    ];

    $response = $this->productService->updateProduct($product, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $product->id,
        'code' => $payload['code'],
        'name' => $payload['name'],
        'category' => $payload['category'],
        'sub_category_1' => $payload['sub_category_1'],
        'sub_category_2' => $payload['sub_category_2'],
        'uom_code' => $payload['uom_code'],
        'unit_price' => $payload['unit_price'],
        'gl_account_code' => $payload['gl_account_code'],
        'description' => $payload['description'],
        'is_active' => $payload['is_active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'id' => $product->id,
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'category' => $payload['category'],
        'sub_category_1' => $payload['sub_category_1'],
        'sub_category_2' => $payload['sub_category_2'],
        'uom_code' => $payload['uom_code'],
        'unit_price' => $payload['unit_price'],
        'gl_account_code' => $payload['gl_account_code'],
        'description' => $payload['description'],
        'is_active' => $payload['is_active'],
    ]);
});

test('deleteProduct()', function () {
    $product = Product::factory()->create();
    $other_products = Product::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->productService->deleteProduct($product);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $product->id]);

    foreach ($other_products as $other_product) {
        $this->assertDatabaseHas($this->table, ['id' => $other_product->id]);
    }
});

test('deleteProduct() failed because being used in UnpaidItem', function () {
    $product = Product::factory()->create();

    UnpaidItem::factory()->create([
        'product_id' => $product->id,
    ]);

    expect(function () use ($product) {
        $this->productService->deleteProduct($product);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(35001);
    }, __('system_error.35001'));
});

test('deleteProduct() failed because being used in BillingDocumentLineItem', function () {
    $product = Product::factory()->create();

    BillingDocumentLineItem::factory()->create([
        'product_id' => $product->id,
    ]);

    expect(function () use ($product) {
        $this->productService->deleteProduct($product);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(35001);
    }, __('system_error.35001'));
});

