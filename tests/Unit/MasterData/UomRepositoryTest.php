<?php

use App\Models\Uom;
use App\Repositories\UomRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->uomRepository = app(UomRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Uom::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->uomRepository->getModelClass();

    expect($response)->toEqual(Uom::class);
});

test('getAll()', function () {
    $uoms = Uom::factory(3)->state(new Sequence(
        [
            'name->en' => 'Pack',
            'code' => 'PACK',
            'is_active' => true,
        ],
        [
            'name->en' => 'Piece',
            'code' => 'PIECE',
            'is_active' => true,
        ],
        [
            'name->en' => 'Dozen',
            'code' => 'DOZEN',
            'is_active' => false,
        ]
    ))->create();

    $response = $this->uomRepository->getAll()->toArray();

    expect($response)->toEqual($uoms->toArray());
});

test('getAllPaginated()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $uoms = [
        'first' => Uom::factory()->create([
            'name->en' => 'Pack',
            'code' => 'PACK',
            'is_active' => true,
        ]),
        'second' => Uom::factory()->create([
            'name->en' => 'Piece',
            'code' => 'PIECE',
            'is_active' => true,
        ]),
        'third' => Uom::factory()->create([
            'name->en' => 'Dozen',
            'code' => 'DOZEN',
            'is_active' => false,
        ]),
    ];

    $result = $this->uomRepository->getAllPaginated([$filter_by => $filter_value])->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($uoms[$value]->toArray());
    }
})->with([
    'filter by name = Pack' => [1, 'name', 'Pack', ['first']],
    'filter by name = Piece' => [1, 'name', 'Piece', ['second']],
    'filter by code = PIECE' => [1, 'code', 'PIECE', ['second']],
    'filter by code = DOZEN' => [1, 'code', 'DOZEN', ['third']],
    'filter by is_active = true' => [2, 'is_active', true, ['first', 'second']],
    'filter by is_active = false' => [1, 'is_active', false, ['third']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by name asc' => [3, 'order_by', ['name' => 'asc'], ['third', 'first', 'second']],
    'sort by name desc' => [3, 'order_by', ['name' => 'desc'], ['second', 'first', 'third']],
]);
