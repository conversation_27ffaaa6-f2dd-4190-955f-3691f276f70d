<?php

use App\Models\BookLanguage;
use App\Repositories\BookLanguageRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->bookLanguageRepository = app(abstract: BookLanguageRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(BookLanguage::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->bookLanguageRepository->getModelClass();

    expect($response)->toEqual(BookLanguage::class);
});

test('getAll()', function () {
    $languages = BookLanguage::factory(3)->state(new Sequence(
        [
            'name' => [
                'en' => 'Chinese',
                'zh' => '华文',
            ],
        ],
        [
            'name' => [
                'en' => 'Malay',
                'zh' => '马来文',
            ],
        ],
        [
            'name' => [
                'en' => 'English',
                'zh' => '英文',
            ],
        ],
    ))->create();

    $response = $this->bookLanguageRepository->getAll()->toArray();

    expect($response)->toEqual($languages->toArray());
});

test('getAllPaginated()', function () {
    $languages = BookLanguage::factory(3)->state(new Sequence(
        [
            'name' => [
                'en' => 'Chinese',
                'zh' => '华文',
            ],
        ],
        [
            'name' => [
                'en' => 'Malay',
                'zh' => '马来文',
            ],
        ],
        [
            'name' => [
                'en' => 'English',
                'zh' => '英文',
            ],
        ],
    ))->create();

    //Filter by name = Chinese
    $payload = [
        'name' => 'Chinese',
    ];
    $response = $this->bookLanguageRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.name.$this->testLocale", 'Chinese');

    //Filter by partial name = Chinese
    $payload = [
        'name' => 'chin'
    ];
    $response = $this->bookLanguageRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.name.$this->testLocale", 'Chinese');

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->bookLanguageRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->bookLanguageRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Chinese'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Malay'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->bookLanguageRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Malay'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Chinese'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->bookLanguageRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $languages[0]->id),
            fn($data) => $data->toHaveKey('id', $languages[1]->id),
            fn($data) => $data->toHaveKey('id', $languages[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->bookLanguageRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $languages[2]->id),
            fn($data) => $data->toHaveKey('id', $languages[1]->id),
            fn($data) => $data->toHaveKey('id', $languages[0]->id),
        );
});
