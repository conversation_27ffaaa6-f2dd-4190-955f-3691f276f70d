<?php

use App\Models\Country;
use App\Models\State;
use App\Repositories\StateRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->stateRepository = app(StateRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
});

test('getModelClass()', function () {
    $response = $this->stateRepository->getModelClass();

    expect($response)->toEqual(State::class);
});

test('getAll()', function () {
    $malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $states = State::factory(3)->state(new Sequence(
        [
            'name->en' => 'Kedah',
            'country_id' => $malaysia->id,
        ],
        [
            'name->en' => 'Kelantan',
            'country_id' => $malaysia->id,
        ]
    ))->create();

    $response = $this->stateRepository->getAll()->toArray();

    expect($response)->toEqual($states->toArray());
});

test('getAllPaginated()', function () {
    $india = Country::factory()->create([
        'name->en' => 'India'
    ]);

    $india_state = State::factory()->create([
        'name->en' => 'Mumbai',
        'country_id' => $india->id
    ]);

    $malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $malaysia_states = State::factory(3)->state(new Sequence(
        [
            'name->en' => 'New State 1',
            'country_id' => $malaysia->id
        ],
        [
            'name->en' => 'New State 2',
            'country_id' => $malaysia->id
        ],
        [
            'name->en' => 'Kedah',
            'country_id' => $malaysia->id
        ]
    ))->create();

    //Filter by partial name = New State
    $payload = [
        'name' => 'New State'
    ];
    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2')
        );

    //Filter by name = New State 2
    $payload = [
        'name' => 'New State 2'
    ];
    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2')
        );

    //Filter non-existing name = Non Exist
    $payload = [
        'name' => 'Non Exist'
    ];
    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter state by country_id = $india->id
    $payload = [
        'country_id' => $india->id
    ];
    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('country_id', $india->id)
        );

    $payload = [
        //sort by name asc
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Kedah'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Mumbai'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Mumbai'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Kedah'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $india_state->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[0]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[1]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->stateRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $malaysia_states[2]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[1]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[0]->id),
            fn($data) => $data->toHaveKey('id', $india_state->id),
        );
});
