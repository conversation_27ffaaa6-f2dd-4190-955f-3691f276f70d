<?php

use App\Models\Country;
use App\Models\State;
use App\Services\StateService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->stateService = app(StateService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(State::class)->getTable();
});

test('getAllPaginatedStates()', function () {
    $india = Country::factory()->create([
        'name->en' => 'India'
    ]);

    $india_state = State::factory()->create([
        'name->en' => 'Mumbai',
        'country_id' => $india->id
    ]);

    $malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $malaysia_states = State::factory(3)->state(new Sequence(
        [
            'name->en' => 'New State 1',
            'country_id' => $malaysia->id
        ],
        [
            'name->en' => 'New State 2',
            'country_id' => $malaysia->id
        ],
        [
            'name->en' => 'Kedah',
            'country_id' => $malaysia->id
        ]
    ))->create();

    //Filter by partial name = New State
    $payload = [
        'name' => 'New State'
    ];
    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2')
        );

    //Filter by name = New State 2
    $payload = [
        'name' => 'New State 2'
    ];
    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2')
        );

    //Filter non-existing name = Non Exist
    $payload = [
        'name' => 'Non Exist'
    ];
    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter state by country_id = $india->id
    $payload = [
        'country_id' => $india->id
    ];
    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('country_id', $india->id)
        );

    $payload = [
        //sort by name asc
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Kedah'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Mumbai'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Mumbai'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Kedah'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $india_state->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[0]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[1]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->stateService->getAllPaginatedStates($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $malaysia_states[2]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[1]->id),
            fn($data) => $data->toHaveKey('id', $malaysia_states[0]->id),
            fn($data) => $data->toHaveKey('id', $india_state->id),
        );
});


test('getAllStates()', function () {
    $india = Country::factory()->create([
        'name->en' => 'India'
    ]);

    $india_state = State::factory()->create([
        'name->en' => 'Mumbai',
        'country_id' => $india->id
    ]);

    $malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $malaysia_states = State::factory(3)->state(new Sequence(
        [
            'name->en' => 'New State 1',
            'country_id' => $malaysia->id
        ],
        [
            'name->en' => 'New State 2',
            'country_id' => $malaysia->id
        ],
        [
            'name->en' => 'Kedah',
            'country_id' => $malaysia->id
        ]
    ))->create();

    //Filter by partial name = New State
    $payload = [
        'name' => 'New State'
    ];
    $response = $this->stateService->getAllStates($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2')
        );

    //Filter by name = New State 2
    $payload = [
        'name' => 'New State 2'
    ];
    $response = $this->stateService->getAllStates($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2')
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->stateService->getAllStates($payload)->toArray();

    expect($response)->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'New State 1'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Mumbai'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Kedah'),
        );
});


test('createState()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $payload = [
        'name' => [
            'en' => fake()->city,
            'zh' => fake()->city,
        ],
        'country_id' => $malaysia->id
    ];

    $response = $this->stateService->createState($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'country_id' => $malaysia->id,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'country_id' => $malaysia->id,
    ]);
});

test('updateState()', function () {
    $malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $state = State::factory()->create([
        'name->en' => 'Test EN',
        'name->zh' => 'Test ZH',
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Test 2',
            'zh' => 'Test 3',
        ],
        'country_id' => $malaysia->id
    ];

    $response = $this->stateService->updateState($state->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $state->id,
        'name' => $payload['name'],
        'country_id' => $malaysia->id,
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'country_id' => $malaysia->id,
    ]);

    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 4',
            'zh' => 'Test 5',
        ],
        'country_id' => $malaysia->id
    ];

    $this->expectException(ModelNotFoundException::class);
    $this->stateService->updateState(9999, $payload)->toArray();
});

test('deleteState()', function () {
    $state = State::factory()->create();
    $other_states = State::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->stateService->deleteState($state->id);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $state->id]);

    foreach ($other_states as $other_state) {
        $this->assertDatabaseHas($this->table, ['id' => $other_state->id]);
    }

    //id not exist
    $this->expectException(ModelNotFoundException::class);
    $this->stateService->deleteState(9999);
});
