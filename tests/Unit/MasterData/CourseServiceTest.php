<?php

use App\Models\Course;
use App\Services\CourseService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->courseService = app(CourseService::class);

    app()->setLocale('en');

    $this->table = resolve(Course::class)->getTable();
    $this->testLocale = app()->getLocale();
});

test('getAllPaginatedCourses()', function () {
    $course_uec = Course::factory()->create([
        'name->en' => 'UEC',
        'name->zh' => '统考'
    ]);

    $course_igcse = Course::factory()->create([
        'name->en' => 'IGCSE',
        'name->zh' => '国际'
    ]);

    //Filter by name = UEC
    $payload = [
        'name' => 'UEC'
    ];

    $response = $this->courseService->getAllPaginatedCourses($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey("0.name.en", 'UEC')
        ->toHaveKey("0.name.zh", '统考');

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->courseService->getAllPaginatedCourses($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'UEC'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'IGCSE'),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->courseService->getAllPaginatedCourses($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("id", $course_igcse->id),
            fn($data) => $data->toHaveKey("id", $course_uec->id),
        );
});

test('getAll()', function () {
    $course_uec = Course::factory()->create([
        'name->en' => 'UEC',
        'name->zh' => '统考'
    ]);

    $course_igcse = Course::factory()->create([
        'name->en' => 'IGCSE',
        'name->zh' => '国际'
    ]);

    //Filter by name = UEC
    $payload = [
        'name' => 'UEC'
    ];

    $response = $this->courseService->getAllCourses($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->toHaveKey("0.name.en", 'UEC')
        ->toHaveKey("0.name.zh", '统考');

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->courseService->getAllCourses($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'UEC'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'IGCSE'),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->courseService->getAllCourses($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("id", $course_igcse->id),
            fn($data) => $data->toHaveKey("id", $course_uec->id),
        );
});

test('createCourse()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => 'Computer Science',
            'zh' => '电脑'
        ],
        'is_active' => true,
    ];

    $response = $this->courseService->createCourse($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'is_active' => $payload['is_active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'is_active' => $payload['is_active'],
    ]);
});

test('updateCourse()', function () {
    $course = Course::factory()->create();

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Computer Science',
            'zh' => '电脑'
        ],
        'is_active' => false,
    ];

    $response = $this->courseService->updateCourse($course->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $course->id,
        'name' => $payload['name'],
        'is_active' => $payload['is_active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $course->id,
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'is_active' => $payload['is_active'],
    ]);

    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 3',
        ],
    ];

    $this->expectException(ModelNotFoundException::class);
    $this->courseService->updateCourse(9999, $payload)->toArray();
});
