<?php

use App\Enums\CardStatus;
use App\Enums\ClassType;
use App\Enums\DietaryRestriction;
use App\Enums\GuardianType;
use App\Enums\LibraryMemberType;
use App\Enums\MarriedStatus;
use App\Enums\SchoolLevel;
use App\Enums\StudentAdmissionType;
use App\Enums\StudentLeaveStatus;
use App\Models\Card;
use App\Models\Config;
use App\Models\Country;
use App\Models\Course;
use App\Models\Currency;
use App\Models\Education;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\HealthConcern;
use App\Models\HostelBedAssignment;
use App\Models\LeaveReason;
use App\Models\LibraryMember;
use App\Models\Media;
use App\Models\PendingStudentEmployeeStatusChange;
use App\Models\Race;
use App\Models\Religion;
use App\Models\Role;
use App\Models\School;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\State;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentHistory;
use App\Models\User;
use App\Models\Wallet;
use App\Services\StudentService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\SystemRoleSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        SystemRoleSeeder::class,
    ]);
    $this->masterStudentService = app(StudentService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Student::class)->getTable();
    $this->libraryMemberTable = resolve(LibraryMember::class)->getTable();
    $this->userTable = resolve(User::class)->getTable();
    $this->guardianTable = resolve(Guardian::class)->getTable();
    $this->guardianStudentTable = resolve(GuardianStudent::class)->getTable();
    $this->mediaTable = resolve(Media::class)->getTable();

    $this->malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $this->first_state = State::factory()->create([
        'name->en' => 'Kedah',
        'country_id' => $this->malaysia->id,
    ]);

    $this->second_state = State::factory()->create([
        'name->en' => 'Kelantan',
        'country_id' => $this->malaysia->id,
    ]);

    $this->first_grade = Grade::factory()->create([
        'name->en' => 'Student 1',
        'name->zh' => '初一',
        'sequence' => 1,
    ]);

    $this->second_grade = Grade::factory()->create([
        'name->en' => 'Student 2',
        'name->zh' => '初二',
        'sequence' => 2,
    ]);

    $this->first_religion = Religion::factory()->create([
        'name' => 'Chinese'
    ]);

    $this->second_religion = Religion::factory()->create([
        'name' => 'Chinese (China)'
    ]);

    $this->first_race = Race::factory()->create([
        'name' => 'malay'
    ]);

    $this->second_race = Race::factory()->create([
        'name' => 'chinese'
    ]);

    $this->currency = Currency::factory()->malaysiaCurrency()->create();

    $config = [
        Config::LIBRARY_BORROW_LIMIT_EMPLOYEE => 3,
        Config::LIBRARY_BORROW_LIMIT_STUDENT => 2,
        Config::LIBRARY_BORROW_LIMIT_LIBRARIAN => 5,
        Config::LIBRARY_BORROW_LIMIT_OTHER => 4,
    ];

    foreach ($config as $key => $value) {
        Config::factory()->create([
            'key' => $key,
            'value' => $value
        ]);
    }
});

test('getAllStudents()', function () {
    $first_student = Student::factory()
        ->create([
            'name->en' => 'Student 1',
            'name->zh' => '学生 1',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'admission_year' => 2024,
            'admission_grade_id' => $this->first_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '001',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2001-01-01',
            'gender' => 'MALE',
            'birth_cert_number' => '001',
            'nric' => '001',
            'passport_number' => '001',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Petaling Jaya',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 1',
            'custom_field' => ['key' => 'custom_key', 'value' => 'custom_value'],
        ]);

    $second_student = Student::factory()
        ->create([
            'name->en' => 'Student 2',
            'name->zh' => '学生 2',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'admission_year' => 2024,
            'admission_grade_id' => $this->first_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '002',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2002-02-02',
            'gender' => 'MALE',
            'birth_cert_number' => '002',
            'nric' => '002',
            'passport_number' => '002',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Damansara',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 2',
            'custom_field' => ['key' => 'custom_key', 'value' => 'custom_value'],
        ]);

    $third_student = Student::factory()
        ->create([
            'name->en' => 'People 3',
            'name->zh' => '学生 3',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'admission_year' => 2024,
            'admission_grade_id' => $this->second_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '003',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2013-11-01',
            'gender' => 'FEMALE',
            'birth_cert_number' => '003',
            'nric' => '003',
            'passport_number' => '003',
            'race_id' => $this->second_race->id,
            'religion_id' => $this->second_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Damansara',
            'state_id' => $this->second_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 3',
            'custom_field' => ['key' => 'custom_key', 'value' => 'custom_value'],
        ]);

    $response = $this->masterStudentService->getAllStudents(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->toEqual([
        $first_student->toArray(),
        $second_student->toArray(),
        $third_student->toArray()
    ]);
});

test('getAllPaginatedStudents()', function () {
    $first_student = Student::factory()
        ->create([
            'name->en' => 'Student 1',
            'name->zh' => '学生 1',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'admission_year' => 2024,
            'admission_grade_id' => $this->first_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '001',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2001-01-01',
            'gender' => 'MALE',
            'birth_cert_number' => '001',
            'nric' => '001',
            'passport_number' => '001',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Petaling Jaya',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 1',
            'custom_field' => ['key' => 'custom_key', 'value' => 'custom_value'],
        ]);

    $second_student = Student::factory()
        ->create([
            'name->en' => 'Student 2',
            'name->zh' => '学生 2',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'admission_year' => 2024,
            'admission_grade_id' => $this->first_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '002',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2002-02-02',
            'gender' => 'MALE',
            'birth_cert_number' => '002',
            'nric' => '002',
            'passport_number' => '002',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Damansara',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 2',
            'custom_field' => ['key' => 'custom_key', 'value' => 'custom_value'],
        ]);

    $third_student = Student::factory()
        ->create([
            'name->en' => 'People 3',
            'name->zh' => '学生 3',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'admission_year' => 2024,
            'admission_grade_id' => $this->second_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '003',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2013-11-01',
            'gender' => 'FEMALE',
            'birth_cert_number' => '003',
            'nric' => '003',
            'passport_number' => '003',
            'race_id' => $this->second_race->id,
            'religion_id' => $this->second_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Damansara',
            'state_id' => $this->second_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 3',
            'custom_field' => ['key' => 'custom_key', 'value' => 'custom_value'],
        ]);
    // Filter by name = Student 1
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'name' => ['en' => 'Student 1']
    ])->toArray();

    expect($response['data'])->toEqual([$first_student->toArray()]);

    // Filter by name = Student 2
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'name' => ['en' => 'Student 2']
    ])->toArray();

    expect($response['data'])->toEqual([$second_student->toArray()]);

    // Filter by non-existing name = Student 3
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'name' => ['en' => 'Student 3']
    ])->toArray();

    expect($response['data'])->toBeEmpty();

    // Sort by name asc
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($third_student->toArray()),
        fn($item) => $item->toEqual($first_student->toArray()),
        fn($item) => $item->toEqual($second_student->toArray()),
    );

    // Sort by name desc
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($second_student->toArray()),
        fn($item) => $item->toEqual($first_student->toArray()),
        fn($item) => $item->toEqual($third_student->toArray()),
    );

    // Sort by id asc
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($first_student->toArray()),
        fn($item) => $item->toEqual($second_student->toArray()),
        fn($item) => $item->toEqual($third_student->toArray()),
    );

    // Sort by id desc
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($third_student->toArray()),
        fn($item) => $item->toEqual($second_student->toArray()),
        fn($item) => $item->toEqual($first_student->toArray()),
    );
});

test('getAllPaginatedStudent() - filter by guardian name', function () {
    $student_A1 = Student::factory()->create([
        'name->en' => 'Student A1',
        'email' => '<EMAIL>',
        'phone_number' => '+60128889999',
        'student_number' => 'STUDENT-A001',
    ]);

    $student_A2 = Student::factory()->create([
        'name->en' => 'Student A2',
        'email' => '<EMAIL>',
        'phone_number' => '+60123334444',
        'student_number' => 'STUDENT-A002',
    ]);

    $guardian_A = Guardian::factory()->create([
        'name->en' => 'Father A',
        'email' => '<EMAIL>',
        'phone_number' => '+60175558888',
    ]);

    $student_B = Student::factory()->create([
        'name->en' => 'Student B2',
        'email' => '<EMAIL>',
        'phone_number' => '+60134569875',
        'student_number' => 'STUDENT-B001',
    ]);


    $guardian_B = Guardian::factory()->create([
        'name->en' => 'Father B',
        'email' => '<EMAIL>',
        'phone_number' => '+60185550000',
    ]);

    GuardianStudent::factory(4)->state(new Sequence(
        [
            'guardian_id' => $guardian_A->id,
            'studenable_id' => $student_A1->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_A->id,
            'studenable_id' => $student_A2->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_B->id,
            'studenable_id' => $student_B->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_B->id,
            'studenable_id' => $student_A2->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => false
        ]

    ))->create();

    // TESTING GUARDIAN NAME FILTER
    // Return student A1 and student A2
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_name' => $guardian_A->name,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray());

    // Return student B
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_name' => $guardian_B->name,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A2->toArray())
        ->and($response['data'][1])->toMatchArray($student_B->toArray());

    // Return student A1, student A2, student B (testing wildcard filter)
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_name' => 'Father',
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray())
        ->and($response['data'][2])->toMatchArray($student_B->toArray());

    // TESTING GUARDIAN_PHONE_NUMBER FILTER
    // Return student A1 and student A2
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_phone_number' => $guardian_A->phone_number,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray());

    // Return student B
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_phone_number' => $guardian_B->phone_number,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A2->toArray())
        ->and($response['data'][1])->toMatchArray($student_B->toArray());

    // Return student A1, student A2, student B (testing wildcard filter)
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_phone_number' => +60,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray())
        ->and($response['data'][2])->toMatchArray($student_B->toArray());

    // TESTING GUARDIAN_EMAIL FILTER
    // Return student A1 and student A2
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_email' => $guardian_A->email,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray());

    // Return student B
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_email' => $guardian_B->email,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A2->toArray())
        ->and($response['data'][1])->toMatchArray($student_B->toArray());

    // Return student A1, student A2, student B (testing wildcard filter)
    $response = $this->masterStudentService->getAllPaginatedStudents([
        'guardian_email' => 'father',
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray())
        ->and($response['data'][2])->toMatchArray($student_B->toArray());
});

test('createStudent()', function () {
    config([
        'school.email_domain' => '@smpinhwa.edu.my'
    ]);
    $guardian = Guardian::factory()->create();
    $education = Education::factory()->create();

    $this->assertDatabaseCount($this->table, 0);
    $this->assertDatabaseCount($this->userTable, 1);
    $this->assertDatabaseCount($this->libraryMemberTable, 0);
    $this->assertDatabaseCount('wallets', 0);

    $payload = [
        'admission_year' => '2024',
        'admission_grade_id' => $this->first_grade->id,
        'join_date' => fake()->date(),
        // Auto generated by admission year
//        'student_number' => fake()->unique()->numberBetween(1, 1000),
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'phone_number' => '*********',
        'phone_number_2' => '*********0',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'date_of_birth' => fake()->date(),
        'gender' => fake()->randomElement(['MALE', 'FEMALE']),
        'birth_cert_number' => fake()->unique()->numberBetween(1000, 9999),
        'nric' => fake()->unique()->numberBetween(1000000000, 9999999999),
        'passport_number' => fake()->unique()->numberBetween(1000000, 9999999),
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => fake()->address,
        'postal_code' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'address_2' => fake()->address,
        'is_hostel' => true,
        'remarks' => fake()->sentence,
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => [
            'key' => fake()->word,
            'value' => fake()->sentence
        ],
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '*********0',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '1234'
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Mother 1',
                ],
                'phone_number' => '**********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null
            ],
            [
                'id' => $guardian->id,
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Guardian 1',
                ],
                'phone_number' => '**********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => '33333',
                'education_id' => $education->id,
                'married_status' => MarriedStatus::WIDOWED->value,
                'occupation' => 'IT',
                'occupation_description' => 'Fix Computer'
            ]
        ],
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => HealthConcern::factory()->create()->id,
        'primary_school_id' => School::factory()->create([
            'level' => SchoolLevel::PRIMARY,
        ])->id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ];

    $response = $this->masterStudentService->createStudent($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'phone_number' => $payload['phone_number'],
        'phone_number_2' => $payload['phone_number_2'],
        'student_number' => "H2024001",
        'email' => '<EMAIL>',
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'admission_year' => $payload['admission_year'],
        'admission_grade_id' => $payload['admission_grade_id'],
        'join_date' => $payload['join_date'],
        'birthplace' => $payload['birthplace'],
        'nationality_id' => $payload['nationality_id'],
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'birth_cert_number' => $payload['birth_cert_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postal_code' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'address_2' => $payload['address_2'],
        'remarks' => $payload['remarks'],
        'is_hostel' => $payload['is_hostel'],
        'dietary_restriction' => $payload['dietary_restriction'],
        'health_concern_id' => $payload['health_concern_id'],
        'primary_school_id' => $payload['primary_school_id'],
    ])
        ->and(Student::first()->photo)->not()->toBeNull();

    //These will be saved into users table
    $this->assertDatabaseCount($this->table, 1);

    expect(Student::first()->photo)->not()->toBeNull();

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'phone_number' => $payload['phone_number'],
        'phone_number_2' => $payload['phone_number_2'],
        'email' => '<EMAIL>',
        'admission_year' => $payload['admission_year'],
        'admission_grade_id' => $payload['admission_grade_id'],
        'join_date' => $payload['join_date'],
        'student_number' => "H2024001",
        'birthplace' => $payload['birthplace'],
        'nationality_id' => $payload['nationality_id'],
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'birth_cert_number' => $payload['birth_cert_number'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postal_code' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'address_2' => $payload['address_2'],
        'is_hostel' => $payload['is_hostel'],
        'remarks' => $payload['remarks'],
        'custom_field->key' => $payload['custom_field']['key'],
        'custom_field->value' => $payload['custom_field']['value'],
        'dietary_restriction' => $payload['dietary_restriction'],
        'health_concern_id' => $payload['health_concern_id'],
        'primary_school_id' => $payload['primary_school_id'],
    ]);

    $this->assertDatabaseCount($this->userTable, 3);
    $this->assertDatabaseHas($this->userTable, [
        'email' => '<EMAIL>',
        'phone_number' => $payload['phone_number'],
        'is_active' => true,
        'is_password_reset_required' => true,
    ]);

    //guardian user
    $this->assertDatabaseHas($this->userTable, [
        'phone_number' => $payload['guardians'][0]['phone_number'],
        'email' => $payload['guardians'][0]['email'],
        'is_password_reset_required' => true,
    ]);

    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => Student::class,
        'model_id' => Student::first()->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    foreach ($payload['guardians'] as $guardian_data) {
        $data = [
            'name->en' => $guardian_data['name']['en'],
            'phone_number' => $guardian_data['phone_number'],
            'email' => $guardian_data['email'],
            'nric' => $guardian_data['nric'],
            'education_id' => Arr::get($guardian_data, 'education_id'),
            'married_status' => Arr::get($guardian_data, 'married_status'),
            'occupation' => Arr::get($guardian_data, 'occupation'),
            'occupation_description' => Arr::get($guardian_data, 'occupation_description'),
        ];

        if (isset($guardian_data['id'])) {
            $data['id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianTable, $data);

        $data = [
            'type' => $guardian_data['type'],
            'studenable_type' => Student::class,
            'studenable_id' => $response['id'],
        ];

        if (isset($guardian_data['id'])) {
            $data['guardian_id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianStudentTable, $data);
    }

    $this->assertDatabaseCount($this->libraryMemberTable, 1);
    $this->assertDatabaseHas($this->libraryMemberTable, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'phone_number' => $payload['phone_number'],
        'email' => '<EMAIL>',
        'member_number' => "H2024001",
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'borrow_limit' => 2,
        'is_librarian' => false,
        'type' => LibraryMemberType::STUDENT->value,
        'userable_type' => Student::class,
        'userable_id' => $response['id'],
        'is_active' => true
    ]);

    $created_user = Student::first()->user;

    $this->assertDatabaseCount('wallets', 2);
    $this->assertDatabaseHas('wallets', [
        'user_id' => $created_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 0,
    ]);

    $created_guardian_user = User::query()->where('email', '<EMAIL>')->first();
    $this->assertDatabaseHas('wallets', [
        'user_id' => $created_guardian_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 0,
    ]);

    $created_user = Student::first()->user;
    $library_member = LibraryMember::where('userable_type', Student::class)->where('userable_id', $response['id'])->first();

    $this->assertDatabaseCount('wallets', 2);
    $this->assertDatabaseHas('wallets', [
        'user_id' => $created_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 0,
    ]);

    $this->assertDatabaseHas('media', [
        'model_type' => Student::class,
        'model_id' => Student::first()->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    $this->assertDatabaseHas('media', [
        'model_type' => LibraryMember::class,
        'model_id' => $library_member->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);
});

test('updateStudent() guardian will clear old linking', function () {
    $student_user = User::factory()->create();

    $first_student = Student::factory()
        ->create([
            'user_id' => $student_user->id,
            'name->en' => 'Student 1',
            'name->zh' => '学生 1',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'admission_year' => '2024',
            'admission_grade_id' => $this->first_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '001',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2001-01-01',
            'gender' => 'MALE',
            'birth_cert_number' => '001',
            'nric' => '001',
            'passport_number' => '001',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Petaling Jaya',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'is_hostel' => true,
            'remarks' => 'This is a student remark for student 1',
            'custom_field' => [
                'key' => 'custom_key',
                'value' => 'custom_value'
            ],
        ]);

    $guardian_user = User::factory()->create();
    $guardian = Guardian::factory()->create([
        'user_id' => $guardian_user->id,
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'studenable_id' => $first_student->id,
        'studenable_type' => Student::class,
        'type' => GuardianType::GUARDIAN->value,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);

    //guardian will clear old linking
    $payload = [
        'admission_year' => 2025,
        'admission_grade_id' => $this->first_grade->id,
        'join_date' => '2024-01-02',
        'name' => [
            'en' => 'Updated Student 1',
            'zh' => '更新的学生 1',
        ],
        'phone_number' => '*********',
        'phone_number_2' => '*********0',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'gender' => 'FEMALE',
        'birth_cert_number' => '0010',
        'nric' => '0010',
        'passport_number' => '0010',
        'race_id' => $this->second_race->id,
        'religion_id' => $this->second_religion->id,
        'date_of_birth' => '2022-02-02',
        'address' => '12345 Main Street',
        'postal_code' => '1234567',
        'city' => 'Damansara',
        'state_id' => $this->second_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => false,
        'remarks' => 'This is a student remark for student 1 changed',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '*********0',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => '1234'
            ]
        ]
    ];

    $this->masterStudentService->updateStudent($first_student, $payload);

    foreach ($payload['guardians'] as $guardian_data) {
        $this->assertDatabaseHas($this->guardianStudentTable, [
            'type' => $guardian_data['type'],
            'studenable_id' => $first_student->id,
            'studenable_type' => Student::class,
        ]);
    }

    $this->assertDatabaseMissing($this->guardianStudentTable, [
        'type' => GuardianType::GUARDIAN->value,
        'guardian_id' => $guardian->id,
        'studenable_id' => $first_student->id,
        'studenable_type' => Student::class,
    ]);
});

test('updateStudent()', function () {
    $student_user = User::factory()->create();
    $other_health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    $first_student = Student::factory()
        ->create([
            'user_id' => $student_user->id,
            'name->en' => 'Student 1',
            'name->zh' => '学生 1',
            'phone_number' => '*********',
            'phone_number_2' => '*********0',
            'email' => '<EMAIL>',
            'admission_year' => '2024',
            'admission_grade_id' => $this->first_grade->id,
            'join_date' => '2024-01-01',
            'student_number' => '001',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2001-01-01',
            'gender' => 'MALE',
            'birth_cert_number' => '001',
            'nric' => '001',
            'passport_number' => '001',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Petaling Jaya',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'is_hostel' => true,
            'remarks' => 'This is a student remark for student 1',
            'custom_field' => [
                'key' => 'custom_key',
                'value' => 'custom_value'
            ],
        ]);

    $education = Education::factory()->create();
    $guardian_user = User::factory()->create();
    $guardian = Guardian::factory()->create([
        'user_id' => $guardian_user->id,
        'education_id' => null,
        'married_status' => MarriedStatus::WIDOWED->value,
        'occupation' => 'IT',
        'occupation_description' => 'Fix Computer'
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'studenable_id' => $first_student->id,
        'studenable_type' => Student::class,
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);

    //update success
    $payload = [
        'admission_year' => 2025,
        'admission_grade_id' => $this->first_grade->id,
        'join_date' => '2024-01-02',
        'name' => [
            'en' => 'Updated Student 1',
            'zh' => '更新的学生 1',
        ],
        'phone_number' => '*********',
        'phone_number_2' => '*********0',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'gender' => 'FEMALE',
        'birth_cert_number' => '0010',
        'nric' => '0010',
        'passport_number' => '0010',
        'race_id' => $this->second_race->id,
        'religion_id' => $this->second_religion->id,
        'date_of_birth' => '2022-02-02',
        'address' => '12345 Main Street',
        'address_2' => '12345 Main Street 2',
        'postal_code' => '1234567',
        'city' => 'Damansara',
        'state_id' => $this->second_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => false,
        'remarks' => 'This is a student remark for student 1 changed',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'leave_date' => '2024-01-01',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '*********0',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '1234'
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Mother 1',
                ],
                'phone_number' => '**********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null
            ],
            [
                'id' => $guardian->id,
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Guardian 1',
                ],
                'phone_number' => '**********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'education_id' => $education->id,
                'married_status' => MarriedStatus::SINGLE->value,
                'occupation' => 'Test',
                'occupation_description' => 'Test'
            ]
        ],
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $other_health_concern->id,
        'primary_school_id' => $primary_school->id,
    ];

    $response = $this->masterStudentService->updateStudent($first_student, $payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'phone_number' => $payload['phone_number'],
        'phone_number_2' => $payload['phone_number_2'],
        'email' => $first_student->email,
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'admission_year' => $payload['admission_year'],
        'admission_grade_id' => $payload['admission_grade_id'],
        'join_date' => $payload['join_date'],
        'birthplace' => $payload['birthplace'],
        'nationality_id' => $payload['nationality_id'],
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'birth_cert_number' => $payload['birth_cert_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postal_code' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'remarks' => $payload['remarks'],
        'is_hostel' => $payload['is_hostel'],
        'leave_date' => $payload['leave_date'],
        'address_2' => $payload['address_2'],
        'dietary_restriction' => $payload['dietary_restriction'],
        'health_concern_id' => $payload['health_concern_id'],
        'primary_school_id' => $payload['primary_school_id'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    expect(Student::first()->photo)->not()->toBeNull();

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'phone_number' => $payload['phone_number'],
        'phone_number_2' => $payload['phone_number_2'],
        'email' => $first_student->email,
        'admission_year' => $payload['admission_year'],
        'admission_grade_id' => $payload['admission_grade_id'],
        'join_date' => $payload['join_date'],
        'birthplace' => $payload['birthplace'],
        'nationality_id' => $payload['nationality_id'],
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'birth_cert_number' => $payload['birth_cert_number'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postal_code' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'is_hostel' => $payload['is_hostel'],
        'leave_date' => $payload['leave_date'],
        'address_2' => $payload['address_2'],
        'remarks' => $payload['remarks'],
        'custom_field->key' => $payload['custom_field']['key'],
        'custom_field->value' => $payload['custom_field']['value'],
        'dietary_restriction' => $payload['dietary_restriction'],
        'health_concern_id' => $payload['health_concern_id'],
        'primary_school_id' => $payload['primary_school_id'],
    ]);

    $this->assertDatabaseHas($this->userTable, [
        'email' => $first_student->user->email,
        'phone_number' => $first_student->user->phone_number,
    ]);

    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => Student::class,
        'model_id' => Student::first()->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    //guardian user
    $this->assertDatabaseHas($this->userTable, [
        'phone_number' => $payload['guardians'][0]['phone_number'],
        'email' => $payload['guardians'][0]['email'],
    ]);

    foreach ($payload['guardians'] as $guardian_data) {
        $data = [
            'name->en' => $guardian_data['name']['en'],
            'phone_number' => $guardian_data['phone_number'],
            'email' => $guardian_data['email'],
            'nric' => $guardian_data['nric'],
            'education_id' => Arr::get($guardian_data, 'education_id'),
            'married_status' => Arr::get($guardian_data, 'married_status'),
            'occupation' => Arr::get($guardian_data, 'occupation'),
            'occupation_description' => Arr::get($guardian_data, 'occupation_description'),
        ];

        if (isset($guardian_data['id'])) {
            $data['id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianTable, $data);

        $data = [
            'type' => $guardian_data['type'],
            'studenable_type' => Student::class,
            'studenable_id' => $response['id'],
        ];

        if (isset($guardian_data['id'])) {
            $data['guardian_id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianStudentTable, $data);
    }
});

test('deleteStudent() Guardian cannot be deleted once is linked.', function () {
    $linked_student = Student::factory()->create();
    GuardianStudent::factory()->create([
        'studenable_id' => $linked_student->id,
        'studenable_type' => Student::class,
    ]);

    // Delete failed - Guardian cannot be deleted once is linked.
    $this->expectExceptionMessage("Guardian cannot be deleted once is linked.");
    $this->masterStudentService->deleteStudent($linked_student);
});

test('deleteStudent()', function () {
    $first_student = Student::factory()->create();
    $other_students = Student::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    // Delete success
    $this->masterStudentService->deleteStudent($first_student);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $first_student->id]);

    foreach ($other_students as $other_student) {
        $this->assertDatabaseHas($this->table, ['id' => $other_student->id]);
    }
});

test('regenerateStudentNumber()', function () {
    $student_hostel = Student::factory()->create([
        'admission_year' => 2024,
        'is_hostel' => true,
    ]);

    $student_non_hostel = Student::factory()->create([
        'admission_year' => 2024,
        'is_hostel' => false,
    ]);

    $this->assertDatabaseCount('running_numbers', 0);

    $student_number = resolve(StudentService::class)->regenerateStudentNumber($student_hostel);

    // Hostel student: H + admission year + 4 digit running number
    expect($student_number)->toEqual('H2024001');

    // Non-hostel student: admission year + 4 digit running number
    $student_number = resolve(StudentService::class)->regenerateStudentNumber($student_non_hostel);

    expect($student_number)->toEqual('2024002');

    // Try regenerate again
    $student_number = resolve(StudentService::class)->regenerateStudentNumber($student_hostel);

    expect($student_number)->toEqual('H2024003');
});

test('getFirstByStudentNumberOrCardNumber()', function () {
    $studentAA = Student::factory()->create([
        'student_number' => 'AA-11'
    ]);

    Card::factory()->create([
        'card_number' => 'AA1',
        'card_number2' => 'AA2',
        'card_number3' => 'AA3',
        'userable_type' => Student::class,
        'userable_id' => $studentAA->id,
        'status' => CardStatus::ACTIVE->value,
    ]);

    $studentCC = Student::factory()->create([
        'student_number' => 'CC-33'
    ]);

    Card::factory()->create([ // inactive
        'card_number' => 'CC1',
        'card_number2' => 'CC2',
        'card_number3' => 'CC3',
        'userable_type' => Student::class,
        'userable_id' => $studentCC->id,
        'status' => CardStatus::INACTIVE->value,
    ]);

    // ================================================ filter by student_number
    $response = $this->masterStudentService->getFirstByStudentNumberOrCardNumber('AA-11');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentAA->id);

    // ================================================ filter by card_number
    $response = $this->masterStudentService->getFirstByStudentNumberOrCardNumber('AA1');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentAA->id);

    // ======================================================================================

    // ================================================ filter by student_number - even tho card inactive, using student number is still legit
    $response = $this->masterStudentService->getFirstByStudentNumberOrCardNumber('CC-33');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentCC->id);

    // ================================================ filter by card_number - card inactive
    $has_error = false;

    try {
        $response = $this->masterStudentService->getFirstByStudentNumberOrCardNumber('CC1');

    } catch (\Throwable $th) {
        expect($th)->toBeInstanceOf(ModelNotFoundException::class);
        $has_error = true;
    }

    expect($has_error)->toBeTrue();
});

test('checkStudentCanBeDeleted()', function () {
    $student = Student::factory()->create();
    $guardian = Guardian::factory()->create();

    GuardianStudent::factory()->create([
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'guardian_id' => $guardian->id,
    ]);

    expect(function () use ($student) {
        $this->masterStudentService->setStudent($student)->checkStudentCanBeDeleted();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(7004);
    }, __('system_error.7004'));

    /**
     * cannot be deleted because currently assigned to a bed
     */
    $student2 = Student::factory()->create();

    HostelBedAssignment::factory()->create([
        'assignable_id' => $student2->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    expect(function () use ($student2) {
        $this->masterStudentService->setStudent($student2)->checkStudentCanBeDeleted();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(29001);
    }, __('system_error.29001'));
});

test('getCurrentSemesterStudentPrimaryClassId()', function () {
    $student = Student::factory()->create();

    $course = Course::factory()->create();

    $semester_classes_2024_sem_1 = SemesterClass::factory()
        ->forSemesterSetting([
            'course_id' => $course->id,
            'from' => '2024-01-01',
            'to' => '2024-06-30',
        ])
        ->create();

    $semester_classes_2024_sem_2 = SemesterClass::factory()
        ->forSemesterSetting([
            'course_id' => $course->id,
            'from' => '2024-07-01',
            'to' => '2024-08-31',
            'is_current_semester' => true
        ])
        ->create();

    $semester_classes_2025_sem_1 = SemesterClass::factory()
        ->forSemesterSetting([
            'course_id' => $course->id,
            'from' => '2025-01-01',
            'to' => '2025-06-30',
        ])
        ->create();

    Carbon::setTestNow('2024-08-01'); // Set current time to sem 2

    $student_classes = StudentClass::factory(3)->state(new Sequence(
        [
            'student_id' => $student->id,
            'semester_setting_id' => $semester_classes_2024_sem_1->semester_setting_id,
            'semester_class_id' => $semester_classes_2024_sem_1->id,
            'is_active' => true,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'student_id' => $student->id,
            'semester_setting_id' => $semester_classes_2024_sem_2->semester_setting_id,
            'semester_class_id' => $semester_classes_2024_sem_2->id,
            'is_active' => true,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'student_id' => $student->id,
            'semester_setting_id' => $semester_classes_2025_sem_1->semester_setting_id,
            'semester_class_id' => $semester_classes_2025_sem_1->id,
            'is_active' => true,
            'class_type' => ClassType::PRIMARY
        ],
    ))->create();

    $response = $this->masterStudentService->getCurrentSemesterStudentPrimaryClassId($student->id);

    expect($response)->toBe($student_classes[1]->id);
});

test('saveGuardians() with user account true and exist phone number', function () {
    $student = Student::factory()->create();

    $user = User::factory()->create([
        'phone_number' => '+***********'
    ]);

    $payload = [
        [
            'type' => GuardianType::FATHER->value,
            'name' => [
                'en' => 'Father 1',
            ],
            'phone_number' => "+***********",
            'email' => '<EMAIL>',
            'with_user_account' => true,
            'nric' => '1234'
        ]
    ];

    $this->assertDatabaseCount($this->userTable, 2);
    $this->assertDatabaseCount($this->guardianTable, 0);

    $this->masterStudentService
        ->setStudent($student)
        ->saveGuardians($payload);

    $this->assertDatabaseCount($this->userTable, 2);
    $this->assertDatabaseCount($this->guardianTable, 1);

    $this->assertDatabaseHas($this->guardianTable, [
        'user_id' => $user->id,
        'phone_number' => $payload[0]['phone_number'],
        'email' => $payload[0]['email'],
        'nric' => $payload[0]['nric'],
        'name->en' => $payload[0]['name']['en'],
    ]);

    $guardian = Guardian::query()->where('phone_number', $payload[0]['phone_number'])->first();

    $this->assertDatabaseHas($this->guardianStudentTable, [
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'type' => GuardianType::FATHER->value
    ]);
});

test('saveGuardians() with user account true and new phone number', function () {
    $student = Student::factory()->create();

    $payload = [
        [
            'type' => GuardianType::FATHER->value,
            'name' => [
                'en' => 'Father 1',
            ],
            'phone_number' => "+***********",
            'email' => '<EMAIL>',
            'with_user_account' => true,
            'nric' => '1234'
        ]
    ];

    $this->assertDatabaseCount($this->userTable, 1);
    $this->assertDatabaseCount($this->guardianTable, 0);

    $this->masterStudentService
        ->setStudent($student)
        ->saveGuardians($payload);

    $this->assertDatabaseCount($this->userTable, 2);
    $this->assertDatabaseCount($this->guardianTable, 1);

    $user = User::query()->where('phone_number', $payload[0]['phone_number'])->first();
    $this->assertDatabaseHas($this->guardianTable, [
        'user_id' => $user->id,
        'phone_number' => $payload[0]['phone_number'],
        'email' => $payload[0]['email'],
        'nric' => $payload[0]['nric'],
        'name->en' => $payload[0]['name']['en'],
    ]);

    $guardian = Guardian::query()->where('phone_number', $payload[0]['phone_number'])->first();

    $this->assertDatabaseHas($this->userTable, [
        'phone_number' => $payload[0]['phone_number'],
        'email' => $payload[0]['email'],
    ]);

    $this->assertDatabaseHas($this->guardianStudentTable, [
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'type' => GuardianType::FATHER->value
    ]);


    // Scenario guardian change phone number
    $guardian = Guardian::first();
    $payload = [
        [
            'id' => $guardian->id,
            'type' => GuardianType::FATHER->value,
            'name' => [
                'en' => 'Father 1',
            ],
            'phone_number' => "+***********",
            'email' => '<EMAIL>',
            'with_user_account' => true,
            'nric' => '1234',
        ]
    ];

    $this->masterStudentService
        ->setStudent($student)
        ->saveGuardians($payload);

    $this->assertDatabaseCount($this->userTable, 2); // No new user is created
    $this->assertDatabaseCount($this->guardianTable, 1); // No new guardian is created

    $guardian_user = User::find($guardian->user_id);
    $this->assertDatabaseHas('guardians', [
        'id' => $guardian->id,
        'phone_number' => $payload[0]['phone_number'],
    ]);

    $this->assertDatabaseHas('users', [
        'id' => $guardian_user->id,
        'phone_number' => '+***********' // Phone number is not updated
    ]);
});

test('saveGuardians() with guardian id exist and with user account true', function () {
    $student = Student::factory()->create();

    $guardian = Guardian::factory()->create([
        'user_id' => null,
    ]);
    $payload = [
        [
            'id' => $guardian->id,
            'type' => GuardianType::FATHER->value,
            'name' => [
                'en' => 'Father 1',
            ],
            'phone_number' => "+***********",
            'email' => '<EMAIL>',
            'with_user_account' => true,
            'nric' => '1234'
        ]
    ];

    $this->assertDatabaseCount($this->userTable, 1);
    $this->assertDatabaseCount($this->guardianTable, 1);

    $this->masterStudentService
        ->setStudent($student)
        ->saveGuardians($payload);

    $this->assertDatabaseCount($this->userTable, 2);
    $this->assertDatabaseCount($this->guardianTable, 1);
});

test('saveGuardians() with guardian id exist and with user account false', function () {
    $student = Student::factory()->create();

    $user = User::factory()->create();

    $guardian = Guardian::factory()->create([
        'user_id' => $user->id,
    ]);

    $payload = [
        [
            'id' => $guardian->id,
            'with_user_account' => false,
            'type' => GuardianType::FATHER->value,
        ]
    ];

    $this->assertDatabaseCount($this->guardianTable, 1);

    $this->masterStudentService
        ->setStudent($student)
        ->saveGuardians($payload);

    $this->assertDatabaseCount($this->guardianTable, 1);

    $this->assertDatabaseHas($this->guardianTable, [
        'id' => $guardian->id,
        'user_id' => null,
    ]);

    $this->assertDatabaseHas($this->guardianStudentTable, [
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'type' => GuardianType::FATHER->value
    ]);
});

test('markStudentLeftAt() success effective date = future date', function () {
    $user = User::factory()->create();
    $user->assignRole(Role::STUDENT);
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => true,
        'user_id' => $user->id,
        'leave_status' => null,
    ]);
    $student_balance = 500;
    $guardian_balance = 10;
    $student_wallet = Wallet::factory()->create(['user_id' => $user->id, 'balance' => $student_balance]);

    $primary_guardian_student_without_wallet = GuardianStudent::factory()->create([
        'guardian_id' => Guardian::factory()->create()->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'is_primary' => true,
    ]);

    $guardian_wallet = Wallet::factory()->withGuardian()->create(['balance' => $guardian_balance]);
    $primary_guardian_with_wallet = $guardian_wallet->user->guardian;

    $primary_guardian_student_with_wallet = GuardianStudent::factory()->create([
        'guardian_id' => $primary_guardian_with_wallet->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'is_primary' => true,
    ]);

    $non_primary_guardian_student = GuardianStudent::factory()->create([
        'guardian_id' => Guardian::factory()->create()->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'is_primary' => false,
    ]);

    $leave_reason = LeaveReason::factory()->create();

    $leave_date = now(config('school.timezone'))->addDay()->toDateString();

    $user = $student->user;
    expect($user->hasRole(Role::STUDENT))->toBeTrue()
        ->and($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeFalse();

    $this->masterStudentService->setStudent($student)
        ->setRemarks('test123')
        ->setApiRequest(['effective_date' => $leave_date, 'leave_reason_id' => $leave_reason->id, 'remarks' => 'test123'])
        ->setLeaveReason($leave_reason)
        ->markStudentLeftAt($leave_date);

    $user->refresh();

    // nothing changed
    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => true,
        'user_id' => $user->id,
        'leave_status' => null,
    ]);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_LEAVE,
        'execution_date' => $leave_date,
        'data->effective_date' => $leave_date,
        'data->remarks' => 'test123',
        'data->leave_reason_id' => $leave_reason->id,
        'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
        'status_changeable_type' => Student::class,
        'status_changeable_id' => $student->id,
    ]);

    $this->assertDatabaseEmpty(StudentHistory::class);

    // role not updated
    expect($user->hasRole(Role::STUDENT))->toBeTrue();
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeFalse();

    // wallets not updated
    $this->assertDatabaseHas('wallets', [
        'user_id' => $student_wallet->user->id,
        'balance' => $student_balance,
    ]);

    // run command
    Artisan::call("execute:pending-status-change --execution_date={$leave_date}");

    $user->refresh();
    $student_wallet->refresh();
    $guardian_wallet->refresh();

    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => $leave_date,
        'is_active' => false,
        'user_id' => $user->id,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    $this->assertDatabaseHas(StudentHistory::class, [
        'student_id' => $student->id,
        'start_date' => '2024-11-01',
        'end_date' => $leave_date,
        'event_type' => Student::LEAVE,
        'remarks' => 'test123',
        'leave_reason_id' => $leave_reason->id,
    ]);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_LEAVE,
        'execution_date' => $leave_date,
        'data->effective_date' => $leave_date,
        'data->remarks' => 'test123',
        'data->leave_reason_id' => $leave_reason->id,
        'status' => PendingStudentEmployeeStatusChange::STATUS_SUCCESS,
        'status_changeable_type' => Student::class,
        'status_changeable_id' => $student->id,
    ]);

    expect($user->hasRole(Role::STUDENT))->toBeFalse();
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeTrue();

    // 25/2 update - student's remaining balance will no longer be transferred to primary guardian
    // $this->assertDatabaseHas(WalletTransaction::class, [
    //     'wallet_id' => $student_wallet->id,
    //     'type' => WalletTransactionType::TRANSFER,
    //     'status' => WalletTransactionStatus::SUCCESS,
    //     'wallet_transactable_type' => get_class($guardian_wallet),
    //     'wallet_transactable_id' => $guardian_wallet->id,
    //     'userable_type' => Student::class,
    //     'userable_id' => $student->id,
    //     'total_amount' => $student_balance * -1,
    //     'amount_before_tax' => $student_balance * -1,
    //     'amount_after_tax' => $student_balance * -1,
    //     'tax_type' => TaxType::FLAT_AMOUNT,
    //     'tax_value' => 0,
    //     'balance_before' => $student_balance,
    //     'balance_after' => 0,
    //     'description' => 'Transfer Student Remaining Balance to Guardian',
    // ]);

    $this->assertDatabaseHas(Wallet::class, [
        'user_id' => $student_wallet->user->id,
        'balance' => $student_balance,
    ]);

    // //receiver record (guardian)
    // $this->assertDatabaseHas(WalletTransaction::class, [
    //     'wallet_id' => $guardian_wallet->id,
    //     'type' => WalletTransactionType::TRANSFER,
    //     'status' => WalletTransactionStatus::SUCCESS,
    //     'wallet_transactable_type' => get_class($student_wallet),
    //     'wallet_transactable_id' => $student_wallet->id,
    //     'total_amount' => $student_balance,
    //     'amount_before_tax' => $student_balance,
    //     'amount_after_tax' => $student_balance,
    //     'tax_type' => TaxType::FLAT_AMOUNT,
    //     'tax_value' => 0,
    //     'balance_before' => $guardian_balance,
    //     'balance_after' => $student_balance + $guardian_balance,
    //     'description' => 'Transfer Student Remaining Balance to Guardian',
    // ]);

    $this->assertDatabaseHas(Wallet::class, [
        'user_id' => $guardian_wallet->user->id,
        'balance' => $guardian_balance,
    ]);
});

test('markStudentLeftAt() success effective date = current date', function () {

    $user = User::factory()->create();
    $user->assignRole(Role::STUDENT);

    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => true,
        'user_id' => $user->id,
        'leave_status' => null,
    ]);
    $student_balance = 500;
    $guardian_balance = 10;
    $student_wallet = Wallet::factory()->create(['user_id' => $user->id, 'balance' => $student_balance]);

    $primary_guardian_student_without_wallet = GuardianStudent::factory()->create([
        'guardian_id' => Guardian::factory()->create()->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'is_primary' => true,
    ]);

    $guardian_wallet = Wallet::factory()->withGuardian()->create(['balance' => $guardian_balance]);
    $primary_guardian_with_wallet = $guardian_wallet->user->guardian;

    $primary_guardian_student_with_wallet = GuardianStudent::factory()->create([
        'guardian_id' => $primary_guardian_with_wallet->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'is_primary' => true,
    ]);

    $non_primary_guardian_student = GuardianStudent::factory()->create([
        'guardian_id' => Guardian::factory()->create()->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'is_primary' => false,
    ]);

    $leave_reason = LeaveReason::factory()->create();

    $leave_date = now()->toDateString();

    $user = $student->user;
    expect($user->hasRole(Role::STUDENT))->toBeTrue();
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeFalse();

    $this->masterStudentService->setStudent($student)
        ->setRemarks('test')
        ->setApiRequest(['effective_date' => $leave_date, 'leave_reason_id' => $leave_reason->id, 'remarks' => 'test'])
        ->setLeaveReason($leave_reason)
        ->markStudentLeftAt($leave_date);

    $user->refresh();
    $student_wallet->refresh();
    $guardian_wallet->refresh();

    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => $leave_date, // updated leave_date
        'is_active' => false, // updated to false
        'user_id' => $user->id,
        'leave_status' => StudentLeaveStatus::LEFT, // updated to left status
    ]);

    // pending_student_employee_status_changes not created
    $this->assertDatabaseEmpty(PendingStudentEmployeeStatusChange::class);

    $this->assertDatabaseHas(StudentHistory::class, [
        'student_id' => $student->id,
        'start_date' => '2024-11-01',
        'end_date' => $leave_date,
        'event_type' => Student::LEAVE,
        'remarks' => 'test',
        'leave_reason_id' => $leave_reason->id,
    ]);

    // role updated
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeTrue();
    expect($user->hasRole(Role::STUDENT))->toBeFalse();

    // 25/2 update - student's remaining balance will no longer be transferred to primary guardian
    $this->assertDatabaseHas('wallets', [
        'user_id' => $student_wallet->user->id,
        'balance' => $student_balance,
    ]);
    $this->assertDatabaseHas('wallets', [
        'user_id' => $guardian_wallet->user->id,
        'balance' => $guardian_balance,
    ]);

    // //sender record (student)
    // $this->assertDatabaseHas('wallet_transactions', [
    //     'wallet_id' => $student_wallet->id,
    //     'type' => WalletTransactionType::TRANSFER,
    //     'status' => WalletTransactionStatus::SUCCESS,
    //     'wallet_transactable_type' => get_class($guardian_wallet),
    //     'wallet_transactable_id' => $guardian_wallet->id,
    //     'total_amount' => $student_balance * -1,
    //     'amount_before_tax' => $student_balance * -1,
    //     'amount_after_tax' => $student_balance * -1,
    //     'tax_type' => TaxType::FLAT_AMOUNT,
    //     'tax_value' => 0,
    //     'balance_before' => $student_balance,
    //     'balance_after' => 0,
    //     'description' => 'Transfer Student Remaining Balance to Guardian',
    // ]);
    // //receiver record (guardian)
    // $this->assertDatabaseHas('wallet_transactions', [
    //     'wallet_id' => $guardian_wallet->id,
    //     'type' => WalletTransactionType::TRANSFER,
    //     'status' => WalletTransactionStatus::SUCCESS,
    //     'wallet_transactable_type' => get_class($student_wallet),
    //     'wallet_transactable_id' => $student_wallet->id,
    //     'total_amount' => $student_balance,
    //     'amount_before_tax' => $student_balance,
    //     'amount_after_tax' => $student_balance,
    //     'tax_type' => TaxType::FLAT_AMOUNT,
    //     'tax_value' => 0,
    //     'balance_before' => $guardian_balance,
    //     'balance_after' => $student_balance + $guardian_balance,
    //     'description' => 'Transfer Student Remaining Balance to Guardian',
    // ]);

});

test('markStudentLeftAt() error student not set', function () {
    $leave_reason = LeaveReason::factory()->create();
    $leave_date = now()->toDateString();

    $this->expectExceptionMessage('Student not found.');
    $this->expectExceptionCode(29009);
    $this->masterStudentService->setRemarks('test')
        ->setApiRequest(['effective_date' => $leave_date, 'leave_reason_id' => $leave_reason->id, 'remarks' => 'test'])
        ->markStudentLeftAt($leave_date);
});

test('markStudentLeftAt() error inactive student', function () {
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => false,
        'leave_status' => null,
    ]);
    $leave_reason = LeaveReason::factory()->create();
    $leave_date = now()->toDateString();

    $this->expectExceptionMessage('Inactive student is not allowed to perform leave school action.');
    $this->expectExceptionCode(29002);
    $this->masterStudentService->setStudent($student)
        ->setRemarks('test')
        ->setApiRequest(['effective_date' => $leave_date, 'leave_reason_id' => $leave_reason->id, 'remarks' => 'test'])
        ->setLeaveReason($leave_reason)
        ->markStudentLeftAt($leave_date);
});

test('markStudentLeftAt() error student join date is after leave date', function () {
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => now()->addDay()->toDateString(),
        'leave_date' => null,
        'is_active' => true,
        'leave_status' => null,
    ]);
    $leave_reason = LeaveReason::factory()->create();
    $leave_date = now()->toDateString();

    $this->expectExceptionMessage('Effective date of student leave school cannot be before student join date.');
    $this->expectExceptionCode(29003);
    $this->masterStudentService->setStudent($student)
        ->setRemarks('test')
        ->setApiRequest(['effective_date' => $leave_date, 'leave_reason_id' => $leave_reason->id, 'remarks' => 'test'])
        ->setLeaveReason($leave_reason)
        ->markStudentLeftAt($leave_date);
});

test('markStudentLeftAt() error student with bed assignment', function () {
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => true,
        'leave_status' => null,
    ]);
    $leave_reason = LeaveReason::factory()->create();
    $leave_date = now()->toDateString();

    HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student->id,
        'hostel_room_bed_id' => $student->id,
        'start_date' => '2024-11-01',
        'end_date' => null,
    ]);

    $this->expectExceptionMessage('Student with bed assignment is not allowed to leave/graduate.');
    $this->expectExceptionCode(29004);
    $this->masterStudentService->setStudent($student)
        ->setRemarks('test')
        ->setApiRequest(['effective_date' => $leave_date, 'leave_reason_id' => $leave_reason->id, 'remarks' => 'test'])
        ->setLeaveReason($leave_reason)
        ->markStudentLeftAt($leave_date);
});

// 25/2 update - student's remaining balance will no longer be transferred to primary guardian
// test('markStudentLeftAt() error guardian with wallet not found for student to transfer remaining balance', function () {
//     $user = User::factory()->create();
//     $user->assignRole(Role::STUDENT);

//     $student = Student::factory()->create([
//         'student_number' => 'AA-11',
//         'join_date' => '2024-11-01',
//         'leave_date' => null,
//         'is_active' => true,
//         'user_id' => $user->id,
//         'leave_status' => null,
//     ]);
//     $student_balance = 500;
//     $student_wallet = Wallet::factory()->create(['user_id' => $user->id, 'balance' => $student_balance]);

//     $primary_guardian_student_without_wallet = GuardianStudent::factory()->create([
//         'guardian_id' => Guardian::factory()->create()->id,
//         'type' => GuardianType::FATHER->value,
//         'studenable_type' => Student::class,
//         'studenable_id' => $student->id,
//         'is_primary' => true,
//     ]);

//     $leave_reason = LeaveReason::factory()->create();
//     $leave_date = now()->toDateString();

//     $this->expectExceptionMessage("No eligible guardian was found to transfer the student's remaining balance. Ensure the primary guardian has wallet.");
//     $this->expectExceptionCode(29006);
//     $this->masterStudentService->setStudent($student)
//         ->setRemarks('test')
//         ->setApiRequest(['effective_date' => $leave_date, 'leave_reason_id' => $leave_reason->id, 'remarks' => 'test'])
//         ->setLeaveReason($leave_reason)
//         ->markStudentLeftAt($leave_date);
// });

test('markStudentReturnedAt() success effective date = future date', function () {
    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    // create inactive left student with student deactivated role
    $user = User::factory()->create();
    $user->assignRole(Role::STUDENT_DEACTIVATED);
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => '2024-11-05',
        'is_active' => false,
        'user_id' => $user->id,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    $return_date = now(config('school.timezone'))->addDay()->toDateString();

    $user = $student->user;
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeTrue();
    expect($user->hasRole(Role::STUDENT))->toBeFalse();

    $this->masterStudentService->setStudent($student)
        ->setApiRequest(['effective_date' => $return_date, 'semester_setting_id' => $semester_setting->id, 'semester_class_id' => $semester_class->id])
        ->setSemesterSetting($semester_setting)
        ->setSemesterClass($semester_class)
        ->markStudentReturnedAt($return_date);

    $user->refresh();

    // role not updated
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeTrue()
        ->and($user->hasRole(Role::STUDENT))->toBeFalse();

    // student not updated
    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => '2024-11-05',
        'is_active' => false,
        'user_id' => $user->id,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    // created pending_student_employee_status_changes
    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_RETURN,
        'execution_date' => $return_date,
        'data->effective_date' => $return_date,
        'data->semester_setting_id' => $semester_setting->id,
        'data->semester_class_id' => $semester_class->id,
        'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
        'status_changeable_type' => Student::class,
        'status_changeable_id' => $student->id,
    ]);


    Artisan::call("execute:pending-status-change --execution_date={$return_date}");

    $user->refresh();

    // student updated
    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'student_number' => 'AA-11',
        'join_date' => $return_date, // set return date as join_date
        'leave_date' => null, // set leave_date to null
        'is_active' => true, // false -> true
        'user_id' => $user->id,
        'leave_status' => null, // LEFT -> null
    ]);

    // removed deactivated student role. assigned student role
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeFalse();
    expect($user->hasRole(Role::STUDENT))->toBeTrue();

    // assigned student to class
    $this->assertDatabaseHas(StudentClass::class, [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student->id,
        'class_enter_date' => $return_date,
        'class_leave_date' => null,
        'is_active' => true,
    ]);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_RETURN,
        'execution_date' => $return_date,
        'data->effective_date' => $return_date,
        'data->semester_setting_id' => $semester_setting->id,
        'data->semester_class_id' => $semester_class->id,
        'status' => PendingStudentEmployeeStatusChange::STATUS_SUCCESS,
        'status_changeable_type' => Student::class,
        'status_changeable_id' => $student->id,
    ]);
});

test('markStudentReturnedAt() success effective date = current date', function () {
    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    // create inactive left student with student deactivated role
    $user = User::factory()->create();
    $user->assignRole(Role::STUDENT_DEACTIVATED);
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => '2024-11-05',
        'is_active' => false,
        'user_id' => $user->id,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    $return_date = now()->toDateString();

    $user = $student->user;
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeTrue();
    expect($user->hasRole(Role::STUDENT))->toBeFalse();

    $this->masterStudentService->setStudent($student)
        ->setApiRequest(['effective_date' => $return_date, 'semester_setting_id' => $semester_setting->id, 'semester_class_id' => $semester_class->id])
        ->setSemesterSetting($semester_setting)
        ->setSemesterClass($semester_class)
        ->markStudentReturnedAt($return_date);

    $user->refresh();
    // student updated
    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'student_number' => 'AA-11',
        'join_date' => $return_date, // set return date as join_date
        'leave_date' => null, // set leave_date to null
        'is_active' => true, // false -> true
        'user_id' => $user->id,
        'leave_status' => null, // LEFT -> null
    ]);

    // removed deactivated student role. assigned student role
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeFalse()
        ->and($user->hasRole(Role::STUDENT))->toBeTrue();

    // assigned student to class
    $this->assertDatabaseHas(StudentClass::class, [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student->id,
        'class_enter_date' => $return_date,
        'class_leave_date' => null,
        'is_active' => true,
    ]);

    // won't create pending status change
    $this->assertDatabaseEmpty(PendingStudentEmployeeStatusChange::class);
});

test('markStudentReturnedAt() error student status not left', function () {
    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => false,
        'leave_status' => null,
    ]);

    $return_date = now()->toDateString();

    $this->expectExceptionMessage('Only student with left status can perform return school action.');
    $this->expectExceptionCode(29005);
    $this->masterStudentService->setStudent($student)
        ->setApiRequest(['effective_date' => $return_date, 'semester_setting_id' => $semester_setting->id, 'semester_class_id' => $semester_class->id])
        ->setSemesterSetting($semester_setting)
        ->setSemesterClass($semester_class)
        ->markStudentReturnedAt($return_date);
});

test('markStudentReturnedAt() error semester setting not set', function () {
    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => '2024-11-11',
        'is_active' => false,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    $return_date = now()->toDateString();

    $this->expectExceptionMessage('Semester setting is required for student to return school.');
    $this->expectExceptionCode(29007);
    $this->masterStudentService->setStudent($student)
        ->setApiRequest(['effective_date' => $return_date, 'semester_setting_id' => $semester_setting->id, 'semester_class_id' => $semester_class->id])
        ->setSemesterClass($semester_class)
        ->markStudentReturnedAt($return_date);
});

test('markStudentReturnedAt() error semester class not set', function () {
    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => '2024-11-11',
        'is_active' => false,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    $return_date = now()->toDateString();

    $this->expectExceptionMessage('Semester class is required for student to return school.');
    $this->expectExceptionCode(29008);
    $this->masterStudentService->setStudent($student)
        ->setApiRequest(['effective_date' => $return_date, 'semester_setting_id' => $semester_setting->id, 'semester_class_id' => $semester_class->id])
        ->setSemesterSetting($semester_setting)
        ->markStudentReturnedAt($return_date);
});

test('markStudentExistingClassToInactive()', function () {

    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting_1 = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_setting_2 = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => false
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_1->id,
        'is_active' => true
    ]);

    $semester_class_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_2->id,
        'is_active' => false,
    ]);

    $student = Student::factory()->create();

    StudentClass::factory()->create([
        'semester_class_id' => $semester_class_1->id,
        'student_id' => $student->id,
        'is_active' => true,
    ]);

    StudentClass::factory()->create([
        'semester_class_id' => $semester_class_2->id,
        'student_id' => $student->id,
        'is_active' => false,
    ]);

    $this->masterStudentService->setStudent($student)
        ->markStudentExistingClassToInactive();

    $student_classes = StudentClass::all()->toArray();

    foreach ($student_classes as $student_class) {
        expect($student_class)->toMatchArray([
            "is_active" => false
        ]);
    }

    $student_without_classes = Student::factory()->create();

    $has_error = false;
    try {
        $this->masterStudentService->setStudent($student_without_classes)
            ->markStudentExistingClassToInactive();
    } catch (\Exception $e) {
        $has_error = true;
    }
    expect($has_error)->toBeFalse();

});


test('isFutureDate()', function () {

    \Carbon\Carbon::setTestNow('2024-11-14 23:59:00'); // malaysia timezone 2024-11-15 07:59:59

    expect($this->masterStudentService->isFutureDate('2024-11-16'))->toBeTrue();
    expect($this->masterStudentService->isFutureDate('2024-11-16 00:00:01'))->toBeTrue();
    expect($this->masterStudentService->isFutureDate('2024-11-15'))->toBeFalse();
    expect($this->masterStudentService->isFutureDate('2024-11-15 23:59:00'))->toBeFalse();
    expect($this->masterStudentService->isFutureDate('2024-11-15 07:59:59'))->toBeFalse();
    expect($this->masterStudentService->isFutureDate('2024-11-15 08:00:00'))->toBeFalse();
    expect($this->masterStudentService->isFutureDate('2024-11-14'))->toBeFalse();
    expect($this->masterStudentService->isFutureDate('2024-11-13'))->toBeFalse();
});
