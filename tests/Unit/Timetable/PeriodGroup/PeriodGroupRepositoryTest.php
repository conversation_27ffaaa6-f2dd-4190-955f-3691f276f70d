<?php

use App\Models\PeriodGroup;
use App\Repositories\PeriodGroupRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->periodGroupRepository = app(PeriodGroupRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(PeriodGroup::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->periodGroupRepository->getModelClass();

    expect($response)->toEqual(PeriodGroup::class);
});

test('getAll()', function () {
    $period_group = PeriodGroup::factory(3)->state(new Sequence(
        [
            'name->en' => 'English'
        ],
        [
            'name->en' => 'English 2'
        ],
        [
            'name->en' => 'Tamil',
        ]
    ))->create();

    $response = $this->periodGroupRepository->getAll()->toArray();

    expect($response)->toEqual($period_group->toArray());
});

test('getAllPaginated()', function () {
    $period_group = PeriodGroup::factory(3)->state(new Sequence(
        [
            'name->en' => 'English'
        ],
        [
            'name->en' => 'English 2'
        ],
        [
            'name->en' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->periodGroupRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.en", 'English 2')
        );

    //Filter by partial name = English
    $payload = [
        'name' => 'English'
    ];
    $response = $this->periodGroupRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2')
        );

    //Filter non-existing name = Not Exist
    $payload = [
        'name' => 'Not Exist'
    ];
    $response = $this->periodGroupRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->periodGroupRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Tamil'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->periodGroupRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Tamil'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->periodGroupRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $period_group[0]->id),
            fn($data) => $data->toHaveKey('id', $period_group[1]->id),
            fn($data) => $data->toHaveKey('id', $period_group[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->periodGroupRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $period_group[2]->id),
            fn($data) => $data->toHaveKey('id', $period_group[1]->id),
            fn($data) => $data->toHaveKey('id', $period_group[0]->id),
        );
});
