<?php

use App\Models\PeriodGroup;
use App\Services\PeriodGroupService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->periodGroupService = app(PeriodGroupService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(PeriodGroup::class)->getTable();
});

test('getAllPaginatedPeriodGroups()', function () {
    $period_groups = PeriodGroup::factory(3)->state(new Sequence(
        [
            'name->en' => 'English'
        ],
        [
            'name->en' => 'English 2'
        ],
        [
            'name->en' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->periodGroupService->getAllPaginatedPeriodGroups($payload)->toArray();


    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
        );
});

test('getAllPeriodGroups()', function () {
    $period_groups = PeriodGroup::factory(3)->state(new Sequence(
        [
            'name->en' => 'English'
        ],
        [
            'name->en' => 'English 2'
        ],
        [
            'name->en' => 'Tamil',
        ]
    ))->create();

    //Filter by name = English 2
    $payload = [
        'name' => 'English 2'
    ];
    $response = $this->periodGroupService->getAllPeriodGroups($payload)->toArray();


    expect($response)->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'English 2'),
        );
});

test('createPeriodGroup()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => 'English 2',
        ],
        'number_of_periods' => 2,
    ];

    $response = $this->periodGroupService->createPeriodGroup($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'number_of_periods' => $payload['number_of_periods'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'number_of_periods' => $payload['number_of_periods'],
    ]);
});

test('updatePeriodGroup()', function () {
    $period_group = PeriodGroup::factory()->create([
        'name->en' => 'Test EN'
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Test 2',
            'zh' => 'Test 3'
        ],
        'number_of_periods' => 2,
    ];

    $response = $this->periodGroupService->updatePeriodGroup($period_group->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $period_group->id,
        'name' => $payload['name'],
        'number_of_periods' => $payload['number_of_periods'],
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'number_of_periods' => $payload['number_of_periods'],
    ]);

    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 3'
        ],
    ];

    $this->expectException(ModelNotFoundException::class);
    $this->periodGroupService->updatePeriodGroup(9999, $payload)->toArray();
});

test('deletePeriodGroup()', function () {
    $period_group = PeriodGroup::factory()->create();
    $other_period_groups = PeriodGroup::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->periodGroupService->deletePeriodGroup($period_group->id);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $period_group->id]);

    foreach ($other_period_groups as $other_period_group) {
        $this->assertDatabaseHas($this->table, ['id' => $other_period_group->id]);
    }

    //id not exist
    $this->expectException(ModelNotFoundException::class);
    $this->periodGroupService->deletePeriodGroup(9999);
});
