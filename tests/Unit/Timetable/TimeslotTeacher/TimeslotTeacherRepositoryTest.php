<?php

use App\Models\TimeslotTeacher;
use App\Repositories\TimeslotTeacherRepository;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->timeslotTeacherRepository = app(TimeslotTeacherRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(TimeslotTeacher::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->timeslotTeacherRepository->getModelClass();

    expect($response)->toEqual(TimeslotTeacher::class);
});

test('getAll()', function () {
    $timeslot_teachers = TimeslotTeacher::factory(3)->create();

    $response = $this->timeslotTeacherRepository->getAll()->toArray();

    expect($response)->toEqual($timeslot_teachers->toArray());
});

test('getAllPaginated()', function () {
    $timeslot_teachers = TimeslotTeacher::factory(3)->create();

    $response = $this->timeslotTeacherRepository->getAllPaginated([])->toArray();

    expect($response['data'])->toEqual($timeslot_teachers->toArray());
});
