<?php

use App\Enums\Day;
use App\Models\Period;
use App\Models\PeriodGroup;
use App\Services\PeriodService;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->periodService = app(PeriodService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Period::class)->getTable();
});

test('getAllPaginatedPeriods()', function () {
    $period_groups = PeriodGroup::factory(2)->create();
    Period::factory(3)->state(new Sequence(
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[1]->id,
        ]
    ))->create();

    //Filter by period_group_id
    $payload = [
        'period_group_id' => $period_groups[0]->id,
    ];
    $response = $this->periodService->getAllPaginatedPeriods($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("period_group_id", $period_groups[0]->id),
            fn($data) => $data->toHaveKey("period_group_id", $period_groups[0]->id)
        );
});

test('removePeriodsByPeriodGroup()', function () {
    $period_groups = PeriodGroup::factory(2)->create();
    Period::factory(3)->state(new Sequence(
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[1]->id,
        ]
    ))->create();

    $this->assertDatabaseCount($this->table, 3);

    $this->periodService
        ->setPeriodGroupId($period_groups[0]->id)
        ->removePeriodsByPeriodGroup();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'period_group_id' => $period_groups[1]->id,
    ]);
    $this->assertDatabaseMissing($this->table, [
        'period_group_id' => $period_groups[0]->id,
    ]);
});

test('validateData() checkNoDuplicatePeriod', function () {
    $period_group = PeriodGroup::factory()->create();

    //same day, same period = failed
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 1,
                'from_time' => '10:00',
                'to_time' => '11:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ]
        ]
    ];

    try {
        $this->periodService
            ->setPeriodGroupId($payload['period_group_id'])
            ->setPeriodsData($payload['periods'])
            ->validateData();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe("Period must be unique in same day.");
    }

    //different day, same period = pass
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '10:00',
                'to_time' => '11:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::TUESDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '10:00',
                'to_time' => '11:00',
                'day' => Day::TUESDAY->value,
                'display_group' => 1,
            ],
        ]
    ];

    $this->periodService
        ->setPeriodGroupId($payload['period_group_id'])
        ->setPeriodsData($payload['periods'])
        ->validateData();
});

test('validateData checkNoDuplicateFromToTime', function () {
    $period_group = PeriodGroup::factory()->create();

    //same time, same day, same display group = failed
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ]
        ]
    ];

    try {
        $this->periodService
            ->setPeriodGroupId($payload['period_group_id'])
            ->setPeriodsData($payload['periods'])
            ->validateData();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe("From to time must be unique in same day.");
    }

    //same time, different day, same display group = pass
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::TUESDAY->value,
                'display_group' => 1,
            ]
        ]
    ];

    $this->periodService
        ->setPeriodGroupId($payload['period_group_id'])
        ->setPeriodsData($payload['periods'])
        ->validateData();
});

test('createPeriods()', function () {
    $this->assertDatabaseCount($this->table, 0);

    $period_group = PeriodGroup::factory()->create();
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '10:00',
                'to_time' => '11:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 2,
            ],
            [
                'period' => 2,
                'from_time' => '10:00',
                'to_time' => '11:00',
                'day' => Day::MONDAY->value,
                'display_group' => 2,
            ],
        ]
    ];

    $response = $this->periodService
        ->setPeriodGroupId($payload['period_group_id'])
        ->setPeriodsData($payload['periods'])
        ->createPeriods();

    expect($response)
        ->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'period_group_id' => $period_group->id,
                'from_time' => $payload['periods'][0]['from_time'],
                'to_time' => $payload['periods'][0]['to_time'],
                'period' => $payload['periods'][0]['period'],
                'day' => $payload['periods'][0]['day'],
                'display_group' => $payload['periods'][0]['display_group'],
            ]),
            fn($data) => $data->toMatchArray([

                'period_group_id' => $period_group->id,
                'from_time' => $payload['periods'][1]['from_time'],
                'to_time' => $payload['periods'][1]['to_time'],
                'period' => $payload['periods'][1]['period'],
                'day' => $payload['periods'][1]['day'],
                'display_group' => $payload['periods'][1]['display_group'],
            ]),
            fn($data) => $data->toMatchArray([

                'period_group_id' => $period_group->id,
                'from_time' => $payload['periods'][2]['from_time'],
                'to_time' => $payload['periods'][2]['to_time'],
                'period' => $payload['periods'][2]['period'],
                'day' => $payload['periods'][2]['day'],
                'display_group' => $payload['periods'][2]['display_group'],
            ]),
            fn($data) => $data->toMatchArray([

                'period_group_id' => $period_group->id,
                'from_time' => $payload['periods'][3]['from_time'],
                'to_time' => $payload['periods'][3]['to_time'],
                'period' => $payload['periods'][3]['period'],
                'day' => $payload['periods'][3]['day'],
                'display_group' => $payload['periods'][3]['display_group'],
            ]),
        );

    //check db record
    $this->assertDatabaseCount($this->table, 4);
    foreach ($payload['periods'] as $period_data) {
        $this->assertDatabaseHas($this->table, [
            'period_group_id' => $period_group->id,
            'from_time' => $period_data['from_time'],
            'to_time' => $period_data['to_time'],
            'period' => $period_data['period'],
            'day' => $period_data['day'],
            'display_group' => $period_data['display_group'],
        ]);;
    }

});

test('validateData checkEachDayHaveSameSetPeriod', function () {
    $period_group = PeriodGroup::factory()->create();

    //same display group, monday and tuesday period set is different = fail
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '10:00',
                'to_time' => '11:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::TUESDAY->value,
                'display_group' => 1,
            ],
        ]
    ];

    try {
        $this->periodService
            ->setPeriodGroupId($payload['period_group_id'])
            ->setPeriodsData($payload['periods'])
            ->validateData();
    } catch (Exception $e) {
        expect($e->getMessage())->toBe('Each day must have same set of periods.');
    }
});

test('validateData() checkEachDayHaveSameSetFromToTime', function () {
    $period_group = PeriodGroup::factory()->create();
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '10:00',
                'to_time' => '11:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::TUESDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '13:00',
                'to_time' => '14:00',
                'day' => Day::TUESDAY->value,
                'display_group' => 1,
            ],
        ]
    ];

    try {
        $this->periodService
            ->setPeriodGroupId($payload['period_group_id'])
            ->setPeriodsData($payload['periods'])
            ->validateData();
    } catch (Exception $e) {
        expect($e->getMessage())->toBe('Each day must have same set of from to time.');
    }
});

test('validateData() checkFromTimeIsBeforeToTime', function () {
    $period_group = PeriodGroup::factory()->create();
    $payload = [
        'period_group_id' => $period_group->id,
        'periods' => [
            [
                'period' => 1,
                'from_time' => '09:00',
                'to_time' => '10:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
            [
                'period' => 2,
                'from_time' => '10:00',
                'to_time' => '09:00',
                'day' => Day::MONDAY->value,
                'display_group' => 1,
            ],
        ]
    ];

    $this->expectExceptionMessage("Period 1 'from time' must be before 'to time'.");
    $this->periodService
        ->setPeriodGroupId($payload['period_group_id'])
        ->setPeriodsData($payload['periods'])
        ->validateData();
});
