<?php

use App\Models\Period;
use App\Models\PeriodGroup;
use App\Repositories\PeriodRepository;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->periodRepository = app(PeriodRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Period::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->periodRepository->getModelClass();

    expect($response)->toEqual(Period::class);
});

test('getAll()', function () {
    $period = Period::factory(3)->create();

    $response = $this->periodRepository->getAll()->toArray();

    expect($response)->toEqual($period->toArray());
});

test('getAllPaginated()', function () {
    $period_groups = PeriodGroup::factory(2)->state(new Sequence(
        ['name' => 'PG1'],
        ['name' => 'PG2']
    ))->create();

    $period = Period::factory(3)->state(new Sequence(
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[1]->id,
        ]
    ))->create();

    //Filter by period_group_id
    $payload = [
        'period_group_id' => $period_groups[0]->id,
    ];
    $response = $this->periodRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("period_group_id", $period_groups[0]->id),
            fn($data) => $data->toHaveKey("period_group_id", $period_groups[0]->id),
        );

    //Filter by array period_group_id
    $payload = [
        'period_group_id' => [$period_groups[0]->id, $period_groups[1]->id],
        'order_by' => ['id' => 'asc']
    ];
    $response = $this->periodRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("period_group_id", $period_groups[0]->id),
            fn($data) => $data->toHaveKey("period_group_id", $period_groups[0]->id),
            fn($data) => $data->toHaveKey("period_group_id", $period_groups[1]->id),
        );

    //Filter non-existing period_group_id
    $payload = [
        'period_group_id' => 9999
    ];
    $response = $this->periodRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->periodRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $period[0]->id),
            fn($data) => $data->toHaveKey('id', $period[1]->id),
            fn($data) => $data->toHaveKey('id', $period[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->periodRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $period[2]->id),
            fn($data) => $data->toHaveKey('id', $period[1]->id),
            fn($data) => $data->toHaveKey('id', $period[0]->id),
        );

    // order_by period_group name
    $payload = [
        'order_by' => ['period_group_name' => 'desc'],
    ];
    $response = $this->periodRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('period_group_id', $period_groups[1]->id),
            fn($data) => $data->toHaveKey('period_group_id', $period_groups[0]->id),
            fn($data) => $data->toHaveKey('period_group_id', $period_groups[0]->id),
        );


});

test('removePeriodsByPeriodGroup()', function () {
    $period_groups = PeriodGroup::factory(2)->create();
    Period::factory(3)->state(new Sequence(
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[0]->id,
        ],
        [
            'period_group_id' => $period_groups[1]->id,
        ]
    ))->create();

    $this->assertDatabaseCount($this->table, 3);

    $this->periodRepository
        ->removePeriodsByPeriodGroupId($period_groups[0]->id);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'period_group_id' => $period_groups[1]->id,
    ]);
    $this->assertDatabaseMissing($this->table, [
        'period_group_id' => $period_groups[0]->id,
    ]);
});
