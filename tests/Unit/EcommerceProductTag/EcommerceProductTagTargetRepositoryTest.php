<?php

use App\Models\EcommerceProductTag;
use App\Models\EcommerceProductTagTarget;
use App\Models\Student;
use App\Repositories\EcommerceProductTagTargetRepository;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->productTagTargetRepository = app(EcommerceProductTagTargetRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(EcommerceProductTagTarget::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->productTagTargetRepository->getModelClass();

    expect($response)->toEqual(EcommerceProductTagTarget::class);
});

test('getAllPaginated getAll', function (int $expected_count, array $filters, array $expected_model) {
    $targets = [
        'first' => EcommerceProductTagTarget::factory()->create([
            'tag_id' => EcommerceProductTag::factory()->create(['id' => 1]),
        ]),
        'second' => EcommerceProductTagTarget::factory()->create([
            'tag_id' => EcommerceProductTag::factory()->create(['id' => 2]),
        ]),
    ];

    $result_paginated = $this->productTagTargetRepository->getAllPaginated($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $targets[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }

    $result = $this->productTagTargetRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $targets[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [2, [], ['first', 'second']],
    'filter by tag_id' => [1, ['tag_id' => 1], ['first']],
    'sort by id asc' => [2, ['order_by' => ['id' => 'asc']], ['first', 'second']],
    'sort by id desc' => [2, ['order_by' => ['id' => 'desc']], ['second', 'first']],
]);

test('deleteByProductTag()', function () {
    $product_tags = EcommerceProductTag::factory(2)->create();
    $student = Student::factory()->create();

    EcommerceProductTagTarget::factory(2)->state(new Sequence(
        [
            'tag_id' => $product_tags[0]->id,
            'userable_id' => $student->id,
            'userable_type' => Student::class,
        ],
        [
            'tag_id' => $product_tags[1]->id,
            'userable_id' => $student->id,
            'userable_type' => Student::class,
        ],
    ))->create();

    $this->assertDatabaseCount($this->table, 2);

    $this->productTagTargetRepository->deleteByProductTag($product_tags[0]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseMissing($this->table, ['tag_id' => $product_tags[0]->id]);
    $this->assertDatabaseHas($this->table, ['tag_id' => $product_tags[1]->id]);
});

test('deleteByUserable()', function () {
    $product_tags = EcommerceProductTag::factory(2)->create();
    $students = Student::factory(2)->create();

    EcommerceProductTagTarget::factory(2)->state(new Sequence(
        [
            'tag_id' => $product_tags[0]->id,
            'userable_id' => $students[0]->id,
            'userable_type' => Student::class,
        ],
        [
            'tag_id' => $product_tags[1]->id,
            'userable_id' => $students[1]->id,
            'userable_type' => Student::class,
        ],
    ))->create();

    $this->assertDatabaseCount($this->table, 2);

    $this->productTagTargetRepository->deleteByUserable($students[0]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseMissing($this->table, ['userable_type' => Student::class, 'userable_id' => $students[0]->id]);
    $this->assertDatabaseHas($this->table, ['userable_type' => Student::class, 'userable_id' => $students[1]->id]);
});
