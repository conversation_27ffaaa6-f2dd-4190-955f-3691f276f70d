<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Models\Attendance;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Services\DocumentPrintService;
use App\Services\Report\AttendanceReportService;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Support\Facades\DB;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->attendanceReportService = app(AttendanceReportService::class);
    $this->reportPrintService = app(ReportPrintService::class);

    $this->student_late = Student::factory()->create([
        'name->en' => 'Student Late'
    ]);
    $this->student_attendance_late = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_late->id,
        'date' => '2024-12-01',
        'check_in_datetime' => '2024-12-01 00:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->student_on_time = Student::factory()->create([
        'name->en' => 'Student On time'
    ]);
    $this->student_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_on_time->id,
        'date' => '2024-12-02',
        'check_in_datetime' => '2024-11-30 23:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->student_absent = Student::factory()->create([
        'name->en' => 'Student Absent'
    ]);
    $this->student_attendance_absent = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    $this->teacher_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Employee::class,
        'attendance_recordable_id' => Employee::factory(),
        'date' => '2024-12-03',
        'check_in_datetime' => '2024-11-30 23:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => '2024-12-01 07:00:00', // UTC + 0 Time
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->semester_setting1 = SemesterSetting::factory()->create(['id' => 100]);
    $this->semester_setting1_semester_class = SemesterClass::factory()->create([
        'id' => 100,
        'semester_setting_id' => $this->semester_setting1->id,
    ]);
    $this->semester_setting1_semester_class2 = SemesterClass::factory()->create([
        'id' => 200,
        'semester_setting_id' => $this->semester_setting1->id,
    ]);

    $this->semester_setting2 = SemesterSetting::factory()->create(['id' => 200]);
    $this->semester_setting2_semester_class = SemesterClass::factory()->create([
        'id' => 300,
        'semester_setting_id' => $this->semester_setting2->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class->id,
        'student_id' => $this->student_late->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'student_id' => $this->student_on_time->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $this->student_absent->id,
        'is_active' => true,
    ]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    SnappyPdf::fake();
});

test('getStudentAttendance() filter by date_from and date_to', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-02',
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 1, 2024 (Sunday)',
                        'students' => [
                            [
                                'student_number' => $this->student_late->student_number,
                                'student_name' => $this->student_late->name,
                                'attendance_time_in' => '08:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceCheckInStatus::LATE->value,
                                'attendance_reason' => null
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 0,
                        'total_absent' => 0,
                        'total_late' => 1,
                    ]
                ]
            ]),
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_name' => $this->student_on_time->name,
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() filter by semester_setting_id', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 1, 2024 (Sunday)',
                        'students' => [
                            [
                                'student_number' => $this->student_late->student_number,
                                'student_name' => $this->student_late->name,
                                'attendance_time_in' => '08:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceCheckInStatus::LATE->value,
                                'attendance_reason' => null
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 0,
                        'total_absent' => 0,
                        'total_late' => 1,
                    ]
                ]
            ]),
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_name' => $this->student_on_time->name,
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() filter by status', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => AttendanceStatus::PRESENT->value,
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_name' => $this->student_on_time->name,
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() filter by semester_setting_id and semester_class_id', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_name' => $this->student_on_time->name,
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() generate pdf', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-02',
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'class_type' => 'PRIMARY'
    ];
    $response = $this->attendanceReportService->getStudentAttendance($filters);

    $report_data = [
        'data' => $response,
        'title' => 'Report Student Daily Arrival',
        'subtitle' => Carbon::parse($filters['date_from'])->translatedFormat('F j, Y') . ' - ' . Carbon::parse($filters['date_to'])->translatedFormat('F j, Y'),
    ];
    $report_view_name = 'reports.attendances.student-attendance-report';
    $file_name = 'student-attendance-report';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($report_data['title']);
    SnappyPdf::assertSee($report_data['subtitle']);

    foreach ($response as $class) {
        $expected_headers = ['Student No.', 'Student Class (' . e($class['class_name']) . ')', 'Time In', 'Time Out', 'Attendance Status', 'Reason', 'Remarks'];

        foreach ($expected_headers as $header) {
            SnappyPdf::assertSee($header);

            foreach ($class['dates'] as $date) {
                SnappyPdf::assertSee($date['date']);

                foreach ($date['students'] as $student) {
                    SnappyPdf::assertSee($student['student_number']);
                    SnappyPdf::assertSee($student['student_name']);

                    if ($student['attendance_time_in']) {
                        SnappyPdf::assertSee($student['attendance_time_in']);
                    }

                    if ($student['attendance_time_out']) {
                        SnappyPdf::assertSee($student['attendance_time_out']);
                    }

                    SnappyPdf::assertSee($student['attendance_status']->value);

                    if ($student['attendance_reason']) {
                        SnappyPdf::assertSee($student['attendance_reason']);
                    }
                }

                SnappyPdf::assertSee('Total : ' . $date['total']);
                SnappyPdf::assertSee('Attend : ' . $date['total_present']);
                SnappyPdf::assertSee('Absent : ' . $date['total_absent']);
                SnappyPdf::assertSee('Late : ' . $date['total_late']);
            }
        }
    }
});

test('getStudentAttendance() -  filter by class_type', function (){ 
    $student_present = Student::factory()->create([
        'name' => 'Teachers pet'
    ]);
    $student_absent = Student::factory()->create([
        'name' => 'Mana you? Always absent'
    ]);

    $cocuclass1 = ClassModel::factory()->create([
        'name' => 'Badminton',
        'type' => ClassType::SOCIETY,
    ]);
    $cocuclass2 = ClassModel::factory()->create([
        'name' => 'Football',
        'type' => ClassType::SOCIETY,
    ]);

    $engclass1 = ClassModel::factory()->create([
        'name' => 'A1A',
        'type' => ClassType::ENGLISH
    ]);
    $engclass2 = ClassModel::factory()->create([
        'name' => 'A2A',
        'type' => ClassType::ENGLISH
    ]);

    $sem2_cocuclass1 = SemesterClass::factory()->create([
        'id' => 101,
        'class_id' => $cocuclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_cocuclass2 = SemesterClass::factory()->create([
        'id' => 102,
        'class_id' => $cocuclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);

    $sem2_engclass1 = SemesterClass::factory()->create([
        'id' => 103,
        'class_id' => $engclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_engclass2 = SemesterClass::factory()->create([
        'id' => 104,
        'class_id' => $engclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);


    // Student Present has 1 cocuclass and 1 English Class
    // Student Absent has 2 cocuclass and 2 English class (one of them is inactive, and the other has a later class enter date)
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);


    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    $filters = [
        'semester_setting_id' => $this->semester_setting2->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);
    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            'class_name' => $cocuclass1->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_name' => $student_present->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::PRESENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 1,
                    'total_absent' => 0,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_name' => $student_present->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ])
        ->and($response[1])->toMatchArray([
            'class_name' => $cocuclass2->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_name' => $student_absent->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_name' => $student_absent->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ]);

    $filters = [
        'semester_setting_id' => $this->semester_setting2->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH'
    ];
    
    $response = $this->attendanceReportService->getStudentAttendance($filters);
    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            'class_name' => $engclass1->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_name' => $student_present->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::PRESENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 1,
                    'total_absent' => 0,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_name' => $student_present->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ])
        ->and($response[1])->toMatchArray([
            'class_name' => $engclass2->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_name' => $student_absent->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_name' => $student_absent->name,
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ]);
});


test('getStudentAbsent() filter by absent count', function () {

    $student_present2 = Student::factory()->create([
        'name' => 'A teachers pet'
    ]);
    $student_absent2 = Student::factory()->create([
        'name' => 'Mana you? Always absent'
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $student_present2->id,
        'is_active' => true,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $student_absent2->id,
        'is_active' => true,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present2,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-03',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);
    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'PRIMARY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 0
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(3)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_present2->name,
            "student_number" => $student_present2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "absent_dates" => [],
        ])
        ->and($response[0]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_absent2->name,
            "student_number" => $student_absent2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
        ])
        ->and($response[1]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[2])->toMatchArray([
            "student_name" => $this->student_absent->name,
            "student_number" => $this->student_absent->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "present_dates" => [],
        ])
        ->and($response[2]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'PRIMARY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent2->name,
            "student_number" => $student_absent2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "present_dates" => [],
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $this->student_absent->name,
            "student_number" => $this->student_absent->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "present_dates" => [],
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'PRIMARY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 2
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent2->name,
            "student_number" => $student_absent2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "present_dates" => [],
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 3
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(0)
        ->toBe([]);
});


test('getStudentAbsent() filter by absent count - cocurricular and english', function () {

    $student_present = Student::factory()->create([
        'name' => 'Teachers pet'
    ]);
    $student_absent = Student::factory()->create([
        'name' => 'Mana you? Always absent'
    ]);

    $cocuclass1 = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
    ]);
    $cocuclass2 = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
    ]);

    $engclass1 = ClassModel::factory()->create([
        'type' => ClassType::ENGLISH
    ]);
    $engclass2 = ClassModel::factory()->create([
        'type' => ClassType::ENGLISH
    ]);

    $sem2_cocuclass1 = SemesterClass::factory()->create([
        'id' => 101,
        'class_id' => $cocuclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_cocuclass2 = SemesterClass::factory()->create([
        'id' => 102,
        'class_id' => $cocuclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);

    $sem2_engclass1 = SemesterClass::factory()->create([
        'id' => 103,
        'class_id' => $engclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_engclass2 = SemesterClass::factory()->create([
        'id' => 104,
        'class_id' => $engclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);


    // Student Present has 1 cocuclass and 1 English Class
    // Student Absent has 2 cocuclass and 2 English class (one of them is inactive, and the other has a later class enter date)
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);


    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    // Class Type = Society Test
    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 0
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $cocuclass2->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $cocuclass1->name,
        ])
        ->and($response[1]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $cocuclass2->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $cocuclass1->name,
            "present_dates" => []
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 2
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $cocuclass2->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 3
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(0)
        ->toBe([]);

    // Class Type = English Test
    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 0
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $engclass2->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $engclass1->name,
        ])
        ->and($response[1]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $engclass2->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $engclass1->name,
            "present_dates" => []
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 2
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $engclass2->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 3
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(0)
        ->toBe([]);


});


test('getStudentAbsentReport() generate pdf', function () {

    $student_absent2 = Student::factory()->create([
        'name' => 'Always Absent'
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $student_absent2->id,
        'is_active' => true,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-02',
        'check_in_datetime' => now()->toDateTimeString(),
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-03',
        'check_in_datetime' => now()->toDateTimeString(),
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);
    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'semester_setting_id' => $this->semester_setting2->id,
        'class_type' => 'PRIMARY',
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);

    $report_data = [
        'data' => $response,
        'title' => 'Student Absent Report',
        'subtitle' => Carbon::parse($filters['date_from'])->translatedFormat('F j, Y') . ' - ' . Carbon::parse($filters['date_to'])->translatedFormat('F j, Y'),
    ];
    $report_view_name = 'reports.attendances.student-absent-report';
    $file_name = 'student-absent-report';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($report_data['title']);
    SnappyPdf::assertSee($report_data['subtitle']);

    $expected_headers = ['No.', 'Attendance Status', 'Student No.', 'Student Name', 'Date', 'Time In', 'Time Out', 'Class'];

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($response as $data) {
        SnappyPdf::assertSee($data['student_name']);
        SnappyPdf::assertSee($data['student_number']);
        SnappyPdf::assertSee($data['class']);

        foreach ($data['absent_dates'] as $date) {
            SnappyPdf::assertSee($date['attendance_status']);
            SnappyPdf::assertSee($date['date']);

            if ($date['attendance_time_in'] == null) {
                SnappyPdf::assertSee('-');
            } else {
                SnappyPdf::assertSee($date['attendance_time_in']);
            }

            if ($date['attendance_time_out'] == null) {
                SnappyPdf::assertSee('-');
            } else {
                SnappyPdf::assertSee($date['attendance_time_out']);
            }
        }
    }
});

