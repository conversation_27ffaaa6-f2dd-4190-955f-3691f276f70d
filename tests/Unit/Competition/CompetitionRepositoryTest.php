<?php

use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\Department;
use App\Models\Student;
use App\Repositories\CompetitionRepository;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);
    
    $this->competitionRepository = resolve(CompetitionRepository::class);
});

test('getModelClass()', function () {
    $response = $this->competitionRepository->getModelClass();

    expect($response)->toEqual(Competition::class);
});

test('getAll()', function () {
    $first_competition = Competition::factory()->create();
    $second_competition = Competition::factory()->create();
    $third_competition = Competition::factory()->create();

    $response = $this->competitionRepository->getAll(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->sequence(
        fn($response) => $response->toEqual($first_competition->toArray()),
        fn($response) => $response->toEqual($second_competition->toArray()),
        fn($response) => $response->toEqual($third_competition->toArray()),
    );
});

test('getAllPaginated()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $department1 = Department::factory()->create(['id' => '1200', 'name->en' => 'Department A']);
    $department2 = Department::factory()->create(['name->en' => 'Department B']);
    $department3 = Department::factory()->create(['name->en' => 'Department C']);

    $first_competition = Competition::factory()->create([
        'name' => 'Lompat Galah',
        'department_id' => $department2->id,
        'date' => now()->toDateString(),
    ]);
    CompetitionRecord::factory()->create([
        'competition_id' => $first_competition->id,
        'student_id' => Student::factory()->create([
            'name->en' => 'apple',
            'name->zh' => '苹果',
            'student_number' => '00001',
        ])->id,
    ]);

    $second_competition = Competition::factory()->create([
        'name' => 'Driving',
        'department_id' => $department1->id,
        'date' => now()->addDay()->toDateString(),
    ]);
    CompetitionRecord::factory()->create([
        'competition_id' => $second_competition->id,
        'student_id' => Student::factory()->create([
            'name->en' => 'watermelon',
            'name->zh' => '西瓜',
            'student_number' => '00002',
        ])->id,
    ]);

    $third_competition = Competition::factory()->create([
        'name' => 'Wheelie',
        'department_id' => $department3->id,
        'date' => '2010-01-01'
    ]);
    CompetitionRecord::factory()->create([
        'competition_id' => $third_competition->id,
        'student_id' => Student::factory()->create([
            'name->en' => 'mango',
            'name->zh' => '芒果',
            'student_number' => '00003',
        ])->id,
    ]);

    $keys = [
        'first' => $first_competition,
        'second' => $second_competition,
        'third' => $third_competition,
    ];

    $result = $this->competitionRepository->getAllPaginated([$filter_by => $filter_value])->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'filter by name = Lompat Galah' => [1, 'name', 'Lompat Galah', ['first']],
    'filter by name = Non Existing' => [0, 'name', 'Non Existing', []],
    'filter by department_id = 1200' => [1, 'department_id', '1200', ['second']],
    'filter by department_id = Non Existing' => [0, 'department_id', '10000000', []],
    'filter by date = 2010-01-01' => [1, 'date', '2010-01-01', ['third']],
    'filter by date = Non Existing' => [0, 'date', '2009-01-01', []],
    'filter by student name = apple' => [1, 'student_name', 'apple', ['first']],
    'filter by student name = 西瓜' => [1, 'student_name', '西瓜', ['second']],
    'filter by student number = 00002' => [1, 'student_number', '00002', ['second']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by name asc' => [3, 'order_by', ['name' => 'asc'], ['second', 'first', 'third']],
    'sort by name desc' => [3, 'order_by', ['name' => 'desc'], ['third', 'first', 'second']],
    'sort by department_id asc' => [3, 'order_by', ['department_id' => 'asc'], ['first', 'third', 'second']],
    'sort by department_id desc' => [3, 'order_by', ['department_id' => 'desc'], ['second', 'third', 'first']],
]);
