<?php

use App\Models\ClubCategory;
use App\Repositories\ClubCategoryRepository;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->clubCategoryRepository = resolve(ClubCategoryRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
});

test('getModelClass()', function () {
    $response = $this->clubCategoryRepository->getModelClass();

    expect($response)->toEqual(ClubCategory::class);
});

test('getAll()', function () {
    $club_category_1 = ClubCategory::factory()->create();
    $club_category_2 = ClubCategory::factory()->create();
    $club_category_3 = ClubCategory::factory()->create();

    $response = $this->clubCategoryRepository->getAll(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->sequence(
        fn($item) => $item->toEqual($club_category_1->toArray()),
        fn($item) => $item->toEqual($club_category_2->toArray()),
        fn($item) => $item->toEqual($club_category_3->toArray()),
    );
});

test('getAllPaginated()', function(int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $categories = [
        'first' => ClubCategory::factory()->create(['name->en' => 'Sport','name->zh' => 'zh Sport']),
        'second' => ClubCategory::factory()->create(['name->en' => 'Academic','name->zh' => 'zh Academic']),
    ];

    $result = $this->clubCategoryRepository->getAllPaginated([$filter_by => $filter_value])->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($categories[$value]->toArray());
    }
})->with([
    'filter by name = Sport' => [1, 'name', 'Sport', ['first']],
    'filter by name = Academic' => [1, 'name', 'Academic', ['second']],
    'filter by non-existing name = Non Existing' => [0, 'name', 'Non Existing', []],
    'sort by name asc' => [2, 'order_by', ['name' => 'asc'], ['second', 'first']],
    'sort by name desc' => [2, 'order_by', ['name' => 'desc'], ['first', 'second']],
    'sort by id asc' => [2, 'order_by', ['id' => 'asc'], ['first', 'second']],
    'sort by id desc' => [2, 'order_by', ['id' => 'desc'], ['second', 'first']],
]);
