<?php

use App\Models\Club;
use App\Models\ClubCategory;
use App\Services\ClubCategoryService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->clubCategoryService = app(ClubCategoryService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->tableName = resolve(ClubCategory::class)->getTable();
});

test('getAllPaginatedClubCategories()', function () {
    $club_category_1 = ClubCategory::factory()->create([
        'name->en' => 'Sport',
        'name->zh' => 'zh Sport',
    ]);

    $club_category_2 = ClubCategory::factory()->create([
        'name->en' => 'Academic',
        'name->zh' => 'zh Academic',
    ]);

    // Filter by name = Sport
    $response = $this->clubCategoryService->getAllPaginatedClubCategories(['name' => 'Sport'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$club_category_1->toArray()]);

    // Sort by name asc
    $response = $this->clubCategoryService->getAllPaginatedClubCategories([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Academic'),
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Sport'),
    );

    // Sort by name desc
    $response = $this->clubCategoryService->getAllPaginatedClubCategories([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Sport'),
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Academic'),
    );

    // Sort by id asc
    $response = $this->clubCategoryService->getAllPaginatedClubCategories([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toHaveKey('id', $club_category_1->id),
        fn($item) => $item->toHaveKey('id', $club_category_2->id),
    );

    // Sort by id desc
    $response = $this->clubCategoryService->getAllPaginatedClubCategories([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toHaveKey('id', $club_category_2->id),
        fn($item) => $item->toHaveKey('id', $club_category_1->id),
    );
});

test('getAllClubCategories()', function () {
    $club_category_1 = ClubCategory::factory()->create([
        'name->en' => 'Sport',
        'name->zh' => 'zh Sport',
    ]);

    $club_category_2 = ClubCategory::factory()->create([
        'name->en' => 'Academic',
        'name->zh' => 'zh Academic',
    ]);

    // Filter by name = Sport
    $response = $this->clubCategoryService->getAllClubCategories(['name' => 'Sport'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$club_category_1->toArray()]);

    // Sort by name asc
    $response = $this->clubCategoryService->getAllClubCategories([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Academic'),
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Sport'),
    );

    // Sort by name desc
    $response = $this->clubCategoryService->getAllClubCategories([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Sport'),
        fn($item) => $item->toHaveKey('name.' . $this->testLocale, 'Academic'),
    );

    // Sort by id asc
    $response = $this->clubCategoryService->getAllClubCategories([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($item) => $item->toHaveKey('id', $club_category_1->id),
        fn($item) => $item->toHaveKey('id', $club_category_2->id),
    );

    // Sort by id desc
    $response = $this->clubCategoryService->getAllClubCategories([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($item) => $item->toHaveKey('id', $club_category_2->id),
        fn($item) => $item->toHaveKey('id', $club_category_1->id),
    );
});

test('createClubCategory()', function () {
    $this->assertDatabaseCount($this->tableName, 0);

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake('zh_CN')->name,
        ],
    ];

    $response = $this->clubCategoryService->createClubCategory($payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->tableName, 1);

    $this->assertDatabaseHas($this->tableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);
});

test('updateClubCategory()', function () {
    $first_club_category = ClubCategory::factory()->create();

    //update with id exist
    $this->assertDatabaseCount($this->tableName, 1);

    $payload = [
        'name' => [
            'en' => 'Sport',
            'zh' => 'zh Sport',
        ],
    ];

    $response = $this->clubCategoryService->updateClubCategory($first_club_category->id, $payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->tableName, 1);

    $this->assertDatabaseHas($this->tableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);
});

test('updateClubCategory() : fail because invalid id', function () {
    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 3',
        ],
    ];

    $has_error = false;

    try {
        $this->clubCategoryService->updateClubCategory(9999, $payload)->toArray();
    } catch (\Throwable $th) {
        expect($th)->toBeInstanceOf(ModelNotFoundException::class);
        $has_error = true;
    }
    expect($has_error)->toBeTruthy();

    $this->assertDatabaseCount($this->tableName, 0);
});

test('deleteClubCategory()', function () {
    $first_club_category = ClubCategory::factory()->create();
    $other_club_categories = ClubCategory::factory(3)->create();

    $this->assertDatabaseCount($this->tableName, 4);

    // Delete success
    $this->clubCategoryService->deleteClubCategory($first_club_category->id);

    $this->assertDatabaseCount($this->tableName, 3);

    $this->assertDatabaseMissing($this->tableName, ['id' => $first_club_category->id]);

    foreach ($other_club_categories as $other_club_category) {
        $this->assertDatabaseHas($this->tableName, ['id' => $other_club_category->id]);
    }

    // ID not exist
    $this->expectException(ModelNotFoundException::class);
    $this->clubCategoryService->deleteClubCategory(9999);
});

test('deleteClubCategory() : fail because club_category is used by club', function () {
    $club_category = ClubCategory::factory()->create();

    Club::factory()->create([
        'club_category_id' => $club_category,
    ]);

    $has_error = false;

    try {
        $this->clubCategoryService->deleteClubCategory($club_category->id);
    } catch (\Throwable $th) {
        expect($th->getCode())->toEqual(12001)
            ->and($th->getMessage())->toEqual(__('system_error.12001'));
        $has_error = true;
    }

    expect($has_error)->toBeTruthy();

    $this->assertDatabaseCount($this->tableName, 1);
});
