<?php

use App\Enums\Gender;
use App\Enums\LibraryMemberType;
use App\Exceptions\CannotDeleteModelException;
use App\Models\Country;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\Media;
use App\Models\Race;
use App\Models\Religion;
use App\Models\State;
use App\Models\Student;
use App\Services\LibraryMemberService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\UploadedFile;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->libraryMemberService = app(LibraryMemberService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(LibraryMember::class)->getTable();
    $this->mediaTable = resolve(Media::class)->getTable();
});

test('getAllPaginatedLibraryMembers()', function () {
    LibraryMember::factory(3)->state(new Sequence(
        [
            'type' => LibraryMemberType::STUDENT,
            'member_number' => '12345',
            'nric' => '12345',
            'name->en' => 'LimCH1',
            'phone_number' => '123456789',
            'email' => '<EMAIL>',
            'card_number' => '12345',
            'is_active' => true
        ],
        [
            'type' => LibraryMemberType::OTHERS,
            'member_number' => '12346',
            'nric' => '12346',
            'name->en' => 'LimCH2',
            'phone_number' => '1234567810',
            'email' => '<EMAIL>',
            'card_number' => '12346',
            'is_active' => true
        ],
        [
            'type' => LibraryMemberType::OTHERS,
            'member_number' => '12347',
            'nric' => '12347',
            'name->en' => 'Test3',
            'phone_number' => '1234567811',
            'email' => '<EMAIL>',
            'card_number' => '12347',
            'is_active' => false
        ]
    ))->create();

    //Filter by name = LimCH1
    $payload = [
        'name' => 'LimCH1'
    ];
    $response = $this->libraryMemberService->getAllPaginatedLibraryMembers($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('name.' . $this->testLocale, 'LimCH1')
        );
});

test('createLibraryMember()', function () {
    $this->assertDatabaseCount($this->table, 0);

    $race = Race::factory()->create();
    $religion = Religion::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $student = Student::factory()->create();

    //student
    $payload = [
        'type' => LibraryMemberType::STUDENT->value,
        'student_id' => $student->id,
        'card_number' => '12345',
        'member_number' => '12347',
        'borrow_limit' => 10,
        'is_librarian' => false,
        'register_date' => now()->format('Y-m-d'),
        'valid_from' => now()->format('Y-m-d'),
        'valid_to' => now()->addYear()->format('Y-m-d'),
        'is_active' => true,
        'name' => [
            'en' => 'LimCH1',
        ],
        'gender' => Gender::MALE->value,
        'nric' => uniqid(),
        'passport_number' => '12347',
        'date_of_birth' => '1999-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
        'photo' => UploadedFile::fake()->create('first_file.png', 500)
    ];

    $response = $this->libraryMemberService->createLibraryMember($payload)->toArray();

    expect($response)->toMatchArray([
        'type' => $payload['type'],
        'userable_type' => Student::class,
        'userable_id' => $payload['student_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name' => $payload['name'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ])->and(Carbon::parse($response['date_of_birth'])->format('Y-m-d'))->toBe($payload['date_of_birth'])
        ->and(Carbon::parse($response['register_date'])->format('Y-m-d'))->toBe($payload['register_date'])
        ->and(Carbon::parse($response['valid_from'])->format('Y-m-d'))->toBe($payload['valid_from'])
        ->and(Carbon::parse($response['valid_to'])->format('Y-m-d'))->toBe($payload['valid_to']);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'type' => $payload['type'],
        'userable_type' => Student::class,
        'userable_id' => $payload['student_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name->en' => $payload['name']['en'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ]);

    $library_member = LibraryMember::first();

    $this->assertDatabaseCount($this->table, 1);

    // photo set
    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => LibraryMember::class,
        'model_id' => $library_member->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    //employee
    $employee = Employee::factory()->create();
    $payload = [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'employee_id' => $employee->id,
        'card_number' => '12345',
        'member_number' => '12347',
        'borrow_limit' => 10,
        'is_librarian' => false,
        'register_date' => now()->format('Y-m-d'),
        'valid_from' => now()->format('Y-m-d'),
        'valid_to' => now()->addYear()->format('Y-m-d'),
        'is_active' => true,
        'name' => [
            'en' => 'LimCH1',
        ],
        'gender' => Gender::MALE->value,
        'nric' => uniqid(),
        'passport_number' => '12348',
        'date_of_birth' => '1999-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
    ];

    $response = $this->libraryMemberService->createLibraryMember($payload)->toArray();

    expect($response)->toMatchArray([
        'type' => $payload['type'],
        'userable_type' => Employee::class,
        'userable_id' => $payload['employee_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name' => $payload['name'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ])->and(Carbon::parse($response['date_of_birth'])->format('Y-m-d'))->toBe($payload['date_of_birth'])
        ->and(Carbon::parse($response['register_date'])->format('Y-m-d'))->toBe($payload['register_date'])
        ->and(Carbon::parse($response['valid_from'])->format('Y-m-d'))->toBe($payload['valid_from'])
        ->and(Carbon::parse($response['valid_to'])->format('Y-m-d'))->toBe($payload['valid_to']);

    $this->assertDatabaseCount($this->table, 2);

    $this->assertDatabaseHas($this->table, [
        'type' => $payload['type'],
        'userable_type' => Employee::class,
        'userable_id' => $payload['employee_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name->en' => $payload['name']['en'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ]);

    //other
    $payload = [
        'type' => LibraryMemberType::OTHERS->value,
        'card_number' => '12345',
        'member_number' => '12347',
        'borrow_limit' => 10,
        'is_librarian' => false,
        'register_date' => now()->format('Y-m-d'),
        'valid_from' => now()->format('Y-m-d'),
        'valid_to' => now()->addYear()->format('Y-m-d'),
        'is_active' => true,
        'name' => [
            'en' => 'LimCH1',
        ],
        'gender' => Gender::MALE->value,
        'nric' => uniqid(),
        'passport_number' => '12349',
        'date_of_birth' => '1999-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
    ];

    $response = $this->libraryMemberService->createLibraryMember($payload)->toArray();

    expect($response)->toMatchArray([
        'type' => $payload['type'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name' => $payload['name'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ])->and(Carbon::parse($response['date_of_birth'])->format('Y-m-d'))->toBe($payload['date_of_birth'])
        ->and(Carbon::parse($response['register_date'])->format('Y-m-d'))->toBe($payload['register_date'])
        ->and(Carbon::parse($response['valid_from'])->format('Y-m-d'))->toBe($payload['valid_from'])
        ->and(Carbon::parse($response['valid_to'])->format('Y-m-d'))->toBe($payload['valid_to']);

    $this->assertDatabaseCount($this->table, 3);

    $this->assertDatabaseHas($this->table, [
        'type' => $payload['type'],
        'userable_type' => null,
        'userable_id' => null,
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name->en' => $payload['name']['en'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ]);
});

test('updateLibraryMember()', function () {
    $race = Race::factory()->create();
    $religion = Religion::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $student = Student::factory()->create();

    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT->value,
        'userable_id' => $student->id,
        'userable_type' => Student::class,
        'card_number' => '11111',
        'member_number' => '11111',
        'borrow_limit' => 9,
        'is_librarian' => true,
        'register_date' => now()->subDays(2)->format('Y-m-d'),
        'valid_from' => now()->subDays(2)->format('Y-m-d'),
        'valid_to' => now()->addYears(2)->format('Y-m-d'),
        'is_active' => false,
        'name->en' => 'LimHC0',
        'gender' => Gender::FEMALE->value,
        'nric' => uniqid(),
        'passport_number' => '12349',
        'date_of_birth' => '1998-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
    ]);

    Media::factory()->create([
        'model_type' => LibraryMember::class,
        'model_id' => $member_student->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount($this->mediaTable, 1);

    $payload = [
        'type' => LibraryMemberType::STUDENT->value,
        'student_id' => $student->id,
        'card_number' => '12345',
        'member_number' => '12347',
        'borrow_limit' => 10,
        'is_librarian' => false,
        'register_date' => now()->format('Y-m-d'),
        'valid_from' => now()->format('Y-m-d'),
        'valid_to' => now()->addYear()->format('Y-m-d'),
        'is_active' => true,
        'name' => [
            'en' => 'LimCH1',
            'zh' => 'LimCH2',
        ],
        'gender' => Gender::MALE->value,
        'nric' => uniqid(),
        'passport_number' => '12348',
        'date_of_birth' => '1999-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
        'photo' => UploadedFile::fake()->create('second_file.png', 500),
    ];

    $response = $this->libraryMemberService->updateLibraryMember($member_student->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'type' => $payload['type'],
        'userable_type' => Student::class,
        'userable_id' => $payload['student_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name' => $payload['name'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ])->and(Carbon::parse($response['date_of_birth'])->format('Y-m-d'))->toBe($payload['date_of_birth'])
        ->and(Carbon::parse($response['register_date'])->format('Y-m-d'))->toBe($payload['register_date'])
        ->and(Carbon::parse($response['valid_from'])->format('Y-m-d'))->toBe($payload['valid_from'])
        ->and(Carbon::parse($response['valid_to'])->format('Y-m-d'))->toBe($payload['valid_to']);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount($this->mediaTable, 1);

    $this->assertDatabaseHas($this->table, [
        'type' => $payload['type'],
        'userable_type' => Student::class,
        'userable_id' => $payload['student_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ]);

    $this->assertDatabaseMissing($this->mediaTable, [
        'model_type' => LibraryMember::class,
        'model_id' => $member_student->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => LibraryMember::class,
        'model_id' => $member_student->id,
        'file_name' => 'second_file.png',
        'collection_name' => 'photo'
    ]);

    //employee
    $employee = Employee::factory()->create();
    $member_employee = LibraryMember::factory()->create([
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee->id,
        'card_number' => '11111',
        'member_number' => '11111',
        'borrow_limit' => 9,
        'is_librarian' => true,
        'register_date' => now()->subDays(2)->format('Y-m-d'),
        'valid_from' => now()->subDays(2)->format('Y-m-d'),
        'valid_to' => now()->addYears(2)->format('Y-m-d'),
        'is_active' => false,
        'name->en' => 'LimHC0',
        'gender' => Gender::FEMALE->value,
        'nric' => uniqid(),
        'passport_number' => '12351',
        'date_of_birth' => '1998-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
    ]);

    $this->assertDatabaseCount($this->table, 2);

    $payload = [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'employee_id' => $employee->id,
        'card_number' => '12345',
        'member_number' => '12347',
        'borrow_limit' => 10,
        'is_librarian' => false,
        'passport_number' => '12351',
        'register_date' => now()->format('Y-m-d'),
        'valid_from' => now()->format('Y-m-d'),
        'valid_to' => now()->addYear()->format('Y-m-d'),
        'is_active' => true,
        'name' => [
            'en' => 'LimCH1',
            'zh' => 'LimCH2',
        ],
        'gender' => Gender::MALE->value,
        'nric' => uniqid(),
        'date_of_birth' => '1999-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
    ];

    $response = $this->libraryMemberService->updateLibraryMember($member_employee->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'type' => $payload['type'],
        'userable_type' => Employee::class,
        'userable_id' => $payload['employee_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name' => $payload['name'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ])->and(Carbon::parse($response['date_of_birth'])->format('Y-m-d'))->toBe($payload['date_of_birth'])
        ->and(Carbon::parse($response['register_date'])->format('Y-m-d'))->toBe($payload['register_date'])
        ->and(Carbon::parse($response['valid_from'])->format('Y-m-d'))->toBe($payload['valid_from'])
        ->and(Carbon::parse($response['valid_to'])->format('Y-m-d'))->toBe($payload['valid_to']);

    $this->assertDatabaseCount($this->table, 2);

    $this->assertDatabaseHas($this->table, [
        'type' => $payload['type'],
        'userable_type' => Employee::class,
        'userable_id' => $payload['employee_id'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name->en' => $payload['name']['en'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ]);

    //other
    $member_other = LibraryMember::factory()->create([
        'type' => LibraryMemberType::OTHERS->value,
        'userable_id' => null,
        'userable_type' => null,
        'card_number' => '11111',
        'member_number' => '11111',
        'borrow_limit' => 9,
        'is_librarian' => true,
        'register_date' => now()->subDays(2)->format('Y-m-d'),
        'valid_from' => now()->subDays(2)->format('Y-m-d'),
        'valid_to' => now()->addYears(2)->format('Y-m-d'),
        'is_active' => false,
        'name->en' => 'LimHC0',
        'gender' => Gender::FEMALE->value,
        'nric' => uniqid(),
        'passport_number' => '12352',
        'date_of_birth' => '1998-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
    ]);

    $this->assertDatabaseCount($this->table, 3);
    $payload = [
        'type' => LibraryMemberType::OTHERS->value,
        'card_number' => '12345',
        'member_number' => '12347',
        'borrow_limit' => 10,
        'is_librarian' => false,
        'register_date' => now()->format('Y-m-d'),
        'valid_from' => now()->format('Y-m-d'),
        'valid_to' => now()->addYear()->format('Y-m-d'),
        'is_active' => true,
        'name' => [
            'en' => 'LimCH1',
            'zh' => 'LimCH2',
        ],
        'gender' => Gender::MALE->value,
        'nric' => uniqid(),
        'passport_number' => '12353',
        'date_of_birth' => '1999-01-01',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => fake()->address,
        'postcode' => fake()->postcode,
        'city' => fake()->city,
        'state_id' => $state->id,
        'country_id' => $country->id,
        'phone_number' => fake()->phoneNumber,
        'email' => fake()->email,
    ];

    $response = $this->libraryMemberService->updateLibraryMember($member_other->id, $payload)->toArray();

    expect($response)->toMatchArray([
        'type' => $payload['type'],
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name' => $payload['name'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ])->and(Carbon::parse($response['date_of_birth'])->format('Y-m-d'))->toBe($payload['date_of_birth'])
        ->and(Carbon::parse($response['register_date'])->format('Y-m-d'))->toBe($payload['register_date'])
        ->and(Carbon::parse($response['valid_from'])->format('Y-m-d'))->toBe($payload['valid_from'])
        ->and(Carbon::parse($response['valid_to'])->format('Y-m-d'))->toBe($payload['valid_to']);

    $this->assertDatabaseCount($this->table, 3);

    $this->assertDatabaseHas($this->table, [
        'type' => $payload['type'],
        'userable_type' => null,
        'userable_id' => null,
        'card_number' => $payload['card_number'],
        'member_number' => $payload['member_number'],
        'borrow_limit' => $payload['borrow_limit'],
        'is_librarian' => $payload['is_librarian'],
        'is_active' => $payload['is_active'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postcode'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
    ]);

    //update with id not exist
    $this->expectException(ModelNotFoundException::class);
    $this->libraryMemberService->updateLibraryMember(9999, $payload)->toArray();
});


test('deleteLibraryMember()', function () {
    $member = LibraryMember::factory()->create();
    $other_members = LibraryMember::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->libraryMemberService->deleteLibraryMember($member->id);

    $this->assertDatabaseCount($this->table, 3);

    foreach ($other_members as $other_member) {
        $this->assertDatabaseHas($this->table, ['id' => $other_member->id]);
    }

    //id not exist
    $this->expectException(ModelNotFoundException::class);
    $this->libraryMemberService->deleteLibraryMember(9999);
});

test('deleteLibraryMember() with existing loans', function () {
    $member = LibraryMember::factory()->create();

    $loan = LibraryBookLoan::factory()->create([
        'member_id' => $member->id,
        'penalty_overdue_amount' => 0,
        'penalty_total_fine_amount' => 0
    ]);

    //delete failed
    $this->expectException(CannotDeleteModelException::class);
    $this->expectExceptionMessage('Unable to delete library member with existing loans.');
    $this->libraryMemberService->deleteLibraryMember($member->id);
});

test('findLibraryMember()', function () {
    $members = LibraryMember::factory(3)->create();

    $response = $this->libraryMemberService->findLibraryMember($members[0]->id)->toArray();

    expect($response)->toMatchArray($members[0]->toArray());
});

test('transformToUserableIdAndType()', function () {

    // guardian
    $data = [
        'type' => LibraryMemberType::GUARDIAN->value,
        'guardian_id' => 3
    ];

    $this->libraryMemberService->transformToUserableIdAndType($data);

    expect($data)
        ->toMatchArray([
            'userable_type' => Guardian::class,
            'userable_id' => 3
        ]);

    // student
    $data = [
        'type' => LibraryMemberType::STUDENT->value,
        'student_id' => 4
    ];

    $this->libraryMemberService->transformToUserableIdAndType($data);

    expect($data)
        ->toMatchArray([
            'userable_type' => Student::class,
            'userable_id' => 4
        ]);

    // employee
    $data = [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'employee_id' => 10
    ];

    $this->libraryMemberService->transformToUserableIdAndType($data);

    expect($data)
        ->toMatchArray([
            'userable_type' => Employee::class,
            'userable_id' => 10
        ]);
});
