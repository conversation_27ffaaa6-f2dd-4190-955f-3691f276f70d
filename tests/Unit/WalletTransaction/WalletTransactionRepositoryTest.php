<?php

use App\Enums\TerminalType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Models\Card;
use App\Models\EcommerceOrder;
use App\Models\Guardian;
use App\Models\Student;
use App\Models\Terminal;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Repositories\WalletTransactionRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);

    $this->walletTransactionRepository = app(WalletTransactionRepository::class);

    $this->table = resolve(WalletTransaction::class)->getTable();

    $this->student = Student::factory()->create([
        'name' => 'Student 1',
        'email' => '<EMAIL>',
        'phone_number' => '+6012345678'
    ]);
    $this->guardian = Guardian::factory()->create([
        'name' => 'Guardian 1',
        'email' => '<EMAIL>',
        'phone_number' => '+6012345679'
    ]);

    app()->setLocale('en');
});

test('getModelClass()', function () {
    $response = $this->walletTransactionRepository->getModelClass();

    expect($response)->toEqual(WalletTransaction::class);
});

test('getAll()', function () {
    $wallet_transactions = WalletTransaction::factory(3)->create();

    $response = $this->walletTransactionRepository->getAll()->toArray();

    expect($response)->toEqual($wallet_transactions->toArray());
});

test('getAllPaginated()', function () {
    $wallet_transactions = WalletTransaction::factory(3)
        ->state(new Sequence(
            [
                'status' => WalletTransactionStatus::PENDING,
            ],
            [
                'status' => WalletTransactionStatus::PENDING,
            ],
            [
                'status' => WalletTransactionStatus::SUCCESS,
            ]
        ))
        ->create();

    $response = $this->walletTransactionRepository->getAllPaginated(['order_by' => 'id'])->toArray();

    expect($response['data'])->toEqual($wallet_transactions->toArray());

    // Test if relationship is loaded
    $response = $this->walletTransactionRepository->getAllPaginated();
    expect($response->first()->relationLoaded('wallet'))->toBeFalse();

    $response = $this->walletTransactionRepository->getAllPaginated(['with' => ['wallet']]);
    expect($response->first()->relationLoaded('wallet'))->toBeTrue();

    //filter by status
    $response = $this->walletTransactionRepository->getAllPaginated([
        'status' => WalletTransactionStatus::SUCCESS->value,
        'order_by' => 'id'
    ])->toArray();

    expect($response['data'])->toHaveCount(1)
        ->toHaveKey('0.status', WalletTransactionStatus::SUCCESS->value);
});

test('getAllPaginated() : with full filter and sorting', function (int $expected_count, mixed $filter_by, mixed $filter_value, array $expected_model) {

    $card_1 = Card::factory()->create([
        'card_number' => '1111111111'
    ]);

    $card_2 = Card::factory()->create([
        'card_number' => '222222222222222'
    ]);

    $card_3 = Card::factory()->create([
        'card_number' => '3333333333'
    ]);

    $wallet_transactions = [
        'first' => WalletTransaction::factory()->create([
            'card_id' => $card_1->id,
            'reference_no' => '222',
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
            'userable_type' => get_class($this->student),
            'userable_id' => $this->student->id,
            'created_at' => '2024-01-01 00:00:00',
        ]),
        'second' => WalletTransaction::factory()->create([
            'card_id' => $card_3->id,
            'reference_no' => '111',
            'type' => WalletTransactionType::DEPOSIT->value,
            'status' => WalletTransactionStatus::FAILED->value,
            'userable_type' => get_class($this->student),
            'userable_id' => $this->student->id,
            'created_at' => '2024-02-01 00:00:00',
        ]),
        'third' => WalletTransaction::factory()->create([
            'card_id' => $card_2->id,
            'reference_no' => '333',
            'userable_type' => get_class($this->guardian),
            'userable_id' => $this->guardian->id,
            'type' => WalletTransactionType::TRANSFER->value,
            'status' => WalletTransactionStatus::PENDING->value,
            'created_at' => '2024-03-01 00:00:00',
        ]),
    ];

    $payload = [];

    if (isset($filter_by) && isset($filter_value)) {
        if (is_array($filter_by)) {
            foreach ($filter_by as $key => $value) {
                $payload[$value] = $filter_value[$key];
            }
        } else {
            $payload = [
                $filter_by => $filter_value
            ];
        }

    }

    $result = $this->walletTransactionRepository->getAllPaginated($payload)->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toMatchArray($wallet_transactions[$value]->toArray());
    }
})->with([
    'no filter' => [3, null, null, ['first', 'second', 'third']],
    'filter by reference_no = 111' => [1, 'reference_no', '111', ['second']],
    'filter by reference_no = 222' => [1, 'reference_no', '222', ['first']],
    'filter by reference_no = 333' => [1, 'reference_no', '333', ['third']],
    'filter by reference_no = no-exist' => [0, 'reference_no', 'no-exist', []],
    'filter by card_number = 1111111111' => [1, 'card_number', '1111111111', ['first']],
    'filter by card_number = no-exist' => [0, 'card_number', 'no-exist', []],
    'filter by type = TRANSACTION' => [1, 'type', WalletTransactionType::TRANSACTION->value, ['first']],
    'filter by type = DEPOSIT' => [1, 'type', WalletTransactionType::DEPOSIT->value, ['second']],
    'filter by type = TRANSFER' => [1, 'type', WalletTransactionType::TRANSFER->value, ['third']],
    'filter by status = SUCCESS' => [1, 'status', WalletTransactionStatus::SUCCESS->value, ['first']],
    'filter by status = FAILED' => [1, 'status', WalletTransactionStatus::FAILED->value, ['second']],
    'filter by status = PENDING' => [1, 'status', WalletTransactionStatus::PENDING->value, ['third']],
    'filter by type = DEPOSIT,TRANSACTION' => [2, 'type', [WalletTransactionType::DEPOSIT->value, WalletTransactionType::TRANSACTION->value], ['first', 'second']],
    'filter by transaction_from' => [2, 'transaction_from', '2024-02-01', ['second', 'third']],
    'filter by transaction_to' => [1, 'transaction_to', '2024-01-01', ['first']],
    'filter by user_name' => [1, 'user_name', 'Guardian 1', ['third']],
    'filter by user_email' => [1, 'user_email', '<EMAIL>', ['third']],
    'filter by user_phone_number' => [1, 'user_phone_number', '+6012345679', ['third']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by card.card_number asc' => [3, 'order_by', ['card' => ['card_number' => 'asc']], ['first', 'third', 'second']],
    'sort by card.card_number desc' => [3, 'order_by', ['card' => ['card_number' => 'desc']], ['second', 'third', 'first']],
    'sort by reference_no asc' => [3, 'order_by', ['reference_no' => 'asc'], ['second', 'first', 'third']],
    'sort by reference_no desc' => [3, 'order_by', ['reference_no' => 'desc'], ['third', 'first', 'second']],
    'sort by type asc' => [3, 'order_by', ['type' => 'asc'], ['second', 'first', 'third']],
    'sort by type desc' => [3, 'order_by', ['type' => 'desc'], ['third', 'first', 'second']],
    'sort by status asc' => [3, 'order_by', ['status' => 'asc'], ['second', 'third', 'first']],
    'sort by status desc' => [3, 'order_by', ['status' => 'desc'], ['first', 'third', 'second']],

]);

test('getAllPaginated() : filter by wallet transactable type and wallet transactable id', function () {

    $card_1 = Card::factory()->create([
        'card_number' => '1111111111'
    ]);

    $card_2 = Card::factory()->create([
        'card_number' => '222222222222222'
    ]);

    $card_3 = Card::factory()->create([
        'card_number' => '3333333333'
    ]);

    $order = EcommerceOrder::factory()->create();

    $wallet_transactions = [
        'first' => WalletTransaction::factory()->create([
            'card_id' => $card_1->id,
            'reference_no' => '222',
            'type' => WalletTransactionType::TRANSACTION->value,
            'status' => WalletTransactionStatus::SUCCESS->value,
            'userable_id' => $this->guardian->id,
            'userable_type' => Guardian::class,
            'wallet_transactable_type' => EcommerceOrder::class,
            'wallet_transactable_id' => $order->id,
            'created_at' => '2024-01-01 00:00:00',
        ]),
        'second' => WalletTransaction::factory()->create([
            'card_id' => $card_3->id,
            'reference_no' => '111',
            'type' => WalletTransactionType::DEPOSIT->value,
            'status' => WalletTransactionStatus::FAILED->value,
            'userable_id' => $this->guardian->id,
            'userable_type' => Guardian::class,
            'created_at' => '2024-02-01 00:00:00',
        ]),
        'third' => WalletTransaction::factory()->create([
            'card_id' => $card_2->id,
            'reference_no' => '333',
            'type' => WalletTransactionType::TRANSFER->value,
            'status' => WalletTransactionStatus::PENDING->value,
            'userable_id' => $this->student->id,
            'userable_type' => Student::class,
            'created_at' => '2024-03-01 00:00:00',
        ]),
    ];

    $result = $this->walletTransactionRepository->getAllPaginated([
        'wallet_transactable_type' => EcommerceOrder::class,
        'wallet_transactable_id' => $order->id,
    ])->toArray();

    expect($result['data'])->toHaveCount(1)
        ->and($result['data'][0])->toMatchArray($wallet_transactions['first']->toArray());
});

test('getWalletTransactionByReferenceNo()', function () {
    $wallet_transaction = WalletTransaction::factory()->create();
    $second_wallet_transaction = WalletTransaction::factory()->create();

    $response = $this->walletTransactionRepository->getWalletTransactionByReferenceNo($wallet_transaction->reference_no);

    expect($response->toArray())->toEqual($wallet_transaction->toArray());

    $response = $this->walletTransactionRepository->getWalletTransactionByReferenceNo($second_wallet_transaction->reference_no);

    expect($response->toArray())->toEqual($second_wallet_transaction->toArray());
});

test('getDailyWalletTransactionsFromTerminal()', function () {
    $terminals = Terminal::factory(3)->state(new Sequence(
        [
            'name' => 'Canteen Level 01 Cashier 01',
            'code' => 'CL0101',
            'type' => TerminalType::CANTEEN->value,
        ],
        [
            'name' => 'Canteen Level 01 Cashier 02',
            'code' => 'CL0102',
            'type' => TerminalType::CANTEEN->value,
        ],
        [
            'name' => 'Bookshop Level 01 Cashier 01',
            'code' => 'BL0101',
            'type' => TerminalType::BOOKSHOP->value,
        ],
    ))->create();

    $time_1_MYT = Carbon::parse('2024-10-30 07:40:00', config('school.timezone'));
    $time_2_MYT = Carbon::parse('2024-10-30 08:40:00', config('school.timezone'));
    $time_3_MYT = Carbon::parse('2024-10-29 10:40:00', config('school.timezone'));
    $time_4_MYT = Carbon::parse('2024-10-28 09:40:00', config('school.timezone'));

    $wallet_transactions = WalletTransaction::factory(7)->state(new Sequence(
    // 2024-10-30 CANTEEN
        [
            'wallet_transactable_type' => get_class($terminals[0]),
            'wallet_transactable_id' => $terminals[0]->id,
            'type' => WalletTransactionType::TRANSACTION,
            'status' => WalletTransactionStatus::SUCCESS,
            'total_amount' => -10,
            'created_at' => $time_1_MYT->setTimezone('UTC')->toDateTimeString(), // 2024-10-30 07:40:00 MYT
        ],
        [
            'wallet_transactable_type' => get_class($terminals[1]),
            'wallet_transactable_id' => $terminals[1]->id,
            'type' => WalletTransactionType::TRANSACTION,
            'status' => WalletTransactionStatus::SUCCESS,
            'total_amount' => -7,
            'created_at' => $time_2_MYT->setTimezone('UTC')->toDateTimeString(), // 2024-10-30 08:40:00 MYT
        ],
        // 2024-10-29 CANTEEN
        [
            'wallet_transactable_type' => get_class($terminals[0]),
            'wallet_transactable_id' => $terminals[0]->id,
            'type' => WalletTransactionType::TRANSACTION,
            'status' => WalletTransactionStatus::SUCCESS,
            'total_amount' => -11,
            'created_at' => $time_3_MYT->setTimezone('UTC')->toDateTimeString(), // 2024-10-29 10:40:00 MYT
        ],
        [
            'wallet_transactable_type' => get_class($terminals[1]),
            'wallet_transactable_id' => $terminals[1]->id,
            'type' => WalletTransactionType::TRANSACTION,
            'status' => WalletTransactionStatus::SUCCESS,
            'total_amount' => -2,
            'created_at' => $time_3_MYT->setTimezone('UTC')->toDateTimeString(), // 2024-10-29 10:40:00 MYT
        ],
        // 2024-10-28 CANTEEN
        [
            'wallet_transactable_type' => get_class($terminals[1]),
            'wallet_transactable_id' => $terminals[1]->id,
            'type' => WalletTransactionType::TRANSACTION,
            'status' => WalletTransactionStatus::SUCCESS,
            'total_amount' => -2,
            'created_at' => $time_4_MYT->setTimezone('UTC')->toDateTimeString(), // 2024-10-28 09:40:00 MYT
        ],
        // unrelated wallet_transactable_type = Wallet::class
        [
            'wallet_transactable_type' => Wallet::class,
            'wallet_transactable_id' => $terminals[1]->id,
            'type' => WalletTransactionType::TRANSACTION, //
            'status' => WalletTransactionStatus::SUCCESS,
            'total_amount' => -7,
            'created_at' => $time_2_MYT->setTimezone('UTC')->toDateTimeString(), // 2024-10-30 08:40:00 MYT
        ],
        // BOOKSHOP will not be included during CANTEEN TYPE filter & vice versa
        [
            'wallet_transactable_type' => get_class($terminals[2]), // BOOKSHOP
            'wallet_transactable_id' => $terminals[2]->id, // BOOKSHOP
            'type' => WalletTransactionType::TRANSACTION,
            'status' => WalletTransactionStatus::SUCCESS,
            'total_amount' => -200,
            'created_at' => $time_4_MYT->setTimezone('UTC')->toDateTimeString(), // 2024-10-28 09:40:00 MYT
        ],
    ))->create();

    /**
     * 2024-10-30  TerminalType::CANTEEN
     */
    $payload = [
        'type' => TerminalType::CANTEEN->value,
        'start_date' => '2024-10-30',
        'end_date' => '2024-10-30',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->toEqual([
            [
                'transaction_date' => '2024-10-30',
                'total_amount' => $wallet_transactions[0]->total_amount + $wallet_transactions[1]->total_amount,
            ],
        ]);

    /**
     * 2024-10-29  TerminalType::CANTEEN
     */
    $payload = [
        'type' => TerminalType::CANTEEN->value,
        'start_date' => '2024-10-29',
        'end_date' => '2024-10-29',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->toEqual([
            [
                'transaction_date' => '2024-10-29',
                'total_amount' => $wallet_transactions[2]->total_amount + $wallet_transactions[3]->total_amount,
            ],
        ]);

    /**
     * 2024-10-28  TerminalType::CANTEEN
     */
    $payload = [
        'type' => TerminalType::CANTEEN->value,
        'start_date' => '2024-10-28',
        'end_date' => '2024-10-28',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->toEqual([
            [
                'transaction_date' => '2024-10-28',
                'total_amount' => $wallet_transactions[4]->total_amount,
            ],
        ]);

    /**
     * 2024-10-28 - 2024-10-30   TerminalType::CANTEEN
     */
    $payload = [
        'type' => TerminalType::CANTEEN->value,
        'start_date' => '2024-10-28',
        'end_date' => '2024-10-30',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->toEqual([
            [
                'transaction_date' => '2024-10-30',
                'total_amount' => $wallet_transactions[0]->total_amount + $wallet_transactions[1]->total_amount,
            ],
            [
                'transaction_date' => '2024-10-29',
                'total_amount' => $wallet_transactions[2]->total_amount + $wallet_transactions[3]->total_amount,
            ],
            [
                'transaction_date' => '2024-10-28',
                'total_amount' => $wallet_transactions[4]->total_amount,
            ],
        ]);

    /**
     * filtering by terminal_ids :: $terminals[0]->id
     * 2024-10-28 - 2024-10-30   TerminalType::CANTEEN
     */
    $payload = [
        'type' => TerminalType::CANTEEN->value,
        'terminal_ids' => [
            $terminals[0]->id
        ],
        'start_date' => '2024-10-28',
        'end_date' => '2024-10-30',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(2)
        ->toEqual([
            [
                'transaction_date' => '2024-10-30',
                'total_amount' => $wallet_transactions[0]->total_amount,
            ],
            [
                'transaction_date' => '2024-10-29',
                'total_amount' => $wallet_transactions[2]->total_amount,
            ],
        ]);

    /**
     * filtering by terminal_ids :: $terminals[1]->id
     * 2024-10-28 - 2024-10-30   TerminalType::CANTEEN
     */
    $payload = [
        'type' => TerminalType::CANTEEN->value,
        'terminal_ids' => [
            $terminals[1]->id
        ],
        'start_date' => '2024-10-28',
        'end_date' => '2024-10-30',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->toEqual([
            [
                'transaction_date' => '2024-10-30',
                'total_amount' => $wallet_transactions[1]->total_amount,
            ],
            [
                'transaction_date' => '2024-10-29',
                'total_amount' => $wallet_transactions[3]->total_amount,
            ],
            [
                'transaction_date' => '2024-10-28',
                'total_amount' => $wallet_transactions[4]->total_amount,
            ],
        ]);


    /**
     * filtering by terminal_ids :: all terminals
     * 2024-10-28 - 2024-10-30   TerminalType::CANTEEN
     */
    $payload = [
        'type' => TerminalType::CANTEEN->value,
        'terminal_ids' => [
            $terminals[0]->id,
            $terminals[1]->id,
        ],
        'start_date' => '2024-10-28',
        'end_date' => '2024-10-30',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(3)
        ->toEqual([
            [
                'transaction_date' => '2024-10-30',
                'total_amount' => $wallet_transactions[0]->total_amount + $wallet_transactions[1]->total_amount,
            ],
            [
                'transaction_date' => '2024-10-29',
                'total_amount' => $wallet_transactions[2]->total_amount + $wallet_transactions[3]->total_amount,
            ],
            [
                'transaction_date' => '2024-10-28',
                'total_amount' => $wallet_transactions[4]->total_amount,
            ],
        ]);


    /**
     * 2024-10-28 - 2024-10-30   TerminalType::BOOKSHOP
     */
    $payload = [
        'type' => TerminalType::BOOKSHOP->value,
        'start_date' => '2024-10-28',
        'end_date' => '2024-10-30',
    ];

    $response = $this->walletTransactionRepository->getDailyWalletTransactionsFromTerminal($payload)->toArray();

    expect($response)->toHaveCount(1)
        ->toEqual([
            [
                'transaction_date' => '2024-10-28',
                'total_amount' => $wallet_transactions[6]->total_amount,
            ]
        ]);
});
