<?php

use App\Models\PosTerminalKey;
use App\Models\Terminal;
use App\Services\PosTerminalKeyService;

beforeEach(function () {
    $this->posTerminalKeyService = app(PosTerminalKeyService::class);

    $this->tableName = resolve(PosTerminalKey::class)->getTable();
});

test('getAllPaginatedPosTerminalKeys()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $keys = [
        'first' => PosTerminalKey::factory()->create(['name' => 'Key 1']),
        'second' => PosTerminalKey::factory()->create(['name' => 'Kunci']),
        'third' => PosTerminalKey::factory()->create(['name' => 'AA Terminal Key']),
    ];

    $result = $this->posTerminalKeyService->getAllPaginatedPosTerminalKeys([$filter_by => $filter_value])->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'filter by name = Key 1' => [1, 'name', 'Key 1', ['first']],
    'filter by name = Kunci' => [1, 'name', 'Kunci', ['second']],
    'filter by name = Non Existing' => [0, 'name', 'Non Existing', []],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by name asc' => [3, 'order_by', ['name' => 'asc'], ['third', 'first', 'second']],
    'sort by name desc' => [3, 'order_by', ['name' => 'desc'], ['second', 'first', 'third']],
]);

test('createPosTerminalKey()', function () {
    $this->assertDatabaseCount($this->tableName, 0);

    $current_datetime = now();

    $terminal = Terminal::factory()->create();

    $payload = [
        'name' => 'Kunci 1',
        'terminal_id' => $terminal->id,
        'expires_at' => $current_datetime, // forever
    ];

    $response = $this->posTerminalKeyService->createPosTerminalKey($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'terminal_id' => $payload['terminal_id'],
        'expires_at' => $payload['expires_at'],
    ]);

    $this->assertDatabaseCount($this->tableName, 1);

    $created = PosTerminalKey::first();

    $this->assertDatabaseHas($this->tableName, [
        'name' => $payload['name'],
        'terminal_id' => $payload['terminal_id'],
        'secret' => $created->secret,
        'expires_at' => $payload['expires_at'],
    ]);
});

test('updatePosTerminalKey()', function () {
    $terminal = Terminal::factory()->create();

    $pos_terminal_key = PosTerminalKey::factory()->create([
        'terminal_id' => $terminal->id,
        'name' => 'key 1',
        'secret' => 'secret',
        'expires_at' => now()->subYear()->toDateString(),
    ]);

    $this->assertDatabaseCount($this->tableName, 1);

    $payload = [
        'terminal_id' => $terminal->id,
        'name' => 'KEY 1',
        'expires_at' => null, // forever
    ];

    $response = $this->posTerminalKeyService->updatePosTerminalKey($pos_terminal_key, $payload)->toArray();

    $pos_terminal_key->refresh();

    expect($response)->toMatchArray([
        'terminal_id' => $payload['terminal_id'],
        'name' => $payload['name'],
        'secret' => $pos_terminal_key->secret,
        'expires_at' => $payload['expires_at'],
    ]);

    $this->assertDatabaseCount($this->tableName, 1);

    $this->assertDatabaseHas($this->tableName, [
        'terminal_id' => $payload['terminal_id'],
        'name' => $payload['name'],
        'secret' => $pos_terminal_key->secret,
        'expires_at' => $payload['expires_at'],
    ]);

    $this->assertDatabaseMissing($this->tableName, [
        'name' => 'key 1',
        'secret' => 'secret',
        'expires_at' => now()->subYear()->toDateString(),
    ]);
});

test('updatePosTerminalKey(): regenerate', function () {
    $pos_terminal_key = PosTerminalKey::factory()->create([
        'name' => 'key 1',
        'secret' => 'secret',
        'expires_at' => null,
    ]);

    $this->assertDatabaseCount($this->tableName, 1);

    $response = $this->posTerminalKeyService->updatePosTerminalKey($pos_terminal_key, ['regenerate' => true])->toArray();

    $pos_terminal_key->refresh();

    expect($response)->toMatchArray([
        'id' => $pos_terminal_key->id,
        'name' => $pos_terminal_key->name,
        'secret' => $pos_terminal_key->secret,
        'expires_at' => null,
    ]);

    $this->assertDatabaseCount($this->tableName, 1);

    $this->assertDatabaseHas($this->tableName, [
        'name' => $pos_terminal_key->name,
        'secret' => $pos_terminal_key->secret,
        'expires_at' => null,
    ]);

    $this->assertDatabaseMissing($this->tableName, [
        'name' => 'key 1',
        'secret' => 'secret',
        'expires_at' => null,
    ]);
});

test('deletePosTerminalKey()', function () {
    $first_pos_terminal_key = PosTerminalKey::factory()->create();
    $other_pos_terminal_keys = PosTerminalKey::factory(3)->create();

    $this->assertDatabaseCount($this->tableName, 4);

    //delete success
    $this->posTerminalKeyService->deletePosTerminalKey($first_pos_terminal_key);

    $this->assertDatabaseCount($this->tableName, 3);

    $this->assertDatabaseMissing($this->tableName, ['id' => $first_pos_terminal_key->id]);

    foreach ($other_pos_terminal_keys as $other_pos_terminal_key) {
        $this->assertDatabaseHas($this->tableName, ['id' => $other_pos_terminal_key->id]);
    }
});
