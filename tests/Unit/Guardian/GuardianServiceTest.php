<?php

use App\Models\Country;
use App\Models\Guardian;
use App\Models\Race;
use App\Models\Religion;
use App\Models\User;
use App\Repositories\GuardianRepository;
use App\Services\GuardianService;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Mockery\MockInterface;

beforeEach(function () {
    $this->guardianService = app(GuardianService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Guardian::class)->getTable();
    $this->user_table = resolve(User::class)->getTable();
});

test('getAllPaginatedGuardians()', function () {
    $guardians = Guardian::factory(3)->state(new Sequence(
        [
            'name->en' => 'John Doe',
            'nric' => '1234567890',
            'email' => '<EMAIL>',
            'phone_number' => '+6011111111',
        ],
        [
            'name->en' => '<PERSON>',
            'nric' => '0987654321',
            'email' => '<EMAIL>',
            'phone_number' => '+60122222222',
        ],
        [
            'name->en' => 'Bob Doe',
            'nric' => '9876543210',
            'email' => '<EMAIL>',
            'phone_number' => '+60133333333',
        ]
    ))->create();

    $this->mock(GuardianRepository::class, function (MockInterface $mock) use ($guardians) {
        $mock->shouldReceive('getAllPaginated')->once()->andReturn(new LengthAwarePaginator($guardians, 3, 1));
    });
    //Filter by name = John Doe
    $payload = [
        'name' => 'Doe'
    ];

    app(GuardianService::class)->getAllPaginatedGuardians($payload)->toArray();
});


test('getAllGuardians()', function () {
    $guardians = Guardian::factory(3)->state(new Sequence(
        [
            'name->en' => 'John Doe',
            'nric' => '1234567890',
            'email' => '<EMAIL>',
            'phone_number' => '+6011111111',
        ],
        [
            'name->en' => 'Jane Doe',
            'nric' => '0987654321',
            'email' => '<EMAIL>',
            'phone_number' => '+60122222222',
        ],
        [
            'name->en' => 'Bob Doe',
            'nric' => '9876543210',
            'email' => '<EMAIL>',
            'phone_number' => '+60133333333',
        ]
    ))->create();

    $this->mock(GuardianRepository::class, function (MockInterface $mock) use ($guardians) {
        $mock->shouldReceive('getAll')->once()->andReturn($guardians);
    });
    //Filter by name = John Doe
    $payload = [
        'name' => 'Doe'
    ];

    app(GuardianService::class)->getAllGuardians($payload)->toArray();
});


test('findGuardianById()', function () {
    $guardians = Guardian::factory(2)->create();

    $response = $this->guardianService->findGuardianById($guardians[0]->id)->toArray();

    expect($response['id'])->toBe($guardians[0]->id);
});

test('createGuardian()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $country = Country::factory()->create();
    $race = Race::factory()->create();
    $religion = Religion::factory()->create();

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'nric' => uniqid(),
        'passport_number' => uniqid(),
        'email' => fake()->email,
        'nationality_id' => $country->id,
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'remarks' => fake()->sentence,
    ];

    $response = $this->guardianService->createGuardian($payload)->toArray();

    expect($response)->toMatchArray([
        'name' => $payload['name'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'email' => $payload['email'],
        'nationality_id' => $payload['nationality_id'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'remarks' => $payload['remarks'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'email' => $payload['email'],
        'nationality_id' => $payload['nationality_id'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'remarks' => $payload['remarks'],
    ]);
});

test('updateGuardian()', function () {
    $country = Country::factory()->create();
    $race = Race::factory()->create();
    $religion = Religion::factory()->create();

    $guardian = Guardian::factory()->create([
        'name->en' => 'Test EN',
        'name->zh' => 'Test ZH',
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Test 2',
            'zh' => 'Test 3',
        ],
        'nric' => uniqid(),
        'passport_number' => uniqid(),
        'email' => fake()->email,
        'nationality_id' => $country->id,
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'remarks' => fake()->sentence,
    ];

    $response = $this->guardianService->updateGuardian($guardian, $payload)->toArray();

    expect($response)->toMatchArray([
        'id' => $guardian->id,
        'name' => $payload['name'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'email' => $payload['email'],
        'nationality_id' => $payload['nationality_id'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'remarks' => $payload['remarks'],
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'email' => $payload['email'],
        'nationality_id' => $payload['nationality_id'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'remarks' => $payload['remarks'],
    ]);
});

test('unlinkUser()', function () {
    $user = User::factory()->create();
    $guardian = Guardian::factory()->create([
        'user_id' => $user->id,
    ]);

    $this->guardianService->unlinkUser($guardian);

    $this->assertDatabaseHas($this->table, [
        'id' => $guardian->id,
        'user_id' => null,
    ]);
});
