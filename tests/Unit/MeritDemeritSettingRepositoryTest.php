<?php

use App\Enums\MeritDemeritType;
use App\Models\MeritDemeritSetting;
use App\Repositories\MeritDemeritSettingRepository;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->meritDemeritSettingRepository = resolve(MeritDemeritSettingRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
});

test('getModelClass()', function () {
    $response = $this->meritDemeritSettingRepository->getModelClass();

    expect($response)->toEqual(MeritDemeritSetting::class);
});

test('getAll()', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Bring Phone',
        'name->zh' => 'zh Bring Phone',
        'type' => MeritDemeritType::MERIT->value,
    ]);

    $second_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Bring Laptop',
        'name->zh' => 'zh Bring Laptop',
        'type' => MeritDemeritType::MERIT->value,
    ]);

    $third_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Loiter',
        'name->zh' => 'zh Loiter',
        'type' => MeritDemeritType::DEMERIT->value,
    ]);

    // Filter by name = Bring Phone
    $response = $this->meritDemeritSettingRepository->getAll(['name' => 'Bring Phone'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$first_merit_demerit_setting->toArray()]);

    // Filter by name = Bring Laptop
    $response = $this->meritDemeritSettingRepository->getAll(['name' => 'Bring Laptop'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$second_merit_demerit_setting->toArray()]);

    // Filter by partial name = Bring
    $response = $this->meritDemeritSettingRepository->getAll(['name' => 'Bring'])->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toHaveKey('id', $first_merit_demerit_setting->id),
            fn($item) => $item->toHaveKey('id', $second_merit_demerit_setting->id),
        );

    // Filter by non-existing name = Non existing
    $response = $this->meritDemeritSettingRepository->getAll(['name' => 'Non existing'])->toArray();

    expect($response)->toBeEmpty();

    // Filter by type = MeritDemeritType::DEMERIT->value
    $response = $this->meritDemeritSettingRepository->getAll(['type' => MeritDemeritType::DEMERIT->value])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$third_merit_demerit_setting->toArray()]);

    // Filter by type = MeritDemeritType::MERIT->value
    $response = $this->meritDemeritSettingRepository->getAll(['type' => MeritDemeritType::MERIT->value])->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toHaveKey('id', $first_merit_demerit_setting->id),
            fn($item) => $item->toHaveKey('id', $second_merit_demerit_setting->id),
        );

    // Sort by name asc
    $response = $this->meritDemeritSettingRepository->getAll([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Laptop'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Phone'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Loiter'),
    );

    // Sort by name desc
    $response = $this->meritDemeritSettingRepository->getAll([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Loiter'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Phone'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Laptop'),
    );

    // Sort by id asc
    $response = $this->meritDemeritSettingRepository->getAll([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($response) => $response->toHaveKey('id', $first_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $second_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $third_merit_demerit_setting->id),
    );

    // Sort by id desc
    $response = $this->meritDemeritSettingRepository->getAll([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($response) => $response->toHaveKey('id', $third_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $second_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $first_merit_demerit_setting->id),
    );
});


test('getAllPaginated()', function () {
    $first_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Bring Phone',
        'name->zh' => 'zh Bring Phone',
        'type' => MeritDemeritType::MERIT->value,
    ]);

    $second_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Bring Laptop',
        'name->zh' => 'zh Bring Laptop',
        'type' => MeritDemeritType::MERIT->value,
    ]);

    $third_merit_demerit_setting = MeritDemeritSetting::factory()->create([
        'name->en' => 'Loiter',
        'name->zh' => 'zh Loiter',
        'type' => MeritDemeritType::DEMERIT->value,
    ]);

    // Filter by name = Bring Phone
    $response = $this->meritDemeritSettingRepository->getAllPaginated(['name' => 'Bring Phone'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_merit_demerit_setting->toArray()]);

    // Filter by name = Bring Laptop
    $response = $this->meritDemeritSettingRepository->getAllPaginated(['name' => 'Bring Laptop'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$second_merit_demerit_setting->toArray()]);

    // Filter by partial name = Bring
    $response = $this->meritDemeritSettingRepository->getAllPaginated(['name' => 'Bring'])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toHaveKey('id', $first_merit_demerit_setting->id),
            fn($item) => $item->toHaveKey('id', $second_merit_demerit_setting->id),
        );

    // Filter by non-existing name = Non existing
    $response = $this->meritDemeritSettingRepository->getAllPaginated(['name' => 'Non existing'])->toArray();

    expect($response['data'])->toBeEmpty();

    // Filter by type = MeritDemeritType::DEMERIT->value
    $response = $this->meritDemeritSettingRepository->getAllPaginated(['type' => MeritDemeritType::DEMERIT->value])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$third_merit_demerit_setting->toArray()]);

    // Filter by type = MeritDemeritType::MERIT->value
    $response = $this->meritDemeritSettingRepository->getAllPaginated(['type' => MeritDemeritType::MERIT->value])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toHaveKey('id', $first_merit_demerit_setting->id),
            fn($item) => $item->toHaveKey('id', $second_merit_demerit_setting->id),
        );

    // Sort by name asc
    $response = $this->meritDemeritSettingRepository->getAllPaginated([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Laptop'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Phone'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Loiter'),
    );

    // Sort by name desc
    $response = $this->meritDemeritSettingRepository->getAllPaginated([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Loiter'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Phone'),
        fn($response) => $response->toHaveKey('name.'.$this->testLocale, 'Bring Laptop'),
    );

    // Sort by id asc
    $response = $this->meritDemeritSettingRepository->getAllPaginated([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('id', $first_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $second_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $third_merit_demerit_setting->id),
    );

    // Sort by id desc
    $response = $this->meritDemeritSettingRepository->getAllPaginated([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('id', $third_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $second_merit_demerit_setting->id),
        fn($response) => $response->toHaveKey('id', $first_merit_demerit_setting->id),
    );
});
