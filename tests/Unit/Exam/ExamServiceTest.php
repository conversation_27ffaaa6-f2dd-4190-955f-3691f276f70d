<?php

use App\Models\Exam;
use App\Models\ResultSource;
use App\Models\ResultSourceExam;
use App\Services\Exam\ExamService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();
    $this->examService = app(ExamService::class);

    $this->table = resolve(Exam::class)->getTable();
});

test('getAllPaginatedExams()', function (int $expected_count, array $filters, array $expected_model) {
    $test_date = Carbon::parse('2024-05-01')->format('Y-m-d');
    Carbon::setTestNow($test_date);

    $keys = [
        'first' => Exam::factory()->create([
            'code' => 'A001',
            'name->en' => 'English Exam 1',
            'name->zh' => '英语考试1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'This is the last exam'
        ]),
        'second' => Exam::factory()->create([
            'code' => 'A002',
            'name->en' => 'English Exam 2',
            'name->zh' => '英语考试2',
            'start_date' => Carbon::parse('2024-10-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-11-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-10-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-11-15 15:59:59'),
            'description' => 'This is the last last exam'
        ]),
        'third' => Exam::factory()->create([
            'code' => 'B003',
            'name->en' => 'Math Exam 1',
            'name->zh' => '数学考试 1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'For real this time'
        ]),
    ];

    $result = $this->examService->getAllPaginatedExams($filters)->toArray();
    expect($result['data'])->toHaveCount($expected_count);


    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }

})->with([
    'filter by name = English Exam 1' => [1, ['name' => 'English Exam 1'], ['first']],
    'filter by code = B003' => [1, ['code' => 'B003'], ['third']],
    'filter by code = NONEXISTINGCODE' => [0, ['code' => 'BLA BLA'], []],
    'filter by result entry period (April to June)' => [2, ['results_entry_period_open' => true],['first', 'third']]
]);

test('createExam()', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'code' => 'B003',
        'name' => [
            'en' => 'Math Exam 1',
            'zh' => '数学考试 1',
        ],
        'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
        'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
        'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
        'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
        'description' => 'For real this time'
    ];

    $response = $this->examService->setData($payload)->createExam()->toArray();

    expect($response)->toMatchArray([
        'code' => $payload['code'],
        'name' => [
            'en' => $payload['name']['en'],
            'zh' => $payload['name']['zh']
        ],
        'start_date' => $payload['start_date'],
        'end_date' => $payload['end_date'],
        'results_entry_period_from' => $payload['results_entry_period_from']->toISOString(),
        'results_entry_period_to' => $payload['results_entry_period_to']->toISOString(),
        'description' => $payload['description']
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'start_date' => $payload['start_date'],
        'end_date' => $payload['end_date'],
        'results_entry_period_from' => $payload['results_entry_period_from'],
        'results_entry_period_to' => $payload['results_entry_period_to'],
        'description' => $payload['description']
    ]);
});

test('updateExam()', function () {
    $exam = Exam::factory()->create([
        'code' => 'A001',
        'name->en' => 'English Exam 1',
        'name->zh' => '英语考试1',
        'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
        'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
        'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
        'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
        'description' => 'This is the last exam'
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'code' => 'B004',
        'name' => [
            'en' => 'Add Math 4',
            'zh' => '数学考试 4',
        ],
        'start_date' => Carbon::parse('2024-06-01')->format('Y-m-d'),
        'end_date' => Carbon::parse('2024-08-30')->format('Y-m-d'),
        'results_entry_period_from' => Carbon::parse('2024-10-25 16:00:00'),
        'results_entry_period_to' => Carbon::parse('2024-12-15 15:59:59'),
        'description' => 'New Description'
    ];

    $response = $this->examService->setData($payload)->updateExam($exam)->toArray();

    expect($response)->toMatchArray([
        'code' => $payload['code'],
        'name' => [
            'en' => $payload['name']['en'],
            'zh' => $payload['name']['zh']
        ],
        'start_date' => $payload['start_date'],
        'end_date' => $payload['end_date'],
        'results_entry_period_from' => $payload['results_entry_period_from']->toISOString(),
        'results_entry_period_to' => $payload['results_entry_period_to']->toISOString(),
        'description' => $payload['description']
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'start_date' => $payload['start_date'],
        'end_date' => $payload['end_date'],
        'results_entry_period_from' => $payload['results_entry_period_from'],
        'results_entry_period_to' => $payload['results_entry_period_to'],
        'description' => $payload['description']
    ]);
});

test('deleteExam()', function () {
    $exam = Exam::factory()->create();
    $other_exams = Exam::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->examService->deleteExam($exam);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $exam->id]);

    foreach ($other_exams as $other_exam) {
        $this->assertDatabaseHas($this->table, ['id' => $other_exam->id]);
    }
});

test('deleteExam() failed because being used in result source', function () {
    $exam = Exam::factory()->create();

    $result_source = ResultSource::factory()->create();

    ResultSourceExam::factory()->create([
        'exam_id' => $exam->id,
        'result_source_id' => $result_source->id,
    ]);

    $has_exception = false;
    try {
        $this->examService->deleteExam($exam);
    } catch (\Exception $e) {
        $has_exception = true;
        expect($e->getCode())->toEqual(20001)
            ->and($e->getMessage())->toEqual('This master data cannot be deleted because it is being used.');
    }

    expect($has_exception)->toBeTrue();

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, ['id' => $exam->id]);
});
