<?php

use App\Models\Exam;
use App\Models\ResultSource;
use App\Models\ResultSourceExam;
use App\Models\ResultSourceSubject;
use App\Repositories\ResultSourceSubjectRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->resultSourceSubjectRepository = app(ResultSourceSubjectRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(ResultSourceSubject::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->resultSourceSubjectRepository->getModelClass();

    expect($response)->toEqual(ResultSourceSubject::class);
});

test('closeExpiredResultSourceSubjects', function (){

    Carbon::setTestNow('2024-03-01 00:00:00');

    $exam_1 = Exam::factory()->create([
        'results_entry_period_from' => Carbon::parse('2024-01-25 16:00:00'),
        'results_entry_period_to' => Carbon::parse('2024-02-15 15:59:59'),
    ]);

    $exam_2 = Exam::factory()->create([
        'results_entry_period_from' => Carbon::parse('2024-08-25 16:00:00'),
        'results_entry_period_to' => Carbon::parse('2024-10-15 15:59:59'),
    ]);

    $result_source_1 = ResultSource::factory()->create([
        'code' => 'S001',
        'name->en' => '1st Semester',
        'name->zh' => '第一学期',
        'student_grading_framework_id' => 1,
    ]);
    $result_source_2 = ResultSource::factory()->create([
        'code' => 'S002',
        'name->en' => '2nd Semester',
        'name->zh' => '第二学期',
        'student_grading_framework_id' => 2,
    ]);

    $result_source_exam_1 = ResultSourceExam::factory()->create([
        'result_source_id' => $result_source_1->id,
        'exam_id' => $exam_1->id 
    ]);

    $result_source_exam_2 = ResultSourceExam::factory()->create([
        'result_source_id' => $result_source_2->id,
        'exam_id' => $exam_2->id 
    ]);

    $result_source_subject_A1 = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_1->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,
    ]);

    $result_source_subject_A2 = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_1->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,
    ]);

    $result_source_subject_A3 = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_1->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED,
    ]);

    $result_source_subject_B1 = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_2->id,
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT,       
    ]);

    $response = $this->resultSourceSubjectRepository->closeExpiredResultSourceSubject();

    expect($response)->toBeTrue();

    $rss1 = ResultSourceSubject::find($result_source_subject_A1->id)->toArray();
    expect($rss1)->toMatchArray([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED
    ]);

    $rss2 = ResultSourceSubject::find($result_source_subject_A2->id)->toArray();
    expect($rss2)->toMatchArray([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED
    ]);

    $rss3 = ResultSourceSubject::find($result_source_subject_A3->id)->toArray();
    expect($rss3)->toMatchArray([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_POSTED
    ]);

    $rss4 = ResultSourceSubject::find($result_source_subject_B1->id)->toArray();
    expect($rss4)->toMatchArray([
        'data_entry_status' => ResultSourceSubject::DATA_ENTRY_STATUS_DRAFT
    ]);

});