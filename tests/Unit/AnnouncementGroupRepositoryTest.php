<?php

use App\Models\AnnouncementGroup;
use App\Models\Employee;
use App\Models\Student;
use App\Repositories\AnnouncementGroupRepository;

beforeEach(function () {
    $this->announcementGroupRepository = app(AnnouncementGroupRepository::class);

    app()->setLocale('en');

    $this->testLcale = app()->getLocale();
});

test('getModelClass()', function () {
    $response = $this->announcementGroupRepository->getModelClass();

    expect($response)->toEqual(AnnouncementGroup::class);
});

test('getAll()', function () {
    $first_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test'
    ]);

    $second_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 2'
    ]);

    $third_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Group 3'
    ]);

    $response = $this->announcementGroupRepository->getAll()->toArray();

    expect($response)->toEqual([
        $first_announcement_group->toArray(),
        $second_announcement_group->toArray(),
        $third_announcement_group->toArray(),
    ]);
});

test('getAllPaginated()', function () {
    $first_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 1',
        'is_active' => true
    ]);

    $second_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 2',
        'is_active' => true
    ]);

    $third_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Group 3',
        'is_active' => false
    ]);

    //Filter by is_active = INACTIVE
    $response = $this->announcementGroupRepository->getAllPaginated(['is_active' => false])->toArray();

    expect($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual($third_announcement_group->toArray());

    // Filter by name = Test
    $response = $this->announcementGroupRepository->getAllPaginated(['name' => 'Test'])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toEqual($first_announcement_group->toArray()),
            fn($item) => $item->toEqual($second_announcement_group->toArray())
        );

    // Order by name asc
    $response = $this->announcementGroupRepository->getAllPaginated([
        'order_by' => [
            'name' => 'asc'
        ]
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($item) => $item->toEqual($third_announcement_group->toArray()),
            fn($item) => $item->toEqual($first_announcement_group->toArray()),
            fn($item) => $item->toEqual($second_announcement_group->toArray())
        );

    // Order by name desc
    $response = $this->announcementGroupRepository->getAllPaginated([
        'order_by' => [
            'name' => 'desc'
        ]
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($item) => $item->toEqual($second_announcement_group->toArray()),
            fn($item) => $item->toEqual($first_announcement_group->toArray()),
            fn($item) => $item->toEqual($third_announcement_group->toArray())
        );

    // Order by is_active asc
    $response = $this->announcementGroupRepository->getAllPaginated([
        'order_by' => [
            'is_active' => 'asc'
        ]
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($item) => $item->toHaveKey('is_active', false),
            fn($item) => $item->toHaveKey('is_active', true),
            fn($item) => $item->toHaveKey('is_active', true)
        );

    // Order by is_active desc

    $response = $this->announcementGroupRepository->getAllPaginated([
        'order_by' => [
            'is_active' => 'desc'
        ]
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($item) => $item->toHaveKey('is_active', true),
            fn($item) => $item->toHaveKey('is_active', true),
            fn($item) => $item->toHaveKey('is_active', false)
        );
});

test('syncStudents()', function () {
    $this->assertDatabaseCount('model_has_announcement_groups', 0);
    $announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 1',
        'is_active' => true
    ]);

    $students = Student::factory(4)->create();

    $this->announcementGroupRepository->syncStudents($announcement_group, [
        $students[0]->id,
        $students[1]->id
    ]);

    $this->assertDatabaseCount('model_has_announcement_groups', 2);

    $this->assertDatabaseHas('model_has_announcement_groups', [
        'announcement_group_id' => $announcement_group->id,
        'userable_type' => Student::class,
        'userable_id' => $students[0]->id
    ]);

    $this->assertDatabaseHas('model_has_announcement_groups', [
        'announcement_group_id' => $announcement_group->id,
        'userable_type' => Student::class,
        'userable_id' => $students[1]->id
    ]);
});

test('syncEmployees()', function () {
    $this->assertDatabaseCount('model_has_announcement_groups', 0);
    $announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 1',
        'is_active' => true
    ]);

    $employees = Employee::factory(4)->create();

    $this->announcementGroupRepository->syncEmployees($announcement_group, [
        $employees[0]->id,
        $employees[1]->id
    ]);

    $this->assertDatabaseCount('model_has_announcement_groups', 2);

    $this->assertDatabaseHas('model_has_announcement_groups', [
        'announcement_group_id' => $announcement_group->id,
        'userable_type' => Employee::class,
        'userable_id' => $employees[0]->id
    ]);

    $this->assertDatabaseHas('model_has_announcement_groups', [
        'announcement_group_id' => $announcement_group->id,
        'userable_type' => Employee::class,
        'userable_id' => $employees[1]->id
    ]);
});

test('getStudentIdsByAnnouncementGroupIds()', function () {
    $first_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 1',
        'is_active' => true
    ]);

    $second_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 2',
        'is_active' => true
    ]);

    $students = Student::factory(4)->create();

    $first_announcement_group->students()->attach([
        $students[0]->id,
        $students[1]->id
    ]);

    $second_announcement_group->students()->attach([
        $students[1]->id,
        $students[2]->id
    ]);

    $announcement_group_ids = [$first_announcement_group->id, $second_announcement_group->id];

    $response = $this->announcementGroupRepository->getStudentIdsByAnnouncementGroupIds($announcement_group_ids);

    expect($response)->toHaveCount(3)
        ->toEqual([$students[0]->id, $students[1]->id, $students[2]->id]);
});

test('getEmployeeIdsByAnnouncementGroupIds()', function () {
    $first_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 1',
        'is_active' => true
    ]);

    $second_announcement_group = AnnouncementGroup::factory()->create([
        'name' => 'Test 2',
        'is_active' => true
    ]);

    $employees = Employee::factory(4)->create();

    $first_announcement_group->employees()->attach([
        $employees[0]->id,
        $employees[1]->id
    ]);

    $second_announcement_group->employees()->attach([
        $employees[1]->id,
        $employees[2]->id
    ]);

    $announcement_group_ids = [$first_announcement_group->id, $second_announcement_group->id];

    $response = $this->announcementGroupRepository->getEmployeeIdsByAnnouncementGroupIds($announcement_group_ids);

    expect($response)->toHaveCount(3)
        ->toEqual([$employees[0]->id, $employees[1]->id, $employees[2]->id]);
});

