<?php

use App\Models\DuplicateSemesterSettingStatus;
use App\Models\SemesterSetting;
use App\Repositories\DuplicateSemesterSettingStatusRepository;

beforeEach(function () {
    $this->duplicateSemesterSettingStatusRepository = app()->make(DuplicateSemesterSettingStatusRepository::class);
});

test('getModelClass()', function () {
    $model_class = $this->duplicateSemesterSettingStatusRepository->getModelClass();
    expect($model_class)->toEqual(DuplicateSemesterSettingStatus::class);
});

test('getAllPaginated getAll', function (int $expected_count, array $filters, array $expected_model) {
    $semester_setting = SemesterSetting::factory()->create(['id' => 11111]);
    $semester_setting2 = SemesterSetting::factory()->create(['id' => 11112]);
    $semester_setting3 = SemesterSetting::factory()->create(['id' => 11113]);

    $duplicate_semester_setting_statuses = [
        'first' => DuplicateSemesterSettingStatus::factory()->create([
            'from_semester_setting_id' => $semester_setting->id,
            'to_semester_setting_id' => $semester_setting2->id,
            'status' => DuplicateSemesterSettingStatus::STATUS_PENDING,
        ]),
        'second' => DuplicateSemesterSettingStatus::factory()->create([
            'from_semester_setting_id' => $semester_setting->id,
            'to_semester_setting_id' => $semester_setting3->id,
            'status' => DuplicateSemesterSettingStatus::STATUS_COMPLETED,
        ]),
        'third' => DuplicateSemesterSettingStatus::factory()->create([
            'from_semester_setting_id' => $semester_setting2->id,
            'to_semester_setting_id' => $semester_setting3->id,
            'status' => DuplicateSemesterSettingStatus::STATUS_PENDING,
        ]),
    ];

    $result_paginated = $this->duplicateSemesterSettingStatusRepository->getAllPaginated($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $duplicate_semester_setting_statuses[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }
    $result = $this->duplicateSemesterSettingStatusRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $duplicate_semester_setting_statuses[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [3, [], ['first', 'second', 'third']],
    'filter by from_semester_setting_id' => [2, ['from_semester_setting_id' => '11111'], ['first', 'second']],
    'filter by to_semester_setting_id' => [2, ['to_semester_setting_id' => '11113'], ['second', 'third']],
    'filter by status' => [1, ['status' => DuplicateSemesterSettingStatus::STATUS_COMPLETED], ['second']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
]);

