<?php

use App\Enums\HostelMeritDemeritType;
use App\Models\HostelMeritDemeritSetting;
use App\Models\HostelRewardPunishmentSetting;
use App\Services\HostelRewardPunishmentSettingService;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->hostelRewardPunishmentSettingService = app(HostelRewardPunishmentSettingService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->hostelRewardPunishmentSettingTableName = resolve(HostelRewardPunishmentSetting::class)->getTable();
});

test('getAllPaginatedHostelRewardPunishmentSettings()', function(int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $hostel_merit_demerit_setting = HostelMeritDemeritSetting::factory()->create();

    $settings = [
        'first' => HostelRewardPunishmentSetting::factory()->create([
            'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting->id,
            'code' => '001',
            'name' => 'Bring Laptop',
            'points' => -10,
        ]),
        'second' => HostelRewardPunishmentSetting::factory()->create([
            'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting->id,
            'code' => '002',
            'name' => 'Bring Phone',
            'points' => -5,
        ]),
        'third' => HostelRewardPunishmentSetting::factory()->create([
            'code' => '003',
            'name' => 'Smoking',
            'points' => -15,
        ]),
    ];

    if($filter_by === 'hostel_merit_demerit_setting_id') {
        $filter_value = $hostel_merit_demerit_setting->id;
    }
    $result = $this->hostelRewardPunishmentSettingService->getAllPaginatedHostelRewardPunishmentSettings([$filter_by => $filter_value])->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($settings[$value]->toArray());
    }
})->with([
    'filter by name = Bring Laptop' => [1, 'name', 'Bring Laptop', ['first']],
    'filter by name = Bring Phone' => [1, 'name', 'Bring Phone', ['second']],
    'filter by partial name = bring' => [2, 'name', 'bring', ['first', 'second']],
    'filter by non-existing name = Something' => [0, 'name', 'Something', []],
    'filter by code = 001' => [1, 'code', '001', ['first']],
    'filter by code = 002' => [1, 'code', '002', ['second']],
    'filter by hostel_merit_demerit_setting_id = 1' => [2, 'hostel_merit_demerit_setting_id', 1, ['first', 'second']],
    'sort by name asc' => [3, 'order_by', ['name' => 'asc'], ['first', 'second', 'third']],
    'sort by name desc' => [3, 'order_by', ['name' => 'desc'], ['third', 'second', 'first']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by points asc' => [3, 'order_by', ['points' => 'asc'], ['third', 'first', 'second']],
    'sort by points desc' => [3, 'order_by', ['points' => 'desc'], ['second', 'first', 'third']],
]);

test('createHostelRewardPunishmentSetting()', function () {
    $hostel_merit_demerit_setting = HostelMeritDemeritSetting::factory()->create([
        'name' => 'Bring Electronic Devices',
        'type' => HostelMeritDemeritType::DEMERIT,
    ]);

    $this->assertDatabaseCount($this->hostelRewardPunishmentSettingTableName, 0);

    $payload = [
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting->id,
        'code' => 'CA001',
        'name' => 'Bring Phone',
        'points' => -5,
    ];

    $response = $this->hostelRewardPunishmentSettingService->createHostelRewardPunishmentSetting($payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseState($this->hostelRewardPunishmentSettingTableName, 1, $payload);
});

test('updateHostelRewardPunishmentSetting()', function () {
    $hostel_merit_demerit_setting = HostelMeritDemeritSetting::factory()->create();
    $first_hostel_reward_punishment_setting = HostelRewardPunishmentSetting::factory()->create();

    //update with id exist
    $this->assertDatabaseCount($this->hostelRewardPunishmentSettingTableName, 1);

    $payload = [
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting->id,
        'code' => 'CA001',
        'name' => 'Bring Phone',
        'points' => -20,
    ];

    $response = $this->hostelRewardPunishmentSettingService->updateHostelRewardPunishmentSetting($first_hostel_reward_punishment_setting->id, $payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseState($this->hostelRewardPunishmentSettingTableName, 1, $payload);

    $has_error = false;

    try {
        $this->hostelRewardPunishmentSettingService->updateHostelRewardPunishmentSetting(9999, $payload);
    } catch (ModelNotFoundException $exception) {

        expect($exception->getMessage())->toEqual('No query results for model [App\Models\HostelRewardPunishmentSetting] 9999');

        $has_error = true;
    }

    expect($has_error)->toBeTrue();

    $this->assertDatabaseCount($this->hostelRewardPunishmentSettingTableName, 1);
});

test('deleteHostelRewardPunishmentSetting()', function () {
    $first_hostel_reward_punishment_setting = HostelRewardPunishmentSetting::factory()->create();
    $other_hostel_reward_punishment_settings = HostelRewardPunishmentSetting::factory(3)->create();

    $this->assertDatabaseCount($this->hostelRewardPunishmentSettingTableName, 4);

    // Delete success
    $this->hostelRewardPunishmentSettingService->deleteHostelRewardPunishmentSetting($first_hostel_reward_punishment_setting->id);

    $this->assertDatabaseCount($this->hostelRewardPunishmentSettingTableName, 3);
    $this->assertDatabaseMissing($this->hostelRewardPunishmentSettingTableName, [
        'id' => $first_hostel_reward_punishment_setting->id,
    ]);

    foreach ($other_hostel_reward_punishment_settings as $other) {
        $this->assertDatabaseHas($this->hostelRewardPunishmentSettingTableName, [
            'id' => $other->id,
        ]);
    }

    // ID not exist
    $this->expectException(ModelNotFoundException::class);
    $this->hostelRewardPunishmentSettingService->deleteHostelRewardPunishmentSetting(9999);

    // TODO: Cannot delete due to associated student records
    // Cannot delete due to associated student records
//    $hostel_reward_punishment_setting_with_student = HostelRewardPunishmentSetting::factory()->create();
    // Simulate the setting being used in a student record
    // Add necessary fields for the student record

//    $this->expectException(Exception::class);
//    $this->expectExceptionMessage('The setting cannot be deleted as it is being used in a student record.');
//    $this->hostelRewardPunishmentSettingService->deleteHostelRewardPunishmentSetting($hostel_reward_punishment_setting_with_student->id);
});
