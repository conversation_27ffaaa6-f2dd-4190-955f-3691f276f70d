<?php

use App\Models\AnnouncementGroup;
use App\Models\ModelHasAnnouncementGroup;
use App\Models\Student;
use App\Repositories\ModelHasAnnouncementGroupRepository;

beforeEach(function () {
    $this->modelHasAnnouncementGroupRepository = app(ModelHasAnnouncementGroupRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(ModelHasAnnouncementGroup::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->modelHasAnnouncementGroupRepository->getModelClass();

    expect($response)->toEqual(ModelHasAnnouncementGroup::class);
});

test('getAllPaginated getAll', function (int $expected_count, array $filters, array $expected_model) {
    $student = Student::factory()->create();
    $student2 = Student::factory()->create();
    $announcement_group = AnnouncementGroup::factory()->create(['id' => 1]);
    $announcement_group2 = AnnouncementGroup::factory()->create(['id' => 2]);

    $announcement_group->students()->sync([$student->id]);
    $announcement_group2->students()->sync([$student2->id]);

    $models = [
        'first' => ModelHasAnnouncementGroup::where('announcement_group_id', 1)->first(),
        'second' => ModelHasAnnouncementGroup::where('announcement_group_id', 2)->first(),
    ];

    $result_paginated = $this->modelHasAnnouncementGroupRepository->getAllPaginated($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $models[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }

    $result = $this->modelHasAnnouncementGroupRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $models[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [2, [], ['first', 'second']],
    'filter by announcement_group_id' => [1, ['announcement_group_id' => 2], ['second']],
]);
