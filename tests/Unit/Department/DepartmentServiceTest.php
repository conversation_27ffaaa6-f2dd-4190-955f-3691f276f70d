<?php

use App\Models\Competition;
use App\Models\Department;
use App\Services\DepartmentService;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);

    $this->departmentService = app()->make(DepartmentService::class);
    app()->setLocale('en');
    $this->table = resolve(Department::class)->getTable();
});

test('getAllPaginatedDepartments getAllDepartments', function (int $expected_count, array $filters, array $expected_model) {
    $departments = [
        'science' => Department::factory()->create([
            'code' => 'SCIENCE',
            'name->en' => 'science department',
            'name->zh' => '科学部门',
            'sequence' => 0,
            'is_active' => true,
        ]),
        'mathematics' => Department::factory()->create([
            'code' => 'MATHEMATICS',
            'name->en' => 'mathematics department',
            'name->zh' => '数学部门',
            'sequence' => 0,
            'is_active' => true,
        ]),
        'languages' => Department::factory()->create([
            'code' => 'LANGUAGES',
            'name->en' => 'languages department',
            'name->zh' => '语言部门',
            'sequence' => 0,
            'is_active' => false,
        ]),
    ];

    $result_paginated = $this->departmentService->getAllPaginatedDepartments($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $departments[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }

    $result = $this->departmentService->getAllDepartments($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_model as $key => $model) {
        $expected_data = $departments[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [3, [], ['science', 'mathematics', 'languages']],
    'filter by name en' => [1, ['name' => 'science department'], ['science']],
    'filter by name en wildcard' => [3, ['name' => 'department'], ['science', 'mathematics', 'languages']],
    'filter by name zh' => [1, ['name' => '语言部门'], ['languages']],
    'filter by name zh wildcard' => [3, ['name' => '部门'], ['science', 'mathematics', 'languages']],
    'filter by code' => [1, ['code' => 'MATHEMATICS'], ['mathematics']],
    'filter by is_active true' => [2, ['is_active' => true], ['science', 'mathematics']],
    'filter by is_active false' => [1, ['is_active' => false], ['languages']],
    'not paginated' => [3, ['per_page' => -1], ['science', 'mathematics', 'languages'], false],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['science', 'mathematics', 'languages']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['languages', 'mathematics', 'science']],
]);

test('createDepartment', function () {
    $this->assertDatabaseCount($this->table, 0);
    $payload = [
        'code' => 'SCIENCE',
        'name' => [
            'en' => 'science department',
            'zh' => '科学部门',
        ],
        'description' => 'physics, chemistry, biology',
        'sequence' => 1,
        'is_active' => false
    ];
    $department = $this->departmentService
        ->setData($payload)
        ->createDepartment();

    // Expect only one record is created
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $department->id,
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'description' => $payload['description'],
        'sequence' => $payload['sequence'],
        'is_active' => $payload['is_active'],
    ]);
});

test('updateDepartment', function () {
    $department = Department::factory()->create([
        'code' => 'SCIENCE',
        'name' => [
            'en' => 'science department',
            'zh' => '科学部门',
        ],
        'description' => 'physics, chemistry, biology',
        'sequence' => 1,
        'is_active' => false
    ]);
    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'code' => 'SCIENCE2',
        'name' => [
            'en' => 'science department2',
            'zh' => '科学部门2',
        ],
        'description' => 'physics, chemistry, biology test',
        'sequence' => 2,
        'is_active' => true
    ];
    $updated_department = $this->departmentService
        ->setData($payload)
        ->updateDepartment($department);
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $updated_department->id,
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'description' => $payload['description'],
        'sequence' => $payload['sequence'],
        'is_active' => $payload['is_active'],
    ]);
});

test('deleteDepartment', function () {
    $department = Department::factory()->create();
    $department_being_used = Department::factory()->create();
    Competition::factory()->create([
        'department_id' => $department_being_used->id,
    ]);
    $this->assertDatabaseCount($this->table, 2);

    //delete success
    $this->departmentService->deleteDepartment($department);
    $this->assertDatabaseCount($this->table, 1);

    //delete department that is currently in use, should throw error
    $this->expectExceptionMessage('This master data cannot be deleted because it is being used.');
    $this->expectExceptionCode(20001);
    $this->departmentService->deleteDepartment($department_being_used);
});
