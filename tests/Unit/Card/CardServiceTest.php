<?php

use App\Enums\CardStatus;
use App\Enums\CardTemplateType;
use App\Enums\CardType;
use App\Enums\ExportType;
use App\Enums\LibraryMemberType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Models\Card;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\LibraryMember;
use App\Models\Student;
use App\Models\User;
use App\Services\CardService;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Maatwebsite\Excel\Facades\Excel;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->cardService = app(CardService::class);
    $this->reportPrintService = resolve(ReportPrintService::class);

    app()->setLocale('en');

    $this->table = resolve(Card::class)->getTable();
    $this->libraryMemberTable = resolve(LibraryMember::class)->getTable();
});

test('getAllPaginatedCards()', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();

    $student1 = Student::factory()->create([
        'user_id' => $user1->id
    ]);

    $student2 = Student::factory()->create([
        'user_id' => $user2->id
    ]);

    $first_card = Card::factory()->create([
        'name' => 'Card 1',
        'card_number' => '1234567',
        'status' => CardStatus::ACTIVE->value,
        'userable_type' => Student::class,
        'userable_id' => $student1->id
    ]);

    $second_card = Card::factory()->create([
        'name' => 'Card 2',
        'card_number' => '1234568',
        'status' => CardStatus::ACTIVE->value,
        'userable_type' => Student::class,
        'userable_id' => $student1->id
    ]);

    $third_card = Card::factory()->create([
        'name' => 'Card 3',
        'card_number' => '1234569',
        'status' => CardStatus::INACTIVE->value,
        'userable_type' => Student::class,
        'userable_id' => $student2->id
    ]);

    // Filter by student id = $student1
    $response = $this->cardService->getAllPaginatedCards([
        'userable_id' => $student1->id,
        'userable_type' => Student::class
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0]['id'])->toEqual($first_card->id)
        ->and($response['data'][1]['id'])->toEqual($second_card->id);

    //Filter by name = Card 1
    $response = $this->cardService->getAllPaginatedCards(['name' => 'Card 1'])->toArray();

    expect($response['data'])->toHaveCount(1)
        ->and($response['data'][0]['id'])->toEqual($first_card->id);

    //Filter by card number = 1234568
    $response = $this->cardService->getAllPaginatedCards(['card_number' => '1234568'])->toArray();

    expect($response['data'])->toHaveCount(1)
        ->and($response['data'][0]['id'])->toEqual($second_card->id);

    //Filter by card status = inactive
    $response = $this->cardService->getAllPaginatedCards(['status' => CardStatus::INACTIVE->value])->toArray();

    expect($response['data'])->toHaveCount(1)
        ->and($response['data'][0]['id'])->toEqual($third_card->id);

    //Filter non-existing name = English 3
    $response = $this->cardService->getAllPaginatedCards(['name' => 'English 3'])->toArray();

    expect($response['data'])->toHaveCount(0);
});

test('createCard()', function () {
    $student = Student::factory()->create();

    //store success - with update_library_card_number = true
    $this->assertDatabaseCount($this->table, 0);
    $library_member = LibraryMember::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'card_number' => null
    ]);

    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'name' => fake()->name,
        'card_number' => '123456',
        'card_type' => CardType::NFC->value,
        'status' => CardStatus::ACTIVE->value,
        'update_library_card_number' => true
    ];

    $response = $this->cardService->createCard($payload)->toArray();

    unset($payload['update_library_card_number']);

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, $payload);
    $this->assertDatabaseHas($this->libraryMemberTable, [
        'id' => $library_member->id,
        'card_number' => $payload['card_number']
    ]);

    //store success - with update_library_card_number = false
    $student2 = Student::factory()->create();
    $library_member2 = LibraryMember::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student2->id,
        'card_number' => null
    ]);

    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student2->id,
        'name' => fake()->name,
        'card_number' => '1234599',
        'card_type' => CardType::NFC->value,
        'status' => CardStatus::ACTIVE->value,
        'update_library_card_number' => false
    ];

    $this->cardService->createCard($payload)->toArray();

    $this->assertDatabaseHas($this->libraryMemberTable, [
        'id' => $library_member2->id,
        'card_number' => null
    ]);

    //create another card
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'name' => fake()->name,
        'card_number' => '123457',
        'card_type' => CardType::NFC->value,
        'status' => CardStatus::ACTIVE->value
    ];

    $this->expectExceptionMessage("Please deactivate the existing active card first.");
    $this->cardService->createCard($payload);
});

test('updateCard()', function () {
    $student = Student::factory()->create();

    $card = Card::factory()->create([
        'name' => 'Card 1',
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    $library_member = LibraryMember::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'card_number' => null
    ]);

    //update with id exist - update_library_card_number = true
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'name' => 'Card 2',
        'card_number' => '123456',
        'card_type' => CardType::PROXIMITY->value,
        'status' => CardStatus::ACTIVE->value,
        'update_library_card_number' => true
    ];

    $response = $this->cardService->updateCard($card, $payload)->toArray();
    unset($payload['update_library_card_number']);
    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, $payload);
    $this->assertDatabaseHas($this->libraryMemberTable, [
        'id' => $library_member->id,
        'card_number' => $payload['card_number']
    ]);

    //update with id exist - update_library_card_number = false
    $this->assertDatabaseCount($this->table, 1);
    $payload2 = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'name' => 'Card 2',
        'card_number' => '123457',
        'card_type' => CardType::PROXIMITY->value,
        'status' => CardStatus::ACTIVE->value,
        'update_library_card_number' => false
    ];

    $this->cardService->updateCard($card, $payload2);

    $this->assertDatabaseHas($this->libraryMemberTable, [
        'id' => $library_member->id,
        'card_number' => $payload['card_number']
    ]);
});

test('deleteCard()', function () {
    $first_card = Card::factory()->create();
    $other_cards = Card::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->cardService->deleteCard($first_card);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $first_card->id]);

    foreach ($other_cards as $other_card) {
        $this->assertDatabaseHas($this->table, ['id' => $other_card->id]);
    }
});

test('transformExcelToCollection()', function () {
    // convert Employee Card Excel to collection
    $row1 = ['G2024200', '2222222222', '232', '', 'true', 'PROXIMITY'];
    $row2 = ['G2024201', '1111111111', '555', '', 'false', 'PROXIMITY'];

    $employee_excel = createFakeExcelFileWithData([
        ['Employee Number', 'Card Number', 'Card Number2', 'Card Number', 'Update Library Card', '(NFC, PROXIMITY)'],
        $row1,
        $row2,
    ]);

    $response = $this->cardService
        ->setImportFile($employee_excel)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->transformExcelToCollection();

    expect($response->getData())->toEqual([
        [
            'number' => $row1[0],
            'card_number' => $row1[1],
            'card_number2' => $row1[2],
            'card_number3' => $row1[3],
            'update_library_card' => true,
            'card_type' => $row1[5],
        ],
        [
            'number' => $row2[0],
            'card_number' => $row2[1],
            'card_number2' => $row2[2],
            'card_number3' => $row2[3],
            'update_library_card' => false,
            'card_type' => $row2[5],
        ],
    ]);

    // convert Student Card Excel to collection
    $row1 = ['H2024002', '3333333333', '232', '', 'true', 'PROXIMITY'];
    $row2 = ['H2024001', '6666666666', '555', '', 'true', 'PROXIMITY'];

    $student_excel = createFakeExcelFileWithData([
        ['Student Number', 'Card Number', 'Card Number2', 'Card Number', 'Update Library Card', '(NFC, PROXIMITY)'],
        $row1,
        $row2,
    ]);

    $response = $this->cardService
        ->setImportFile($student_excel)
        ->setImportType(CardTemplateType::STUDENT)
        ->transformExcelToCollection();

    expect($response->getData())->toEqual([
        [
            'number' => $row1[0],
            'card_number' => $row1[1],
            'card_number2' => $row1[2],
            'card_number3' => $row1[3],
            'update_library_card' => true,
            'card_type' => $row1[5],
        ],
        [
            'number' => $row2[0],
            'card_number' => $row2[1],
            'card_number2' => $row2[2],
            'card_number3' => $row2[3],
            'update_library_card' => true,
            'card_type' => $row2[5],
        ],
    ]);


    /**
     *
     * testing boolean conversion
     *
     */
    $row1 = ['G2024200', '2222222222', '232', '', '1', 'PROXIMITY'];
    $row2 = ['G2024200', '2222222222', '232', '', 'true', 'PROXIMITY'];
    $row3 = ['G2024200', '2222222222', '232', '', 'yes', 'PROXIMITY'];
    $row4 = ['G2024200', '2222222222', '232', '', 'on', 'PROXIMITY'];
    $row5 = ['G2024200', '2222222222', '232', '', '0', 'PROXIMITY'];
    $row6 = ['G2024201', '1111111111', '555', '', 'false', 'PROXIMITY'];
    $row7 = ['G2024201', '1111111111', '555', '', 'no', 'PROXIMITY'];
    $row8 = ['G2024201', '1111111111', '555', '', 'off', 'PROXIMITY'];
    $row9 = ['G2024201', '1111111111', '555', '', 'invalid text', 'PROXIMITY'];
    $row10 = ['G2024201', '1111111111', '555', '', 'no valid', 'PROXIMITY'];
    $row11 = ['G2024201', '1111111111', '555', '', 'TRUE', 'PROXIMITY'];
    $row12 = ['G2024201', '1111111111', '555', '', 'FALSE', 'PROXIMITY'];

    $employee_excel = createFakeExcelFileWithData([
        ['Employee Number', 'Card Number', 'Card Number2', 'Card Number', 'Update Library Card', '(NFC, PROXIMITY)'],
        $row1,
        $row2,
        $row3,
        $row4,
        $row5,
        $row6,
        $row7,
        $row8,
        $row9,
        $row10,
        $row11,
        $row12,
    ]);

    $response = $this->cardService
        ->setImportFile($employee_excel)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->transformExcelToCollection();

    $import = $response->getData();

    expect($import)->toHaveCount(12)
        ->and($import[0]['update_library_card'])->toBeBool()->toEqual(true)
        ->and($import[1]['update_library_card'])->toBeBool()->toEqual(true)
        ->and($import[2]['update_library_card'])->toBeBool()->toEqual(true)
        ->and($import[3]['update_library_card'])->toBeBool()->toEqual(true)
        ->and($import[4]['update_library_card'])->toBeBool()->toEqual(false)
        ->and($import[5]['update_library_card'])->toBeBool()->toEqual(false)
        ->and($import[6]['update_library_card'])->toBeBool()->toEqual(false)
        ->and($import[7]['update_library_card'])->toBeBool()->toEqual(false)
        ->and($import[8]['update_library_card'])->not()->toBeBool()->toEqual(null)
        ->and($import[9]['update_library_card'])->not()->toBeBool()->toEqual(null)
        ->and($import[10]['update_library_card'])->toBeBool()->toEqual(true)
        ->and($import[11]['update_library_card'])->toBeBool()->toEqual(false);


    /**
     *
     * testing whitespace is trimmed
     *
     */
    $row1 = ['G2024200  ', '   2222222222', '   232', '    ', 'true', '   PROXIMITY    '];
    $row2 = ['G2024200  ', '2222222222    ', '232   ', '   ', 'true', '     PROXIMITY'];
    $row3 = ['G2024200  ', '   2222222222   ', '   232  ', '    ', 'true', 'PROXIMITY     '];

    $employee_excel = createFakeExcelFileWithData([
        ['Employee Number', 'Card Number', 'Card Number2', 'Card Number', 'Update Library Card', '(NFC, PROXIMITY)'],
        $row1,
        $row2,
        $row3,
    ]);

    $response = $this->cardService
        ->setImportFile($employee_excel)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->transformExcelToCollection();

    expect($response->getData())->toEqual([
        [
            'number' => trim($row1[0]),
            'card_number' => trim($row1[1]),
            'card_number2' => trim($row1[2]),
            'card_number3' => trim($row1[3]),
            'update_library_card' => true,
            'card_type' => trim($row1[5]),
        ],
        [
            'number' => trim($row2[0]),
            'card_number' => trim($row2[1]),
            'card_number2' => trim($row2[2]),
            'card_number3' => trim($row2[3]),
            'update_library_card' => true,
            'card_type' => trim($row2[5]),
        ],
        [
            'number' => trim($row3[0]),
            'card_number' => trim($row3[1]),
            'card_number2' => trim($row3[2]),
            'card_number3' => trim($row3[3]),
            'update_library_card' => true,
            'card_type' => trim($row3[5]),
        ],
    ]);
});


test('validateCardForImport()', function () {
    /**
     *
     *  validate employee_number / student_number already existed in previous row
     *
     */

    // STUDENT
    $payload = [
        [
            'number' => 'H2024200',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'H2024200', // duplicated
            'card_number' => '1234567891',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([$payload[0]]);

    expect($response->getInvalidData())
        ->toHaveCount(1)
        ->toEqual([array_merge($payload[1], ['errors' => ['Student number provided already existed in previous row.']])]);

    // EMPLOYEE
    $payload = [
        [
            'number' => 'G2024200',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024200', // duplicated employee
            'card_number' => '1234567891',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([$payload[0]]);

    expect($response->getInvalidData())
        ->toHaveCount(1)
        ->toEqual([array_merge($payload[1], ['errors' => ['Employee number provided already existed in previous row.']])]);


    /**
     *
     *  validate card_number already existed in previous row
     *
     */

    $payload = [
        [
            'number' => 'G2024200',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024201',
            'card_number' => '1234567890', // duplicated card_number
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([$payload[0]]);

    expect($response->getInvalidData())
        ->toHaveCount(1)
        ->toEqual([array_merge($payload[1], ['errors' => ['This card has already been assigned in previous row.']])]);


    /**
     *
     *  validate card_number must be 10 digits
     *
     */


    $payload = [
        [
            'number' => 'G2024200',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024201',
            'card_number' => '123456789Q', // card_number have char
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024203',
            'card_number' => '123456789', // card_number is not 10 digits
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([$payload[0]]);

    expect($response->getInvalidData())
        ->toHaveCount(2)
        ->toEqual([
            array_merge($payload[1], ['errors' => ['Card number must be 10 digits.']]),
            array_merge($payload[2], ['errors' => ['Card number must be 10 digits.']]),
        ]);

    /**
     *
     *  validate card_number2 must be 3 digits if provided
     *
     */

    $payload = [
        [
            'number' => 'G2024200',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024201',
            'card_number' => '1234567891',
            'card_number2' => '12W', // card_number2 have char
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024203',
            'card_number' => '1234567893',
            'card_number2' => '1223', // card_number2 is not 3 digits
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([$payload[0]]);

    expect($response->getInvalidData())
        ->toHaveCount(2)
        ->toEqual([
            array_merge($payload[1], ['errors' => ['Card number2 must be 3 digits.']]),
            array_merge($payload[2], ['errors' => ['Card number2 must be 3 digits.']]),
        ]);


    /**
     *
     *  validate card_number3 must be 5 digits if provided
     *
     */

    $payload = [
        [
            'number' => 'G2024200',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024201',
            'card_number' => '1234567891',
            'card_number2' => '',
            'card_number3' => '1234Q', // card_number3 have char
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024203',
            'card_number' => '1234567893',
            'card_number2' => '',
            'card_number3' => '1111111', // card_number3 is not 5 digits
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([$payload[0]]);

    expect($response->getInvalidData())
        ->toHaveCount(2)
        ->toEqual([
            array_merge($payload[1], ['errors' => ['Card number3 must be 5 digits.']]),
            array_merge($payload[2], ['errors' => ['Card number3 must be 5 digits.']]),
        ]);


    /**
     *
     *  validate update_library_card must eexist and boolean
     *
     */

    $payload = [
        [
            'number' => 'G2024201',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024200',
            'card_number' => '1234567891',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024207',
            'card_number' => '1232567897',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => null,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024205',
            'card_number' => '1232567892',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => 'null',
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(2)
        ->toEqual([
            $payload[0],
            $payload[1],
        ]);

    expect($response->getInvalidData())
        ->toHaveCount(2)
        ->toEqual([
            array_merge($payload[2], ['errors' => ['Update library card must be either true or false.']]),
            array_merge($payload[3], ['errors' => ['Update library card must be either true or false.']]),
        ]);


    /**
     *
     *  validate card_type must exist and either NFC / PROXIMITY
     *
     */


    $payload = [
        [
            'number' => 'G2024201',
            'card_number' => '1234567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024200',
            'card_number' => '1234567891',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'NFC',
        ],
        [
            'number' => 'G2024222',
            'card_number' => '1234567897',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'invalid',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(2)
        ->toEqual([
            $payload[0],
            $payload[1],
        ]);

    expect($response->getInvalidData())
        ->toHaveCount(1)
        ->toEqual([array_merge($payload[2], ['errors' => ['Card type must be either NFC or PROXIMITY.']])]);


    /**
     *
     *  validate all pass
     *
     */

    $payload = [
        [
            'number' => 'G2024201',
            'card_number' => '1234567890',
            'card_number2' => '123',
            'card_number3' => '55555',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => 'G2024200',
            'card_number' => '1234567891',
            'card_number2' => '222',
            'card_number3' => '87878',
            'update_library_card' => false,
            'card_type' => 'NFC',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateCardForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(2)
        ->toEqual([
            $payload[0],
            $payload[1],
        ]);

    expect($response->getInvalidData())
        ->toHaveCount(0)
        ->toEqual([]);
});


test('validateUserNumberForImport()', function () {
    /**
     *
     *  validate student number dont exist in database
     *
     */

    $payload = [
        [
            'number' => 'not existing',
            'card_number' => '1234567890',
            'card_number2' => '123',
            'card_number3' => '55555',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = app()->make(CardService::class)
        ->setData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateUserNumberForImport()
        ->validated();


    expect($response->getValidData())
        ->toHaveCount(0);

    expect($response->getInvalidData())
        ->toHaveCount(1)
        ->toHaveKey('0.errors', ['Student number does not exist in the database.']);

    expect($response->getCardIdsToBeDeactivated())
        ->toHaveCount(0);

    /**
     *
     *  validate student number exist ond dont have active card
     *
     */

    $valid_student = Student::factory()->create();

    $payload = [
        [
            'number' => $valid_student->student_number,
            'card_number' => '1234567890',
            'card_number2' => '123',
            'card_number3' => '55555',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = app()->make(CardService::class)
        ->setData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateUserNumberForImport()
        ->validated();


    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([
            [
                'student_id' => $valid_student->id,
                'student_name' => $valid_student->getTranslations('name'),
                'number' => $payload[0]['number'],
                'card_number' => $payload[0]['card_number'],
                'card_number2' => $payload[0]['card_number2'],
                'card_number3' => $payload[0]['card_number3'],
                'update_library_card' => $payload[0]['update_library_card'],
                'card_type' => $payload[0]['card_type'],
            ]
        ]);

    expect($response->getInvalidData())
        ->toHaveCount(0)
        ->toEqual([]);

    expect($response->getCardIdsToBeDeactivated())
        ->toHaveCount(0);

    /**
     *
     *  validate student number exist ond already have an active card
     *
     */

    $valid_student = Student::factory()->create();

    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $valid_student->id,
        'card_type' => CardType::PROXIMITY->value,
        'status' => CardStatus::ACTIVE->value,
        'card_number' => '123456781',
    ]);

    $payload = [
        [
            'number' => $valid_student->student_number,
            'card_number' => '1234567890', // new card
            'card_number2' => '123',
            'card_number3' => '55555',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = app()->make(CardService::class)
        ->setData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateUserNumberForImport()
        ->validated();


    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([
            [
                'student_id' => $valid_student->id,
                'student_name' => $valid_student->getTranslations('name'),
                'number' => $payload[0]['number'],
                'card_number' => $payload[0]['card_number'],
                'card_number2' => $payload[0]['card_number2'],
                'card_number3' => $payload[0]['card_number3'],
                'update_library_card' => $payload[0]['update_library_card'],
                'card_type' => $payload[0]['card_type'],
            ]
        ]);

    expect($response->getInvalidData())
        ->toHaveCount(0)
        ->toEqual([]);

    // expect that $card is to be deactivate
    $cards_to_be_deactivated = $response->getCardIdsToBeDeactivated();

    expect($cards_to_be_deactivated)
        ->toHaveCount(1)
        ->and($cards_to_be_deactivated[0])
        ->toEqual($card->id);

    /**
     *
     *  validate contractor number dont exist in database
     *
     */

    $payload = [
        [
            'number' => 'not existing',
            'card_number' => '1234567890',
            'card_number2' => '123',
            'card_number3' => '55555',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = app()->make(CardService::class)
        ->setData($payload)
        ->setImportType(CardTemplateType::CONTRACTOR)
        ->validateUserNumberForImport()
        ->validated();


    expect($response->getValidData())
        ->toHaveCount(0);

    expect($response->getInvalidData())
        ->toHaveCount(1)
        ->toHaveKey('0.errors', ['Contractor number does not exist in the database.']);

    expect($response->getCardIdsToBeDeactivated())
        ->toHaveCount(0);

    /**
     *
     *  validate all pass
     *  validate Contractor number exist ond dont have active card
     *
     */

    $valid_contractor = Contractor::factory()->create();

    $payload = [
        [
            'number' => $valid_contractor->contractor_number,
            'card_number' => '1234567890',
            'card_number2' => '123',
            'card_number3' => '55555',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = app()->make(CardService::class)
        ->setData($payload)
        ->setImportType(CardTemplateType::CONTRACTOR)
        ->validateUserNumberForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(1)
        ->toEqual([
            [
                'contractor_id' => $valid_contractor->id,
                'contractor_name' => $valid_contractor->getTranslations('name'),
                'number' => $payload[0]['number'],
                'card_number' => $payload[0]['card_number'],
                'card_number2' => $payload[0]['card_number2'],
                'card_number3' => $payload[0]['card_number3'],
                'update_library_card' => $payload[0]['update_library_card'],
                'card_type' => $payload[0]['card_type'],
            ]
        ]);

    expect($response->getInvalidData())
        ->toHaveCount(0)
        ->toEqual([]);

    expect($response->getCardIdsToBeDeactivated())
        ->toHaveCount(0);
});

test('validateCardInDatabaseForImport()', function () {
    /**
     *
     *  validate card_number already being used
     *
     */

    $existing_student = Student::factory()->create();

    $existing_card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $existing_student->id,
        'card_type' => CardType::PROXIMITY->value,
        'status' => CardStatus::ACTIVE->value,
        'card_number' => '123456781',
    ]);


    $new_student = Student::factory()->create();

    $payload = [
        [
            'number' => $new_student->student_number,
            'card_number' => $existing_card->card_number, // try to assign used card to new_student
            'card_number2' => '123',
            'card_number3' => '55555',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateCardInDatabaseForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(0);

    expect($response->getInvalidData())
        ->toHaveCount(1)
        ->toHaveKey('0.errors', ['Card number already being used in the database.']);


    /**
     *
     *  validate all pass because card_number not being used
     *
     */

    $students = Student::factory(2)->state(new Sequence(
        [
            'student_number' => 'H20242222',
        ],
        [
            'student_number' => 'H20242202',
        ]
    ))->create();

    $payload = [
        [
            'number' => $students[0]->student_number,
            'card_number' => '1234567891', // new number
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'number' => $students[1]->student_number,
            'card_number' => '7788567891', // new number
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateCardInDatabaseForImport()
        ->validated();

    expect($response->getValidData())
        ->toHaveCount(2)
        ->toEqual($payload);

    expect($response->getInvalidData())
        ->toHaveCount(0)
        ->toEqual([]);
});

test('hasValidationError()', function () {
    /**
     *
     *  return true because there is invalid report data
     *
     */

    $payload = [
        [
            'number' => '122222233',
            'card_number' => '1234567891',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateCardForImport()
        ->validateUserNumberForImport()
        ->validateCardInDatabaseForImport()
        ->validated()
        ->hasValidationError();

    expect($response)->toBeTruthy();

    /**
     *
     *  return false because there is no invalid report data
     *
     */

    $response = $this->cardService
        ->setValidData([])
        ->setInvalidData([])
        ->hasValidationError();

    expect($response)->toBeFalsy();
});


test('insert() without updating library_member', function () {
    expect(function () {
        $this->cardService->insert();
    })->toThrow('Import data, import type must be set.');


    /**
     * prepare data to insert students' card
     */

    $students = Student::factory(3)->state(new Sequence(
        [
            'student_number' => 'STUDENT-A-001',
        ],
        [
            'student_number' => 'STUDENT-A-002',
        ],
        [
            'student_number' => 'STUDENT-A-003',
        ],
    ))->create();

    $payload = [
        [
            'student_id' => $students[0]->id,
            'card_number' => '1234567822',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
        [
            'student_id' => $students[1]->id,
            'card_number' => '3334567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
        [
            'student_id' => $students[2]->id,
            'card_number' => '4434567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setValidData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->insert();

    expect($response)->toBeNull();

    foreach ($payload as $card) {
        $this->assertDatabaseHas('cards', [
            'userable_type' => Student::class,
            'userable_id' => $card['student_id'],
            'card_number' => $card['card_number'],
            'card_number2' => $card['card_number2'],
            'card_number3' => $card['card_number3'],
            'card_type' => $card['card_type'],
            'status' => CardStatus::ACTIVE->value,
        ]);
    }


    /**
     * prepare data to insert employees' card
     */

    $employees = Employee::factory(3)->state(new Sequence(
        [
            'employee_number' => 'EMP-001',
        ],
        [
            'employee_number' => 'EMP-002',
        ],
        [
            'employee_number' => 'EMP-003',
        ],
    ))->create();

    $employee_payload = [
        [
            'employee_id' => $employees[0]->id,
            'card_number' => '333333331',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[1]->id,
            'card_number' => '88777888888',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[2]->id,
            'card_number' => '11122233343',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
    ];


    $response = $this->cardService
        ->setValidData($employee_payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->insert();

    expect($response)->toBeNull();

    foreach ($employee_payload as $card) {
        $this->assertDatabaseHas('cards', [
            'userable_type' => Employee::class,
            'userable_id' => $card['employee_id'],
            'card_number' => $card['card_number'],
            'card_number2' => $card['card_number2'],
            'card_number3' => $card['card_number3'],
            'card_type' => $card['card_type'],
            'status' => CardStatus::ACTIVE->value,
        ]);
    }


    /**
     * prepare data to insert contractors' card
     */

    $contractors = Contractor::factory(3)->state(new Sequence(
        [
            'contractor_number' => 'CONTR-001',
        ],
        [
            'contractor_number' => 'CONTR-002',
        ],
        [
            'contractor_number' => 'CONTR-003',
        ],
    ))->create();

    $contractor_payload = [
        [
            'contractor_id' => $contractors[0]->id,
            'card_number' => '333333330',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
        [
            'contractor_id' => $contractors[1]->id,
            'card_number' => '88777888882',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
        [
            'contractor_id' => $contractors[2]->id,
            'card_number' => '11122233333',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false,
            'card_type' => 'PROXIMITY',
        ],
    ];


    $response = $this->cardService
        ->setValidData($contractor_payload)
        ->setImportType(CardTemplateType::CONTRACTOR)
        ->insert();

    expect($response)->toBeNull();

    foreach ($contractor_payload as $card) {
        $this->assertDatabaseHas('cards', [
            'userable_type' => Contractor::class,
            'userable_id' => $card['contractor_id'],
            'card_number' => $card['card_number'],
            'card_number2' => $card['card_number2'],
            'card_number3' => $card['card_number3'],
            'card_type' => $card['card_type'],
            'status' => CardStatus::ACTIVE->value,
        ]);
    }
});

test('insert() with updating library_member', function () {

    /**
     * prepare data to insert students' card
     */

    $students = Student::factory(2)->state(new Sequence(
        [
            'student_number' => 'STUDENT-A-001',
        ],
        [
            'student_number' => 'STUDENT-A-002',
        ],
    ))->create();


    $library_members = LibraryMember::factory(2)->state(new Sequence(
        [
            'type' => LibraryMemberType::STUDENT->value,
            'userable_id' => $students[0]->id,
            'userable_type' => Student::class,
            'card_number' => '11111111',
        ],
        [
            'type' => LibraryMemberType::STUDENT->value,
            'userable_id' => $students[1]->id,
            'userable_type' => Student::class,
            'card_number' => '2222222',
        ],
    ))->create();

    $payload = [
        [
            'student_id' => $students[0]->id,
            'card_number' => '1234567822',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'student_id' => $students[1]->id,
            'card_number' => '3334567890',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setValidData($payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->insert();

    expect($response)->toBeNull();

    foreach ($payload as $card) {
        $this->assertDatabaseHas('cards', [
            'userable_type' => Student::class,
            'userable_id' => $card['student_id'],
            'card_number' => $card['card_number'],
            'card_number2' => $card['card_number2'],
            'card_number3' => $card['card_number3'],
            'card_type' => $card['card_type'],
            'status' => CardStatus::ACTIVE->value,
        ]);

        $this->assertDatabaseHas('library_members', [
            'type' => LibraryMemberType::STUDENT->value,
            'userable_type' => Student::class,
            'userable_id' => $card['student_id'],
            'card_number' => $card['card_number'],
        ]);
    }


    /**
     * prepare data to insert employees' card
     */

    $employees = Employee::factory(5)->state(new Sequence(
        [
            'employee_number' => 'EMPLO001',
        ],
        [
            'employee_number' => 'EMPLO002',
        ],
        [
            'employee_number' => 'EMPLO003',
        ],
        [
            'employee_number' => 'EMPLO004',
        ],
        [
            'employee_number' => 'EMPLO005',
        ],
    ))->create();


    $library_members = LibraryMember::factory(5)->state(new Sequence(
        [
            'type' => LibraryMemberType::EMPLOYEE->value,
            'userable_id' => $employees[0]->id,
            'userable_type' => Employee::class,
            'card_number' => '11111111',
        ],
        [
            'type' => LibraryMemberType::EMPLOYEE->value,
            'userable_id' => $employees[1]->id,
            'userable_type' => Employee::class,
            'card_number' => '2222222',
        ],
        [
            'type' => LibraryMemberType::EMPLOYEE->value,
            'userable_id' => $employees[2]->id,
            'userable_type' => Employee::class,
            'card_number' => '333333333',
        ],
        [
            'type' => LibraryMemberType::EMPLOYEE->value,
            'userable_id' => $employees[3]->id,
            'userable_type' => Employee::class,
            'card_number' => 'OLD-444444444',
        ],
        [
            'type' => LibraryMemberType::EMPLOYEE->value,
            'userable_id' => $employees[4]->id,
            'userable_type' => Employee::class,
            'card_number' => 'OLD-55555555',
        ],
    ))->create();


    $employee_payload = [
        [
            'employee_id' => $employees[0]->id,
            'card_number' => '1234567812',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[1]->id,
            'card_number' => '1234567811',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[2]->id,
            'card_number' => '1234567813',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[3]->id,
            'card_number' => '1234567814',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false, // dont update library member card_number
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[4]->id,
            'card_number' => '1234567815',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false, // dont update library member card_number
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = $this->cardService
        ->setValidData($employee_payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->insert();

    expect($response)->toBeNull();

    foreach ($employee_payload as $card) {
        $this->assertDatabaseHas('cards', [
            'userable_type' => Employee::class,
            'userable_id' => $card['employee_id'],
            'card_number' => $card['card_number'],
            'card_number2' => $card['card_number2'],
            'card_number3' => $card['card_number3'],
            'card_type' => $card['card_type'],
            'status' => CardStatus::ACTIVE->value,
        ]);
    }

    $this->assertDatabaseHas('library_members', [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee_payload[0]['employee_id'],
        'card_number' => $employee_payload[0]['card_number'],
    ]);

    $this->assertDatabaseHas('library_members', [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee_payload[1]['employee_id'],
        'card_number' => $employee_payload[1]['card_number'],
    ]);

    $this->assertDatabaseHas('library_members', [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee_payload[2]['employee_id'],
        'card_number' => $employee_payload[2]['card_number'],
    ]);

    // update_lib_member is false
    $this->assertDatabaseMissing('library_members', [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee_payload[3]['employee_id'],
        'card_number' => $employee_payload[3]['card_number'],
    ]);

    // update_lib_member is false
    $this->assertDatabaseMissing('library_members', [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee_payload[4]['employee_id'],
        'card_number' => $employee_payload[4]['card_number'],
    ]);

    // update_lib_member remained card_number
    $this->assertDatabaseHas('library_members', [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee_payload[3]['employee_id'],
        'card_number' => $library_members[3]['card_number'],
    ]);

    // update_lib_member remained card_number
    $this->assertDatabaseHas('library_members', [
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $employee_payload[4]['employee_id'],
        'card_number' => $library_members[4]['card_number'],
    ]);
});

test('insert() with deactivating existing cards', function () {

    /**
     * prepare data to insert students' card
     */

    $students = Student::factory(2)->state(new Sequence(
        [
            'student_number' => 'STUDENT-A-001',
        ],
        [
            'student_number' => 'STUDENT-A-002',
        ],
    ))->create();

    $student_active_cards = Card::factory(2)->state(new Sequence(
        [
            'userable_type' => Student::class,
            'userable_id' => $students[0]->id,
            'card_number' => '11111111',
            'card_type' => CardType::PROXIMITY->value,
            'status' => CardStatus::ACTIVE->value,
        ],
        [
            'userable_type' => Student::class,
            'userable_id' => $students[1]->id,
            'card_number' => '2222222',
            'card_type' => CardType::PROXIMITY->value,
            'status' => CardStatus::ACTIVE->value,
        ],
    ))->create();

    $student_payload = [
        [
            'student_id' => $students[0]->id,
            'number' => $students[0]->student_number,
            'card_number' => '1234567822', // new card assigning to student
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
        [
            'student_id' => $students[1]->id,
            'number' => $students[1]->student_number,
            'card_number' => '3334567890', // new card assigning to student
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => true,
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = app()->make(CardService::class)
        ->setData($student_payload)
        ->setImportType(CardTemplateType::STUDENT)
        ->validateUserNumberForImport()
        ->validated()
        ->insert();

    expect($response)->toBeNull();

    foreach ($student_payload as $card) {
        $this->assertDatabaseHas('cards', [
            'userable_type' => Student::class,
            'userable_id' => $card['student_id'],
            'card_number' => $card['card_number'],
            'card_number2' => $card['card_number2'],
            'card_number3' => $card['card_number3'],
            'card_type' => $card['card_type'],
            'status' => CardStatus::ACTIVE->value,
        ]);
    }

    // expect cards to be deactived
    foreach ($student_active_cards as $card) {
        $this->assertDatabaseHas('cards', [
            'id' => $card->id,
            'status' => CardStatus::INACTIVE->value,
        ]);
    }

    /**
     * prepare data to insert employees' card
     */

    $employees = Employee::factory(5)->state(new Sequence(
        [
            'employee_number' => 'EMPLO001',
        ],
        [
            'employee_number' => 'EMPLO002',
        ],
        [
            'employee_number' => 'EMPLO003',
        ],
        [
            'employee_number' => 'EMPLO004',
        ],
        [
            'employee_number' => 'EMPLO005',
        ],
    ))->create();

    $employee_active_cards = Card::factory(5)->state(new Sequence(
        [
            'userable_type' => Employee::class,
            'userable_id' => $employees[0]->id,
            'card_number' => '11111112',
            'card_type' => CardType::PROXIMITY->value,
            'status' => CardStatus::ACTIVE->value,
        ],
        [
            'userable_type' => Employee::class,
            'userable_id' => $employees[1]->id,
            'card_number' => '22222223',
            'card_type' => CardType::PROXIMITY->value,
            'status' => CardStatus::ACTIVE->value,
        ],
        [
            'userable_type' => Employee::class,
            'userable_id' => $employees[2]->id,
            'card_number' => '33333334',
            'card_type' => CardType::PROXIMITY->value,
            'status' => CardStatus::ACTIVE->value,
        ],
        [
            'userable_type' => Employee::class,
            'userable_id' => $employees[3]->id,
            'card_number' => '44444445',
            'card_type' => CardType::PROXIMITY->value,
            'status' => CardStatus::ACTIVE->value,
        ],
        [
            'userable_type' => Employee::class,
            'userable_id' => $employees[4]->id,
            'card_number' => '55555556',
            'card_type' => CardType::PROXIMITY->value,
            'status' => CardStatus::ACTIVE->value,
        ],
    ))->create();

    $employee_payload = [
        [
            'employee_id' => $employees[0]->id,
            'number' => $employees[0]->employee_number,
            'card_number' => '1234567812',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false, // dont update library member card_number
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[1]->id,
            'number' => $employees[1]->employee_number,
            'card_number' => '1234567811',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false, // dont update library member card_number
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[2]->id,
            'number' => $employees[2]->employee_number,
            'card_number' => '1234567813',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false, // dont update library member card_number
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[3]->id,
            'number' => $employees[3]->employee_number,
            'card_number' => '1234567814',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false, // dont update library member card_number
            'card_type' => 'PROXIMITY',
        ],
        [
            'employee_id' => $employees[4]->id,
            'number' => $employees[4]->employee_number,
            'card_number' => '1234567815',
            'card_number2' => '',
            'card_number3' => '',
            'update_library_card' => false, // dont update library member card_number
            'card_type' => 'PROXIMITY',
        ],
    ];

    $response = app()->make(CardService::class)
        ->setData($employee_payload)
        ->setImportType(CardTemplateType::EMPLOYEE)
        ->validateUserNumberForImport()
        ->validated()
        ->insert();

    expect($response)->toBeNull();

    foreach ($employee_payload as $card) {
        $this->assertDatabaseHas('cards', [
            'userable_type' => Employee::class,
            'userable_id' => $card['employee_id'],
            'card_number' => $card['card_number'],
            'card_number2' => $card['card_number2'],
            'card_number3' => $card['card_number3'],
            'card_type' => $card['card_type'],
            'status' => CardStatus::ACTIVE->value,
        ]);
    }

    // expect cards to be deactived
    foreach ($employee_active_cards as $card) {
        $this->assertDatabaseHas('cards', [
            'id' => $card->id,
            'status' => CardStatus::INACTIVE->value,
        ]);
    }
});

test('getCardTemplateData()', function () {
    /**
     * get sample_data when no data in DB
     */

    $response = $this->cardService->getCardTemplateData(['type' => CardTemplateType::EMPLOYEE->value]);

    expect($response)->toEqual([
        'profiles' => [
            [
                'number' => '202410000 - SAMPLEDATA',
                'card_number' => '1234567891 - SAMPLEDATA',
                'card_number2' => '333 - SAMPLEDATA',
                'card_number3' => '12345 - SAMPLEDATA',
                'update_library_card_number' => 'true - SAMPLEDATA',
                'card_type' => 'NFC - SAMPLEDATA',
            ],
            [
                'number' => '202410001 - SAMPLEDATA',
                'card_number' => '1234567892 - SAMPLEDATA',
                'card_number2' => '331 - SAMPLEDATA',
                'card_number3' => '12341 - SAMPLEDATA',
                'update_library_card_number' => 'false - SAMPLEDATA',
                'card_type' => 'PROXIMITY - SAMPLEDATA',
            ],
        ],
        'type' => 'Employee',
    ]);


    $response = $this->cardService->getCardTemplateData(['type' => CardTemplateType::STUDENT->value]);

    expect($response)->toEqual([
        'profiles' => [
            [
                'number' => '202410000 - SAMPLEDATA',
                'card_number' => '1234567891 - SAMPLEDATA',
                'card_number2' => '333 - SAMPLEDATA',
                'card_number3' => '12345 - SAMPLEDATA',
                'update_library_card_number' => 'true - SAMPLEDATA',
                'card_type' => 'NFC - SAMPLEDATA',
            ],
            [
                'number' => '202410001 - SAMPLEDATA',
                'card_number' => '1234567892 - SAMPLEDATA',
                'card_number2' => '331 - SAMPLEDATA',
                'card_number3' => '12341 - SAMPLEDATA',
                'update_library_card_number' => 'false - SAMPLEDATA',
                'card_type' => 'PROXIMITY - SAMPLEDATA',
            ],
        ],
        'type' => 'Student',
    ]);


    $response = $this->cardService->getCardTemplateData(['type' => CardTemplateType::CONTRACTOR->value]);

    expect($response)->toEqual([
        'profiles' => [
            [
                'number' => '202410000 - SAMPLEDATA',
                'card_number' => '1234567891 - SAMPLEDATA',
                'card_number2' => '333 - SAMPLEDATA',
                'card_number3' => '12345 - SAMPLEDATA',
                'update_library_card_number' => 'true - SAMPLEDATA',
                'card_type' => 'NFC - SAMPLEDATA',
            ],
            [
                'number' => '202410001 - SAMPLEDATA',
                'card_number' => '1234567892 - SAMPLEDATA',
                'card_number2' => '331 - SAMPLEDATA',
                'card_number3' => '12341 - SAMPLEDATA',
                'update_library_card_number' => 'false - SAMPLEDATA',
                'card_type' => 'PROXIMITY - SAMPLEDATA',
            ],
        ],
        'type' => 'Contractor',
    ]);


    /**
     *
     * prepare data
     *
     */

    $employees = Employee::factory(2)->state(new Sequence(
        [
            'employee_number' => 'EMPLO001', // have active card
        ],
        [
            'employee_number' => 'EMPLO002',
        ],
    ))->create();

    $students = Student::factory(2)->state(new Sequence(
        [
            'student_number' => 'STUD001', // have active card
        ],
        [
            'student_number' => 'STUD002',
        ],
    ))->create();

    $contractors = Contractor::factory(2)->state(new Sequence(
        [
            'contractor_number' => 'CONTR001', // have active card
        ],
        [
            'contractor_number' => 'CONTR002',
        ],
    ))->create();

    $cards = Card::factory(3)->state(new Sequence(
        [
            'userable_type' => get_class($employees[0]),
            'userable_id' => $employees[0]->id,
        ],
        [
            'userable_type' => get_class($students[0]),
            'userable_id' => $students[0]->id,
        ],
        [
            'userable_type' => get_class($contractors[0]),
            'userable_id' => $contractors[0]->id,
        ],
    ))->create();

    /**
     * get card_data for EMPLOYEE
     *
     * 1 employee have active card , excluded from data
     * 1 employee dont have active card , included in data
     *
     */

    $response = $this->cardService->getCardTemplateData(['type' => CardTemplateType::EMPLOYEE->value]);

    expect($response)->toEqual([
        'profiles' => [
            [
                'number' => '202410000 - SAMPLEDATA',
                'card_number' => '1234567891 - SAMPLEDATA',
                'card_number2' => '333 - SAMPLEDATA',
                'card_number3' => '12345 - SAMPLEDATA',
                'update_library_card_number' => 'true - SAMPLEDATA',
                'card_type' => 'NFC - SAMPLEDATA',
            ],
            [
                'number' => '202410001 - SAMPLEDATA',
                'card_number' => '1234567892 - SAMPLEDATA',
                'card_number2' => '331 - SAMPLEDATA',
                'card_number3' => '12341 - SAMPLEDATA',
                'update_library_card_number' => 'false - SAMPLEDATA',
                'card_type' => 'PROXIMITY - SAMPLEDATA',
            ],
            [
                'number' => $employees[1]->employee_number,
            ],
        ],
        'type' => 'Employee',
    ]);

    /**
     * get card_data for STUDENT
     */

    $response = $this->cardService->getCardTemplateData(['type' => CardTemplateType::STUDENT->value]);

    expect($response)->toEqual([
        'profiles' => [
            [
                'number' => '202410000 - SAMPLEDATA',
                'card_number' => '1234567891 - SAMPLEDATA',
                'card_number2' => '333 - SAMPLEDATA',
                'card_number3' => '12345 - SAMPLEDATA',
                'update_library_card_number' => 'true - SAMPLEDATA',
                'card_type' => 'NFC - SAMPLEDATA',
            ],
            [
                'number' => '202410001 - SAMPLEDATA',
                'card_number' => '1234567892 - SAMPLEDATA',
                'card_number2' => '331 - SAMPLEDATA',
                'card_number3' => '12341 - SAMPLEDATA',
                'update_library_card_number' => 'false - SAMPLEDATA',
                'card_type' => 'PROXIMITY - SAMPLEDATA',
            ],
            [
                'number' => $students[1]->student_number,
            ],
        ],
        'type' => 'Student',
    ]);


    /**
     * get card_data for CONTRACTOR
     */

    $response = $this->cardService->getCardTemplateData(['type' => CardTemplateType::CONTRACTOR->value]);

    expect($response)->toEqual([
        'profiles' => [
            [
                'number' => '202410000 - SAMPLEDATA',
                'card_number' => '1234567891 - SAMPLEDATA',
                'card_number2' => '333 - SAMPLEDATA',
                'card_number3' => '12345 - SAMPLEDATA',
                'update_library_card_number' => 'true - SAMPLEDATA',
                'card_type' => 'NFC - SAMPLEDATA',
            ],
            [
                'number' => '202410001 - SAMPLEDATA',
                'card_number' => '1234567892 - SAMPLEDATA',
                'card_number2' => '331 - SAMPLEDATA',
                'card_number3' => '12341 - SAMPLEDATA',
                'update_library_card_number' => 'false - SAMPLEDATA',
                'card_type' => 'PROXIMITY - SAMPLEDATA',
            ],
            [
                'number' => $contractors[1]->contractor_number,
            ],
        ],
        'type' => 'Contractor',
    ]);
});

test('getCardTemplateData(): test EXCEL content', function () {
    $employees = Employee::factory(2)->state(new Sequence(
        [
            'employee_number' => 'EMPLO001', // have active card
        ],
        [
            'employee_number' => 'EMPLO002',
        ],
    ))->create();

    $students = Student::factory(2)->state(new Sequence(
        [
            'student_number' => 'STUD001', // have active card
        ],
        [
            'student_number' => 'STUD002',
        ],
    ))->create();

    $contractors = Contractor::factory(2)->state(new Sequence(
        [
            'contractor_number' => 'CONTR001', // have active card
        ],
        [
            'contractor_number' => 'CONTR002',
        ],
    ))->create();

    $cards = Card::factory(3)->state(new Sequence(
        [
            'userable_type' => get_class($employees[0]),
            'userable_id' => $employees[0]->id,
        ],
        [
            'userable_type' => get_class($students[0]),
            'userable_id' => $students[0]->id,
        ],
        [
            'userable_type' => get_class($contractors[0]),
            'userable_id' => $contractors[0]->id,
        ],
    ))->create();

    /**
     * test EXCEL content for EMPLOYEE
     *
     */

    $data = $this->cardService->getCardTemplateData(['type' => CardTemplateType::EMPLOYEE->value]);


    $report_data = [
        'data' => $data,
    ];

    $report_view_name = 'templates.card-template';
    $file_name = 'card-template';

    Excel::fake();

    $export_type = ExportType::EXCEL;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['Employee Number', 'Card Number', 'Card Number 2', 'Card Number 3', 'Update Library Card Number (true/false)', 'Card Type (NFC/PROXIMITY)'];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($expected_headers, $report_view_name, $report_data, $employees) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($report_data['data']['profiles'][0]['number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number2']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number3']);
            $view->assertSee($report_data['data']['profiles'][0]['update_library_card_number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_type']);

            $view->assertSee($report_data['data']['profiles'][1]['number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number2']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number3']);
            $view->assertSee($report_data['data']['profiles'][1]['update_library_card_number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_type']);

            $view->assertSee($employees[1]->employee_number);
            $view->assertDontSee($employees[0]->employee_number);

            return true;
        }
    );


    /**
     * test EXCEL content for STUDENT
     *
     */

    $data = $this->cardService->getCardTemplateData(['type' => CardTemplateType::STUDENT->value]);


    $report_data = [
        'data' => $data,
    ];

    $report_view_name = 'templates.card-template';
    $file_name = 'card-template';

    Excel::fake();

    $export_type = ExportType::EXCEL;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['Student Number', 'Card Number', 'Card Number 2', 'Card Number 3', 'Update Library Card Number (true/false)', 'Card Type (NFC/PROXIMITY)'];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($expected_headers, $report_view_name, $report_data, $students) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($report_data['data']['type']);

            $view->assertSee($report_data['data']['profiles'][0]['number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number2']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number3']);
            $view->assertSee($report_data['data']['profiles'][0]['update_library_card_number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_type']);

            $view->assertSee($report_data['data']['profiles'][1]['number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number2']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number3']);
            $view->assertSee($report_data['data']['profiles'][1]['update_library_card_number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_type']);

            $view->assertSee($students[1]->student_number);
            $view->assertDontSee($students[0]->student_number);

            return true;
        }
    );


    /**
     * test EXCEL content for CONTRACTOR
     *
     */

    $data = $this->cardService->getCardTemplateData(['type' => CardTemplateType::CONTRACTOR->value]);


    $report_data = [
        'data' => $data,
    ];

    $report_view_name = 'templates.card-template';
    $file_name = 'card-template';

    Excel::fake();

    $export_type = ExportType::from(ExportType::EXCEL->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['Contractor Number', 'Card Number', 'Card Number 2', 'Card Number 3', 'Update Library Card Number (true/false)', 'Card Type (NFC/PROXIMITY)'];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($expected_headers, $report_view_name, $report_data, $contractors) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($report_data['data']['type']);

            $view->assertSee($report_data['data']['profiles'][0]['number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number2']);
            $view->assertSee($report_data['data']['profiles'][0]['card_number3']);
            $view->assertSee($report_data['data']['profiles'][0]['update_library_card_number']);
            $view->assertSee($report_data['data']['profiles'][0]['card_type']);

            $view->assertSee($report_data['data']['profiles'][1]['number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number2']);
            $view->assertSee($report_data['data']['profiles'][1]['card_number3']);
            $view->assertSee($report_data['data']['profiles'][1]['update_library_card_number']);
            $view->assertSee($report_data['data']['profiles'][1]['card_type']);

            $view->assertSee($contractors[1]->contractor_number);
            $view->assertDontSee($contractors[0]->contractor_number);

            return true;
        }
    );
});
