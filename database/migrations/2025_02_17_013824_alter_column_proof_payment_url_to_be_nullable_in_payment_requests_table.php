<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_requests', function (Blueprint $table) {
            $table->text('proof_of_payment_url')->nullable()->change();
        
            // previous columns all timestamp
            $table->dropColumn([
                'processed_by_employee_id',
                'approved_by_employee_id',
                'posted_by_employee_id'
            ]);
        });
        
        Schema::table('payment_requests', function (Blueprint $table) {
            $table->foreignId('processed_by_employee_id')->nullable();
            $table->foreignId('approved_by_employee_id')->nullable();
            $table->foreignId('posted_by_employee_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_requests', function (Blueprint $table) {
            //
        });
    }
};
