<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('book_loan_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('book_id');
            $table->string('type');
            $table->unsignedInteger('loan_period_day');
            $table->boolean('can_borrow')->default(true);
            $table->timestamps();

            $table->index(['book_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('book_loan_settings');
    }
};
