<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_document_advance_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('billable_type');
            $table->unsignedInteger('billable_id');
            $table->unsignedInteger('advance_invoice_id');
            $table->string('gl_account_code', 16);
            $table->string('currency_code', 4);
            $table->decimal('amount_before_tax', 12, 2);
            $table->unsignedInteger('used_in_invoice_id')->nullable();
            $table->timestamps();

            $table->index(['billable_type', 'billable_id', 'currency_code', 'gl_account_code']);
            $table->index(['advance_invoice_id', 'used_in_invoice_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_document_advance_transactions');
    }
};
