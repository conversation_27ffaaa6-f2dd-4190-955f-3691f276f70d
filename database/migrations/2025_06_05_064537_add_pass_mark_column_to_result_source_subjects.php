<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('result_source_subjects', function (Blueprint $table) {
            $table->decimal('pass_mark', 5, 2)->nullable();  
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('result_source_subjects', function (Blueprint $table) {
            $table->dropColumn('pass_mark');
        });
    }
};
