<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement(file_get_contents(__DIR__ . '/views/employee_timetables_materialized_view.sql'));

        DatabaseHelper::createIndex('employee_timetables', 'employee_id_idx', ['employee_id']);
        DatabaseHelper::createIndex('employee_timetables', 'day_idx', ['day']);
        DatabaseHelper::createIndex('employee_timetables', 'period_idx', ['period']);
        DatabaseHelper::createIndex('employee_timetables', 'timeslot_id_idx', ['timeslot_id']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP MATERIALIZED VIEW IF EXISTS employee_timetables;");
    }
};
