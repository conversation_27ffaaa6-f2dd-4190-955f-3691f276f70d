<?php

use App\Enums\ContractorStatus;
use App\Enums\EmployeeStatus;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Guest;
use App\Models\Merchant;
use App\Models\Student;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE OR REPLACE VIEW userable_views AS
            SELECT
                employees.id AS userable_id,
                '" . Employee::class . "' AS userable_type,
                '" . resolve(Employee::class)->getUserTypeDescription() . "' AS user_type_description,
                employees.name AS name,
                employees.email AS email,
                employees.phone_number AS phone_number,
                employees.employee_number AS number,
                employees.user_id AS user_id,
                CASE WHEN employees.status IS NOT NULL AND employees.status = '" . EmployeeStatus::WORKING->value . "' THEN TRUE ELSE FALSE END AS is_active
            FROM employees
            UNION
            SELECT
                students.id AS userable_id,
                '" . Student::class . "' AS userable_type,
                '" . resolve(Student::class)->getUserTypeDescription() . "' AS user_type_description,
                students.name AS name,
                students.email AS email,
                students.phone_number AS phone_number,
                students.student_number AS number,
                students.user_id AS user_id,
                students.is_active as is_active
            FROM students
            UNION
            SELECT
                guardians.id AS userable_id,
                '" . Guardian::class . "' AS userable_type,
                '" . resolve(Guardian::class)->getUserTypeDescription() . "' AS user_type_description,
                guardians.name AS name,
                guardians.email AS email,
                guardians.phone_number AS phone_number,
                NULL AS number,
                guardians.user_id AS user_id,
                TRUE as is_active
            FROM guardians
            UNION
            SELECT
                contractors.id AS userable_id,
                '" . Contractor::class . "' AS userable_type,
                '" . resolve(Contractor::class)->getUserTypeDescription() . "' AS user_type_description,
                contractors.name AS name,
                contractors.email AS email,
                contractors.phone_number AS phone_number,
                contractors.contractor_number AS number,
                contractors.user_id AS user_id,
                CASE WHEN contractors.status = '" . ContractorStatus::ACTIVE->value . "' THEN TRUE ELSE FALSE END AS is_active
            FROM contractors
            UNION
            SELECT
                merchants.id AS userable_id,
                '" . Merchant::class . "' AS userable_type,
                '" . resolve(Merchant::class)->getUserTypeDescription() . "' AS user_type_description,
                merchants.name AS name,
                merchants.email AS email,
                merchants.phone_number AS phone_number,
                NULL AS number,
                merchants.user_id AS user_id,
                merchants.is_active as is_active
            FROM merchants
            UNION
            SELECT
                guests.id AS userable_id,
                '" . Guest::class . "' AS userable_type,
                '" . resolve(Guest::class)->getUserTypeDescription() . "' AS user_type_description,
                guests.name AS name,
                guests.email AS email,
                guests.phone_number AS phone_number,
                NULL AS number,
                guests.user_id AS user_id,
                TRUE as is_active
            FROM guests
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW userable_views');
    }
};
