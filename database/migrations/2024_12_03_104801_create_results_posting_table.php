<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('results_posting_line_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('header_id');
            $table->foreignId('report_card_output_component_id');
            $table->string('report_card_output_component_code', 32);
            $table->foreignId('student_id');
            $table->foreignId('grade_id');
            $table->foreignId('semester_class_id');
            $table->foreignId('subject_id')->nullable();
            $table->foreignId('grading_scheme_id')->nullable();
            $table->foreignId('grading_framework_id')->nullable();
            $table->decimal('total', 7, 2)->nullable();
            $table->jsonb('total_grade')->nullable();
            $table->string('label', 32)->nullable();
            $table->boolean('calculate_rank')->default(false);
            $table->decimal('weightage_multiplier', 7, 4)->nullable();
            $table->decimal('weightage_total', 7, 2)->nullable();

            $table->decimal('grade_percentile_rank', 5, 2)->nullable();
            $table->unsignedInteger('grade_rank')->nullable();
            $table->unsignedInteger('grade_population')->nullable();
            $table->unsignedInteger('class_rank')->nullable();
            $table->unsignedInteger('class_population')->nullable();

            $table->index(['header_id']);
            $table->index(['report_card_output_component_id'], 'idx_posting_line_item_1');
            $table->index(['report_card_output_component_code'], 'idx_posting_line_item_2');
            $table->index(['header_id', 'student_id', 'report_card_output_component_code'], 'idx_posting_line_item_3');     // update ranking query
            $table->index(['student_id', 'report_card_output_component_id'], 'idx_posting_line_item_4');     // archive
            $table->index(['student_id']);
            $table->index(['subject_id']);
            $table->index(['grading_framework_id']);
            $table->index(['grading_scheme_id']);

            $table->timestamps();
        });

        Schema::create('results_posting_line_items_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('header_id');
            $table->foreignId('report_card_output_component_id');
            $table->string('report_card_output_component_code', 32);
            $table->foreignId('student_id');
            $table->foreignId('grade_id');
            $table->foreignId('semester_class_id');
            $table->foreignId('subject_id')->nullable();
            $table->foreignId('grading_scheme_id')->nullable();
            $table->foreignId('grading_framework_id')->nullable();
            $table->decimal('total', 7, 2)->nullable();
            $table->jsonb('total_grade')->nullable();
            $table->string('label', 32)->nullable();
            $table->boolean('calculate_rank')->default(false);
            $table->decimal('weightage_multiplier', 7, 4)->nullable();
            $table->decimal('weightage_total', 7, 2)->nullable();

            $table->decimal('grade_percentile_rank', 5, 2)->nullable();
            $table->unsignedInteger('grade_rank')->nullable();
            $table->unsignedInteger('grade_population')->nullable();
            $table->unsignedInteger('class_rank')->nullable();
            $table->unsignedInteger('class_population')->nullable();

            $table->index(['header_id']);
            $table->index(['report_card_output_component_id'], 'idx_posting_line_item_history_1');
            $table->index(['report_card_output_component_code'], 'idx_posting_line_item_history_2');
            $table->index(['student_id']);
            $table->index(['subject_id']);
            $table->index(['grading_framework_id']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('results_posting');
    }
};
