<?php

use App\Enums\BookStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('books', function (Blueprint $table) {
            $table->id();
            $table->string('book_no')->nullable();
            $table->string('call_no')->nullable();
            $table->foreignId('book_category_id')->nullable();
            $table->foreignId('book_classification_id');
            $table->foreignId('book_sub_classification_id');
            $table->string('title');
            $table->string('series')->nullable();
            $table->string('edition')->nullable();
            $table->string('remark')->nullable();
            $table->string('topic')->nullable();
            $table->string('location_1')->nullable();
            $table->string('location_2')->nullable();
            $table->string('isbn')->nullable();
            $table->foreignId('book_source_id')->nullable();
            $table->string('binding')->nullable();
            $table->string('publisher')->nullable();
            $table->string('publisher_place')->nullable();
            $table->date('published_date')->nullable();
            $table->unsignedInteger('book_page')->nullable();
            $table->unsignedInteger('words')->nullable();
            $table->string('book_size')->nullable();
            $table->boolean('cdrom')->default(false);
            $table->string('status')->default(BookStatus::AVAILABLE);
            $table->decimal('purchase_value', 10, 2)->default(0);
            $table->decimal('lost_penalty_value', 10, 2)->default(0);
            $table->string('condition')->nullable();
            $table->foreignId('book_language_id')->nullable();
            $table->date('entry_date')->nullable();
            $table->timestamps();

            $table->index([
                'book_no',
                'call_no',
                'book_sub_classification_id',
                'title',
                'status',
            ], 'books_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('books');
    }
};
