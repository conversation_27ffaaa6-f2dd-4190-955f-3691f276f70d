<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cards', function (Blueprint $table) {
            $table->id();
            $table->morphs('userable');
            $table->string('name')->nullable();
            $table->string('card_number')->unique();
            $table->string('card_number2')->nullable();
            $table->string('card_number3')->nullable();
            $table->string('card_type');
            $table->string('status');
            $table->string('remarks')->nullable();
            $table->timestamps();

            $table->index([
                'userable_type', 'userable_id', 'name', 'card_number', 'status', 'card_number2', 'card_number3'
            ], 'cards_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cards');
    }
};
