<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('migration_mapping', function (Blueprint $table) {
            $table->id();
            $table->string('model');
            $table->string('old_table')->nullable();
            $table->string('old');
            $table->string('new');

            $table->index([
                'model', 'old', 'new'
            ]);

            $table->index([
                'model', 'old'
            ]);

            $table->index([
                'model', 'new',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('migration_mapping');
    }
};
