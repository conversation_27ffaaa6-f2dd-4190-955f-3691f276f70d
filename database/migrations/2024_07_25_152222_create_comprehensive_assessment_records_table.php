<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comprehensive_assessment_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('semester_setting_id');
            $table->foreignId('student_id');
            $table->foreignId('semester_class_id');
            $table->foreignId('comprehensive_assessment_question_id');
            $table->string('result');
            $table->foreignId('created_by_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comprehensive_assessment_records');
    }
};
