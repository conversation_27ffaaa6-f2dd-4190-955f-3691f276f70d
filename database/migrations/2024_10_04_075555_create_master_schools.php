<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_schools', function (Blueprint $table) {
            $table->id();
            $table->jsonb('name');
            $table->string('level', 16);
            $table->foreignId('state_id')->nullable();
            $table->timestamps();

            $table->index(['level', 'state_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_schools');
    }
};
