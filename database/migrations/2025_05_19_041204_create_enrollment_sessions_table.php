<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enrollment_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->date('from_date');
            $table->date('to_date');
            $table->string('code')->unique();
            $table->boolean('is_active');
            $table->foreignId('course_id');
            $table->year('admission_year');
            $table->jsonb('fee_assignment_settings')->nullable();
            $table->timestamps();
        });

        DatabaseHelper::createIndex('enrollment_sessions', 'enrollment_sessions_idx', ['code', 'is_active', 'course_id']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enrollment_sessions');
    }
};
