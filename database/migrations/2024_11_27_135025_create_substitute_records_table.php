<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('substitute_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('timeslot_id');
            $table->foreignId('substitute_teacher_id');
            $table->date('substitute_date');
            $table->string('day');
            $table->foreignId('requestor_id');
            $table->foreignId('period_id');
            $table->foreignId('class_subject_id');
            $table->decimal('allowance',6,2)->default(0);
            $table->text('remarks')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('substitute_records');
    }
};
