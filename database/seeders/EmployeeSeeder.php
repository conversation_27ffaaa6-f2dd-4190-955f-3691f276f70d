<?php

namespace Database\Seeders;

use App\Enums\EmployeeStatus;
use App\Enums\Gender;
use App\Enums\GuestType;
use App\Enums\JobType;
use App\Models\Employee;
use App\Models\EmployeeCategory;
use App\Models\Guest;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

/**
 * SAFE TO RUN ON PRD
 */
class EmployeeSeeder extends Seeder
{
    const TESTER_ACCOUNTS = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $employee_category = EmployeeCategory::firstOrCreate([
            'name->en' => 'System Admin (Do not use)'
        ]);

        $system_user = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'password' => Hash::make(md5(rand(10000, 50000)) . time() . rand(0, 1000000)),
        ]);

        // system user - employee number must be SYSTEM
        Employee::firstOrCreate(
            [
                'user_id' => $system_user->id,
            ],
            [
                'name' => 'SYSTEM',
                'email' => $system_user->email,
                'phone_number' => '**********',
                'is_hostel' => false,
                'date_of_birth' => '2024-09-01',
                'user_id' => $system_user->id,
                'employee_number' => 'SYSTEM',
                'badge_no' => 'SYSTEM',
                'gender' => Gender::MALE->value,
                'status' => EmployeeStatus::WORKING->value,
                'employment_start_date' => '2024-01-01',
                'employment_type' => JobType::FULL_TIME->value,
                'employee_category_id' => $employee_category->id
            ]
        );


        foreach (self::TESTER_ACCOUNTS as $index => $email) {
            $user = User::firstOrCreate([
                'email' => $email,
            ], [
                'password' => Hash::make(md5(rand(10000, 50000)) . time() . rand(0, 1000000)),
            ]);

            Guest::firstOrCreate(
                [
                    'user_id' => $user->id,
                ],
                [
                    'name->en' => "TESTER $index",
                    'name->zh' => "测试员 $index",
                    'email' => null,
                    'phone_number' => "+**********" . $index,
                    'type' => GuestType::ALUMNI,
                    'remarks' => 'Tester Account (Do not use)',
                ]
            );
        }
    }
}

