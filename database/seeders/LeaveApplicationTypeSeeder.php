<?php

namespace Database\Seeders;

use App\Models\GlAccount;
use App\Models\LeaveApplicationType;
use App\Models\Product;
use App\Models\Tax;
use App\Models\Uom;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeaveApplicationTypeSeeder extends Seeder
{

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        DB::table('leave_application_types')->truncate();

        $list = [
            [
                'name' => json_encode([
                    'en' => 'Planned Leave',
                    'zh' => '计划休假',
                ]),
                'is_present' => false,
            ],
            [
                'name' => json_encode([
                    'en' => 'Unplanned Leave',
                    'zh' => '计划外休假',
                ]),
                'is_present' => false,
            ],
        ];

        LeaveApplicationType::insert($list);
    }
}
