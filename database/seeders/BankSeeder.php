<?php

namespace Database\Seeders;

use App\Models\Bank;
use App\Models\State;
use App\Repositories\CountryRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BankSeeder extends Seeder
{
    const BANK_LIST = [
        [
            'code' => 'AFFIN',
            'name' => [
                'en' => 'Affin Bank Berhad',
                'zh' => '艾芬银行'
            ],
            'swift_code' => 'PHBMMYKLXXX',
        ],
        [
            'code' => 'MBB',
            'name' => [
                'en' => 'Malayan Banking Berhad',
                'zh' => '马来亚银行'
            ],
            'swift_code' => 'MBBEMYKLXXX',
        ],
        [
            'code' => 'PBB',
            'name' => [
                'en' => 'Public Bank Berhad',
                'zh' => '大众银行'
            ],
            'swift_code' => 'PBBEMYKLXXX',
        ],
        [
            'code' => 'RHB',
            'name' => [
                'en' => 'RHB Bank Berhad',
                'zh' => '兴业银行'
            ],
            'swift_code' => 'RHBBMYKLXXX',
        ],
        [
            'code' => 'CIMB',
            'name' => [
                'en' => 'CIMB Bank Berhad',
                'zh' => '联昌银行'
            ],
            'swift_code' => 'CIBBMYKLXXX',
        ],
        [
            'code' => 'ABMB',
            'name' => [
                'en' => 'Alliance Bank Malaysia Berhad',
                'zh' => '安联银行'
            ],
            'swift_code' => 'MFBBMYKLXXX',
        ],
        [
            'code' => 'HLBB',
            'name' => [
                'en' => 'Hong Leong Bank Berhad',
                'zh' => '丰隆银行'
            ],
            'swift_code' => 'HLBBMYKLXXX',
        ],
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        DB::table('master_banks')->truncate();

        $bank_list = collect(self::BANK_LIST)->map(function ($data)  {
            return [
                'code' => $data['code'],
                'name' => json_encode($data['name']),
                'swift_code' => $data['swift_code'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        })->toArray();

        Bank::insert($bank_list);
    }
}
