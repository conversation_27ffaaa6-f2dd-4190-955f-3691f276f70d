<?php

namespace Database\Seeders;

use App\Enums\ProductCategory;
use App\Models\GlAccount;
use App\Models\Product;
use App\Models\Uom;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductSeeder extends Seeder
{

    const PRODUCT_LIST = [
        [
            'code' => Product::CODE_EWALLET_TOPUP,
            'name' => [
                'en' => 'E-Wallet Topup',
                'zh' => '电子钱包充值',
            ],
            'category' => ProductCategory::OTHERS,
            'uom_code' => Uom::CODE_DEFAULT,
            'unit_price' => 0,
            'gl_account_code' => GlAccount::CODE_EWALLET,
            'is_active' => true,
        ],
        [
            'code' => Product::CODE_ENROLLMENT_EXAM,
            'name' => [
                'en' => 'Enrollment Exam',
                'zh' => '入学考试费用',
            ],
            'category' => ProductCategory::ENROLLMENT,
            'uom_code' => Uom::CODE_DEFAULT,
            'unit_price' => 0,
            'gl_account_code' => GlAccount::CODE_ENROLLMENT_ADMISSION,
            'is_active' => true,
        ],
        [
            'code' => Product::CODE_HOSTEL_SAVINGS,
            'name' => [
                'en' => 'Hostel Savings Account',
                'zh' => '宿舍储蓄账户',
            ],
            'category' => ProductCategory::OTHERS,
            'uom_code' => Uom::CODE_DEFAULT,
            'unit_price' => 0,
            'gl_account_code' => GlAccount::CODE_HOSTEL_STUDENTS_SAVINGS,
            'is_active' => true,
        ],
        [
            'code' => Product::CODE_NOT_APPLICABLE,
            'name' => [
                'en' => 'Not Applicable',
                'zh' => '不适用',
            ],
            'category' => ProductCategory::OTHERS,
            'uom_code' => Uom::CODE_DEFAULT,
            'unit_price' => 0,
            'gl_account_code' => GlAccount::CODE_OTHERS,
            'is_active' => true,
        ],
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        DB::table('master_products')->truncate();

        $list = collect(self::PRODUCT_LIST)->map(function ($data) {
            $data['name'] = json_encode($data['name']);
            return $data;
        })->toArray();

        Product::insert($list);
    }
}
