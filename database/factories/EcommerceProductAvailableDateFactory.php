<?php

namespace Database\Factories;

use App\Models\EcommerceProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

class EcommerceProductAvailableDateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => EcommerceProduct::factory(),
            'available_date' => fake()->date(),
            'product_group_id' => null,
        ];
    }
}
