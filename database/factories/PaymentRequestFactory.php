<?php

namespace Database\Factories;

use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'userable_type' => Student::class,
            'userable_id' => 1,
            'billing_document_id' => 1,
            'payment_method_id' => 1,
            'status' => 'PENDING',
            'payment_reference_no' => 'REF0001',
            'bank_id' => 1,
            'amount' => fake()->numberBetween(10, 100),
            'proof_of_payment_url' => fake()->imageUrl(),
            'processed_by_employee_id' => null,
            'approved_by_employee_id' => null,
            'posted_by_employee_id' => null,
            'processed_at' => null,
            'approved_at' => null,
            'posted_at' => null,
        ];
    }
}
