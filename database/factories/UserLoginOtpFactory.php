<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserLoginOtp>
 */
class UserLoginOtpFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'otp' => hash('sha256', $this->faker->numberBetween(100000, 999999)),
            'expired_at' => now()->addMinutes(5),
        ];
    }
}
