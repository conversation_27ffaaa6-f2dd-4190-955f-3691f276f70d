<?php

namespace Database\Factories;

use App\Enums\ConductRecordStatus;
use App\Models\ConductSetting;
use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ConductRecord>
 */
class ConductRecordFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $conduct_setting = ConductSetting::factory()->withTeachers()->create();
        $conduct_setting_teacher = $conduct_setting->conductSettingTeachers->first();

        return [
            'conduct_setting_id' => $conduct_setting->id,
            'conduct_setting_teacher_id' => $conduct_setting_teacher->id,
            'student_id' => Student::factory(),
            'marks' => 15.5,
            'status' => ConductRecordStatus::DRAFT->value,
        ];
    }
}
