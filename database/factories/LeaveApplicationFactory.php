<?php

namespace Database\Factories;

use App\Enums\LeaveApplicationStatus;
use App\Models\LeaveApplicationType;
use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

class LeaveApplicationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        return [
            'leave_applicable_type' => Student::class,
            'leave_applicable_id' => Student::factory(),
            'status' => LeaveApplicationStatus::PENDING->value,
            'leave_application_type_id' => LeaveApplicationType::factory(),
            'reason' => fake()->realText(),
            'remarks' => null,
            'is_present' => false,
            'is_full_day' => false,
            'average_point_deduction' => 0,
            'conduct_point_deduction' => 0,
        ];
    }
}
