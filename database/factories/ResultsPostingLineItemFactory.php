<?php

namespace Database\Factories;

use App\Models\ResultsPostingHeader;
use Illuminate\Database\Eloquent\Factories\Factory;

class ResultsPostingLineItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'header_id' => 1,
            'report_card_output_component_id' => 1,
            'report_card_output_component_code' => $this->faker->word(),
            'student_id' => 1,
            'subject_id' => 1,
            'grade_id' => 1,
            'semester_class_id' => 1,
            'grading_framework_id' => 1,
            'total' => 0,
            'total_grade' => null,
            'label' => null,
            'calculate_rank' => false,
        ];
    }
}
