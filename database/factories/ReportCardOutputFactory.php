<?php

namespace Database\Factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Exam>
 */
class ReportCardOutputFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'student_grading_framework_id' => 1,
            'service_class' => 'GeneralReportCardOutputService',
            'code' => fake()->unique()->postcode,
            'name->en' => 'Semester 1',
            'name->zh' => '第一学期',
        ];
    }
}
