<?php

namespace Database\Factories;

use App\Models\ResultsPostingHeader;
use Illuminate\Database\Eloquent\Factories\Factory;

class ResultsPostingHeaderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => fake()->unique()->postcode,
            'report_card_output_code' => 1,
            'report_card_template_service' => 'PinHwaDefaultReportCardTemplateService',
            'grade_id' => 1,
            'semester_setting_id' => 1,
            'status' => ResultsPostingHeader::STATUS_PENDING,
            'posted_by_employee_id' => 1,
            'publish_date' => now(),
            'student_ids' => [1],
            'metadata' => null,
            'errors' => null,
            'posted_at' => now(),
        ];
    }
}
