<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GradingFramework>
 */
class GradingFrameworkFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => fake()->unique()->postcode(),
            'name->en' => 'Junior High School Grading Framework',
            'name->zh' => '初中生成绩模板',
            'is_active' => true,
            'configuration' => null,
        ];
    }
}
