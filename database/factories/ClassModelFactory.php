<?php

namespace Database\Factories;

use App\Enums\ClassStream;
use App\Enums\ClassType;
use App\Enums\EnglishLevel;
use App\Models\Grade;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ClassModel>
 */
class ClassModelFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name->en' => fake()->name,
            'name->zh' => fake('zh_CN')->name,
            'code' => uniqid(),
            'stream' => ClassStream::NOT_APPLICABLE,
            'type' => ClassType::PRIMARY,
            'english_level' => null,
            'grade_id' => Grade::factory(),
            'is_active' => true
        ];
    }

    public function english()
    {
        return $this->state(fn(array $attributes) => [
            'type' => ClassType::ENGLISH,
            'english_level' => EnglishLevel::STARTER,
        ]);
    }
}
