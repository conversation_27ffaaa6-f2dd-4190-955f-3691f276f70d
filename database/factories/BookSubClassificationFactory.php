<?php

namespace Database\Factories;

use App\Models\BookClassification;
use Illuminate\Database\Eloquent\Factories\Factory;

class BookSubClassificationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'book_classification_id' => BookClassification::factory(),
            'name->en' => fake()->name,
            'name->zh' => fake()->name,
            'code' => uniqid()
        ];
    }
}
