<?php

namespace Database\Factories;

use App\Enums\LiveStatus;
use App\Enums\MarriedStatus;
use App\Models\Country;
use App\Models\Education;
use App\Models\Race;
use App\Models\Religion;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Guardian>
 */
class GuardianFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'name->en' => fake('ms_MY')->name(),
            'name->zh' => fake('zh_CN')->name,
            'email' => fake()->unique()->safeEmail(),
            'phone_number' => fake()->e164PhoneNumber(),
            'nric' => fake('ms_MY')->myKadNumber(),
            'passport_number' => fake()->uuid(),
            'nationality_id' => Country::factory(),
            'race_id' => Race::factory(),
            'religion_id' => Religion::factory(),
            'education_id' => Education::factory(),
            'married_status' => MarriedStatus::SINGLE,
            'occupation' => fake()->jobTitle(),
            'occupation_description' => fake()->text(100),
            'remarks' => fake()->sentence(),
            'live_status' => LiveStatus::NORMAL,
        ];
    }
}
