<?php

namespace Database\Factories;

use App\Models\PosTerminalKey;
use App\Models\Terminal;
use Illuminate\Database\Eloquent\Factories\Factory;

class PosTerminalKeyFactory extends Factory
{
    public function configure(): static
    {
        return $this->afterCreating(function (PosTerminalKey $pos_terminal_key) {
            if(!$pos_terminal_key->secret) {
                $secret = $pos_terminal_key->id . now()->format('Y-m-d H:i:s.u') . rand(0, 1000);
                $pos_terminal_key->secret = hash('sha256', $secret);

                $pos_terminal_key->save();
            }
        });
    }

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'terminal_id' => Terminal::factory(),
            'name' => fake()->uuid(),
            'secret' => null,
            'expires_at' => null,
        ];
    }
}
