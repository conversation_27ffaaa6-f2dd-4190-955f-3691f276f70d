<?php

namespace Database\Factories;

use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

class BankAccountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'bank_id' => 1,
            'bankable_type' => Student::class,
            'bankable_id' => 1,
            'currency_code' => 'MYR',
            'label' => 'My bank account',
            'account_name' => fake()->words(3, true),
            'account_number' => fake()->randomNumber(8, true),
            'is_active' => true,
        ];
    }
}
