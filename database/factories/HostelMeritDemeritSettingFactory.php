<?php

namespace Database\Factories;

use App\Enums\HostelMeritDemeritType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\HostelMeritDemeritSetting>
 */
class HostelMeritDemeritSettingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => HostelMeritDemeritType::DEMERIT->value,
            'name' => fake()->jobTitle(),
        ];
    }

    public function merit()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => HostelMeritDemeritType::MERIT->value
            ];
        });
    }
}
