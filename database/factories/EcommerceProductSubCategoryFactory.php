<?php

namespace Database\Factories;

use App\Models\EcommerceProductCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class EcommerceProductSubCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_category_id' => EcommerceProductCategory::factory(),
            'name' => fake()->name,
            'sequence' => fake()->numberBetween(0, 100),
        ];
    }
}
