<?php

namespace Database\Factories;

use App\Enums\HostelRoomBedStatus;
use App\Models\HostelRoom;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\HostelRoomBed>
 */
class HostelRoomBedFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'hostel_room_id' => HostelRoom::factory(),
            'name' => fake()->ean8(),
            'is_active' => true,
            'status' => HostelRoomBedStatus::AVAILABLE->value
        ];
    }

    public function occupied(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'status' => HostelRoomBedStatus::OCCUPIED->value,
        ]);
    }

    public function employeeBed(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'hostel_room_id' => HostelRoom::factory()->employeeRoom(),
        ]);
    }

    public function studentBed(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'hostel_room_id' => HostelRoom::factory()->studentRoom(),
        ]);
    }
}
