<?php

namespace Database\Factories;

use App\Enums\Gender;
use App\Enums\LibraryMemberType;
use App\Models\Country;
use App\Models\Race;
use App\Models\Religion;
use App\Models\State;
use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

class LibraryMemberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => LibraryMemberType::STUDENT->value,
            'userable_type' => Student::class,
            'userable_id' => Student::factory(),
            'card_number' => uniqid(),
            'member_number' => uniqid(),
            'borrow_limit' => rand(1, 10),
            'is_librarian' => false,
            'register_date' => now(),
            'valid_from' => now(),
            'valid_to' => now()->addMonths(rand(1, 12)),
            'is_active' => true,
            'name->en' => fake()->unique()->name,
            'name->zh' => fake()->unique()->name,
            'gender' => Gender::MALE->value,
            'nric' => rand(1000000000000, 9999999999999),
            'passport_number' => rand(1000000000000, 9999999999999),
            'date_of_birth' => '1999-01-01',
            'race_id' => Race::factory(),
            'religion_id' => Religion::factory(),
            'address' => fake()->address,
            'postcode' => fake()->postcode,
            'city' => fake()->city,
            'state_id' => State::factory(),
            'country_id' => Country::factory(),
            'phone_number' => fake()->unique()->phoneNumber,
            'email' => fake()->unique()->email,
        ];
    }
}
