<?php

namespace Database\Factories;

use App\Models\Grade;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterSetting;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\StudentClass>
 */
class ReportCardByMorphFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'results_posting_header_id' => ResultsPostingHeader::factory(),
            'report_card_morphable_type' => Grade::class,
            'report_card_morphable_id' => Grade::factory(),
            'semester_setting_id' => SemesterSetting::factory(),
            'is_active' => true,
            'file_url' => fake()->url(),
            'file_generated_at' => now(),
        ];
    }
}
