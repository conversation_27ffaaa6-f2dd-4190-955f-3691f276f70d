<?php

namespace Database\Factories;

use App\Models\EcommerceProduct;
use App\Models\EcommerceProductGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

class EcommerceProductGroupAssignmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => EcommerceProduct::factory(),
            'product_group_id' => EcommerceProductGroup::factory(),
        ];
    }
}
