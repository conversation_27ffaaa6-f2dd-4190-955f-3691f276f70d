81a5ad5487c6aeba5d41f5761c740daf87e4da44 b0d8c20f07b8791c775112647d3ebf4fb857275a Sim <PERSON>hen <PERSON>uan <<EMAIL>> 1738811977 +0800	checkout: moving from main to dev
b0d8c20f07b8791c775112647d3ebf4fb857275a ef0e2d0d99eca3a0828428aedf7dc15ef3658590 Sim Zhen <PERSON>uan <<EMAIL>> 1738811992 +0800	checkout: moving from dev to go-live-enhancements
ef0e2d0d99eca3a0828428aedf7dc15ef3658590 b3d9f7b28a50a3893b31ccf7871e6bbfb952c6a4 Sim Zhen Quan <<EMAIL>> 1738812004 +0800	commit: Add SimpleUserableViewResource and update WalletResource to use it
b3d9f7b28a50a3893b31ccf7871e6bbfb952c6a4 53d7b65f6f784dbc5086b301dc6771098fc60368 Sim Zhen Quan <<EMAIL>> 1738812279 +0800	merge origin/main: Merge made by the 'ort' strategy.
53d7b65f6f784dbc5086b301dc6771098fc60368 cae532389ddb6554f6a7f7ea35aa0af83abc4d7f Sim Zhen Quan <<EMAIL>> 1738812910 +0800	commit: Test case fixes
cae532389ddb6554f6a7f7ea35aa0af83abc4d7f f01efa361ac4a15a34ebe2e15037b5b3e6985cd5 Sim Zhen Quan <<EMAIL>> 1738813324 +0800	commit: Test case fixes
f01efa361ac4a15a34ebe2e15037b5b3e6985cd5 762787da69acf266150f8e52262f508148624e82 Sim Zhen Quan <<EMAIL>> 1738815751 +0800	merge origin/main: Merge made by the 'ort' strategy.
762787da69acf266150f8e52262f508148624e82 b0d8c20f07b8791c775112647d3ebf4fb857275a Sim Zhen Quan <<EMAIL>> 1738815764 +0800	checkout: moving from go-live-enhancements to dev
b0d8c20f07b8791c775112647d3ebf4fb857275a 8c56548ccce5344912786043adb384fb4fb1b62b Sim Zhen Quan <<EMAIL>> 1738815772 +0800	merge go-live-enhancements: Merge made by the 'ort' strategy.
8c56548ccce5344912786043adb384fb4fb1b62b fb66e517937161934b41a1786c8f1b4547da0feb Sim Zhen Quan <<EMAIL>> 1738816323 +0800	commit: Deployed to DEV
fb66e517937161934b41a1786c8f1b4547da0feb b9882f4ede34a0f899c0ff3ac4908295975a50b6 Sim Zhen Quan <<EMAIL>> 1738816543 +0800	checkout: moving from dev to migration-hostel-merit-demerit
b9882f4ede34a0f899c0ff3ac4908295975a50b6 b67f0753a7ce205243bcd370e1ff95cccf03ae8b Sim Zhen Quan <<EMAIL>> 1738828706 +0800	commit (merge): Merged origin/main
b67f0753a7ce205243bcd370e1ff95cccf03ae8b 825138bbcc719f8c974d93718931280b9d70708c Sim Zhen Quan <<EMAIL>> 1738828795 +0800	checkout: moving from migration-hostel-merit-demerit to assign-product-index-api
825138bbcc719f8c974d93718931280b9d70708c 825138bbcc719f8c974d93718931280b9d70708c Sim Zhen Quan <<EMAIL>> 1738851889 +0800	reset: moving to HEAD
825138bbcc719f8c974d93718931280b9d70708c 81a5ad5487c6aeba5d41f5761c740daf87e4da44 Sim Zhen Quan <<EMAIL>> 1738851912 +0800	checkout: moving from assign-product-index-api to main
81a5ad5487c6aeba5d41f5761c740daf87e4da44 59f94863608ae8c56fb4ea34ff58fcc14870d76e Sim Zhen Quan <<EMAIL>> 1738851920 +0800	pull: Fast-forward
59f94863608ae8c56fb4ea34ff58fcc14870d76e 6de66fbb0fb22e7cf136e960dacfa15d83b95e36 Sim Zhen Quan <<EMAIL>> 1738854506 +0800	commit: Deployed to PRD
6de66fbb0fb22e7cf136e960dacfa15d83b95e36 6de66fbb0fb22e7cf136e960dacfa15d83b95e36 Sim Zhen Quan <<EMAIL>> 1738893858 +0800	checkout: moving from main to dev-1.1.30-changes
6de66fbb0fb22e7cf136e960dacfa15d83b95e36 0f59de0ee333a5f2000d9265f98f54e1dbb32706 Sim Zhen Quan <<EMAIL>> 1738897764 +0800	commit: Fixed StudentResource
0f59de0ee333a5f2000d9265f98f54e1dbb32706 6add702f5e063e429a51589bdb929d0d18d3b119 Sim Zhen Quan <<EMAIL>> 1738900219 +0800	commit: Set default employee status to WORKING and remove status validation from requests
6add702f5e063e429a51589bdb929d0d18d3b119 825138bbcc719f8c974d93718931280b9d70708c Sim Zhen Quan <<EMAIL>> 1738900861 +0800	checkout: moving from dev-1.1.30-changes to assign-product-index-api
825138bbcc719f8c974d93718931280b9d70708c 90ac8472199bfb1a6a76dea54e30211ffc2ce055 Sim Zhen Quan <<EMAIL>> 1738900865 +0800	merge origin/main: Merge made by the 'ort' strategy.
90ac8472199bfb1a6a76dea54e30211ffc2ce055 0bbf87a1f4c8d755945adcbb8b5c66baceaed1ec Sim Zhen Quan <<EMAIL>> 1738909610 +0800	commit: Review WIP
0bbf87a1f4c8d755945adcbb8b5c66baceaed1ec 0b804cb99182a597385b899fe102c95e7951500a Sim Zhen Quan <<EMAIL>> 1738910269 +0800	checkout: moving from assign-product-index-api to add-support-for-multiple-payment-per-invoice
0b804cb99182a597385b899fe102c95e7951500a ea03ed1b154a9fcdf88f96c76752583e83866d8c Sim Zhen Quan <<EMAIL>> 1738910344 +0800	commit (merge): Merge origin/main
ea03ed1b154a9fcdf88f96c76752583e83866d8c b67f0753a7ce205243bcd370e1ff95cccf03ae8b Sim Zhen Quan <<EMAIL>> 1738914115 +0800	checkout: moving from add-support-for-multiple-payment-per-invoice to migration-hostel-merit-demerit
b67f0753a7ce205243bcd370e1ff95cccf03ae8b 673de14cffe81a135ee8419f337f8f0e07de055e Sim Zhen Quan <<EMAIL>> 1738914142 +0800	merge origin/main: Merge made by the 'ort' strategy.
673de14cffe81a135ee8419f337f8f0e07de055e 0bbf87a1f4c8d755945adcbb8b5c66baceaed1ec Sim Zhen Quan <<EMAIL>> 1738916807 +0800	checkout: moving from migration-hostel-merit-demerit to assign-product-index-api
0bbf87a1f4c8d755945adcbb8b5c66baceaed1ec 650273a355b25d19c1bc700f9a530b43df8799ad Sim Zhen Quan <<EMAIL>> 1738916812 +0800	pull: Fast-forward
650273a355b25d19c1bc700f9a530b43df8799ad 6de66fbb0fb22e7cf136e960dacfa15d83b95e36 Sim Zhen Quan <<EMAIL>> 1738923380 +0800	checkout: moving from assign-product-index-api to main
6de66fbb0fb22e7cf136e960dacfa15d83b95e36 668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b Sim Zhen Quan <<EMAIL>> 1738923387 +0800	pull: Fast-forward
668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b 668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b Sim Zhen Quan <<EMAIL>> 1738923424 +0800	checkout: moving from main to add-is-direct-dependant
668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b 3b7e161b2f9e8d2ed71082ce9242b0311919b0a0 Sim Zhen Quan <<EMAIL>> 1738923499 +0800	commit: Add is_direct_dependant column to guardian_student table
3b7e161b2f9e8d2ed71082ce9242b0311919b0a0 d41774dfacfa278436ec5fce01e1b585637e8323 Sim Zhen Quan <<EMAIL>> 1738925419 +0800	commit: Add directDependants relationship to Guardian model and update getStudentUserables method
d41774dfacfa278436ec5fce01e1b585637e8323 378adeb81a656e0a1e1ce3cac532d46aa47ce135 Sim Zhen Quan <<EMAIL>> 1739110252 +0800	pull: Fast-forward
378adeb81a656e0a1e1ce3cac532d46aa47ce135 7ed2fef13cc28eefd0a560d8cb305b4992f89b40 Sim Zhen Quan <<EMAIL>> 1739114681 +0800	commit: Unit test fixes
7ed2fef13cc28eefd0a560d8cb305b4992f89b40 fb66e517937161934b41a1786c8f1b4547da0feb Sim Zhen Quan <<EMAIL>> 1739114696 +0800	checkout: moving from add-is-direct-dependant to dev
fb66e517937161934b41a1786c8f1b4547da0feb **************************************** Sim Zhen Quan <<EMAIL>> 1739114702 +0800	merge add-is-direct-dependant: Merge made by the 'ort' strategy.
**************************************** ec653bf24198ab17a7965e8ff026c7ddc94b3147 Sim Zhen Quan <<EMAIL>> 1739115118 +0800	commit: Deployed to dev
ec653bf24198ab17a7965e8ff026c7ddc94b3147 668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b Sim Zhen Quan <<EMAIL>> 1739115219 +0800	checkout: moving from dev to main
668999e3cea55ea9ed6dbc0e9e1b29261bd4a11b 42beb70dcab1c3f81e7d190a3fc9818f31e752b7 Sim Zhen Quan <<EMAIL>> 1739115245 +0800	pull: Fast-forward
42beb70dcab1c3f81e7d190a3fc9818f31e752b7 08147cdf3bd0896b9cc2135699f8b143ce44a892 Sim Zhen Quan <<EMAIL>> 1739119549 +0800	commit: Deployed to PRD
08147cdf3bd0896b9cc2135699f8b143ce44a892 08147cdf3bd0896b9cc2135699f8b143ce44a892 Sim Zhen Quan <<EMAIL>> 1739150683 +0800	checkout: moving from main to hotfix/2025-02-10
08147cdf3bd0896b9cc2135699f8b143ce44a892 dfbc00e0192304792b59e328ed89993f43be3911 Sim Zhen Quan <<EMAIL>> 1739150801 +0800	commit: load additional relationships for user in UserController
dfbc00e0192304792b59e328ed89993f43be3911 117412964aa8fa7dfd73cf776b40cb110b851a0a Sim Zhen Quan <<EMAIL>> 1739152127 +0800	commit: array_unique() array before loading relationship
117412964aa8fa7dfd73cf776b40cb110b851a0a f67957f76292bb450e80c9fe49678c0b7b23e176 Sim Zhen Quan <<EMAIL>> 1739152280 +0800	checkout: moving from hotfix/2025-02-10 to remove-get-transaction-payment-gateway
f67957f76292bb450e80c9fe49678c0b7b23e176 171f0d17c20c7fcfb8c8b9c52e56b8ad669ea49e Sim Zhen Quan <<EMAIL>> 1739152286 +0800	merge origin/main: Merge made by the 'ort' strategy.
171f0d17c20c7fcfb8c8b9c52e56b8ad669ea49e e7a73916355ac43a1bba4f62925426516481be7f Sim Zhen Quan <<EMAIL>> 1739152918 +0800	commit: Test case fix
e7a73916355ac43a1bba4f62925426516481be7f b629b643f2eb3f56534b3c282332f0099873e408 Sim Zhen Quan <<EMAIL>> 1739153268 +0800	checkout: moving from remove-get-transaction-payment-gateway to optimize-get-users
b629b643f2eb3f56534b3c282332f0099873e408 d7b009f62d2e980c9ea2cf63ddb5e3bf3c8c1602 Sim Zhen Quan <<EMAIL>> 1739153271 +0800	merge origin/main: Merge made by the 'ort' strategy.
d7b009f62d2e980c9ea2cf63ddb5e3bf3c8c1602 5b5187d918dd02abf169d233e5307fc68e60fe11 Sim Zhen Quan <<EMAIL>> 1739154312 +0800	commit: Change relation to use directDependant instead of student
5b5187d918dd02abf169d233e5307fc68e60fe11 117412964aa8fa7dfd73cf776b40cb110b851a0a Sim Zhen Quan <<EMAIL>> 1739156057 +0800	checkout: moving from optimize-get-users to hotfix/2025-02-10
117412964aa8fa7dfd73cf776b40cb110b851a0a 18bd3f99a2d55ebef39c411d461a4ce54f840a45 Sim Zhen Quan <<EMAIL>> 1739156063 +0800	pull: Fast-forward
18bd3f99a2d55ebef39c411d461a4ce54f840a45 d248f1209247562f9d92e94013817463f67d82c9 Sim Zhen Quan <<EMAIL>> 1739156241 +0800	checkout: moving from hotfix/2025-02-10 to migration-hostel-reward-punishment
d248f1209247562f9d92e94013817463f67d82c9 4a76e4ef25c13773f9204ebd53210f086d1e3e52 Sim Zhen Quan <<EMAIL>> 1739156517 +0800	commit (merge): Merge origin/main
4a76e4ef25c13773f9204ebd53210f086d1e3e52 eede4375eee19abafdbcb09283f48bada7098924 Sim Zhen Quan <<EMAIL>> 1739157918 +0800	commit: Fix imports
eede4375eee19abafdbcb09283f48bada7098924 08147cdf3bd0896b9cc2135699f8b143ce44a892 Sim Zhen Quan <<EMAIL>> 1739157939 +0800	checkout: moving from migration-hostel-reward-punishment to main
08147cdf3bd0896b9cc2135699f8b143ce44a892 ce82e2f2c55583f5b97ce3472ea20e61b2af5be2 Sim Zhen Quan <<EMAIL>> 1739157943 +0800	pull: Fast-forward
ce82e2f2c55583f5b97ce3472ea20e61b2af5be2 537e0ee93b0f8cedbc07b84cbb286b4b0d689824 Sim Zhen Quan <<EMAIL>> 1739157943 +0800	merge migration-hostel-reward-punishment: Merge made by the 'ort' strategy.
537e0ee93b0f8cedbc07b84cbb286b4b0d689824 dec833e6cb08d0733d2306e8a8510d369dae2af0 Sim Zhen Quan <<EMAIL>> 1739157953 +0800	checkout: moving from main to migration-hostel-reward-punishment-record-1
dec833e6cb08d0733d2306e8a8510d369dae2af0 13c1a62df241c6f6651243ce82710d09e0873123 Sim Zhen Quan <<EMAIL>> 1739158082 +0800	commit (merge): Merge origin/main
13c1a62df241c6f6651243ce82710d09e0873123 2ecf659006caad3e8ee38e690ceadd1daa70b59a Sim Zhen Quan <<EMAIL>> 1739164105 +0800	commit: Reviewed
2ecf659006caad3e8ee38e690ceadd1daa70b59a 054b3dfb1ef8fde0d6f76d72533e6fd3a462826c Sim Zhen Quan <<EMAIL>> 1739164184 +0800	checkout: moving from migration-hostel-reward-punishment-record-1 to migration-hostel-room
054b3dfb1ef8fde0d6f76d72533e6fd3a462826c af4d648241b6ca1ca1516b33788179bde6d6ef38 Sim Zhen Quan <<EMAIL>> 1739164189 +0800	pull: Fast-forward
af4d648241b6ca1ca1516b33788179bde6d6ef38 c73a237816d62da245d0ad5027515e8c39cf3a99 Sim Zhen Quan <<EMAIL>> 1739164408 +0800	commit (merge): Merge origin/main
c73a237816d62da245d0ad5027515e8c39cf3a99 14c7e39e43fd6ff4eee4944f068c5b859fb69581 Sim Zhen Quan <<EMAIL>> 1739165122 +0800	commit: Reviewed
14c7e39e43fd6ff4eee4944f068c5b859fb69581 a223897929954b4d993edba1d65460504a57cada Sim Zhen Quan <<EMAIL>> 1739165220 +0800	checkout: moving from migration-hostel-room to migration-hostel-bed
a223897929954b4d993edba1d65460504a57cada d3f42557274108b3a5b329432b0877feea61c6b8 Sim Zhen Quan <<EMAIL>> 1739165226 +0800	pull: Fast-forward
d3f42557274108b3a5b329432b0877feea61c6b8 a2cc8ac610ef07f1f01fccf4685d00288a73b32e Sim Zhen Quan <<EMAIL>> 1739165302 +0800	commit (merge): Merge origin/main
a2cc8ac610ef07f1f01fccf4685d00288a73b32e 4dd8deae0b432ec04fe343c0d2bfca55d7ca33c9 Sim Zhen Quan <<EMAIL>> 1739167075 +0800	commit: Reviewed
4dd8deae0b432ec04fe343c0d2bfca55d7ca33c9 88ba40233ea6099ab06a3d0f1dce402668e50d77 Sim Zhen Quan <<EMAIL>> 1739169046 +0800	checkout: moving from migration-hostel-bed to migration-hostel-bed-assignment
88ba40233ea6099ab06a3d0f1dce402668e50d77 a2ac21df7d39f90f38767c8adb067152f109f244 Sim Zhen Quan <<EMAIL>> 1739169240 +0800	commit (merge): Merge origin/main
a2ac21df7d39f90f38767c8adb067152f109f244 18bd3f99a2d55ebef39c411d461a4ce54f840a45 Sim Zhen Quan <<EMAIL>> 1739169292 +0800	checkout: moving from migration-hostel-bed-assignment to hotfix/2025-02-10
18bd3f99a2d55ebef39c411d461a4ce54f840a45 d3dc08a7971e0d7b3f86503f05bdff3ceb7bce0c Sim Zhen Quan <<EMAIL>> 1739169298 +0800	merge origin/main: Merge made by the 'ort' strategy.
d3dc08a7971e0d7b3f86503f05bdff3ceb7bce0c ec653bf24198ab17a7965e8ff026c7ddc94b3147 Sim Zhen Quan <<EMAIL>> 1739169348 +0800	checkout: moving from hotfix/2025-02-10 to dev
ec653bf24198ab17a7965e8ff026c7ddc94b3147 17048c7c13045c1e4991213597dca7d459e35481 Sim Zhen Quan <<EMAIL>> 1739169348 +0800	merge hotfix/2025-02-10: Merge made by the 'ort' strategy.
17048c7c13045c1e4991213597dca7d459e35481 cbd12bc8c288648e4ec3b694555b44119d4ceab8 Sim Zhen Quan <<EMAIL>> 1739169676 +0800	commit: Deployed to DEV
cbd12bc8c288648e4ec3b694555b44119d4ceab8 b9169b997d0a2bfca6598464d58e9d5d61bd25d0 Sim Zhen Quan <<EMAIL>> 1739169804 +0800	checkout: moving from dev to migrate-script-education
b9169b997d0a2bfca6598464d58e9d5d61bd25d0 f2c99a3ac0bb7e013ef34112227b32a9e410fce6 Sim Zhen Quan <<EMAIL>> 1739169817 +0800	merge origin/main: Merge made by the 'ort' strategy.
f2c99a3ac0bb7e013ef34112227b32a9e410fce6 fb892083dc0ed1a2afd8b18f3fc510cf262417d1 Sim Zhen Quan <<EMAIL>> 1739172497 +0800	commit: Reviewed
fb892083dc0ed1a2afd8b18f3fc510cf262417d1 650273a355b25d19c1bc700f9a530b43df8799ad Sim Zhen Quan <<EMAIL>> 1739172580 +0800	checkout: moving from migrate-script-education to assign-product-index-api
650273a355b25d19c1bc700f9a530b43df8799ad 6c02f676ada5b6017976d42bce8cabad38ffcca0 Sim Zhen Quan <<EMAIL>> 1739172587 +0800	merge origin/main: Merge made by the 'ort' strategy.
6c02f676ada5b6017976d42bce8cabad38ffcca0 cbd12bc8c288648e4ec3b694555b44119d4ceab8 Sim Zhen Quan <<EMAIL>> 1739176532 +0800	checkout: moving from assign-product-index-api to dev
cbd12bc8c288648e4ec3b694555b44119d4ceab8 79b34776ebb735d6da2cef5a79ba067b32748367 Sim Zhen Quan <<EMAIL>> 1739176532 +0800	merge assign-product-index-api: Merge made by the 'ort' strategy.
79b34776ebb735d6da2cef5a79ba067b32748367 79b34776ebb735d6da2cef5a79ba067b32748367 Sim Zhen Quan <<EMAIL>> 1739176559 +0800	checkout: moving from dev to dev
79b34776ebb735d6da2cef5a79ba067b32748367 a1a84f7527c0c01c21a978c92d79b2139a5b7e9b Sim Zhen Quan <<EMAIL>> 1739177682 +0800	commit: Deployed to DEV
a1a84f7527c0c01c21a978c92d79b2139a5b7e9b a2ac21df7d39f90f38767c8adb067152f109f244 Sim Zhen Quan <<EMAIL>> 1739177692 +0800	checkout: moving from dev to migration-hostel-bed-assignment
a2ac21df7d39f90f38767c8adb067152f109f244 ffcac27fa68a31f2f87c43fca60343fea5523f1c Sim Zhen Quan <<EMAIL>> 1739177698 +0800	merge origin/main: Merge made by the 'ort' strategy.
ffcac27fa68a31f2f87c43fca60343fea5523f1c a1a84f7527c0c01c21a978c92d79b2139a5b7e9b Sim Zhen Quan <<EMAIL>> 1739179643 +0800	checkout: moving from migration-hostel-bed-assignment to dev
a1a84f7527c0c01c21a978c92d79b2139a5b7e9b a1a84f7527c0c01c21a978c92d79b2139a5b7e9b Sim Zhen Quan <<EMAIL>> 1739180467 +0800	reset: moving to HEAD
a1a84f7527c0c01c21a978c92d79b2139a5b7e9b ffcac27fa68a31f2f87c43fca60343fea5523f1c Sim Zhen Quan <<EMAIL>> 1739180469 +0800	checkout: moving from dev to migration-hostel-bed-assignment
ffcac27fa68a31f2f87c43fca60343fea5523f1c d3dc08a7971e0d7b3f86503f05bdff3ceb7bce0c Sim Zhen Quan <<EMAIL>> 1739180516 +0800	checkout: moving from migration-hostel-bed-assignment to hotfix/2025-02-10
d3dc08a7971e0d7b3f86503f05bdff3ceb7bce0c d85d6730f831d8ff51cc3c45b4b8ecbd05f19e66 Sim Zhen Quan <<EMAIL>> 1739182975 +0800	commit: Revert to use UserableViewResource
d85d6730f831d8ff51cc3c45b4b8ecbd05f19e66 95454ff9ff9e1faf044a0531acd8064a3ca69457 Sim Zhen Quan <<EMAIL>> 1739183200 +0800	commit: Fix test case
95454ff9ff9e1faf044a0531acd8064a3ca69457 a1a84f7527c0c01c21a978c92d79b2139a5b7e9b Sim Zhen Quan <<EMAIL>> 1739183271 +0800	checkout: moving from hotfix/2025-02-10 to dev
a1a84f7527c0c01c21a978c92d79b2139a5b7e9b ffcac27fa68a31f2f87c43fca60343fea5523f1c Sim Zhen Quan <<EMAIL>> 1739183355 +0800	checkout: moving from dev to migration-hostel-bed-assignment
ffcac27fa68a31f2f87c43fca60343fea5523f1c 537e0ee93b0f8cedbc07b84cbb286b4b0d689824 Sim Zhen Quan <<EMAIL>> 1739183711 +0800	checkout: moving from migration-hostel-bed-assignment to main
537e0ee93b0f8cedbc07b84cbb286b4b0d689824 4911b2f54d55a62c5043b5e09a65075ccb0f6598 Sim Zhen Quan <<EMAIL>> 1739183718 +0800	pull: Fast-forward
4911b2f54d55a62c5043b5e09a65075ccb0f6598 95454ff9ff9e1faf044a0531acd8064a3ca69457 Sim Zhen Quan <<EMAIL>> 1739183720 +0800	checkout: moving from main to hotfix/2025-02-10
95454ff9ff9e1faf044a0531acd8064a3ca69457 74bc7d5a4cd82756720ccbec89da3b73ae9369fb Sim Zhen Quan <<EMAIL>> 1739184068 +0800	commit: Added auditable to used models
74bc7d5a4cd82756720ccbec89da3b73ae9369fb 7e8f5a769c4f0be4d36039a7fd25d25110d13403 Sim Zhen Quan <<EMAIL>> 1739184259 +0800	commit: Added more models to Auditable
7e8f5a769c4f0be4d36039a7fd25d25110d13403 23d3b8491be08cdda85c27cd0fc18218d618875f Sim Zhen Quan <<EMAIL>> 1739197677 +0800	checkout: moving from hotfix/2025-02-10 to add-outbound-api-logging-for-payex
23d3b8491be08cdda85c27cd0fc18218d618875f a7308b77c19f33a42f815fc37ff2e05e846a4016 Sim Zhen Quan <<EMAIL>> 1739197774 +0800	commit (merge): Merge origin/main
a7308b77c19f33a42f815fc37ff2e05e846a4016 4911b2f54d55a62c5043b5e09a65075ccb0f6598 Sim Zhen Quan <<EMAIL>> 1739198105 +0800	checkout: moving from add-outbound-api-logging-for-payex to main
4911b2f54d55a62c5043b5e09a65075ccb0f6598 9d6948759a235b944facf7316f6b3fcd285ecb07 Sim Zhen Quan <<EMAIL>> 1739198111 +0800	pull: Fast-forward
9d6948759a235b944facf7316f6b3fcd285ecb07 cbe645208a66ec0211ba5c3651ee6b29cfe2de5d Sim Zhen Quan <<EMAIL>> 1739326032 +0800	pull: Fast-forward
cbe645208a66ec0211ba5c3651ee6b29cfe2de5d 80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 Sim Zhen Quan <<EMAIL>> 1739326037 +0800	commit: Deployed to PRD
80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 Sim Zhen Quan <<EMAIL>> 1739326188 +0800	checkout: moving from main to optimize-user-resource
80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 1807e70e1c9512046b4ae6cc6ba72a25de839604 Sim Zhen Quan <<EMAIL>> 1739328836 +0800	commit: Refactor user-related resources and controllers for improved data loading and structure
1807e70e1c9512046b4ae6cc6ba72a25de839604 43ae8b46c470b955b0b337b659835b02e658a517 Sim Zhen Quan <<EMAIL>> 1739336041 +0800	commit: Updated test case for optimized queries
43ae8b46c470b955b0b337b659835b02e658a517 e866cc82cc7bfa9dadb640ba357975a36d90e559 Sim Zhen Quan <<EMAIL>> 1739336784 +0800	commit: Include media on student API temporarily
e866cc82cc7bfa9dadb640ba357975a36d90e559 a1a84f7527c0c01c21a978c92d79b2139a5b7e9b Sim Zhen Quan <<EMAIL>> 1739336807 +0800	checkout: moving from optimize-user-resource to dev
a1a84f7527c0c01c21a978c92d79b2139a5b7e9b 320c21849e79417697895c8d55e5af167c2cef5f Sim Zhen Quan <<EMAIL>> 1739336813 +0800	merge optimize-user-resource: Merge made by the 'ort' strategy.
320c21849e79417697895c8d55e5af167c2cef5f 1eb43272ba0c2f98fd8eb5476e294d26920013fc Sim Zhen Quan <<EMAIL>> 1739337531 +0800	commit: Deployed to DEV
1eb43272ba0c2f98fd8eb5476e294d26920013fc 5495b476352f1d78db635b486042715b37efd39f Sim Zhen Quan <<EMAIL>> 1739337779 +0800	checkout: moving from dev to fix/card-import
5495b476352f1d78db635b486042715b37efd39f 3fc7c4098b2e1a5ee36defec0c014e03d6e6c90d Sim Zhen Quan <<EMAIL>> 1739337785 +0800	merge origin/main: Merge made by the 'ort' strategy.
3fc7c4098b2e1a5ee36defec0c014e03d6e6c90d 3d2d74445e78b1786a679bb30c3dde9514ad5f95 Sim Zhen Quan <<EMAIL>> 1739346327 +0800	commit: Reviewed
3d2d74445e78b1786a679bb30c3dde9514ad5f95 eba7c5259b2b11747118ad39084cabbc1817ad42 Sim Zhen Quan <<EMAIL>> 1739346446 +0800	merge origin/main: Merge made by the 'ort' strategy.
eba7c5259b2b11747118ad39084cabbc1817ad42 1eb43272ba0c2f98fd8eb5476e294d26920013fc Sim Zhen Quan <<EMAIL>> 1739346477 +0800	checkout: moving from fix/card-import to dev
1eb43272ba0c2f98fd8eb5476e294d26920013fc f437a06b0dd3df611b2d429e967cbcbf5b6dc860 Sim Zhen Quan <<EMAIL>> 1739346477 +0800	merge fix/card-import: Merge made by the 'ort' strategy.
f437a06b0dd3df611b2d429e967cbcbf5b6dc860 cfd8e67e0a284dee4f9430dc01e503e473517496 Sim Zhen Quan <<EMAIL>> 1739347924 +0800	commit: Deployed to DEV
cfd8e67e0a284dee4f9430dc01e503e473517496 0dfa6999e9cbe1c50936646802228af53d41fa86 Sim Zhen Quan <<EMAIL>> 1739352323 +0800	checkout: moving from dev to regenerate-employee-number
0dfa6999e9cbe1c50936646802228af53d41fa86 80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 Sim Zhen Quan <<EMAIL>> 1739376283 +0800	checkout: moving from regenerate-employee-number to main
80966e4c0f68d16ac8c9672ba0ae78dfa5e19815 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739376290 +0800	pull: Fast-forward
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739376312 +0800	checkout: moving from main to hotfix/canteen-billing-document-add-cn
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 a8c60cc2d95edc11525b99649390b08ff9743221 Sim Zhen Quan <<EMAIL>> 1739376370 +0800	commit: Enhance invoice line item description formatting and adjust layout for better readability
a8c60cc2d95edc11525b99649390b08ff9743221 3442477d5968ba71ee95915d263ebce56b5a121d Sim Zhen Quan <<EMAIL>> 1739381978 +0800	commit: Implement credit note generation and improve line item description formatting in invoices
3442477d5968ba71ee95915d263ebce56b5a121d 02e437cd9d9dbf1c281db8ee7602ed9eac106d95 Sim Zhen Quan <<EMAIL>> 1739428645 +0800	commit: Updated test case
02e437cd9d9dbf1c281db8ee7602ed9eac106d95 ab5d8b3f872462a1bb4a9f6f868635af497cb8bc Sim Zhen Quan <<EMAIL>> 1739428880 +0800	commit: Updated test case
ab5d8b3f872462a1bb4a9f6f868635af497cb8bc 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739428888 +0800	checkout: moving from hotfix/canteen-billing-document-add-cn to main
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739428898 +0800	checkout: moving from main to staging/2025-02-13
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 e866cc82cc7bfa9dadb640ba357975a36d90e559 Sim Zhen Quan <<EMAIL>> 1739428953 +0800	checkout: moving from staging/2025-02-13 to optimize-user-resource
e866cc82cc7bfa9dadb640ba357975a36d90e559 eeb64eb05eaa8021cf2333428cd70987a3d339ee Sim Zhen Quan <<EMAIL>> 1739429064 +0800	commit (merge): Merge origin/main
eeb64eb05eaa8021cf2333428cd70987a3d339ee 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739429389 +0800	checkout: moving from optimize-user-resource to main
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 956335824e93d738493d6186d93531f2be5d03b8 Sim Zhen Quan <<EMAIL>> 1739430047 +0800	checkout: moving from main to feature/billing-documents-API
956335824e93d738493d6186d93531f2be5d03b8 f596ad32be99b054c0b66e5f1a5f082f02d11418 Sim Zhen Quan <<EMAIL>> 1739430447 +0800	commit (merge): Merge origin/main
f596ad32be99b054c0b66e5f1a5f082f02d11418 f596ad32be99b054c0b66e5f1a5f082f02d11418 Sim Zhen Quan <<EMAIL>> 1739435575 +0800	checkout: moving from feature/billing-documents-API to feature/billing-documents-API
f596ad32be99b054c0b66e5f1a5f082f02d11418 93bd3bdf18cd53ecf6a46a3fc402fb46d6c2ff23 Sim Zhen Quan <<EMAIL>> 1739441468 +0800	commit: WIP
93bd3bdf18cd53ecf6a46a3fc402fb46d6c2ff23 dad71247ed557d60464c6692e47e6c6ca9c4a1d5 Sim Zhen Quan <<EMAIL>> 1739441478 +0800	checkout: moving from feature/billing-documents-API to ecommerce-report-merchant-filter
dad71247ed557d60464c6692e47e6c6ca9c4a1d5 b7b517971dd7f4752ae97e3309c8a794b27019a9 Sim Zhen Quan <<EMAIL>> 1739441545 +0800	commit (merge): Merge origin/main
b7b517971dd7f4752ae97e3309c8a794b27019a9 b7b517971dd7f4752ae97e3309c8a794b27019a9 Sim Zhen Quan <<EMAIL>> 1739445035 +0800	reset: moving to HEAD
b7b517971dd7f4752ae97e3309c8a794b27019a9 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739445038 +0800	checkout: moving from ecommerce-report-merchant-filter to main
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 Sim Zhen Quan <<EMAIL>> 1739445098 +0800	commit: Set Payex to single attempt only (No retries)
3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 b7b517971dd7f4752ae97e3309c8a794b27019a9 Sim Zhen Quan <<EMAIL>> 1739445122 +0800	checkout: moving from main to ecommerce-report-merchant-filter
b7b517971dd7f4752ae97e3309c8a794b27019a9 c3fdf57130b95b5075702d20fdbec083e2698fb7 Sim Zhen Quan <<EMAIL>> 1739459669 +0800	merge origin/main: Merge made by the 'ort' strategy.
c3fdf57130b95b5075702d20fdbec083e2698fb7 9c4d1eee8a975f1951af84d8edede1bb00942cd0 Sim Zhen Quan <<EMAIL>> 1739459715 +0800	commit: Reviewed
9c4d1eee8a975f1951af84d8edede1bb00942cd0 3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 Sim Zhen Quan <<EMAIL>> 1739459727 +0800	checkout: moving from ecommerce-report-merchant-filter to main
3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 Sim Zhen Quan <<EMAIL>> 1739459742 +0800	checkout: moving from main to staging/2025-02-13
6c99e811a922c8c9b6cf91dc6bd534e42a7d9a00 45243c851ad01d4e9260c3d3c2b61d6b3774e67f Sim Zhen Quan <<EMAIL>> 1739459746 +0800	pull: Fast-forward
45243c851ad01d4e9260c3d3c2b61d6b3774e67f 982f16a1c9f8a182ea37267c97fcb5c376c5ed04 Sim Zhen Quan <<EMAIL>> 1739459746 +0800	merge ecommerce-report-merchant-filter: Merge made by the 'ort' strategy.
982f16a1c9f8a182ea37267c97fcb5c376c5ed04 2a0f44e4869dc12422709515d943299ca2985f90 Sim Zhen Quan <<EMAIL>> 1739461838 +0800	commit: Fix test
2a0f44e4869dc12422709515d943299ca2985f90 3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 Sim Zhen Quan <<EMAIL>> 1739461867 +0800	checkout: moving from staging/2025-02-13 to main
3ea34b4d01730f8d32de8c8c13f4e3a1f4607745 2a0f44e4869dc12422709515d943299ca2985f90 Sim Zhen Quan <<EMAIL>> 1739461875 +0800	merge staging/2025-02-13: Fast-forward
2a0f44e4869dc12422709515d943299ca2985f90 2a0f44e4869dc12422709515d943299ca2985f90 Sim Zhen Quan <<EMAIL>> 1739461989 +0800	reset: moving to HEAD
2a0f44e4869dc12422709515d943299ca2985f90 ab349ed05aa33a248bbfa1d3e3a37459ed7b7990 Sim Zhen Quan <<EMAIL>> 1739461996 +0800	reset: moving to ab349ed
ab349ed05aa33a248bbfa1d3e3a37459ed7b7990 1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 Sim Zhen Quan <<EMAIL>> 1739462029 +0800	merge staging/2025-02-13: Merge made by the 'ort' strategy.
1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 Sim Zhen Quan <<EMAIL>> 1739463469 +0800	reset: moving to HEAD
1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 6c02f676ada5b6017976d42bce8cabad38ffcca0 Sim Zhen Quan <<EMAIL>> 1739463470 +0800	checkout: moving from main to assign-product-index-api
6c02f676ada5b6017976d42bce8cabad38ffcca0 af058cf771f1208cbcbc15fecdd2d2f1e11cb567 Sim Zhen Quan <<EMAIL>> 1739463612 +0800	commit (merge): Merge main
af058cf771f1208cbcbc15fecdd2d2f1e11cb567 1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 Sim Zhen Quan <<EMAIL>> 1739463872 +0800	checkout: moving from assign-product-index-api to main
1facb6aabedb0e52b16ba6f30bc0c3672ee1a6a2 586f1ab762b8839941095bce828f02fcb42e8a47 Sim Zhen Quan <<EMAIL>> 1739463882 +0800	pull: Fast-forward
586f1ab762b8839941095bce828f02fcb42e8a47 4b6841f96df0160e7616aada954894819fa10073 Sim Zhen Quan <<EMAIL>> 1739464705 +0800	commit: Deployed to PRD
4b6841f96df0160e7616aada954894819fa10073 bcc5e5b2becc6ebc51aef1a9ad8893826049a610 Sim Zhen Quan <<EMAIL>> 1739500129 +0800	commit: Increase max length for guardian occupation description to 200 characters
bcc5e5b2becc6ebc51aef1a9ad8893826049a610 93bd3bdf18cd53ecf6a46a3fc402fb46d6c2ff23 Sim Zhen Quan <<EMAIL>> 1739500208 +0800	checkout: moving from main to feature/billing-documents-API
93bd3bdf18cd53ecf6a46a3fc402fb46d6c2ff23 f4da47fa17a7edaac94c51a0727f962404ee3f4e Sim Zhen Quan <<EMAIL>> 1739500222 +0800	merge origin/main: Merge made by the 'ort' strategy.
f4da47fa17a7edaac94c51a0727f962404ee3f4e 3b8cb347ed37286aeb7b134828369858b57dcbdf Sim Zhen Quan <<EMAIL>> 1739504528 +0800	checkout: moving from feature/billing-documents-API to separate-payex-callback-and-return-url
3b8cb347ed37286aeb7b134828369858b57dcbdf bdfedc32c41f65802be6f197237c135435ab7564 Sim Zhen Quan <<EMAIL>> 1739504535 +0800	merge origin/main: Merge made by the 'ort' strategy.
bdfedc32c41f65802be6f197237c135435ab7564 cfd8e67e0a284dee4f9430dc01e503e473517496 Sim Zhen Quan <<EMAIL>> 1739504858 +0800	checkout: moving from separate-payex-callback-and-return-url to dev
cfd8e67e0a284dee4f9430dc01e503e473517496 2d7f3a3c700c171154c92f9b436e04233747ea56 Sim Zhen Quan <<EMAIL>> 1739504880 +0800	merge separate-payex-callback-and-return-url: Merge made by the 'ort' strategy.
2d7f3a3c700c171154c92f9b436e04233747ea56 ab5d8b3f872462a1bb4a9f6f868635af497cb8bc Sim Zhen Quan <<EMAIL>> 1739504964 +0800	checkout: moving from dev to hotfix/canteen-billing-document-add-cn
ab5d8b3f872462a1bb4a9f6f868635af497cb8bc b58b6f63145af4f9ca3756cec241ac9b93780b53 Sim Zhen Quan <<EMAIL>> 1739504971 +0800	pull: Fast-forward
b58b6f63145af4f9ca3756cec241ac9b93780b53 c5789bc3c12bb3030cfc26ee3565661c2b8fc495 Sim Zhen Quan <<EMAIL>> 1739504971 +0800	merge origin/main: Merge made by the 'ort' strategy.
c5789bc3c12bb3030cfc26ee3565661c2b8fc495 2d7f3a3c700c171154c92f9b436e04233747ea56 Sim Zhen Quan <<EMAIL>> 1739505501 +0800	checkout: moving from hotfix/canteen-billing-document-add-cn to dev
2d7f3a3c700c171154c92f9b436e04233747ea56 ed48a5774171bc4688939ccb9e6af0d9d61ed3bc Sim Zhen Quan <<EMAIL>> 1739505501 +0800	merge hotfix/canteen-billing-document-add-cn: Merge made by the 'ort' strategy.
ed48a5774171bc4688939ccb9e6af0d9d61ed3bc 55cb53bf9a63b5d43b4db122ab098adc31492c38 Sim Zhen Quan <<EMAIL>> 1739506358 +0800	commit: Deployed to DEV
55cb53bf9a63b5d43b4db122ab098adc31492c38 f4da47fa17a7edaac94c51a0727f962404ee3f4e Sim Zhen Quan <<EMAIL>> 1739506366 +0800	checkout: moving from dev to feature/billing-documents-API
f4da47fa17a7edaac94c51a0727f962404ee3f4e 55cb53bf9a63b5d43b4db122ab098adc31492c38 Sim Zhen Quan <<EMAIL>> 1739514281 +0800	checkout: moving from feature/billing-documents-API to dev
55cb53bf9a63b5d43b4db122ab098adc31492c38 fe27432f9a7f2a5622de4b4aafa2ca184b107e7b Sim Zhen Quan <<EMAIL>> 1739514308 +0800	merge origin/separate-payex-callback-and-return-url: Merge made by the 'ort' strategy.
fe27432f9a7f2a5622de4b4aafa2ca184b107e7b f4da47fa17a7edaac94c51a0727f962404ee3f4e Sim Zhen Quan <<EMAIL>> 1739514523 +0800	checkout: moving from dev to feature/billing-documents-API
f4da47fa17a7edaac94c51a0727f962404ee3f4e 0550476bf8df2ecf1fa30d360321570d77103044 Sim Zhen Quan <<EMAIL>> 1739516000 +0800	commit: Reviewed
0550476bf8df2ecf1fa30d360321570d77103044 fe27432f9a7f2a5622de4b4aafa2ca184b107e7b Sim Zhen Quan <<EMAIL>> 1739516072 +0800	checkout: moving from feature/billing-documents-API to dev
fe27432f9a7f2a5622de4b4aafa2ca184b107e7b 183f26de2172e7f7d20306d91b5e322bbaa2e875 Sim Zhen Quan <<EMAIL>> 1739516072 +0800	merge feature/billing-documents-API: Merge made by the 'ort' strategy.
183f26de2172e7f7d20306d91b5e322bbaa2e875 0550476bf8df2ecf1fa30d360321570d77103044 Sim Zhen Quan <<EMAIL>> 1739516269 +0800	checkout: moving from dev to feature/billing-documents-API
0550476bf8df2ecf1fa30d360321570d77103044 ce652615ca27ce0cc4edd3967b9c5e2b3cf696e4 Sim Zhen Quan <<EMAIL>> 1739517290 +0800	commit: Remove lock
ce652615ca27ce0cc4edd3967b9c5e2b3cf696e4 183f26de2172e7f7d20306d91b5e322bbaa2e875 Sim Zhen Quan <<EMAIL>> 1739517303 +0800	checkout: moving from feature/billing-documents-API to dev
183f26de2172e7f7d20306d91b5e322bbaa2e875 8ecf27ac4d304a7d74732e221657340706e9fa26 Sim Zhen Quan <<EMAIL>> 1739517303 +0800	merge feature/billing-documents-API: Merge made by the 'ort' strategy.
8ecf27ac4d304a7d74732e221657340706e9fa26 a63521b64058068682e98502fd32555e14501922 Sim Zhen Quan <<EMAIL>> 1739520778 +0800	commit: Deployed to DEV
a63521b64058068682e98502fd32555e14501922 bcc5e5b2becc6ebc51aef1a9ad8893826049a610 Sim Zhen Quan <<EMAIL>> 1739522343 +0800	checkout: moving from dev to main
bcc5e5b2becc6ebc51aef1a9ad8893826049a610 9e0dfb5c4f59059772e78526a2c13981ee853794 Sim Zhen Quan <<EMAIL>> 1739522355 +0800	pull: Fast-forward
9e0dfb5c4f59059772e78526a2c13981ee853794 9e0dfb5c4f59059772e78526a2c13981ee853794 Sim Zhen Quan <<EMAIL>> 1739522370 +0800	checkout: moving from main to hotfix/2025-02-14
9e0dfb5c4f59059772e78526a2c13981ee853794 7b6d22b7206c8eeedd2ba7c9c996ca873c4e6fed Sim Zhen Quan <<EMAIL>> 1739522753 +0800	commit: Bug fix for guardian creating new user
7b6d22b7206c8eeedd2ba7c9c996ca873c4e6fed d13b1e0702d0902d28168d4386b15fd5f88f8f40 Sim Zhen Quan <<EMAIL>> 1739522962 +0800	commit: Fix test case
d13b1e0702d0902d28168d4386b15fd5f88f8f40 a63521b64058068682e98502fd32555e14501922 Sim Zhen Quan <<EMAIL>> 1739522981 +0800	checkout: moving from hotfix/2025-02-14 to dev
a63521b64058068682e98502fd32555e14501922 fc96f24356b4cc8bba02e89267443b897e07422a Sim Zhen Quan <<EMAIL>> 1739522995 +0800	merge hotfix/2025-02-14: Merge made by the 'ort' strategy.
fc96f24356b4cc8bba02e89267443b897e07422a 9d002234f970352ff1ffc5a49c2096944f665a21 Sim Zhen Quan <<EMAIL>> 1739525474 +0800	commit: Deployed to DEV
9d002234f970352ff1ffc5a49c2096944f665a21 d13b1e0702d0902d28168d4386b15fd5f88f8f40 Sim Zhen Quan <<EMAIL>> 1739525487 +0800	checkout: moving from dev to hotfix/2025-02-14
d13b1e0702d0902d28168d4386b15fd5f88f8f40 4efd9cd34cdf535d348bdfae490880feebb25d02 Sim Zhen Quan <<EMAIL>> 1739525496 +0800	commit: Fix validation Rule
4efd9cd34cdf535d348bdfae490880feebb25d02 a35718f7870a09976a4cb9b5c1bc6db6943febc2 Sim Zhen Quan <<EMAIL>> 1739525578 +0800	checkout: moving from hotfix/2025-02-14 to bugfix/announcement_and_semester_class_enhancements
a35718f7870a09976a4cb9b5c1bc6db6943febc2 087c4a16657dad616f9e91fe2dfd72c2e002ff83 Sim Zhen Quan <<EMAIL>> 1739525586 +0800	merge origin/main: Merge made by the 'ort' strategy.
087c4a16657dad616f9e91fe2dfd72c2e002ff83 319760bc890f19475d64bf6e4e484b7a0794ba5c Sim Zhen Quan <<EMAIL>> 1739526338 +0800	commit: Reviewed
319760bc890f19475d64bf6e4e484b7a0794ba5c 9d002234f970352ff1ffc5a49c2096944f665a21 Sim Zhen Quan <<EMAIL>> 1739526375 +0800	checkout: moving from bugfix/announcement_and_semester_class_enhancements to dev
9d002234f970352ff1ffc5a49c2096944f665a21 75be5df5f49fddb25a70874e0ab529ff5ab33ea3 Sim Zhen Quan <<EMAIL>> 1739526379 +0800	merge bugfix/announcement_and_semester_class_enhancements: Merge made by the 'ort' strategy.
75be5df5f49fddb25a70874e0ab529ff5ab33ea3 8e470594dc1e5fe411e1006f667e12cde7643356 Sim Zhen Quan <<EMAIL>> 1739526762 +0800	checkout: moving from dev to fix-employee-list-simple-response-fix
8e470594dc1e5fe411e1006f667e12cde7643356 ecfb04bbf2bd2007602b4ac79f07c4ea97dd2e9a Sim Zhen Quan <<EMAIL>> 1739526766 +0800	merge origin/main: Merge made by the 'ort' strategy.
ecfb04bbf2bd2007602b4ac79f07c4ea97dd2e9a 75be5df5f49fddb25a70874e0ab529ff5ab33ea3 Sim Zhen Quan <<EMAIL>> 1739526838 +0800	checkout: moving from fix-employee-list-simple-response-fix to dev
75be5df5f49fddb25a70874e0ab529ff5ab33ea3 7901320cc39b40b9da5c78d5523ee60c08f3b346 Sim Zhen Quan <<EMAIL>> 1739526844 +0800	merge fix-employee-list-simple-response-fix: Merge made by the 'ort' strategy.
7901320cc39b40b9da5c78d5523ee60c08f3b346 78778efcf5709a3e6a9f9d570cabc5e1acc9b70d Sim Zhen Quan <<EMAIL>> 1739527233 +0800	commit: Deployed to DEV
78778efcf5709a3e6a9f9d570cabc5e1acc9b70d 400c0ab1ae91af01f04513de6e19547c6e42e7fb Sim Zhen Quan <<EMAIL>> 1739527398 +0800	merge hotfix/2025-02-14: Merge made by the 'ort' strategy.
400c0ab1ae91af01f04513de6e19547c6e42e7fb 9e0dfb5c4f59059772e78526a2c13981ee853794 Sim Zhen Quan <<EMAIL>> 1739529823 +0800	checkout: moving from dev to main
9e0dfb5c4f59059772e78526a2c13981ee853794 9e0dfb5c4f59059772e78526a2c13981ee853794 Sim Zhen Quan <<EMAIL>> 1739529845 +0800	checkout: moving from main to staging/2025-02-14
9e0dfb5c4f59059772e78526a2c13981ee853794 823ec21435ce4b33d9e3f882293f04eec30e9ab8 Sim Zhen Quan <<EMAIL>> 1739531864 +0800	commit: Add directGuardians method to Student model and update WalletRepository to use it
823ec21435ce4b33d9e3f882293f04eec30e9ab8 c2d130e80c1d0b34d8c15522037843cbf1b91e35 Sim Zhen Quan <<EMAIL>> 1739531878 +0800	merge origin/staging/2025-02-14: Merge made by the 'ort' strategy.
c2d130e80c1d0b34d8c15522037843cbf1b91e35 9e0dfb5c4f59059772e78526a2c13981ee853794 Sim Zhen Quan <<EMAIL>> 1739547231 +0800	checkout: moving from staging/2025-02-14 to main
9e0dfb5c4f59059772e78526a2c13981ee853794 fddc2d8f2da766d2ad7aa2c3627d5da027d5acda Sim Zhen Quan <<EMAIL>> 1739547235 +0800	pull: Fast-forward
fddc2d8f2da766d2ad7aa2c3627d5da027d5acda 7e6e6813adf38bd4247159b94505a1dd4d6fc3fd Sim Zhen Quan <<EMAIL>> 1739547353 +0800	pull: Fast-forward
7e6e6813adf38bd4247159b94505a1dd4d6fc3fd 551e6bedf269d8ebbe7af4f945756800987440a1 Sim Zhen Quan <<EMAIL>> 1739548695 +0800	commit: Deployed to PRD
551e6bedf269d8ebbe7af4f945756800987440a1 551e6bedf269d8ebbe7af4f945756800987440a1 Sim Zhen Quan <<EMAIL>> 1739715564 +0800	checkout: moving from main to hotfix/2025-02-17
551e6bedf269d8ebbe7af4f945756800987440a1 5c0b1b2684c38bf8fa6a839014a5b47d93d2b7bb Sim Zhen Quan <<EMAIL>> 1739715590 +0800	commit: Set Payex payment intent to expire in 5minutes and sort canteen report
5c0b1b2684c38bf8fa6a839014a5b47d93d2b7bb ca4c3489e30494b5483990d929e1f15a99a90239 Sim Zhen Quan <<EMAIL>> 1739715645 +0800	checkout: moving from hotfix/2025-02-17 to feature/unpaid-item-assignment-API
ca4c3489e30494b5483990d929e1f15a99a90239 ca4c3489e30494b5483990d929e1f15a99a90239 Sim Zhen Quan <<EMAIL>> 1739718496 +0800	reset: moving to HEAD
ca4c3489e30494b5483990d929e1f15a99a90239 ca4c3489e30494b5483990d929e1f15a99a90239 Sim Zhen Quan <<EMAIL>> 1739718543 +0800	reset: moving to HEAD
ca4c3489e30494b5483990d929e1f15a99a90239 551e6bedf269d8ebbe7af4f945756800987440a1 Sim Zhen Quan <<EMAIL>> 1739718546 +0800	checkout: moving from feature/unpaid-item-assignment-API to main
551e6bedf269d8ebbe7af4f945756800987440a1 551e6bedf269d8ebbe7af4f945756800987440a1 Sim Zhen Quan <<EMAIL>> 1739718561 +0800	checkout: moving from main to enhancement/permission
551e6bedf269d8ebbe7af4f945756800987440a1 164cb47e3c7487a0d39315338de3fc4e5e0ab070 Sim Zhen Quan <<EMAIL>> 1739718816 +0800	commit: Updated permission to have multiple dependencies
164cb47e3c7487a0d39315338de3fc4e5e0ab070 164cb47e3c7487a0d39315338de3fc4e5e0ab070 Sim Zhen Quan <<EMAIL>> 1739720726 +0800	reset: moving to HEAD
164cb47e3c7487a0d39315338de3fc4e5e0ab070 551e6bedf269d8ebbe7af4f945756800987440a1 Sim Zhen Quan <<EMAIL>> 1739720727 +0800	checkout: moving from enhancement/permission to main
551e6bedf269d8ebbe7af4f945756800987440a1 5d53150448e759fd1d0513eab998951e6602b050 Sim Zhen Quan <<EMAIL>> 1739721300 +0800	commit: Update rate limiter and update Payex cronjob check to process data within last 2hour - last 1hour only
5d53150448e759fd1d0513eab998951e6602b050 49718ae7191a192d7217d8825d1fccdabae9fc4c Sim Zhen Quan <<EMAIL>> 1739721311 +0800	cherry-pick: Set Payex payment intent to expire in 5minutes and sort canteen report
49718ae7191a192d7217d8825d1fccdabae9fc4c 51c38d7d5c0e675538f3a7adac60153bee1877e0 Sim Zhen Quan <<EMAIL>> 1739721710 +0800	commit: Deployed to PRD
51c38d7d5c0e675538f3a7adac60153bee1877e0 ae5527b8765f27943dda1e515546872890686611 Sim Zhen Quan <<EMAIL>> 1739755839 +0800	commit: Hotfix number_format issue
ae5527b8765f27943dda1e515546872890686611 ca4c3489e30494b5483990d929e1f15a99a90239 Sim Zhen Quan <<EMAIL>> 1739757599 +0800	checkout: moving from main to feature/unpaid-item-assignment-API
ca4c3489e30494b5483990d929e1f15a99a90239 77417c58eec031510d7f5b58f7d2181c2fb8852e Sim Zhen Quan <<EMAIL>> 1739757824 +0800	commit (merge): Merge origin/main
77417c58eec031510d7f5b58f7d2181c2fb8852e 164cb47e3c7487a0d39315338de3fc4e5e0ab070 Sim Zhen Quan <<EMAIL>> 1739757836 +0800	checkout: moving from feature/unpaid-item-assignment-API to enhancement/permission
164cb47e3c7487a0d39315338de3fc4e5e0ab070 77417c58eec031510d7f5b58f7d2181c2fb8852e Sim Zhen Quan <<EMAIL>> 1739766093 +0800	checkout: moving from enhancement/permission to feature/unpaid-item-assignment-API
77417c58eec031510d7f5b58f7d2181c2fb8852e 7924470fc0fb2e0cc5e646bf08b5fad21c4ae4c3 Sim Zhen Quan <<EMAIL>> 1739778555 +0800	commit: Reviewed and grouped transaction date by school timezone
7924470fc0fb2e0cc5e646bf08b5fad21c4ae4c3 400c0ab1ae91af01f04513de6e19547c6e42e7fb Sim Zhen Quan <<EMAIL>> 1739778688 +0800	checkout: moving from feature/unpaid-item-assignment-API to dev
400c0ab1ae91af01f04513de6e19547c6e42e7fb 448324567d7d6d61cdf5c0d536d6c29fffb99d96 Sim Zhen Quan <<EMAIL>> 1739778694 +0800	merge origin/main: Merge made by the 'ort' strategy.
448324567d7d6d61cdf5c0d536d6c29fffb99d96 205758678fb04a1866cfa4f0fea8723cce153c95 Sim Zhen Quan <<EMAIL>> 1739778870 +0800	commit: Deployed to DEV
205758678fb04a1866cfa4f0fea8723cce153c95 6677b5ee2b47fd2198ae6559c9139223ec29f259 Sim Zhen Quan <<EMAIL>> 1739778877 +0800	checkout: moving from dev to feature/scholarship-CRUD
6677b5ee2b47fd2198ae6559c9139223ec29f259 02be4644a58335c31dde48dcd0e6b4a1ee4cd5ff Sim Zhen Quan <<EMAIL>> 1739779047 +0800	commit (merge): Merge origin/main
02be4644a58335c31dde48dcd0e6b4a1ee4cd5ff ae5527b8765f27943dda1e515546872890686611 Sim Zhen Quan <<EMAIL>> 1739779631 +0800	checkout: moving from feature/scholarship-CRUD to main
ae5527b8765f27943dda1e515546872890686611 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> 1739779635 +0800	pull: Fast-forward
9cc37e0ed353656a6218ef977789232f4cca370f 02be4644a58335c31dde48dcd0e6b4a1ee4cd5ff Sim Zhen Quan <<EMAIL>> 1739779879 +0800	checkout: moving from main to feature/scholarship-CRUD
02be4644a58335c31dde48dcd0e6b4a1ee4cd5ff 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> 1739779883 +0800	checkout: moving from feature/scholarship-CRUD to main
9cc37e0ed353656a6218ef977789232f4cca370f 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> 1739779905 +0800	checkout: moving from main to add-create-billing-document-from-unpaid-items
9cc37e0ed353656a6218ef977789232f4cca370f 75db019850566276bbd6f6103ffadb2cbd61424c Sim Zhen Quan <<EMAIL>> 1739780547 +0800	commit: Change make payment from unpaid item to just create billing document
75db019850566276bbd6f6103ffadb2cbd61424c 9b6dceef2d3757cf4f523099dda497535a633dbb Sim Zhen Quan <<EMAIL>> 1739781004 +0800	commit: Rename variable
9b6dceef2d3757cf4f523099dda497535a633dbb 205758678fb04a1866cfa4f0fea8723cce153c95 Sim Zhen Quan <<EMAIL>> 1739782007 +0800	checkout: moving from add-create-billing-document-from-unpaid-items to dev
205758678fb04a1866cfa4f0fea8723cce153c95 65f142203f032aa699d92bdc15d650a56be31a7d Sim Zhen Quan <<EMAIL>> 1739782011 +0800	merge add-create-billing-document-from-unpaid-items: Merge made by the 'ort' strategy.
65f142203f032aa699d92bdc15d650a56be31a7d 0cb4a7aca9e87985a2b44c03eb68398f30caf3e7 Sim Zhen Quan <<EMAIL>> 1739782179 +0800	commit: Deployed to DEV
0cb4a7aca9e87985a2b44c03eb68398f30caf3e7 02be4644a58335c31dde48dcd0e6b4a1ee4cd5ff Sim Zhen Quan <<EMAIL>> 1739782208 +0800	checkout: moving from dev to feature/scholarship-CRUD
02be4644a58335c31dde48dcd0e6b4a1ee4cd5ff d210ee2f66dfaf2c8598d4b99ba0b17d491b50af Sim Zhen Quan <<EMAIL>> 1739788162 +0800	commit: Reviewed, need to update code to use repository instead of directly retrieving data from model
d210ee2f66dfaf2c8598d4b99ba0b17d491b50af 0cb4a7aca9e87985a2b44c03eb68398f30caf3e7 Sim Zhen Quan <<EMAIL>> 1739788212 +0800	checkout: moving from feature/scholarship-CRUD to dev
0cb4a7aca9e87985a2b44c03eb68398f30caf3e7 353db981f801b8a187dc8ebc3b6098335bd702e8 Sim Zhen Quan <<EMAIL>> 17******** +0800	commit (merge): Merged scholarship CRUD
353db981f801b8a187dc8ebc3b6098335bd702e8 5233c13954a867bff3dd5a66d30f1f55adb65794 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
5233c13954a867bff3dd5a66d30f1f55adb65794 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to main
9cc37e0ed353656a6218ef977789232f4cca370f 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to accounting-module
9cc37e0ed353656a6218ef977789232f4cca370f 5233c13954a867bff3dd5a66d30f1f55adb65794 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from accounting-module to dev
5233c13954a867bff3dd5a66d30f1f55adb65794 d2bdf7f146e09a96620fced0dd6042adf6dd95f5 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Controller fix
d2bdf7f146e09a96620fced0dd6042adf6dd95f5 9b6dceef2d3757cf4f523099dda497535a633dbb Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to add-create-billing-document-from-unpaid-items
9b6dceef2d3757cf4f523099dda497535a633dbb fce50f59a4101c34e7b44d649503c3c4805d1cb5 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge
fce50f59a4101c34e7b44d649503c3c4805d1cb5 d2bdf7f146e09a96620fced0dd6042adf6dd95f5 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from add-create-billing-document-from-unpaid-items to dev
d2bdf7f146e09a96620fced0dd6042adf6dd95f5 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to accounting-module
9cc37e0ed353656a6218ef977789232f4cca370f 576fd60e2156e9a7a93d5072d02a1291e38e29dc Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
576fd60e2156e9a7a93d5072d02a1291e38e29dc 8bb36ca4da1982f695f9078e0625ea3dee0b5978 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Set period_from and period_to to nullable
8bb36ca4da1982f695f9078e0625ea3dee0b5978 872a1e6f195c740c224f21c8efbbac0604cdba07 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from accounting-module to feature/discount-CRUD
872a1e6f195c740c224f21c8efbbac0604cdba07 c90fb6e2a53676ef5f600814749c818eed2b9676 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge accounting module changes
c90fb6e2a53676ef5f600814749c818eed2b9676 c90fb6e2a53676ef5f600814749c818eed2b9676 Sim Zhen Quan <<EMAIL>> ********** +0800	reset: moving to HEAD
c90fb6e2a53676ef5f600814749c818eed2b9676 9cc37e0ed353656a6218ef977789232f4cca370f Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/discount-CRUD to main
9cc37e0ed353656a6218ef977789232f4cca370f 8087ad45ec0fb1af1580d189d94a9888badeb571 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Fix resource nullable issue
8087ad45ec0fb1af1580d189d94a9888badeb571 4ba1cca35b436bbc376c4c273827730ab95350ac Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
4ba1cca35b436bbc376c4c273827730ab95350ac 8bb36ca4da1982f695f9078e0625ea3dee0b5978 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to accounting-module
8bb36ca4da1982f695f9078e0625ea3dee0b5978 8385de1c27be9f822aea54dc8c7a49ef997b7a9b Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
8385de1c27be9f822aea54dc8c7a49ef997b7a9b bb60e5588ba3501869e2cbd46e02ec3df8237db6 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
bb60e5588ba3501869e2cbd46e02ec3df8237db6 c90fb6e2a53676ef5f600814749c818eed2b9676 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from accounting-module to feature/discount-CRUD
c90fb6e2a53676ef5f600814749c818eed2b9676 341acea29ee73f77bc1402b5d4ffe77e8046bf55 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge accounting-module branch
341acea29ee73f77bc1402b5d4ffe77e8046bf55 4ba1cca35b436bbc376c4c273827730ab95350ac Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/discount-CRUD to main
4ba1cca35b436bbc376c4c273827730ab95350ac a6c6a5a0d3699bea541929f19b084866eea40f6c Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Fix validation
a6c6a5a0d3699bea541929f19b084866eea40f6c 341acea29ee73f77bc1402b5d4ffe77e8046bf55 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to feature/discount-CRUD
341acea29ee73f77bc1402b5d4ffe77e8046bf55 0dfa6999e9cbe1c50936646802228af53d41fa86 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/discount-CRUD to regenerate-employee-number
0dfa6999e9cbe1c50936646802228af53d41fa86 341acea29ee73f77bc1402b5d4ffe77e8046bf55 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from regenerate-employee-number to feature/discount-CRUD
341acea29ee73f77bc1402b5d4ffe77e8046bf55 3faba8ed91f5c56fe1002820e56d774aea6865c7 Sim Zhen Quan <<EMAIL>> 1739937285 +0800	commit: Reviewed
3faba8ed91f5c56fe1002820e56d774aea6865c7 d2bdf7f146e09a96620fced0dd6042adf6dd95f5 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/discount-CRUD to dev
d2bdf7f146e09a96620fced0dd6042adf6dd95f5 5056b6cff74933cb897d6f13cdc0469df3806675 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
5056b6cff74933cb897d6f13cdc0469df3806675 023b219cfc5e056dae9a2ffa1829d9ae35c20c1d Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/accounting-module: Merge made by the 'ort' strategy.
023b219cfc5e056dae9a2ffa1829d9ae35c20c1d ac328bfd1a1072723c3f6f2ae1d5c67e031587bb Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
ac328bfd1a1072723c3f6f2ae1d5c67e031587bb d961316e1e27ab0a89e446b69add1d95c293294a Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to feature/manual-payment
d961316e1e27ab0a89e446b69add1d95c293294a 2c21d5c7208c646d15622fa75d42090cd98ea9a4 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
2c21d5c7208c646d15622fa75d42090cd98ea9a4 71d838db99a644c9e36c41fbacff2f7961a78861 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/accounting-module: Merge made by the 'ort' strategy.
71d838db99a644c9e36c41fbacff2f7961a78861 00b642ba6b40a0c1a2dd03d8ff0dcaadf6457f0e Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Review WIP
00b642ba6b40a0c1a2dd03d8ff0dcaadf6457f0e ac328bfd1a1072723c3f6f2ae1d5c67e031587bb Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/manual-payment to dev
ac328bfd1a1072723c3f6f2ae1d5c67e031587bb e7f19110d95374086528de1b45fb98e3aaf6d08e Sim Zhen Quan <<EMAIL>> ********** +0800	merge feature/manual-payment: Merge made by the 'ort' strategy.
e7f19110d95374086528de1b45fb98e3aaf6d08e bfa9278b58dac6cc5da3160fd4197ab3297648aa Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
bfa9278b58dac6cc5da3160fd4197ab3297648aa de21403f5fa6c2398609a32e3a37cd4673824138 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to attendance-module
de21403f5fa6c2398609a32e3a37cd4673824138 48ff7b9927073ac26148222c25ec4b592c15e409 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
48ff7b9927073ac26148222c25ec4b592c15e409 bb60e5588ba3501869e2cbd46e02ec3df8237db6 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from attendance-module to accounting-module
bb60e5588ba3501869e2cbd46e02ec3df8237db6 12291c4d88586343f31fef704a16048b694d933c Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
12291c4d88586343f31fef704a16048b694d933c 3d035d79064b5c3b9e3e88c2e58c0a43d386e15e Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from accounting-module to feature/hostel-reward-punishment-report
3d035d79064b5c3b9e3e88c2e58c0a43d386e15e d20fa05a2109497b2ee5b8868cb82d8e7467705c Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
d20fa05a2109497b2ee5b8868cb82d8e7467705c da95870aa2bd9f9b74e40886e6fa961fe20a7b6d Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Review WIP
da95870aa2bd9f9b74e40886e6fa961fe20a7b6d bfa9278b58dac6cc5da3160fd4197ab3297648aa Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/hostel-reward-punishment-report to dev
bfa9278b58dac6cc5da3160fd4197ab3297648aa b22fc50fe215dbe421b1b9d306afbc27696e8286 Sim Zhen Quan <<EMAIL>> ********** +0800	merge feature/hostel-reward-punishment-report: Merge made by the 'ort' strategy.
b22fc50fe215dbe421b1b9d306afbc27696e8286 22dc8e2659dc7e89774596ca59d5f9e9ac2c7c9b Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
22dc8e2659dc7e89774596ca59d5f9e9ac2c7c9b da95870aa2bd9f9b74e40886e6fa961fe20a7b6d Sim Zhen Quan <<EMAIL>> 1740035772 +0800	checkout: moving from dev to feature/hostel-reward-punishment-report
da95870aa2bd9f9b74e40886e6fa961fe20a7b6d da95870aa2bd9f9b74e40886e6fa961fe20a7b6d Sim Zhen Quan <<EMAIL>> 1740035782 +0800	checkout: moving from feature/hostel-reward-punishment-report to feature/hostel-reward-punishment-report
da95870aa2bd9f9b74e40886e6fa961fe20a7b6d fe173763be43c7564c766a964a3c9432bd5cf4a6 Sim Zhen Quan <<EMAIL>> 1740036119 +0800	commit: Formatting changes
fe173763be43c7564c766a964a3c9432bd5cf4a6 3572342bbbe19cfe61e5f912590484dc24d9fa21 Sim Zhen Quan <<EMAIL>> 1740037824 +0800	checkout: moving from feature/hostel-reward-punishment-report to push-notification-order-confirmed
3572342bbbe19cfe61e5f912590484dc24d9fa21 3572342bbbe19cfe61e5f912590484dc24d9fa21 Sim Zhen Quan <<EMAIL>> 1740037891 +0800	checkout: moving from push-notification-order-confirmed to push-notification-order-confirmed
3572342bbbe19cfe61e5f912590484dc24d9fa21 7a0ab4f15ff0efe014206c344d861b7f9c7ec695 Sim Zhen Quan <<EMAIL>> 1740038654 +0800	commit: Reviewed
7a0ab4f15ff0efe014206c344d861b7f9c7ec695 22dc8e2659dc7e89774596ca59d5f9e9ac2c7c9b Sim Zhen Quan <<EMAIL>> 1740038865 +0800	checkout: moving from push-notification-order-confirmed to dev
22dc8e2659dc7e89774596ca59d5f9e9ac2c7c9b a92d223cebf54e378df3d2005d50c49085781581 Sim Zhen Quan <<EMAIL>> 1740038881 +0800	merge push-notification-order-confirmed: Merge made by the 'ort' strategy.
a92d223cebf54e378df3d2005d50c49085781581 1f0ae7d476d4761b836f308f724e1d1e3b208910 Sim Zhen Quan <<EMAIL>> 1740038901 +0800	merge feature/hostel-reward-punishment-report: Merge made by the 'ort' strategy.
1f0ae7d476d4761b836f308f724e1d1e3b208910 fe173763be43c7564c766a964a3c9432bd5cf4a6 Sim Zhen Quan <<EMAIL>> 1740039351 +0800	checkout: moving from dev to feature/hostel-reward-punishment-report
fe173763be43c7564c766a964a3c9432bd5cf4a6 536b8eaaf1f2b09e604e3e8aa9a0fb72de8a0728 Sim Zhen Quan <<EMAIL>> 1740039357 +0800	pull: Fast-forward
536b8eaaf1f2b09e604e3e8aa9a0fb72de8a0728 1f0ae7d476d4761b836f308f724e1d1e3b208910 Sim Zhen Quan <<EMAIL>> 1740042227 +0800	checkout: moving from feature/hostel-reward-punishment-report to dev
1f0ae7d476d4761b836f308f724e1d1e3b208910 53f7e0b1a604ce08fc0cef9ad9e75cacf7e85785 Sim Zhen Quan <<EMAIL>> 1740042227 +0800	merge feature/hostel-reward-punishment-report: Merge made by the 'ort' strategy.
53f7e0b1a604ce08fc0cef9ad9e75cacf7e85785 745fbc6aae5d9218b45878d44b9d9dc30ac2fd06 Sim Zhen Quan <<EMAIL>> 1740043628 +0800	checkout: moving from dev to add-is-direct-dependant-to-student-create-update-api
745fbc6aae5d9218b45878d44b9d9dc30ac2fd06 5a196046a2a60760819135ebdff9ccfd052b0d9b Sim Zhen Quan <<EMAIL>> 1740043640 +0800	merge origin/main: Merge made by the 'ort' strategy.
5a196046a2a60760819135ebdff9ccfd052b0d9b 30fac8fefd08dfc9007c21e01d3b5167146fde4c Sim Zhen Quan <<EMAIL>> 1740044038 +0800	commit: Split hostel permission to student and employee
30fac8fefd08dfc9007c21e01d3b5167146fde4c 53f7e0b1a604ce08fc0cef9ad9e75cacf7e85785 Sim Zhen Quan <<EMAIL>> 1740044060 +0800	checkout: moving from add-is-direct-dependant-to-student-create-update-api to dev
53f7e0b1a604ce08fc0cef9ad9e75cacf7e85785 d1b42fd2e271aef40f973347a9250fb78fb78f10 Sim Zhen Quan <<EMAIL>> 1740044106 +0800	commit (merge): Merge add-is-direct-dependant-to-student-create-update-api
d1b42fd2e271aef40f973347a9250fb78fb78f10 47aba25492d183a21e43f1dc731fc4bc8c45c8f1 Sim Zhen Quan <<EMAIL>> 1740045348 +0800	commit: Revert permission
47aba25492d183a21e43f1dc731fc4bc8c45c8f1 b8e44f3e73343b56a7cde7101bd463efaa2eb4fa Sim Zhen Quan <<EMAIL>> 1740045562 +0800	commit: Deployed to dev
b8e44f3e73343b56a7cde7101bd463efaa2eb4fa a6c6a5a0d3699bea541929f19b084866eea40f6c Sim Zhen Quan <<EMAIL>> 1740062966 +0800	checkout: moving from dev to main
a6c6a5a0d3699bea541929f19b084866eea40f6c a6c6a5a0d3699bea541929f19b084866eea40f6c Sim Zhen Quan <<EMAIL>> 1740062983 +0800	checkout: moving from main to staging/2025-02-20
a6c6a5a0d3699bea541929f19b084866eea40f6c 2a0f44e4869dc12422709515d943299ca2985f90 Sim Zhen Quan <<EMAIL>> 1740063043 +0800	checkout: moving from staging/2025-02-20 to staging/2025-02-13
2a0f44e4869dc12422709515d943299ca2985f90 a6c6a5a0d3699bea541929f19b084866eea40f6c Sim Zhen Quan <<EMAIL>> 1740063074 +0800	checkout: moving from staging/2025-02-13 to main
a6c6a5a0d3699bea541929f19b084866eea40f6c 536b8eaaf1f2b09e604e3e8aa9a0fb72de8a0728 Sim Zhen Quan <<EMAIL>> 1740063146 +0800	checkout: moving from main to feature/hostel-reward-punishment-report
536b8eaaf1f2b09e604e3e8aa9a0fb72de8a0728 29bd4faa2cae8a8b57fc5c53ef91599611c12059 Sim Zhen Quan <<EMAIL>> 1740063152 +0800	pull: Fast-forward
29bd4faa2cae8a8b57fc5c53ef91599611c12059 a6c6a5a0d3699bea541929f19b084866eea40f6c Sim Zhen Quan <<EMAIL>> 1740063227 +0800	checkout: moving from feature/hostel-reward-punishment-report to staging/2025-02-20
a6c6a5a0d3699bea541929f19b084866eea40f6c c7faa6fa24db39beb81cd0da089383c983585d04 Sim Zhen Quan <<EMAIL>> 1740063232 +0800	pull: Fast-forward
c7faa6fa24db39beb81cd0da089383c983585d04 29bd4faa2cae8a8b57fc5c53ef91599611c12059 Sim Zhen Quan <<EMAIL>> 1740063362 +0800	checkout: moving from staging/2025-02-20 to feature/hostel-reward-punishment-report
29bd4faa2cae8a8b57fc5c53ef91599611c12059 62908d97dc72ab69978a36fdfe9c70723485d340 Sim Zhen Quan <<EMAIL>> 1740064217 +0800	commit (merge): Merge staging code
62908d97dc72ab69978a36fdfe9c70723485d340 c7faa6fa24db39beb81cd0da089383c983585d04 Sim Zhen Quan <<EMAIL>> 1740064276 +0800	checkout: moving from feature/hostel-reward-punishment-report to staging/2025-02-20
c7faa6fa24db39beb81cd0da089383c983585d04 c7faa6fa24db39beb81cd0da089383c983585d04 Sim Zhen Quan <<EMAIL>> 1740064279 +0800	checkout: moving from staging/2025-02-20 to staging/2025-02-20
c7faa6fa24db39beb81cd0da089383c983585d04 cb2c39708b35d221e666bfc4d371a3472d0c626a Sim Zhen Quan <<EMAIL>> 1740064291 +0800	pull: Fast-forward
cb2c39708b35d221e666bfc4d371a3472d0c626a ea7f670c5cd41b996b78ff0d4b6a416e0e49e0ef Sim Zhen Quan <<EMAIL>> 1740064666 +0800	commit: Fix routes
ea7f670c5cd41b996b78ff0d4b6a416e0e49e0ef 3a0546499aaac0b75edb0138ff7962a0a5c1e7bb Sim Zhen Quan <<EMAIL>> 1740065321 +0800	checkout: moving from staging/2025-02-20 to enhancement/invoice_printing_and_student_guardian_search
3a0546499aaac0b75edb0138ff7962a0a5c1e7bb f418fb4d7510a7c80f547a573f490983f106c44c Sim Zhen Quan <<EMAIL>> 1740069247 +0800	commit: Reviewed
f418fb4d7510a7c80f547a573f490983f106c44c b8e44f3e73343b56a7cde7101bd463efaa2eb4fa Sim Zhen Quan <<EMAIL>> 1740069460 +0800	checkout: moving from enhancement/invoice_printing_and_student_guardian_search to dev
b8e44f3e73343b56a7cde7101bd463efaa2eb4fa da3923c3c209d72fa441255d6af9d96753997387 Sim Zhen Quan <<EMAIL>> 1740069530 +0800	commit (merge): Merge branch
da3923c3c209d72fa441255d6af9d96753997387 af4a0c42df8aa150985bc237f24933ea0f00bd1a Sim Zhen Quan <<EMAIL>> 1740069754 +0800	commit: Deployed to DEV
af4a0c42df8aa150985bc237f24933ea0f00bd1a 0dfa6999e9cbe1c50936646802228af53d41fa86 Sim Zhen Quan <<EMAIL>> 1740069825 +0800	checkout: moving from dev to regenerate-employee-number
0dfa6999e9cbe1c50936646802228af53d41fa86 11ac0b3147c40f36e32e22c8f71bf4f147678f92 Sim Zhen Quan <<EMAIL>> 1740069831 +0800	merge origin/main: Merge made by the 'ort' strategy.
11ac0b3147c40f36e32e22c8f71bf4f147678f92 c52c2e3f84707c4abc8ed7c1911da6628f2b3eb9 Sim Zhen Quan <<EMAIL>> 1740101404 +0800	commit: Exclude unnecessary employee
c52c2e3f84707c4abc8ed7c1911da6628f2b3eb9 ffcac27fa68a31f2f87c43fca60343fea5523f1c Sim Zhen Quan <<EMAIL>> 1740101418 +0800	checkout: moving from regenerate-employee-number to migration-hostel-bed-assignment
ffcac27fa68a31f2f87c43fca60343fea5523f1c ffcac27fa68a31f2f87c43fca60343fea5523f1c Sim Zhen Quan <<EMAIL>> 1740103128 +0800	reset: moving to HEAD
ffcac27fa68a31f2f87c43fca60343fea5523f1c f418fb4d7510a7c80f547a573f490983f106c44c Sim Zhen Quan <<EMAIL>> 1740103138 +0800	checkout: moving from migration-hostel-bed-assignment to enhancement/invoice_printing_and_student_guardian_search
f418fb4d7510a7c80f547a573f490983f106c44c f418fb4d7510a7c80f547a573f490983f106c44c Sim Zhen Quan <<EMAIL>> 1740104330 +0800	reset: moving to HEAD
f418fb4d7510a7c80f547a573f490983f106c44c ea7f670c5cd41b996b78ff0d4b6a416e0e49e0ef Sim Zhen Quan <<EMAIL>> 1740104332 +0800	checkout: moving from enhancement/invoice_printing_and_student_guardian_search to staging/2025-02-20
ea7f670c5cd41b996b78ff0d4b6a416e0e49e0ef 7743372396b29315f47c828449b98f684b3cd545 Sim Zhen Quan <<EMAIL>> 1740104338 +0800	pull: Fast-forward
7743372396b29315f47c828449b98f684b3cd545 c7d1193b20ea9c26c07e42059bca5c9ff6bcf9ad Sim Zhen Quan <<EMAIL>> 1740104352 +0800	commit: Fix where statement
c7d1193b20ea9c26c07e42059bca5c9ff6bcf9ad ffcac27fa68a31f2f87c43fca60343fea5523f1c Sim Zhen Quan <<EMAIL>> 1740104479 +0800	checkout: moving from staging/2025-02-20 to migration-hostel-bed-assignment
ffcac27fa68a31f2f87c43fca60343fea5523f1c cd0d97a8fe56b198770c934531465ac8f146d74e Sim Zhen Quan <<EMAIL>> 1740105140 +0800	commit: Reviewed
cd0d97a8fe56b198770c934531465ac8f146d74e 444cab1862c16c54a35697e329029c0bd086d53f Sim Zhen Quan <<EMAIL>> 1740105153 +0800	merge origin/main: Merge made by the 'ort' strategy.
444cab1862c16c54a35697e329029c0bd086d53f 164cb47e3c7487a0d39315338de3fc4e5e0ab070 Sim Zhen Quan <<EMAIL>> 1740105410 +0800	checkout: moving from migration-hostel-bed-assignment to enhancement/permission
164cb47e3c7487a0d39315338de3fc4e5e0ab070 0b337d8f45a29293dfbf25620fdfe87916cfcc39 Sim Zhen Quan <<EMAIL>> 1740105415 +0800	pull: Fast-forward
0b337d8f45a29293dfbf25620fdfe87916cfcc39 524e9aa127bc3dae678ed966c0ca910db504f838 Sim Zhen Quan <<EMAIL>> 1740105422 +0800	merge origin/main: Merge made by the 'ort' strategy.
524e9aa127bc3dae678ed966c0ca910db504f838 1a2f5aca61c4b0f23672b42beb43211e33448799 Sim Zhen Quan <<EMAIL>> 1740106130 +0800	commit: Reviewed
1a2f5aca61c4b0f23672b42beb43211e33448799 ed5e4f6fe5b4a9b89e2dfe12dda29576b2fa6ea6 Sim Zhen Quan <<EMAIL>> 1740106341 +0800	merge origin/staging/2025-02-20: Merge made by the 'ort' strategy.
ed5e4f6fe5b4a9b89e2dfe12dda29576b2fa6ea6 af4a0c42df8aa150985bc237f24933ea0f00bd1a Sim Zhen Quan <<EMAIL>> 1740108067 +0800	checkout: moving from enhancement/permission to dev
af4a0c42df8aa150985bc237f24933ea0f00bd1a e8b6415bfae6369925011f90b2ae0e0cb861e5f7 Sim Zhen Quan <<EMAIL>> 1740108571 +0800	checkout: moving from dev to unpaid-item-return-amount-after-discount
e8b6415bfae6369925011f90b2ae0e0cb861e5f7 8af2f594047e5dd37156c422d69a583925ebc7ed Sim Zhen Quan <<EMAIL>> 1740108600 +0800	merge origin/main: Merge made by the 'ort' strategy.
8af2f594047e5dd37156c422d69a583925ebc7ed af4a0c42df8aa150985bc237f24933ea0f00bd1a Sim Zhen Quan <<EMAIL>> 1740110276 +0800	checkout: moving from unpaid-item-return-amount-after-discount to dev
af4a0c42df8aa150985bc237f24933ea0f00bd1a 8af2f594047e5dd37156c422d69a583925ebc7ed Sim Zhen Quan <<EMAIL>> 1740117815 +0800	checkout: moving from dev to unpaid-item-return-amount-after-discount
8af2f594047e5dd37156c422d69a583925ebc7ed af4a0c42df8aa150985bc237f24933ea0f00bd1a Sim Zhen Quan <<EMAIL>> 1740118069 +0800	checkout: moving from unpaid-item-return-amount-after-discount to dev
af4a0c42df8aa150985bc237f24933ea0f00bd1a 7fa5ee617e66733922660d4c4c48a51fa4c3f87b Sim Zhen Quan <<EMAIL>> 1740118902 +0800	commit (merge): Merge
7fa5ee617e66733922660d4c4c48a51fa4c3f87b 86cc63c25d1ced041c05906429f3734fcf6b4e0a Sim Zhen Quan <<EMAIL>> 1740118929 +0800	merge origin/staging/2025-02-20: Merge made by the 'ort' strategy.
86cc63c25d1ced041c05906429f3734fcf6b4e0a d5da2d9e34c67ecf8fd507bc28d0edfd13a9c3c2 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/staging/2025-02-20: Merge made by the 'ort' strategy.
d5da2d9e34c67ecf8fd507bc28d0edfd13a9c3c2 8eadb7c24240db2ee72aa4b0f37a51b9e1c1b053 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
8eadb7c24240db2ee72aa4b0f37a51b9e1c1b053 12291c4d88586343f31fef704a16048b694d933c Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to accounting-module
12291c4d88586343f31fef704a16048b694d933c 0ffebae8515325896cf8e86faa7cbef1b6e2d77d Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
0ffebae8515325896cf8e86faa7cbef1b6e2d77d 540c59c5d2aed36a3b7ea0784c936dd495e738a8 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
540c59c5d2aed36a3b7ea0784c936dd495e738a8 8eadb7c24240db2ee72aa4b0f37a51b9e1c1b053 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from accounting-module to dev
8eadb7c24240db2ee72aa4b0f37a51b9e1c1b053 540c59c5d2aed36a3b7ea0784c936dd495e738a8 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to accounting-module
540c59c5d2aed36a3b7ea0784c936dd495e738a8 8af2f594047e5dd37156c422d69a583925ebc7ed Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from accounting-module to unpaid-item-return-amount-after-discount
8af2f594047e5dd37156c422d69a583925ebc7ed ddd5af3e03245d8f80c27d54f733b609e5d52a99 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge main
ddd5af3e03245d8f80c27d54f733b609e5d52a99 8eadb7c24240db2ee72aa4b0f37a51b9e1c1b053 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from unpaid-item-return-amount-after-discount to dev
8eadb7c24240db2ee72aa4b0f37a51b9e1c1b053 e3eab95c4d2c18b7004855379784b2ee3970fae0 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
e3eab95c4d2c18b7004855379784b2ee3970fae0 81be796c805e8a0419a9db21270ce93a7455f847 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Added thumbnail generation
81be796c805e8a0419a9db21270ce93a7455f847 ca7ed22a0bb4743d4ae0ef963045d8236934edab Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to dev
ca7ed22a0bb4743d4ae0ef963045d8236934edab c7d1193b20ea9c26c07e42059bca5c9ff6bcf9ad Sim Zhen Quan <<EMAIL>> ********25 +0800	checkout: moving from dev to staging/2025-02-20
c7d1193b20ea9c26c07e42059bca5c9ff6bcf9ad d759c6e5b9b83403edf894c32c54d0fcf20a8a49 Sim Zhen Quan <<EMAIL>> ********30 +0800	pull: Fast-forward
d759c6e5b9b83403edf894c32c54d0fcf20a8a49 ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 eac06b8b82a5d93de7174b14434b85d17177d822 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from staging/2025-02-20 to fix/accounting-changes
eac06b8b82a5d93de7174b14434b85d17177d822 5e4e7f2794eb3d15f16a7abb4730552d199ae868 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed. Need to update print unpaid item list to include discount as well
5e4e7f2794eb3d15f16a7abb4730552d199ae868 9fbb3342f27677ef49c5807d6d85ad9f22b7ced9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/accounting-changes to master-grading-framework-CRUD
9fbb3342f27677ef49c5807d6d85ad9f22b7ced9 4b15a6e018e9412a200fda41a43cbe7c336b9abc Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
4b15a6e018e9412a200fda41a43cbe7c336b9abc ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from master-grading-framework-CRUD to staging/2025-02-20
ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 Sim Zhen Quan <<EMAIL>> ********** +0800	reset: moving to HEAD
ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from staging/2025-02-20 to staging/2025-02-20
ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from staging/2025-02-20 to add-direct-dependant-to-guardian-resource
ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 07afce6b636bb325ea671b10825d941ff9429689 Sim Zhen Quan <<EMAIL>> 1740367153 +0800	commit: Add direct dependant to guardian resource
07afce6b636bb325ea671b10825d941ff9429689 9038116dc99736e39b5a5bb2b7296036d39d4326 Sim Zhen Quan <<EMAIL>> 1740368143 +0800	checkout: moving from add-direct-dependant-to-guardian-resource to 9038116
9038116dc99736e39b5a5bb2b7296036d39d4326 9038116dc99736e39b5a5bb2b7296036d39d4326 Sim Zhen Quan <<EMAIL>> 1740368156 +0800	checkout: moving from 9038116dc99736e39b5a5bb2b7296036d39d4326 to grading-framework-crud
9038116dc99736e39b5a5bb2b7296036d39d4326 c0f712b11d2cea5f48945e73f08cb0438ffd2239 Sim Zhen Quan <<EMAIL>> 1740368493 +0800	commit (merge): Merge origin/main
c0f712b11d2cea5f48945e73f08cb0438ffd2239 83cac59ef4a87982ddd4482b0d20932253b61474 Sim Zhen Quan <<EMAIL>> 1740372632 +0800	commit: Reviewed, pending to update test cases
83cac59ef4a87982ddd4482b0d20932253b61474 ca7ed22a0bb4743d4ae0ef963045d8236934edab Sim Zhen Quan <<EMAIL>> 1740372651 +0800	checkout: moving from grading-framework-crud to dev
ca7ed22a0bb4743d4ae0ef963045d8236934edab eb35071966633ef7cf10fb22750b404d801d14e0 Sim Zhen Quan <<EMAIL>> 1740372753 +0800	commit (merge): Merge branch
eb35071966633ef7cf10fb22750b404d801d14e0 bf6e129be29ca6b6a78af5264e2e545f233c3a7f Sim Zhen Quan <<EMAIL>> 1740372778 +0800	merge origin/add-is-direct-dependant-to-guardian-resource: Merge made by the 'ort' strategy.
bf6e129be29ca6b6a78af5264e2e545f233c3a7f 4fe4486a24b79ca9107e645018bad33e566ec367 Sim Zhen Quan <<EMAIL>> 1740373915 +0800	commit: Deployed to dev
4fe4486a24b79ca9107e645018bad33e566ec367 a6c6a5a0d3699bea541929f19b084866eea40f6c Sim Zhen Quan <<EMAIL>> 1740373924 +0800	checkout: moving from dev to main
a6c6a5a0d3699bea541929f19b084866eea40f6c aadd544c66bf3fbaf7576c81f1e6c3046596de30 Sim Zhen Quan <<EMAIL>> 1740373930 +0800	pull: Fast-forward
aadd544c66bf3fbaf7576c81f1e6c3046596de30 ba9f430ab636520e55497cb82c9e63458cf05556 Sim Zhen Quan <<EMAIL>> 1740373966 +0800	commit: Disable media conversion on testing env
ba9f430ab636520e55497cb82c9e63458cf05556 e70b3961e27b0cf52e75685a1345631b60a55eec Sim Zhen Quan <<EMAIL>> 1740392406 +0800	commit: Test case fixes
e70b3961e27b0cf52e75685a1345631b60a55eec 1e501ceac7a739cc8b80bf5e62517e890af6222c Sim Zhen Quan <<EMAIL>> 1740409421 +0800	commit: Fix weekly canteen report to show next week data
1e501ceac7a739cc8b80bf5e62517e890af6222c 8551355ac021c2ef36e5bbcc5c6c3795c79ee6a8 Sim Zhen Quan <<EMAIL>> 1740409465 +0800	merge origin/main: Merge made by the 'ort' strategy.
8551355ac021c2ef36e5bbcc5c6c3795c79ee6a8 ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 Sim Zhen Quan <<EMAIL>> 1740409599 +0800	checkout: moving from main to staging/2025-02-20
ebf19f46899fb8aa7dfdb82eac6b4987f4a29de9 93b1c28988adada2f5b9e575d3f4bb33cb1dd557 Sim Zhen Quan <<EMAIL>> 1740409606 +0800	merge origin/main: Merge made by the 'ort' strategy.
93b1c28988adada2f5b9e575d3f4bb33cb1dd557 6228876b4b4a55e5ec6f62d5c21a8386ca96624b Sim Zhen Quan <<EMAIL>> 1740411000 +0800	commit: Fix test cases
6228876b4b4a55e5ec6f62d5c21a8386ca96624b 8551355ac021c2ef36e5bbcc5c6c3795c79ee6a8 Sim Zhen Quan <<EMAIL>> 1740411128 +0800	checkout: moving from staging/2025-02-20 to main
8551355ac021c2ef36e5bbcc5c6c3795c79ee6a8 f666563d28af33777f02028b28e0663dc966a467 Sim Zhen Quan <<EMAIL>> 1740411135 +0800	pull: Fast-forward
f666563d28af33777f02028b28e0663dc966a467 87ebb989d28ca7e96a6230852b9469af08004ab6 Sim Zhen Quan <<EMAIL>> 1740411480 +0800	commit: Test case fix
87ebb989d28ca7e96a6230852b9469af08004ab6 67fbcdbd1b99a3df0ca4a9cec10b268b545b81a3 Sim Zhen Quan <<EMAIL>> 1740412871 +0800	commit: Deployed to PRD
67fbcdbd1b99a3df0ca4a9cec10b268b545b81a3 83cac59ef4a87982ddd4482b0d20932253b61474 Sim Zhen Quan <<EMAIL>> 1740451225 +0800	checkout: moving from main to grading-framework-crud
83cac59ef4a87982ddd4482b0d20932253b61474 16f9a5f1d372e90bc971736a9daea2f6813b288f Sim Zhen Quan <<EMAIL>> 1740451231 +0800	pull: Fast-forward
16f9a5f1d372e90bc971736a9daea2f6813b288f 87822eecd61a1a6b00143110414b17bcc2c406b4 Sim Zhen Quan <<EMAIL>> 1740451232 +0800	merge origin/main: Merge made by the 'ort' strategy.
87822eecd61a1a6b00143110414b17bcc2c406b4 c0a666403e8e0e57eddfa9698dcb9281d07dd1b5 Sim Zhen Quan <<EMAIL>> 1740473922 +0800	checkout: moving from grading-framework-crud to pos-check-charge-status
c0a666403e8e0e57eddfa9698dcb9281d07dd1b5 4fe4486a24b79ca9107e645018bad33e566ec367 Sim Zhen Quan <<EMAIL>> 1740474276 +0800	checkout: moving from pos-check-charge-status to dev
4fe4486a24b79ca9107e645018bad33e566ec367 770a1db086e1ce9b7530e6003a200a665d4c29bb Sim Zhen Quan <<EMAIL>> 1740474327 +0800	commit (merge): Merge branch
770a1db086e1ce9b7530e6003a200a665d4c29bb beb612e218a6554dcdd763d8bcd307197238d614 Sim Zhen Quan <<EMAIL>> 1740474758 +0800	commit: Deployed to DEV
beb612e218a6554dcdd763d8bcd307197238d614 87822eecd61a1a6b00143110414b17bcc2c406b4 Sim Zhen Quan <<EMAIL>> 1740496533 +0800	checkout: moving from dev to grading-framework-crud
87822eecd61a1a6b00143110414b17bcc2c406b4 fd59fdd9eb8d7bef3a6de59623750ec84529249c Sim Zhen Quan <<EMAIL>> 1740505426 +0800	commit: Reviewed
fd59fdd9eb8d7bef3a6de59623750ec84529249c 67fbcdbd1b99a3df0ca4a9cec10b268b545b81a3 Sim Zhen Quan <<EMAIL>> 1740505440 +0800	checkout: moving from grading-framework-crud to main
67fbcdbd1b99a3df0ca4a9cec10b268b545b81a3 fd59fdd9eb8d7bef3a6de59623750ec84529249c Sim Zhen Quan <<EMAIL>> 1740505440 +0800	merge grading-framework-crud: Fast-forward
fd59fdd9eb8d7bef3a6de59623750ec84529249c d64644598c89714a0b4eac292fe002f422dc4b28 Sim Zhen Quan <<EMAIL>> 1740505516 +0800	checkout: moving from main to route_enhancement_handle_paginations
d64644598c89714a0b4eac292fe002f422dc4b28 a26f3f3fcf6e279a0f2815d27cb6f34830705eee Sim Zhen Quan <<EMAIL>> 1740505723 +0800	commit (merge): Merge origin/main
a26f3f3fcf6e279a0f2815d27cb6f34830705eee b97ea91bac1d9591ac51567e98cbd2798d4b7b6d Sim Zhen Quan <<EMAIL>> 1740506402 +0800	commit: Fix test cases
b97ea91bac1d9591ac51567e98cbd2798d4b7b6d beb612e218a6554dcdd763d8bcd307197238d614 Sim Zhen Quan <<EMAIL>> 1740506413 +0800	checkout: moving from route_enhancement_handle_paginations to dev
beb612e218a6554dcdd763d8bcd307197238d614 5881f9732c616cb5c0be4d936ecbe8cda884ac02 Sim Zhen Quan <<EMAIL>> 1740506413 +0800	merge main: Merge made by the 'ort' strategy.
5881f9732c616cb5c0be4d936ecbe8cda884ac02 7f982ee7052b73d223342773b0508ef2f52d03fd Sim Zhen Quan <<EMAIL>> 1740506423 +0800	merge route_enhancement_handle_paginations: Merge made by the 'ort' strategy.
7f982ee7052b73d223342773b0508ef2f52d03fd 90ea0b3a3598f25517ec727e72b9aee8b126813d Sim Zhen Quan <<EMAIL>> 1740561390 +0800	commit: Deployed to dev
90ea0b3a3598f25517ec727e72b9aee8b126813d 7cbfc57cd129022f3a87cd94ccb1a2a9cc8c4055 Sim Zhen Quan <<EMAIL>> 1740561448 +0800	checkout: moving from dev to fix/announcement
7cbfc57cd129022f3a87cd94ccb1a2a9cc8c4055 fdb5e308eb85f6f19539e025c26ac340cc4943f9 Sim Zhen Quan <<EMAIL>> 1740588593 +0800	commit: Reviewed
fdb5e308eb85f6f19539e025c26ac340cc4943f9 90ea0b3a3598f25517ec727e72b9aee8b126813d Sim Zhen Quan <<EMAIL>> 1740588613 +0800	checkout: moving from fix/announcement to dev
90ea0b3a3598f25517ec727e72b9aee8b126813d 8cd00793e62b554f8b16b98a7abc9d24bab0c564 Sim Zhen Quan <<EMAIL>> 1740588613 +0800	merge fix/announcement: Merge made by the 'ort' strategy.
8cd00793e62b554f8b16b98a7abc9d24bab0c564 fdb5e308eb85f6f19539e025c26ac340cc4943f9 Sim Zhen Quan <<EMAIL>> 1740588947 +0800	checkout: moving from dev to fix/announcement
fdb5e308eb85f6f19539e025c26ac340cc4943f9 73eaa2be60f178482e3b7e98e960428113325243 Sim Zhen Quan <<EMAIL>> 1740589153 +0800	commit: Update recipient count and chunk size
73eaa2be60f178482e3b7e98e960428113325243 8cd00793e62b554f8b16b98a7abc9d24bab0c564 Sim Zhen Quan <<EMAIL>> 1740589162 +0800	checkout: moving from fix/announcement to dev
8cd00793e62b554f8b16b98a7abc9d24bab0c564 cee1a22b134040856a3d6329acc174b5fa951852 Sim Zhen Quan <<EMAIL>> 1740589162 +0800	merge fix/announcement: Merge made by the 'ort' strategy.
cee1a22b134040856a3d6329acc174b5fa951852 73eaa2be60f178482e3b7e98e960428113325243 Sim Zhen Quan <<EMAIL>> 1740590065 +0800	checkout: moving from dev to fix/announcement
73eaa2be60f178482e3b7e98e960428113325243 349e7afac59da0d9103b5fe10f827e0f6b9bd277 Sim Zhen Quan <<EMAIL>> 1740590093 +0800	commit: Reduce chunk size to 100
349e7afac59da0d9103b5fe10f827e0f6b9bd277 fd59fdd9eb8d7bef3a6de59623750ec84529249c Sim Zhen Quan <<EMAIL>> 1740590156 +0800	checkout: moving from fix/announcement to main
fd59fdd9eb8d7bef3a6de59623750ec84529249c c09093f5cac1dfd8a9368d5ca8e3397708ee399f Sim Zhen Quan <<EMAIL>> 1740590163 +0800	pull: Fast-forward
c09093f5cac1dfd8a9368d5ca8e3397708ee399f 4e724d4ad5555642e6c25238d8757780cb167c37 Sim Zhen Quan <<EMAIL>> 1740617376 +0800	commit: Deployed to PRD
4e724d4ad5555642e6c25238d8757780cb167c37 57cf8898a17301e2fddc34c48e1c28030d34ccba Sim Zhen Quan <<EMAIL>> 1740617390 +0800	checkout: moving from main to wallet-transaction
57cf8898a17301e2fddc34c48e1c28030d34ccba dc6ce3732d63e815048d78a6de2bfed0df87b68f Sim Zhen Quan <<EMAIL>> 1740618395 +0800	commit (merge): Merge origin/main
dc6ce3732d63e815048d78a6de2bfed0df87b68f 8e1e1de7eddcce452cd4df9ff9a0951c26757f46 Sim Zhen Quan <<EMAIL>> 1740622390 +0800	commit: Review WIP
8e1e1de7eddcce452cd4df9ff9a0951c26757f46 d3ee79f4a450a7492868833b36efcf7ccd55ff97 Sim Zhen Quan <<EMAIL>> 1740622750 +0800	checkout: moving from wallet-transaction to fix/terminal-report-add-name
d3ee79f4a450a7492868833b36efcf7ccd55ff97 914f7d720de90e08f678bbbd14f0139fb57a9a2a Sim Zhen Quan <<EMAIL>> 1740622756 +0800	merge origin/main: Merge made by the 'ort' strategy.
914f7d720de90e08f678bbbd14f0139fb57a9a2a 8e1e1de7eddcce452cd4df9ff9a0951c26757f46 Sim Zhen Quan <<EMAIL>> 1740623162 +0800	checkout: moving from fix/terminal-report-add-name to wallet-transaction
8e1e1de7eddcce452cd4df9ff9a0951c26757f46 214d0f51dd956125142c359e5ba5cd6bc11d2511 Sim Zhen Quan <<EMAIL>> 1740624425 +0800	checkout: moving from wallet-transaction to disable-auto-transfer-remaining-balance-to-primary-guardian
214d0f51dd956125142c359e5ba5cd6bc11d2511 7ef7839e82d60168f5f664c879326a3d5de95703 Sim Zhen Quan <<EMAIL>> 1740624434 +0800	merge origin/main: Merge made by the 'ort' strategy.
7ef7839e82d60168f5f664c879326a3d5de95703 4e724d4ad5555642e6c25238d8757780cb167c37 Sim Zhen Quan <<EMAIL>> 1740624499 +0800	checkout: moving from disable-auto-transfer-remaining-balance-to-primary-guardian to main
4e724d4ad5555642e6c25238d8757780cb167c37 4e724d4ad5555642e6c25238d8757780cb167c37 Sim Zhen Quan <<EMAIL>> 1740624510 +0800	checkout: moving from main to staging/2025-02-27
4e724d4ad5555642e6c25238d8757780cb167c37 c52c2e3f84707c4abc8ed7c1911da6628f2b3eb9 Sim Zhen Quan <<EMAIL>> 1740624652 +0800	checkout: moving from staging/2025-02-27 to regenerate-employee-number
c52c2e3f84707c4abc8ed7c1911da6628f2b3eb9 67e9dd9c45b441576b6e931e2d60591e5d41eef7 Sim Zhen Quan <<EMAIL>> 1740624658 +0800	merge origin/main: Merge made by the 'ort' strategy.
67e9dd9c45b441576b6e931e2d60591e5d41eef7 cee1a22b134040856a3d6329acc174b5fa951852 Sim Zhen Quan <<EMAIL>> 1740626504 +0800	checkout: moving from regenerate-employee-number to dev
cee1a22b134040856a3d6329acc174b5fa951852 30b4236e8f055528454fb3e1c09564e3bc32c3b7 Sim Zhen Quan <<EMAIL>> 1740626677 +0800	commit (merge): Merge refund functionality
30b4236e8f055528454fb3e1c09564e3bc32c3b7 e17cebd825e9a69792767617dc4bb4d9dd432f5b Sim Zhen Quan <<EMAIL>> 1740626846 +0800	commit: Deployed to DEV
e17cebd825e9a69792767617dc4bb4d9dd432f5b 914f7d720de90e08f678bbbd14f0139fb57a9a2a Sim Zhen Quan <<EMAIL>> 1740626867 +0800	checkout: moving from dev to fix/terminal-report-add-name
914f7d720de90e08f678bbbd14f0139fb57a9a2a e17cebd825e9a69792767617dc4bb4d9dd432f5b Sim Zhen Quan <<EMAIL>> 1740627311 +0800	checkout: moving from fix/terminal-report-add-name to dev
e17cebd825e9a69792767617dc4bb4d9dd432f5b 914f7d720de90e08f678bbbd14f0139fb57a9a2a Sim Zhen Quan <<EMAIL>> 1740627457 +0800	checkout: moving from dev to fix/terminal-report-add-name
914f7d720de90e08f678bbbd14f0139fb57a9a2a bd641641c6b871ec79686f8ac4a8080365bbfef6 Sim Zhen Quan <<EMAIL>> 1740627460 +0800	merge origin/main: Merge made by the 'ort' strategy.
bd641641c6b871ec79686f8ac4a8080365bbfef6 e17cebd825e9a69792767617dc4bb4d9dd432f5b Sim Zhen Quan <<EMAIL>> 1740627531 +0800	checkout: moving from fix/terminal-report-add-name to dev
e17cebd825e9a69792767617dc4bb4d9dd432f5b bd641641c6b871ec79686f8ac4a8080365bbfef6 Sim Zhen Quan <<EMAIL>> 1740627865 +0800	checkout: moving from dev to fix/terminal-report-add-name
bd641641c6b871ec79686f8ac4a8080365bbfef6 bd641641c6b871ec79686f8ac4a8080365bbfef6 Sim Zhen Quan <<EMAIL>> 1740628518 +0800	reset: moving to HEAD
bd641641c6b871ec79686f8ac4a8080365bbfef6 e17cebd825e9a69792767617dc4bb4d9dd432f5b Sim Zhen Quan <<EMAIL>> 1740628520 +0800	checkout: moving from fix/terminal-report-add-name to dev
e17cebd825e9a69792767617dc4bb4d9dd432f5b 9c5a87ad549b7b63b8535a37a79b091f7acee170 Sim Zhen Quan <<EMAIL>> 1740629573 +0800	merge origin/main: Merge made by the 'ort' strategy.
9c5a87ad549b7b63b8535a37a79b091f7acee170 dde3b4bdd82013aab821a206f74b1d5896f36e86 Sim Zhen Quan <<EMAIL>> 1740629644 +0800	merge origin/staging/2025-02-27: Merge made by the 'ort' strategy.
dde3b4bdd82013aab821a206f74b1d5896f36e86 4e724d4ad5555642e6c25238d8757780cb167c37 Sim Zhen Quan <<EMAIL>> 1740632029 +0800	checkout: moving from dev to main
4e724d4ad5555642e6c25238d8757780cb167c37 5321655a46edd4a6d0f0acbaac8987bb1d19724c Sim Zhen Quan <<EMAIL>> 1740632035 +0800	pull: Fast-forward
5321655a46edd4a6d0f0acbaac8987bb1d19724c d3dded91b9db442d6810e1a35a7d43538f1b6849 Sim Zhen Quan <<EMAIL>> 1740632069 +0800	commit: Enhancements
d3dded91b9db442d6810e1a35a7d43538f1b6849 bd641641c6b871ec79686f8ac4a8080365bbfef6 Sim Zhen Quan <<EMAIL>> 1740632157 +0800	checkout: moving from main to fix/terminal-report-add-name
bd641641c6b871ec79686f8ac4a8080365bbfef6 ef2578b53a4c7c8543ac9d5bc71a16a8961edf7d Sim Zhen Quan <<EMAIL>> 1740632160 +0800	merge origin/main: Merge made by the 'ort' strategy.
ef2578b53a4c7c8543ac9d5bc71a16a8961edf7d 21fcc6b99143dbc3f884f46ff97e5dedc2daa0e0 Sim Zhen Quan <<EMAIL>> 1740632392 +0800	checkout: moving from fix/terminal-report-add-name to wallet-refund
21fcc6b99143dbc3f884f46ff97e5dedc2daa0e0 677d920b639dba31a19456e8a6b6b2411837ec30 Sim Zhen Quan <<EMAIL>> 1740632398 +0800	merge origin/main: Merge made by the 'ort' strategy.
677d920b639dba31a19456e8a6b6b2411837ec30 aa1f3fa32f5c21f0f3a81ca58ab8359e7ae6964e Sim Zhen Quan <<EMAIL>> 1740652828 +0800	commit: Reviewed
aa1f3fa32f5c21f0f3a81ca58ab8359e7ae6964e c2c54ab4ecd151bdde16811885e072bb46c10ce4 Sim Zhen Quan <<EMAIL>> 1740653344 +0800	merge origin/staging/2025-02-27: Merge made by the 'ort' strategy.
c2c54ab4ecd151bdde16811885e072bb46c10ce4 27df32ffea9f85551a413308a0684b6592b65b61 Sim Zhen Quan <<EMAIL>> 1740653364 +0800	commit: Change remark to required
27df32ffea9f85551a413308a0684b6592b65b61 7a0ab4f15ff0efe014206c344d861b7f9c7ec695 Sim Zhen Quan <<EMAIL>> 1740653598 +0800	checkout: moving from wallet-refund to push-notification-order-confirmed
7a0ab4f15ff0efe014206c344d861b7f9c7ec695 01716742f62c6ec56f7ef15029a589e08dfb2583 Sim Zhen Quan <<EMAIL>> 1740653612 +0800	pull: Fast-forward
01716742f62c6ec56f7ef15029a589e08dfb2583 119334060f17a403ae2fe5538a535b246a2a689a Sim Zhen Quan <<EMAIL>> 1740653619 +0800	merge origin/main: Merge made by the 'ort' strategy.
119334060f17a403ae2fe5538a535b246a2a689a 119334060f17a403ae2fe5538a535b246a2a689a Sim Zhen Quan <<EMAIL>> 1740653669 +0800	checkout: moving from push-notification-order-confirmed to push-notification-order-confirmed
119334060f17a403ae2fe5538a535b246a2a689a 1b6cf2817d3c1558173a7b315674c640137bdb29 Sim Zhen Quan <<EMAIL>> 1740654175 +0800	commit: Reviewed
1b6cf2817d3c1558173a7b315674c640137bdb29 c4246a4ccd519ac7890f27ea927cfd0c4f4d2683 Sim Zhen Quan <<EMAIL>> 1740654189 +0800	merge origin/staging/2025-02-27: Merge made by the 'ort' strategy.
c4246a4ccd519ac7890f27ea927cfd0c4f4d2683 8e1e1de7eddcce452cd4df9ff9a0951c26757f46 Sim Zhen Quan <<EMAIL>> 1740654516 +0800	checkout: moving from push-notification-order-confirmed to wallet-transaction
8e1e1de7eddcce452cd4df9ff9a0951c26757f46 57bf0367e0e0d443c11eadedd7534f1c29d27295 Sim Zhen Quan <<EMAIL>> 1740654522 +0800	pull: Fast-forward
57bf0367e0e0d443c11eadedd7534f1c29d27295 8cf54ff611f40dc2f2e2deccd7b38825f76d879a Sim Zhen Quan <<EMAIL>> 1740654920 +0800	commit (merge): Merge staging/2025-02-27
8cf54ff611f40dc2f2e2deccd7b38825f76d879a d8e98105a4c6707c3984f3166ea72e1b6e205741 Sim Zhen Quan <<EMAIL>> 1740708664 +0800	merge origin/main: Merge made by the 'ort' strategy.
d8e98105a4c6707c3984f3166ea72e1b6e205741 510b57b8ffcaad4f96ba622fc08e8097753fd033 Sim Zhen Quan <<EMAIL>> 1740729700 +0800	commit: Reviewed
510b57b8ffcaad4f96ba622fc08e8097753fd033 dde3b4bdd82013aab821a206f74b1d5896f36e86 Sim Zhen Quan <<EMAIL>> 1740729940 +0800	checkout: moving from wallet-transaction to dev
dde3b4bdd82013aab821a206f74b1d5896f36e86 4e724d4ad5555642e6c25238d8757780cb167c37 Sim Zhen Quan <<EMAIL>> 1740730043 +0800	checkout: moving from dev to staging/2025-02-27
4e724d4ad5555642e6c25238d8757780cb167c37 62766f5f6eb053c578ec45f38a8c006f53ef66e5 Sim Zhen Quan <<EMAIL>> 1740730050 +0800	pull: Fast-forward
62766f5f6eb053c578ec45f38a8c006f53ef66e5 dde3b4bdd82013aab821a206f74b1d5896f36e86 Sim Zhen Quan <<EMAIL>> 1740730265 +0800	checkout: moving from staging/2025-02-27 to dev
dde3b4bdd82013aab821a206f74b1d5896f36e86 4b7a1d8ef417930569131e3b719cac81594f4a3c Sim Zhen Quan <<EMAIL>> 1740730275 +0800	merge origin/staging/2025-02-27: Merge made by the 'ort' strategy.
4b7a1d8ef417930569131e3b719cac81594f4a3c 14eb9cecb75e7e9e6662cc19c768b97fe015e263 Sim Zhen Quan <<EMAIL>> 1740732375 +0800	commit: Deployed to DEV
14eb9cecb75e7e9e6662cc19c768b97fe015e263 d3dded91b9db442d6810e1a35a7d43538f1b6849 Sim Zhen Quan <<EMAIL>> 1740732383 +0800	checkout: moving from dev to main
d3dded91b9db442d6810e1a35a7d43538f1b6849 b1e9a0b4ab315972bfaae2437fd675d0513c68d2 Sim Zhen Quan <<EMAIL>> 1740732488 +0800	pull: Fast-forward
b1e9a0b4ab315972bfaae2437fd675d0513c68d2 e6425834bf6b43d52c921374ac7832477c5c4f05 Sim Zhen Quan <<EMAIL>> 1740732616 +0800	checkout: moving from main to grouping-grading-framework-errors
e6425834bf6b43d52c921374ac7832477c5c4f05 d7ae7a57bc7c723cb57d06eb50a3feb2f1bcba37 Sim Zhen Quan <<EMAIL>> 1740733231 +0800	commit: Reviewed
d7ae7a57bc7c723cb57d06eb50a3feb2f1bcba37 14eb9cecb75e7e9e6662cc19c768b97fe015e263 Sim Zhen Quan <<EMAIL>> 1740733429 +0800	checkout: moving from grouping-grading-framework-errors to dev
14eb9cecb75e7e9e6662cc19c768b97fe015e263 5783d41bc66349c8639a46e3dd36ae9c9ac0578c Sim Zhen Quan <<EMAIL>> 1740733445 +0800	merge origin/main: Merge made by the 'ort' strategy.
5783d41bc66349c8639a46e3dd36ae9c9ac0578c f62e1fa9e234a11c0daa3285838b6c332563efe5 Sim Zhen Quan <<EMAIL>> 1740734224 +0800	commit: Deployed to DEV
f62e1fa9e234a11c0daa3285838b6c332563efe5 c31176359f98029c16183fb191006740c6c77e52 Sim Zhen Quan <<EMAIL>> 1740736445 +0800	checkout: moving from dev to grading-framework-formula
c31176359f98029c16183fb191006740c6c77e52 2f38435246f3c5e66188cada46c41d220b1878be Sim Zhen Quan <<EMAIL>> 1740736576 +0800	commit (merge): Merge origin/main
2f38435246f3c5e66188cada46c41d220b1878be f62e1fa9e234a11c0daa3285838b6c332563efe5 Sim Zhen Quan <<EMAIL>> 1740736661 +0800	checkout: moving from grading-framework-formula to dev
f62e1fa9e234a11c0daa3285838b6c332563efe5 64f0877d019878d0a81b116f027f172cb43d6c75 Sim Zhen Quan <<EMAIL>> 1740736669 +0800	merge origin/grading-framework-formula: Merge made by the 'ort' strategy.
64f0877d019878d0a81b116f027f172cb43d6c75 ce325864d18f37ec0216ebd424460e103cceb63a Sim Zhen Quan <<EMAIL>> 1740753661 +0800	commit: Deployed to DEV
ce325864d18f37ec0216ebd424460e103cceb63a 2f38435246f3c5e66188cada46c41d220b1878be Sim Zhen Quan <<EMAIL>> 1740753672 +0800	checkout: moving from dev to grading-framework-formula
2f38435246f3c5e66188cada46c41d220b1878be c31176359f98029c16183fb191006740c6c77e52 Sim Zhen Quan <<EMAIL>> 1740753759 +0800	checkout: moving from grading-framework-formula to c311763
c31176359f98029c16183fb191006740c6c77e52 c31176359f98029c16183fb191006740c6c77e52 Sim Zhen Quan <<EMAIL>> 1740753774 +0800	checkout: moving from c31176359f98029c16183fb191006740c6c77e52 to grading-framework-formula-fix
c31176359f98029c16183fb191006740c6c77e52 b1e9a0b4ab315972bfaae2437fd675d0513c68d2 Sim Zhen Quan <<EMAIL>> 1740756903 +0800	checkout: moving from grading-framework-formula-fix to main
b1e9a0b4ab315972bfaae2437fd675d0513c68d2 8cef72c02e0852f133fe7b2d492848a1062d1ddd Sim Zhen Quan <<EMAIL>> 1740756985 +0800	pull: Fast-forward
8cef72c02e0852f133fe7b2d492848a1062d1ddd 2f38435246f3c5e66188cada46c41d220b1878be Sim Zhen Quan <<EMAIL>> 1740757450 +0800	checkout: moving from main to grading-framework-formula
2f38435246f3c5e66188cada46c41d220b1878be bccbb4c4c3b14675580431dbc7eaa2c140548803 Sim Zhen Quan <<EMAIL>> 1740932700 +0800	commit: Review WIP
bccbb4c4c3b14675580431dbc7eaa2c140548803 8cef72c02e0852f133fe7b2d492848a1062d1ddd Sim Zhen Quan <<EMAIL>> 1740961417 +0800	checkout: moving from grading-framework-formula to main
8cef72c02e0852f133fe7b2d492848a1062d1ddd 1c2976450b04d250575cc6a52f932084f98ebc64 Sim Zhen Quan <<EMAIL>> 1740962926 +0800	commit: Hotfix announcement to send out encoded html instead
1c2976450b04d250575cc6a52f932084f98ebc64 ce325864d18f37ec0216ebd424460e103cceb63a Sim Zhen Quan <<EMAIL>> 1740969310 +0800	checkout: moving from main to dev
ce325864d18f37ec0216ebd424460e103cceb63a 1c2976450b04d250575cc6a52f932084f98ebc64 Sim Zhen Quan <<EMAIL>> 1740992223 +0800	checkout: moving from dev to main
1c2976450b04d250575cc6a52f932084f98ebc64 48ff7b9927073ac26148222c25ec4b592c15e409 Sim Zhen Quan <<EMAIL>> 1740992243 +0800	checkout: moving from main to attendance-module
48ff7b9927073ac26148222c25ec4b592c15e409 378f7594e196d5699e964b5a428456c0f86562be Sim Zhen Quan <<EMAIL>> 1740992248 +0800	pull: Fast-forward
378f7594e196d5699e964b5a428456c0f86562be 99b91f461358656f913760bd9c95793e21c01e8e Sim Zhen Quan <<EMAIL>> 1740992249 +0800	merge origin/main: Merge made by the 'ort' strategy.
99b91f461358656f913760bd9c95793e21c01e8e 62766f5f6eb053c578ec45f38a8c006f53ef66e5 Sim Zhen Quan <<EMAIL>> 1740992269 +0800	checkout: moving from attendance-module to staging/2025-02-27
62766f5f6eb053c578ec45f38a8c006f53ef66e5 99b91f461358656f913760bd9c95793e21c01e8e Sim Zhen Quan <<EMAIL>> 1740992286 +0800	checkout: moving from staging/2025-02-27 to attendance-module
99b91f461358656f913760bd9c95793e21c01e8e 1c2976450b04d250575cc6a52f932084f98ebc64 Sim Zhen Quan <<EMAIL>> 1740992371 +0800	checkout: moving from attendance-module to main
1c2976450b04d250575cc6a52f932084f98ebc64 99b91f461358656f913760bd9c95793e21c01e8e Sim Zhen Quan <<EMAIL>> 1740992533 +0800	checkout: moving from main to attendance-module
99b91f461358656f913760bd9c95793e21c01e8e 62766f5f6eb053c578ec45f38a8c006f53ef66e5 Sim Zhen Quan <<EMAIL>> 1740993263 +0800	checkout: moving from attendance-module to staging/2025-02-27
62766f5f6eb053c578ec45f38a8c006f53ef66e5 ce325864d18f37ec0216ebd424460e103cceb63a Sim Zhen Quan <<EMAIL>> 1740996526 +0800	checkout: moving from staging/2025-02-27 to dev
ce325864d18f37ec0216ebd424460e103cceb63a 4c57745023ffc904334ae46a32f0d0a86d439bdb Sim Zhen Quan <<EMAIL>> 1740996534 +0800	merge origin/main: Merge made by the 'ort' strategy.
4c57745023ffc904334ae46a32f0d0a86d439bdb 504089564b8295b74c70fb7e38226c07690af431 Sim Zhen Quan <<EMAIL>> 1740997937 +0800	commit: Deployed to DEV
504089564b8295b74c70fb7e38226c07690af431 f8dd7bbbb468c54dcf76aedd4189759e9b4581c3 Sim Zhen Quan <<EMAIL>> 1741056185 +0800	checkout: moving from dev to wallet-adjustment
f8dd7bbbb468c54dcf76aedd4189759e9b4581c3 bff9e339f23bbf6f5d01a2c1b9e2a8285a3e4511 Sim Zhen Quan <<EMAIL>> 1741058087 +0800	commit: Reviewed
bff9e339f23bbf6f5d01a2c1b9e2a8285a3e4511 504089564b8295b74c70fb7e38226c07690af431 Sim Zhen Quan <<EMAIL>> 1741058198 +0800	checkout: moving from wallet-adjustment to dev
504089564b8295b74c70fb7e38226c07690af431 73422261fdacb93c643c24342bdf1aedd7862d81 Sim Zhen Quan <<EMAIL>> 1741058238 +0800	merge origin/staging/2025-02-27: Merge made by the 'ort' strategy.
73422261fdacb93c643c24342bdf1aedd7862d81 **************************************** Sim Zhen Quan <<EMAIL>> 1741059323 +0800	commit: Deployed to DEV
**************************************** 62766f5f6eb053c578ec45f38a8c006f53ef66e5 Sim Zhen Quan <<EMAIL>> 1741059351 +0800	checkout: moving from dev to staging/2025-02-27
62766f5f6eb053c578ec45f38a8c006f53ef66e5 58264860762ce426f59cf889a0db8537c25f4a2f Sim Zhen Quan <<EMAIL>> 1741059360 +0800	pull: Fast-forward
58264860762ce426f59cf889a0db8537c25f4a2f 58264860762ce426f59cf889a0db8537c25f4a2f Sim Zhen Quan <<EMAIL>> 1741059374 +0800	checkout: moving from staging/2025-02-27 to add-can-refund-and-created-by
58264860762ce426f59cf889a0db8537c25f4a2f 51f3c09a3aee4a9fb86f751deac83dfc5eeec580 Sim Zhen Quan <<EMAIL>> 1741061626 +0800	commit: Optimize queries
51f3c09a3aee4a9fb86f751deac83dfc5eeec580 51f3c09a3aee4a9fb86f751deac83dfc5eeec580 Sim Zhen Quan <<EMAIL>> 1741062205 +0800	reset: moving to HEAD
51f3c09a3aee4a9fb86f751deac83dfc5eeec580 **************************************** Sim Zhen Quan <<EMAIL>> 1741062216 +0800	checkout: moving from add-can-refund-and-created-by to dev
**************************************** **************************************** Sim Zhen Quan <<EMAIL>> 1741062223 +0800	checkout: moving from dev to dev
**************************************** 1ebaa0d4da0105baab26bea15711a7ec93c12e6d Sim Zhen Quan <<EMAIL>> 1741062223 +0800	merge add-can-refund-and-created-by: Merge made by the 'ort' strategy.
1ebaa0d4da0105baab26bea15711a7ec93c12e6d 51f3c09a3aee4a9fb86f751deac83dfc5eeec580 Sim Zhen Quan <<EMAIL>> 1741063403 +0800	checkout: moving from dev to add-can-refund-and-created-by
51f3c09a3aee4a9fb86f751deac83dfc5eeec580 54d9db0198531a2fcfec97f51c8c756abe88586d Sim Zhen Quan <<EMAIL>> 1741063433 +0800	commit: Remove dd
54d9db0198531a2fcfec97f51c8c756abe88586d 0d66f6a56aaad9149494dacd372db49399c16d51 Sim Zhen Quan <<EMAIL>> 1741064129 +0800	checkout: moving from add-can-refund-and-created-by to feature/conduct-record-mark-entry
0d66f6a56aaad9149494dacd372db49399c16d51 96979ffee179c51f709ff4b7673888191dc2f83c Sim Zhen Quan <<EMAIL>> 1741064710 +0800	commit (merge): Merge main
96979ffee179c51f709ff4b7673888191dc2f83c a3ee1bb3795df10c878f8c808bb9daad4a0b3768 Sim Zhen Quan <<EMAIL>> 1741073955 +0800	commit: Reviewed
a3ee1bb3795df10c878f8c808bb9daad4a0b3768 dae2c700afcea4dc431a6732d3f06ffa3fc23b69 Sim Zhen Quan <<EMAIL>> 1741074515 +0800	checkout: moving from feature/conduct-record-mark-entry to fix/unpaid-item-multiple-discount
dae2c700afcea4dc431a6732d3f06ffa3fc23b69 6afb297210cb98b308bebf8c5d2317ae5b0b3d1f Sim Zhen Quan <<EMAIL>> 1741074520 +0800	pull: Fast-forward
6afb297210cb98b308bebf8c5d2317ae5b0b3d1f 400e3b0671b605fc8a26c3a8ee61bda002c58b10 Sim Zhen Quan <<EMAIL>> 1741074520 +0800	merge origin/main: Merge made by the 'ort' strategy.
400e3b0671b605fc8a26c3a8ee61bda002c58b10 b352d438bf26609a197134648daaf394fe369a70 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Refactor codes
b352d438bf26609a197134648daaf394fe369a70 5767a8614b32ce51deb284ea753af85def7638fe Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
5767a8614b32ce51deb284ea753af85def7638fe cb81f8841a54076183771271589e5407eea561d9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/unpaid-item-multiple-discount to saving-account-void
cb81f8841a54076183771271589e5407eea561d9 1c2976450b04d250575cc6a52f932084f98ebc64 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from saving-account-void to main
1c2976450b04d250575cc6a52f932084f98ebc64 ed0e49d4d6081e5ae9e48e0c0e01773345914e84 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
ed0e49d4d6081e5ae9e48e0c0e01773345914e84 86888a9a93b1978aec5f3c89e7763fec13285697 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/staging/2025-02-27: Merge made by the 'ort' strategy.
86888a9a93b1978aec5f3c89e7763fec13285697 1ed4e836f8f5d261a73d96b334c4ddb0efdc6c59 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to PRD
1ed4e836f8f5d261a73d96b334c4ddb0efdc6c59 c2d60e97a6a9fd1ff7d011a1b1302b058d0331eb Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to added-contractor-data-to-attendance-period
c2d60e97a6a9fd1ff7d011a1b1302b058d0331eb 2f2815ed20129d5740c98a31e669c03fcaa4b04d Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
2f2815ed20129d5740c98a31e669c03fcaa4b04d 7da4731eb86bdfaf6a519cf668d35e06ad3c91a4 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
7da4731eb86bdfaf6a519cf668d35e06ad3c91a4 b94206af8b57049ad0816587b3db6241999df11f Sim Zhen Quan <<EMAIL>> 1741166644 +0800	pull: Fast-forward
b94206af8b57049ad0816587b3db6241999df11f de85493d8d9e487fba19f71231ce241e7f6e2aed Sim Zhen Quan <<EMAIL>> 1741187746 +0800	pull: Fast-forward
de85493d8d9e487fba19f71231ce241e7f6e2aed b733afe8334a2c2e9e2d6f61cfe43fab930246b7 Sim Zhen Quan <<EMAIL>> 1741193300 +0800	commit: Reviewed
b733afe8334a2c2e9e2d6f61cfe43fab930246b7 1ebaa0d4da0105baab26bea15711a7ec93c12e6d Sim Zhen Quan <<EMAIL>> 1741193408 +0800	checkout: moving from added-contractor-data-to-attendance-period to dev
1ebaa0d4da0105baab26bea15711a7ec93c12e6d 9edaa335303325de034ce4efef47accd39a20ba6 Sim Zhen Quan <<EMAIL>> 1741193419 +0800	merge origin/main: Merge made by the 'ort' strategy.
9edaa335303325de034ce4efef47accd39a20ba6 4e67aed0808dae88e5c2705424e0f98902a544c7 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
4e67aed0808dae88e5c2705424e0f98902a544c7 4e67aed0808dae88e5c2705424e0f98902a544c7 Sim Zhen Quan <<EMAIL>> ********** +0800	reset: moving to HEAD
4e67aed0808dae88e5c2705424e0f98902a544c7 f1191c1e61dacace3e3932fea05f34a9fd81b8e6 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to fix/accounting-flow-changes
f1191c1e61dacace3e3932fea05f34a9fd81b8e6 37dcac71338ea268d2e9d35328c3227c1c27e679 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
37dcac71338ea268d2e9d35328c3227c1c27e679 4e67aed0808dae88e5c2705424e0f98902a544c7 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/accounting-flow-changes to dev
4e67aed0808dae88e5c2705424e0f98902a544c7 7c70c6233066a134b2960a5e738ab85846986b6d Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Fix dev unused imports
7c70c6233066a134b2960a5e738ab85846986b6d 37dcac71338ea268d2e9d35328c3227c1c27e679 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to fix/accounting-flow-changes
37dcac71338ea268d2e9d35328c3227c1c27e679 b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d 1ed4e836f8f5d261a73d96b334c4ddb0efdc6c59 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/accounting-flow-changes to main
1ed4e836f8f5d261a73d96b334c4ddb0efdc6c59 b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d Sim Zhen Quan <<EMAIL>> ********** +0800	merge fix/accounting-flow-changes: Fast-forward
b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d 7c70c6233066a134b2960a5e738ab85846986b6d Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to dev
7c70c6233066a134b2960a5e738ab85846986b6d a2e497807dddd4a81fd4cf7f3424d7ca79ccb438 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
a2e497807dddd4a81fd4cf7f3424d7ca79ccb438 bbe114f580e39209f27c9a3a1fbae82519ecb760 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
bbe114f580e39209f27c9a3a1fbae82519ecb760 ed6232ba55f25c2b8c370806e5e90d051bc2bdf1 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to attendance-enhancements
ed6232ba55f25c2b8c370806e5e90d051bc2bdf1 a33158ece3dc008b7b1ca814ed37dc81ddfa98ae Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
a33158ece3dc008b7b1ca814ed37dc81ddfa98ae bbe114f580e39209f27c9a3a1fbae82519ecb760 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from attendance-enhancements to dev
bbe114f580e39209f27c9a3a1fbae82519ecb760 a33158ece3dc008b7b1ca814ed37dc81ddfa98ae Sim Zhen Quan <<EMAIL>> 1741318541 +0800	checkout: moving from dev to attendance-enhancements
a33158ece3dc008b7b1ca814ed37dc81ddfa98ae bbe114f580e39209f27c9a3a1fbae82519ecb760 Sim Zhen Quan <<EMAIL>> 1741318551 +0800	checkout: moving from attendance-enhancements to dev
bbe114f580e39209f27c9a3a1fbae82519ecb760 277d75d7db9505361c235344987b197faaeac418 Sim Zhen Quan <<EMAIL>> 1741318554 +0800	merge attendance-enhancements: Merge made by the 'ort' strategy.
277d75d7db9505361c235344987b197faaeac418 6a8a8e9b99ad1b0b74400ded469c4b01f643b721 Sim Zhen Quan <<EMAIL>> 1741326546 +0800	commit: Deployed to DEV
6a8a8e9b99ad1b0b74400ded469c4b01f643b721 a4a15c7c5bd32b6306e9987616733a09731085d2 Sim Zhen Quan <<EMAIL>> 1741326557 +0800	checkout: moving from dev to fix/hostel-bed-assignment-import
a4a15c7c5bd32b6306e9987616733a09731085d2 edf14bf8d1ed23721e6f941baaf1c99eafe8a69b Sim Zhen Quan <<EMAIL>> 1741326870 +0800	commit (merge): Merge origin/main
edf14bf8d1ed23721e6f941baaf1c99eafe8a69b 6a66c4958eff979e8fb91600ec5132ce258a1ce2 Sim Zhen Quan <<EMAIL>> 1741328031 +0800	commit: Reviewed
6a66c4958eff979e8fb91600ec5132ce258a1ce2 594555396b41e27f7ac00079e673f51b6a49a3a2 Sim Zhen Quan <<EMAIL>> 1741328310 +0800	checkout: moving from fix/hostel-bed-assignment-import to fix/add-discount-student-outstanding-balance-report
594555396b41e27f7ac00079e673f51b6a49a3a2 b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d Sim Zhen Quan <<EMAIL>> 1741329994 +0800	checkout: moving from fix/add-discount-student-outstanding-balance-report to main
b5383bb9d5fc005d4eab27e7efaf0b23c2dee14d df6c19bcb1f63b55bed8d1d1e8c2572641f32348 Sim Zhen Quan <<EMAIL>> 1741329999 +0800	pull: Fast-forward
df6c19bcb1f63b55bed8d1d1e8c2572641f32348 a29963940a2b7b953e18c499abdae489db19fb50 Sim Zhen Quan <<EMAIL>> 1741330181 +0800	commit: Added todo
a29963940a2b7b953e18c499abdae489db19fb50 594555396b41e27f7ac00079e673f51b6a49a3a2 Sim Zhen Quan <<EMAIL>> 1741330197 +0800	checkout: moving from main to fix/add-discount-student-outstanding-balance-report
594555396b41e27f7ac00079e673f51b6a49a3a2 3b4b3c9fa4ebc8313079b648b980ea400eb4d4ee Sim Zhen Quan <<EMAIL>> 1741330203 +0800	merge origin/main: Merge made by the 'ort' strategy.
3b4b3c9fa4ebc8313079b648b980ea400eb4d4ee c150c90a0723ea037eac7f6c85836ca770b1b7ef Sim Zhen Quan <<EMAIL>> 1741330279 +0800	checkout: moving from fix/add-discount-student-outstanding-balance-report to student-outstanding-balance-report-by-classes
c150c90a0723ea037eac7f6c85836ca770b1b7ef 784fac2ed31a0998f8d1e44564e3a670a8f05877 Sim Zhen Quan <<EMAIL>> 1741330284 +0800	merge origin/main: Merge made by the 'ort' strategy.
784fac2ed31a0998f8d1e44564e3a670a8f05877 3b4b3c9fa4ebc8313079b648b980ea400eb4d4ee Sim Zhen Quan <<EMAIL>> 1741330351 +0800	checkout: moving from student-outstanding-balance-report-by-classes to fix/add-discount-student-outstanding-balance-report
3b4b3c9fa4ebc8313079b648b980ea400eb4d4ee 9a37c512594cc49497a3c99302277651aaa7b76e Sim Zhen Quan <<EMAIL>> 1741330878 +0800	pull: Fast-forward
9a37c512594cc49497a3c99302277651aaa7b76e 9a37c512594cc49497a3c99302277651aaa7b76e Sim Zhen Quan <<EMAIL>> 1741335163 +0800	reset: moving to HEAD
9a37c512594cc49497a3c99302277651aaa7b76e 9a37c512594cc49497a3c99302277651aaa7b76e Sim Zhen Quan <<EMAIL>> 1741335550 +0800	reset: moving to HEAD
9a37c512594cc49497a3c99302277651aaa7b76e 9a37c512594cc49497a3c99302277651aaa7b76e Sim Zhen Quan <<EMAIL>> 1741337592 +0800	reset: moving to HEAD
9a37c512594cc49497a3c99302277651aaa7b76e 9a37c512594cc49497a3c99302277651aaa7b76e Sim Zhen Quan <<EMAIL>> 1741339727 +0800	reset: moving to HEAD
9a37c512594cc49497a3c99302277651aaa7b76e 9a37c512594cc49497a3c99302277651aaa7b76e Sim Zhen Quan <<EMAIL>> 1741339741 +0800	reset: moving to HEAD
9a37c512594cc49497a3c99302277651aaa7b76e 6068c87f00f6e760462c9411101347ae5d5180de Sim Zhen Quan <<EMAIL>> 1741340487 +0800	commit: Reviewed
6068c87f00f6e760462c9411101347ae5d5180de 6a8a8e9b99ad1b0b74400ded469c4b01f643b721 Sim Zhen Quan <<EMAIL>> 1741340621 +0800	checkout: moving from fix/add-discount-student-outstanding-balance-report to dev
6a8a8e9b99ad1b0b74400ded469c4b01f643b721 bac42829afb2e5f2b3e88b6d02a0ac7a5e46e351 Sim Zhen Quan <<EMAIL>> 1741340736 +0800	commit (merge): Merge origin/main
bac42829afb2e5f2b3e88b6d02a0ac7a5e46e351 453b660d29b2585094d43e3714fa25b45bca7bf0 Sim Zhen Quan <<EMAIL>> 1741341145 +0800	commit: Deploy to DEV
453b660d29b2585094d43e3714fa25b45bca7bf0 ce6b2265136c41f6a58fe230ee1bf2a288e0102e Sim Zhen Quan <<EMAIL>> 1741341450 +0800	checkout: moving from dev to student-search-enhancement
ce6b2265136c41f6a58fe230ee1bf2a288e0102e ce6b2265136c41f6a58fe230ee1bf2a288e0102e Sim Zhen Quan <<EMAIL>> 1741341502 +0800	checkout: moving from student-search-enhancement to student-search-enhancement
ce6b2265136c41f6a58fe230ee1bf2a288e0102e 7f24778d8867f33b6bb6c1247c0131b3bec09f87 Sim Zhen Quan <<EMAIL>> 1741341507 +0800	merge origin/main: Merge made by the 'ort' strategy.
7f24778d8867f33b6bb6c1247c0131b3bec09f87 0df7805dfbd88e12c523fab408bd7cae253ef9f1 Sim Zhen Quan <<EMAIL>> 1741342893 +0800	commit: Reviewed
0df7805dfbd88e12c523fab408bd7cae253ef9f1 88534e390fe3ebc75e3ca9191a8f192c732a677e Sim Zhen Quan <<EMAIL>> 1741343195 +0800	commit: Reviewed
88534e390fe3ebc75e3ca9191a8f192c732a677e a29963940a2b7b953e18c499abdae489db19fb50 Sim Zhen Quan <<EMAIL>> 1741343239 +0800	checkout: moving from student-search-enhancement to main
a29963940a2b7b953e18c499abdae489db19fb50 2112dfba27ef58426f7863b3a7fa0805362b4fcf Sim Zhen Quan <<EMAIL>> 1741343244 +0800	pull: Fast-forward
2112dfba27ef58426f7863b3a7fa0805362b4fcf 2112dfba27ef58426f7863b3a7fa0805362b4fcf Sim Zhen Quan <<EMAIL>> 1741343250 +0800	checkout: moving from main to staging/2025-03-07
2112dfba27ef58426f7863b3a7fa0805362b4fcf 453b660d29b2585094d43e3714fa25b45bca7bf0 Sim Zhen Quan <<EMAIL>> 1741343318 +0800	checkout: moving from staging/2025-03-07 to dev
453b660d29b2585094d43e3714fa25b45bca7bf0 51220083457ba99f9c3e686d35689e39686c694a Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/staging/2025-03-07: Merge made by the 'ort' strategy.
51220083457ba99f9c3e686d35689e39686c694a 2399eb9168b27f48616e57d2d5dd3c41d3dc73da Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to dev
2399eb9168b27f48616e57d2d5dd3c41d3dc73da cb81f8841a54076183771271589e5407eea561d9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to saving-account-void
cb81f8841a54076183771271589e5407eea561d9 97dfb8d928d857c1c43a95f9fc20fd90da861831 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
97dfb8d928d857c1c43a95f9fc20fd90da861831 63ea094e6f4bb953f93898836b1b478fd714b065 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
63ea094e6f4bb953f93898836b1b478fd714b065 f5bb514205fd83573f897f3f9737686f31fa7e92 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from saving-account-void to fix-adhoc-notification
f5bb514205fd83573f897f3f9737686f31fa7e92 2db0465a53a8af792e542c55c6033c52914022f6 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
2db0465a53a8af792e542c55c6033c52914022f6 bccbb4c4c3b14675580431dbc7eaa2c140548803 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix-adhoc-notification to grading-framework-formula
bccbb4c4c3b14675580431dbc7eaa2c140548803 06072c691d803f4d55edc444cb639f615011a92d Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
06072c691d803f4d55edc444cb639f615011a92d 4b15a6e018e9412a200fda41a43cbe7c336b9abc Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from grading-framework-formula to master-grading-framework-CRUD
4b15a6e018e9412a200fda41a43cbe7c336b9abc ffd80261772fa0adf7ee98f323efeb048bfb683a Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
ffd80261772fa0adf7ee98f323efeb048bfb683a 2112dfba27ef58426f7863b3a7fa0805362b4fcf Sim Zhen Quan <<EMAIL>> 1741579122 +0800	checkout: moving from master-grading-framework-CRUD to staging/2025-03-07
2112dfba27ef58426f7863b3a7fa0805362b4fcf 8f8ac8bdd134f29092e32b24929bc7bb59554ea1 Sim Zhen Quan <<EMAIL>> 1741579129 +0800	pull: Fast-forward
8f8ac8bdd134f29092e32b24929bc7bb59554ea1 e69d16d38e786b97be55115db591161cd2f5d00a Sim Zhen Quan <<EMAIL>> 1741579144 +0800	commit: Typo fix
e69d16d38e786b97be55115db591161cd2f5d00a a3baf8122cda29c02f48717dfc957fe9221babfa Sim Zhen Quan <<EMAIL>> 1741579180 +0800	merge origin/main: Merge made by the 'ort' strategy.
a3baf8122cda29c02f48717dfc957fe9221babfa 9d98f63e912b20760085f065c2b57ea29a54baa3 Sim Zhen Quan <<EMAIL>> 1741579654 +0800	checkout: moving from staging/2025-03-07 to add-point-duduction-columns-to-leave-application-type
9d98f63e912b20760085f065c2b57ea29a54baa3 33e3556cdb7d5354470cfcdcb8193e978efaabfe Sim Zhen Quan <<EMAIL>> 1741579721 +0800	merge origin/main: Merge made by the 'ort' strategy.
33e3556cdb7d5354470cfcdcb8193e978efaabfe d4fdd50adbf31b67464c8b6883cdb92a2c228a62 Sim Zhen Quan <<EMAIL>> 1741580405 +0800	commit: Reviewed
d4fdd50adbf31b67464c8b6883cdb92a2c228a62 2399eb9168b27f48616e57d2d5dd3c41d3dc73da Sim Zhen Quan <<EMAIL>> 1741580471 +0800	checkout: moving from add-point-duduction-columns-to-leave-application-type to dev
2399eb9168b27f48616e57d2d5dd3c41d3dc73da a3baf8122cda29c02f48717dfc957fe9221babfa Sim Zhen Quan <<EMAIL>> 1741580559 +0800	checkout: moving from dev to staging/2025-03-07
a3baf8122cda29c02f48717dfc957fe9221babfa 52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 Sim Zhen Quan <<EMAIL>> 1741580563 +0800	merge origin/main: Merge made by the 'ort' strategy.
52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 2399eb9168b27f48616e57d2d5dd3c41d3dc73da Sim Zhen Quan <<EMAIL>> 1741580626 +0800	checkout: moving from staging/2025-03-07 to dev
2399eb9168b27f48616e57d2d5dd3c41d3dc73da 553c6f4332c3af368eb8788572af9a8620dcf76b Sim Zhen Quan <<EMAIL>> 1741580686 +0800	commit (merge): Merge branch
553c6f4332c3af368eb8788572af9a8620dcf76b 9a2ec92e50943650f2be7e1d5bd2ef8133f5e3d0 Sim Zhen Quan <<EMAIL>> 1741581228 +0800	commit: Deployed to DEV
9a2ec92e50943650f2be7e1d5bd2ef8133f5e3d0 06072c691d803f4d55edc444cb639f615011a92d Sim Zhen Quan <<EMAIL>> 1741581238 +0800	checkout: moving from dev to grading-framework-formula
06072c691d803f4d55edc444cb639f615011a92d 1dcf50686bd128d84e83a64bd6b246ec4733bf12 Sim Zhen Quan <<EMAIL>> 1741581245 +0800	merge origin/main: Merge made by the 'ort' strategy.
1dcf50686bd128d84e83a64bd6b246ec4733bf12 2112dfba27ef58426f7863b3a7fa0805362b4fcf Sim Zhen Quan <<EMAIL>> 1741592023 +0800	checkout: moving from grading-framework-formula to main
2112dfba27ef58426f7863b3a7fa0805362b4fcf 42b5fe97fdf535bf0dbd721962c5158c3814414e Sim Zhen Quan <<EMAIL>> 1741592028 +0800	pull: Fast-forward
42b5fe97fdf535bf0dbd721962c5158c3814414e 1dcf50686bd128d84e83a64bd6b246ec4733bf12 Sim Zhen Quan <<EMAIL>> 1741595704 +0800	checkout: moving from main to grading-framework-formula
1dcf50686bd128d84e83a64bd6b246ec4733bf12 7ecc11a9923c47c77a7eef5cd8f780f56f629006 Sim Zhen Quan <<EMAIL>> 1741597720 +0800	checkout: moving from grading-framework-formula to fix-migration-bed
7ecc11a9923c47c77a7eef5cd8f780f56f629006 72abfa4d7a5815085ef3f2b4b9729de968f58918 Sim Zhen Quan <<EMAIL>> 1741597725 +0800	merge origin/main: Merge made by the 'ort' strategy.
72abfa4d7a5815085ef3f2b4b9729de968f58918 6158179907fca935da166d8eb67624ea0760b097 Sim Zhen Quan <<EMAIL>> 1741599842 +0800	commit: Added class_type on class subject api
6158179907fca935da166d8eb67624ea0760b097 b97ea91bac1d9591ac51567e98cbd2798d4b7b6d Sim Zhen Quan <<EMAIL>> 1741661705 +0800	checkout: moving from fix-migration-bed to route_enhancement_handle_paginations
b97ea91bac1d9591ac51567e98cbd2798d4b7b6d 43f728df89696d4cd6617af05660e2bfc2398e4f Sim Zhen Quan <<EMAIL>> 1741661712 +0800	merge origin/main: Merge made by the 'ort' strategy.
43f728df89696d4cd6617af05660e2bfc2398e4f 144e7feed25f2b410f7dd61933fc5b96ba2c8bab Sim Zhen Quan <<EMAIL>> 1741661819 +0800	checkout: moving from route_enhancement_handle_paginations to fix/billing-document-sub-type-filter
144e7feed25f2b410f7dd61933fc5b96ba2c8bab 9a2ec92e50943650f2be7e1d5bd2ef8133f5e3d0 Sim Zhen Quan <<EMAIL>> 1741661919 +0800	checkout: moving from fix/billing-document-sub-type-filter to dev
9a2ec92e50943650f2be7e1d5bd2ef8133f5e3d0 410215328b509663a8d1c845ce5ba6ece08f3e6e Sim Zhen Quan <<EMAIL>> 1741662032 +0800	commit (merge): Merge origin/main
410215328b509663a8d1c845ce5ba6ece08f3e6e a75c51bd2f60c3976c42ebcd2098da68d2523c2f Sim Zhen Quan <<EMAIL>> 1741662558 +0800	commit: Deployed to dev
a75c51bd2f60c3976c42ebcd2098da68d2523c2f 1dcf50686bd128d84e83a64bd6b246ec4733bf12 Sim Zhen Quan <<EMAIL>> 1741662566 +0800	checkout: moving from dev to grading-framework-formula
1dcf50686bd128d84e83a64bd6b246ec4733bf12 f99c79d9014730e4a5cf04af69c04c1a4535d53d Sim Zhen Quan <<EMAIL>> 1741662571 +0800	pull: Fast-forward
f99c79d9014730e4a5cf04af69c04c1a4535d53d f45da67a9ce063ef77baeee1bb1292e99d9e90dd Sim Zhen Quan <<EMAIL>> 1741662572 +0800	merge origin/main: Merge made by the 'ort' strategy.
f45da67a9ce063ef77baeee1bb1292e99d9e90dd 3d0fd26efe4a9a3e45a8b89bc35188f97bce61f9 Sim Zhen Quan <<EMAIL>> 1741665731 +0800	commit: Reviewed
3d0fd26efe4a9a3e45a8b89bc35188f97bce61f9 7920a5560ddcb19534c424f61bb17344e5adf017 Sim Zhen Quan <<EMAIL>> 1741667833 +0800	checkout: moving from grading-framework-formula to optimized-send-annoucement-function
7920a5560ddcb19534c424f61bb17344e5adf017 ed7643a78836dec9ae3e033138385fa52e6a8a27 Sim Zhen Quan <<EMAIL>> 1741667840 +0800	merge origin/main: Merge made by the 'ort' strategy.
ed7643a78836dec9ae3e033138385fa52e6a8a27 dae34ba80aa8ae143c849c949b3d59d49741bece Sim Zhen Quan <<EMAIL>> 1741675753 +0800	pull: Fast-forward
dae34ba80aa8ae143c849c949b3d59d49741bece 667f0d81c619ad8e6db4f2e35bda565c4bf9fb87 Sim Zhen Quan <<EMAIL>> 1741678982 +0800	commit: Enhance announcement sending
667f0d81c619ad8e6db4f2e35bda565c4bf9fb87 20155464de677a15180f3fecc10697437bc599b3 Sim Zhen Quan <<EMAIL>> 1741679574 +0800	pull: Fast-forward
20155464de677a15180f3fecc10697437bc599b3 a75c51bd2f60c3976c42ebcd2098da68d2523c2f Sim Zhen Quan <<EMAIL>> 1741680973 +0800	checkout: moving from optimized-send-annoucement-function to dev
a75c51bd2f60c3976c42ebcd2098da68d2523c2f 799b08f011b6981e945b97863d0260e25d4c701b Sim Zhen Quan <<EMAIL>> 1741680991 +0800	merge optimized-send-annoucement-function: Merge made by the 'ort' strategy.
799b08f011b6981e945b97863d0260e25d4c701b a7379ab00dd702d3efffb5e6dd99b5c4b410a60e Sim Zhen Quan <<EMAIL>> 1741681524 +0800	commit: Deployed to DEV
a7379ab00dd702d3efffb5e6dd99b5c4b410a60e d9aa8b88b5c25ee572dafb8c391de4d907e77c81 Sim Zhen Quan <<EMAIL>> 1741681532 +0800	checkout: moving from dev to fix-slow-query
d9aa8b88b5c25ee572dafb8c391de4d907e77c81 d9aa8b88b5c25ee572dafb8c391de4d907e77c81 Sim Zhen Quan <<EMAIL>> 1741681989 +0800	reset: moving to HEAD
d9aa8b88b5c25ee572dafb8c391de4d907e77c81 743789ed9364bd60442e1f15b83f0e0233f879a0 Sim Zhen Quan <<EMAIL>> 1741681992 +0800	merge origin/main: Merge made by the 'ort' strategy.
743789ed9364bd60442e1f15b83f0e0233f879a0 743789ed9364bd60442e1f15b83f0e0233f879a0 Sim Zhen Quan <<EMAIL>> 1741683794 +0800	reset: moving to HEAD
743789ed9364bd60442e1f15b83f0e0233f879a0 52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 Sim Zhen Quan <<EMAIL>> 1741683802 +0800	checkout: moving from fix-slow-query to staging/2025-03-07
52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 743789ed9364bd60442e1f15b83f0e0233f879a0 Sim Zhen Quan <<EMAIL>> 1741684388 +0800	checkout: moving from staging/2025-03-07 to fix-slow-query
743789ed9364bd60442e1f15b83f0e0233f879a0 743789ed9364bd60442e1f15b83f0e0233f879a0 Sim Zhen Quan <<EMAIL>> 1741684580 +0800	reset: moving to HEAD
743789ed9364bd60442e1f15b83f0e0233f879a0 52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 Sim Zhen Quan <<EMAIL>> 1741684588 +0800	checkout: moving from fix-slow-query to staging/2025-03-07
52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 743789ed9364bd60442e1f15b83f0e0233f879a0 Sim Zhen Quan <<EMAIL>> 1741684777 +0800	checkout: moving from staging/2025-03-07 to fix-slow-query
743789ed9364bd60442e1f15b83f0e0233f879a0 743789ed9364bd60442e1f15b83f0e0233f879a0 Sim Zhen Quan <<EMAIL>> 1741689781 +0800	reset: moving to HEAD
743789ed9364bd60442e1f15b83f0e0233f879a0 d12e68916d79210a6ed606c0631105b13d8e328e Sim Zhen Quan <<EMAIL>> 1741689878 +0800	commit: Review WIP
d12e68916d79210a6ed606c0631105b13d8e328e 07c2e82e27bfcc6eb627770d149292308c5ff3c9 Sim Zhen Quan <<EMAIL>> 1741690871 +0800	commit: Reviewed
07c2e82e27bfcc6eb627770d149292308c5ff3c9 735899449cbc2e571819603fd369d0328d059d31 Sim Zhen Quan <<EMAIL>> 1741798309 +0800	merge origin/staging/2025-03-07: Merge made by the 'ort' strategy.
735899449cbc2e571819603fd369d0328d059d31 52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 Sim Zhen Quan <<EMAIL>> 1741798339 +0800	checkout: moving from fix-slow-query to staging/2025-03-07
52a18f34ca7c1d03b92ffff5ba44ba693a28bd35 1fe0e71ba675da4980352529d8bfa7b0abd59b80 Sim Zhen Quan <<EMAIL>> 1741798345 +0800	merge origin/main: Merge made by the 'ort' strategy.
1fe0e71ba675da4980352529d8bfa7b0abd59b80 62e2b7b8ea48a6aac9faf2efb777ab0cfe35b885 Sim Zhen Quan <<EMAIL>> 1741798410 +0800	checkout: moving from staging/2025-03-07 to migrate-script-subject
62e2b7b8ea48a6aac9faf2efb777ab0cfe35b885 54bbdef594226d23e7d43b9319f12290022f0c45 Sim Zhen Quan <<EMAIL>> 1741798416 +0800	merge origin/main: Merge made by the 'ort' strategy.
54bbdef594226d23e7d43b9319f12290022f0c45 54bbdef594226d23e7d43b9319f12290022f0c45 Sim Zhen Quan <<EMAIL>> 1741831661 +0800	checkout: moving from migrate-script-subject to migrate-script-subject
54bbdef594226d23e7d43b9319f12290022f0c45 2fb82345e53981bfaec169f56b54af246fe26829 Sim Zhen Quan <<EMAIL>> 1741918809 +0800	commit: Reviewed
2fb82345e53981bfaec169f56b54af246fe26829 5f284e237ffc19c288e065940cd6c5311f7e4c44 Sim Zhen Quan <<EMAIL>> 1741920143 +0800	commit: Reviewed
5f284e237ffc19c288e065940cd6c5311f7e4c44 a7379ab00dd702d3efffb5e6dd99b5c4b410a60e Sim Zhen Quan <<EMAIL>> 1741920407 +0800	checkout: moving from migrate-script-subject to dev
a7379ab00dd702d3efffb5e6dd99b5c4b410a60e 5f284e237ffc19c288e065940cd6c5311f7e4c44 Sim Zhen Quan <<EMAIL>> 1741920407 +0800	checkout: moving from dev to migrate-script-subject
5f284e237ffc19c288e065940cd6c5311f7e4c44 a7379ab00dd702d3efffb5e6dd99b5c4b410a60e Sim Zhen Quan <<EMAIL>> 1741920448 +0800	checkout: moving from migrate-script-subject to dev
a7379ab00dd702d3efffb5e6dd99b5c4b410a60e 79faf7c945b3ea67e2f60eb93f576220e10ea1ed Sim Zhen Quan <<EMAIL>> 1741920448 +0800	merge migrate-script-subject: Merge made by the 'ort' strategy.
79faf7c945b3ea67e2f60eb93f576220e10ea1ed 09176d9c88961d6d149d0487a6fd52c2b81bc7fc Sim Zhen Quan <<EMAIL>> 1741933327 +0800	commit: Deployed to DEV
09176d9c88961d6d149d0487a6fd52c2b81bc7fc 1fe0e71ba675da4980352529d8bfa7b0abd59b80 Sim Zhen Quan <<EMAIL>> 1741933337 +0800	checkout: moving from dev to staging/2025-03-07
1fe0e71ba675da4980352529d8bfa7b0abd59b80 0d53bc3e7b6167b37fea5e773786ac59305084ba Sim Zhen Quan <<EMAIL>> 1741933345 +0800	pull: Fast-forward
0d53bc3e7b6167b37fea5e773786ac59305084ba 9183916b17c96cbf574e356165ea9715a051f430 Sim Zhen Quan <<EMAIL>> 1741935830 +0800	commit (merge): Merge branch from main
9183916b17c96cbf574e356165ea9715a051f430 085114ba9897566ae01b81d785cea9c766af0ae0 Sim Zhen Quan <<EMAIL>> 1741936178 +0800	checkout: moving from staging/2025-03-07 to leave-application-index-api-remove-id-validation
085114ba9897566ae01b81d785cea9c766af0ae0 6b668493cea166f95af6ae3f2fd97c316b3e0830 Sim Zhen Quan <<EMAIL>> 1741936187 +0800	merge origin/main: Merge made by the 'ort' strategy.
6b668493cea166f95af6ae3f2fd97c316b3e0830 7b73c3533ea10fdfbc7d2865a28e7654aaf5d818 Sim Zhen Quan <<EMAIL>> 1741937745 +0800	commit: Attendance fix
7b73c3533ea10fdfbc7d2865a28e7654aaf5d818 1b43816a81ca08b150aefe62e380b9d6b4b9aa27 Sim Zhen Quan <<EMAIL>> 1741939681 +0800	checkout: moving from leave-application-index-api-remove-id-validation to asset-rental
1b43816a81ca08b150aefe62e380b9d6b4b9aa27 c680673241703b862210b95c416a6823e6cd5956 Sim Zhen Quan <<EMAIL>> 1741939755 +0800	checkout: moving from asset-rental to product-resource-eager-loading
c680673241703b862210b95c416a6823e6cd5956 002b805574e0a909462eab8b0310024862440872 Sim Zhen Quan <<EMAIL>> 1741940083 +0800	commit (merge): Merge origin/main
002b805574e0a909462eab8b0310024862440872 d5737af2b1f27095ddc9f82f52390b4e3f9b569a Sim Zhen Quan <<EMAIL>> 1741942243 +0800	checkout: moving from product-resource-eager-loading to migrate-script-competition
d5737af2b1f27095ddc9f82f52390b4e3f9b569a 6edd605c0dac9a52fd7a05cc14ca3c560129fef4 Sim Zhen Quan <<EMAIL>> 1741942428 +0800	commit (merge): Merge origin/main
6edd605c0dac9a52fd7a05cc14ca3c560129fef4 3ce06b84752b89e6ded1129e6c4396e61e54eedb Sim Zhen Quan <<EMAIL>> 1742051654 +0800	commit: Review WIP
3ce06b84752b89e6ded1129e6c4396e61e54eedb 09176d9c88961d6d149d0487a6fd52c2b81bc7fc Sim Zhen Quan <<EMAIL>> 1742051738 +0800	checkout: moving from migrate-script-competition to dev
09176d9c88961d6d149d0487a6fd52c2b81bc7fc 42b5fe97fdf535bf0dbd721962c5158c3814414e Sim Zhen Quan <<EMAIL>> 1742051741 +0800	checkout: moving from dev to main
42b5fe97fdf535bf0dbd721962c5158c3814414e 85b575f8bf630138e8bb83cf5ccc70830f30070d Sim Zhen Quan <<EMAIL>> 1742051747 +0800	pull: Fast-forward
85b575f8bf630138e8bb83cf5ccc70830f30070d 5b10200afe509ad749da9a57de93d36d8fe848d0 Sim Zhen Quan <<EMAIL>> 1742053191 +0800	pull: Fast-forward
5b10200afe509ad749da9a57de93d36d8fe848d0 63e88e731221cfcd429347a3540d1b65af81478e Sim Zhen Quan <<EMAIL>> 1742053527 +0800	commit: Deployed to PRD
63e88e731221cfcd429347a3540d1b65af81478e bc0ae2467d98f1057a5bea99efbb0c41cd5e311a Sim Zhen Quan <<EMAIL>> 1742178050 +0800	checkout: moving from main to attendance-online-mode-tap-card-api
bc0ae2467d98f1057a5bea99efbb0c41cd5e311a 7f225b596c56082c763a903f3c160c1fbdb3f6f3 Sim Zhen Quan <<EMAIL>> 1742178057 +0800	merge origin/main: Merge made by the 'ort' strategy.
7f225b596c56082c763a903f3c160c1fbdb3f6f3 d21635c33fdf9e425922d6b18d35da25f02820e7 Sim Zhen Quan <<EMAIL>> 1742181322 +0800	commit: Reviewed
d21635c33fdf9e425922d6b18d35da25f02820e7 63e88e731221cfcd429347a3540d1b65af81478e Sim Zhen Quan <<EMAIL>> 1742181597 +0800	checkout: moving from attendance-online-mode-tap-card-api to main
63e88e731221cfcd429347a3540d1b65af81478e 09176d9c88961d6d149d0487a6fd52c2b81bc7fc Sim Zhen Quan <<EMAIL>> 1742181601 +0800	checkout: moving from main to dev
09176d9c88961d6d149d0487a6fd52c2b81bc7fc 5c0196444265e2519184b8007ca4e1ea686a1cf7 Sim Zhen Quan <<EMAIL>> 1742181645 +0800	merge origin/main: Merge made by the 'ort' strategy.
5c0196444265e2519184b8007ca4e1ea686a1cf7 af4aed619c4a933cb46b942f9377b9d9fa17f4e3 Sim Zhen Quan <<EMAIL>> 1742182056 +0800	commit: Deployed to DEV
af4aed619c4a933cb46b942f9377b9d9fa17f4e3 af4aed619c4a933cb46b942f9377b9d9fa17f4e3 Sim Zhen Quan <<EMAIL>> 1742182090 +0800	reset: moving to HEAD
af4aed619c4a933cb46b942f9377b9d9fa17f4e3 63e88e731221cfcd429347a3540d1b65af81478e Sim Zhen Quan <<EMAIL>> 1742182092 +0800	checkout: moving from dev to main
63e88e731221cfcd429347a3540d1b65af81478e 3ea95bfcf2a3e6447c2f8c68bf5fe695d8d55d81 Sim Zhen Quan <<EMAIL>> 1742182097 +0800	pull: Fast-forward
3ea95bfcf2a3e6447c2f8c68bf5fe695d8d55d81 52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff Sim Zhen Quan <<EMAIL>> 1742182139 +0800	commit: Move ConfigHelper call to handle() to prevent composer dump issue
52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff 3ce06b84752b89e6ded1129e6c4396e61e54eedb Sim Zhen Quan <<EMAIL>> 1742193252 +0800	checkout: moving from main to migrate-script-competition
3ce06b84752b89e6ded1129e6c4396e61e54eedb 099a01e756fefb4b09bf7f45417431adb4c2b860 Sim Zhen Quan <<EMAIL>> 1742193261 +0800	merge origin/main: Merge made by the 'ort' strategy.
099a01e756fefb4b09bf7f45417431adb4c2b860 d6940cc2a14713c2e8f4e46d9ae0b9cf06a3b35c Sim Zhen Quan <<EMAIL>> 1742195015 +0800	commit: Reviewed
d6940cc2a14713c2e8f4e46d9ae0b9cf06a3b35c 002b805574e0a909462eab8b0310024862440872 Sim Zhen Quan <<EMAIL>> 1742195289 +0800	checkout: moving from migrate-script-competition to product-resource-eager-loading
002b805574e0a909462eab8b0310024862440872 eae5ca0a31bdb4d15fcadae1007cf15406e5415d Sim Zhen Quan <<EMAIL>> 1742195300 +0800	merge origin/main: Merge made by the 'ort' strategy.
eae5ca0a31bdb4d15fcadae1007cf15406e5415d d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 Sim Zhen Quan <<EMAIL>> 1742197069 +0800	commit: Reviewed
d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 Sim Zhen Quan <<EMAIL>> 1742197629 +0800	checkout: moving from product-resource-eager-loading to staging/2025-03-17
d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 3c7b98b463371b6d58b068ad10fdfbdbc51aa126 Sim Zhen Quan <<EMAIL>> 1742198500 +0800	checkout: moving from staging/2025-03-17 to leave-application-status-enhancements
3c7b98b463371b6d58b068ad10fdfbdbc51aa126 57771ad5fa5836650386ec41e4fb1f5f5a5c8f69 Sim Zhen Quan <<EMAIL>> 1742198507 +0800	merge origin/main: Merge made by the 'ort' strategy.
57771ad5fa5836650386ec41e4fb1f5f5a5c8f69 c30dec98d135815f487625a676a419d2fd3029d2 Sim Zhen Quan <<EMAIL>> 1742201222 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
c30dec98d135815f487625a676a419d2fd3029d2 383e7dc6b8d5fb3825980f4b649307d75b27ff3d Sim Zhen Quan <<EMAIL>> 1742201441 +0800	commit: Reviewed
383e7dc6b8d5fb3825980f4b649307d75b27ff3d af4aed619c4a933cb46b942f9377b9d9fa17f4e3 Sim Zhen Quan <<EMAIL>> 1742201563 +0800	checkout: moving from leave-application-status-enhancements to dev
af4aed619c4a933cb46b942f9377b9d9fa17f4e3 de0f4cadd6ef6753654e7ea55880c8808670fd5e Sim Zhen Quan <<EMAIL>> 1742201582 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
de0f4cadd6ef6753654e7ea55880c8808670fd5e eb9338d9bfbfd02e04ee65802d85b7b6d731293c Sim Zhen Quan <<EMAIL>> 1742202054 +0800	commit: Deployed to DEV
eb9338d9bfbfd02e04ee65802d85b7b6d731293c c7b02cd0a88fd410d58d284856f07fa8cb7a859d Sim Zhen Quan <<EMAIL>> 1742202063 +0800	checkout: moving from dev to fix/library-report
c7b02cd0a88fd410d58d284856f07fa8cb7a859d 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742202079 +0800	merge origin/main: Merge made by the 'ort' strategy.
5f05c526464c607f8e32125758f95730154afcb9 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742203264 +0800	checkout: moving from fix/library-report to fix/library-report
5f05c526464c607f8e32125758f95730154afcb9 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742367530 +0800	reset: moving to HEAD
5f05c526464c607f8e32125758f95730154afcb9 d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 Sim Zhen Quan <<EMAIL>> 1742367536 +0800	checkout: moving from fix/library-report to staging/2025-03-17
d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 4d5dcbe06afbfad3518a31f23bd22a1840cf687c Sim Zhen Quan <<EMAIL>> 1742367543 +0800	pull: Fast-forward
4d5dcbe06afbfad3518a31f23bd22a1840cf687c 4d5dcbe06afbfad3518a31f23bd22a1840cf687c Sim Zhen Quan <<EMAIL>> 1742367552 +0800	checkout: moving from staging/2025-03-17 to tap-card-api-respond-userable
4d5dcbe06afbfad3518a31f23bd22a1840cf687c 6665432e624a22e36424993319453462ebe2c24b Sim Zhen Quan <<EMAIL>> 1742370227 +0800	commit: Attendance tap card APi returns userable info
6665432e624a22e36424993319453462ebe2c24b 52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff Sim Zhen Quan <<EMAIL>> 1742370275 +0800	checkout: moving from tap-card-api-respond-userable to main
52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff 061a165481e164679e5e8c0d5fcb9a2e971c5d6d Sim Zhen Quan <<EMAIL>> 1742370280 +0800	pull: Fast-forward
061a165481e164679e5e8c0d5fcb9a2e971c5d6d 903944840d3648668e9b4d311acbc0f61422b8b4 Sim Zhen Quan <<EMAIL>> 1742370317 +0800	commit: Remove bracket for exam marks formula
903944840d3648668e9b4d311acbc0f61422b8b4 eb9338d9bfbfd02e04ee65802d85b7b6d731293c Sim Zhen Quan <<EMAIL>> 1742370326 +0800	checkout: moving from main to dev
eb9338d9bfbfd02e04ee65802d85b7b6d731293c 4d5dcbe06afbfad3518a31f23bd22a1840cf687c Sim Zhen Quan <<EMAIL>> 1742370337 +0800	checkout: moving from dev to staging/2025-03-17
4d5dcbe06afbfad3518a31f23bd22a1840cf687c c76837d7cf9a9ae0090213564cfdab621698e807 Sim Zhen Quan <<EMAIL>> 1742370343 +0800	pull: Fast-forward
c76837d7cf9a9ae0090213564cfdab621698e807 063366ebe21d42b416c5a005808a10d4347a1e51 Sim Zhen Quan <<EMAIL>> 1742370343 +0800	merge origin/main: Merge made by the 'ort' strategy.
063366ebe21d42b416c5a005808a10d4347a1e51 eb9338d9bfbfd02e04ee65802d85b7b6d731293c Sim Zhen Quan <<EMAIL>> 1742370354 +0800	checkout: moving from staging/2025-03-17 to dev
eb9338d9bfbfd02e04ee65802d85b7b6d731293c d5448e8985523ca2168aa3a06752def4b7edcd86 Sim Zhen Quan <<EMAIL>> 1742370361 +0800	merge staging/2025-03-17: Merge made by the 'ort' strategy.
d5448e8985523ca2168aa3a06752def4b7edcd86 063366ebe21d42b416c5a005808a10d4347a1e51 Sim Zhen Quan <<EMAIL>> 1742370600 +0800	checkout: moving from dev to staging/2025-03-17
063366ebe21d42b416c5a005808a10d4347a1e51 3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 Sim Zhen Quan <<EMAIL>> 1742371865 +0800	commit: Use UserableResource instead of SimpleUserableResource
3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 Sim Zhen Quan <<EMAIL>> 1742375856 +0800	checkout: moving from staging/2025-03-17 to DO-NOT-MERGE-qas-temporary-data
3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 d9c7cc288405f1d63af37459f64b45e307b93221 Sim Zhen Quan <<EMAIL>> 1742376442 +0800	commit: PLEASE REPORT TO LUCAS IF YOU SEE THIS COMMITED TO MAIN BRANCH
d9c7cc288405f1d63af37459f64b45e307b93221 4c063d2070b586535e17b6610b279722c792e52b Sim Zhen Quan <<EMAIL>> 1742376464 +0800	checkout: moving from DO-NOT-MERGE-qas-temporary-data to qas
4c063d2070b586535e17b6610b279722c792e52b af3d0da2cf58819a4b83e9d09d16cc4b96d6ebcd Sim Zhen Quan <<EMAIL>> 1742376468 +0800	merge DO-NOT-MERGE-qas-temporary-data: Merge made by the 'ort' strategy.
af3d0da2cf58819a4b83e9d09d16cc4b96d6ebcd b11aefa89a0d2983e1847bdef6544c77517257d4 Sim Zhen Quan <<EMAIL>> 1742376712 +0800	commit: Deployed to qas
b11aefa89a0d2983e1847bdef6544c77517257d4 5b2d273be6028018632180ae6683034a6dab04a9 Sim Zhen Quan <<EMAIL>> 1742376720 +0800	checkout: moving from qas to substitute-teacher-enhancements
5b2d273be6028018632180ae6683034a6dab04a9 7472e3cbee395adbdfa37fbada10f2718f0bae63 Sim Zhen Quan <<EMAIL>> 1742376726 +0800	merge origin/main: Merge made by the 'ort' strategy.
7472e3cbee395adbdfa37fbada10f2718f0bae63 d5448e8985523ca2168aa3a06752def4b7edcd86 Sim Zhen Quan <<EMAIL>> 1742377377 +0800	checkout: moving from substitute-teacher-enhancements to dev
d5448e8985523ca2168aa3a06752def4b7edcd86 5d4dbb156cdf62ad593c7fa0f247f02879b3991c Sim Zhen Quan <<EMAIL>> 1742377459 +0800	commit (merge): Merge origin/main
5d4dbb156cdf62ad593c7fa0f247f02879b3991c c300f3c79ed869c41c06070078cb6b39cbb6aec7 Sim Zhen Quan <<EMAIL>> 1742379726 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
c300f3c79ed869c41c06070078cb6b39cbb6aec7 c300f3c79ed869c41c06070078cb6b39cbb6aec7 Sim Zhen Quan <<EMAIL>> 1742404414 +0800	reset: moving to HEAD
c300f3c79ed869c41c06070078cb6b39cbb6aec7 903944840d3648668e9b4d311acbc0f61422b8b4 Sim Zhen Quan <<EMAIL>> 1742404414 +0800	checkout: moving from dev to main
903944840d3648668e9b4d311acbc0f61422b8b4 81aa281a02aa8f4aa65f6a2527b5b8c7e614d74c Sim Zhen Quan <<EMAIL>> 1742404419 +0800	pull: Fast-forward
81aa281a02aa8f4aa65f6a2527b5b8c7e614d74c 7b0774741487bce02df6bd5811dfb5c06ba20bea Sim Zhen Quan <<EMAIL>> 1742404436 +0800	commit: Fixed substitute_record includes overwrite
7b0774741487bce02df6bd5811dfb5c06ba20bea d3690a3bcbcd5028aa7b61454ccf9f618a48d43a Sim Zhen Quan <<EMAIL>> 1742404447 +0800	checkout: moving from main to feature/report-card-custom-functions
d3690a3bcbcd5028aa7b61454ccf9f618a48d43a f0caf1773a114c7a559337fbdcdb97680acc7c29 Sim Zhen Quan <<EMAIL>> 1742404556 +0800	commit (merge): Merge origin/main
f0caf1773a114c7a559337fbdcdb97680acc7c29 02f34cdaf6f3b4cade83741c7f5261f6f9f49560 Sim Zhen Quan <<EMAIL>> 1742445431 +0800	commit: Removed testing code
02f34cdaf6f3b4cade83741c7f5261f6f9f49560 616bd0a0253f10e734543840ba4d1393cd366e59 Sim Zhen Quan <<EMAIL>> 1742446433 +0800	checkout: moving from feature/report-card-custom-functions to fix/billing-document-enhancement
616bd0a0253f10e734543840ba4d1393cd366e59 1cf01439175a955d8852955112743e303c1158c0 Sim Zhen Quan <<EMAIL>> 1742446438 +0800	pull: Fast-forward
1cf01439175a955d8852955112743e303c1158c0 1cf01439175a955d8852955112743e303c1158c0 Sim Zhen Quan <<EMAIL>> 1742449485 +0800	reset: moving to HEAD
1cf01439175a955d8852955112743e303c1158c0 02f34cdaf6f3b4cade83741c7f5261f6f9f49560 Sim Zhen Quan <<EMAIL>> 1742449506 +0800	checkout: moving from fix/billing-document-enhancement to feature/report-card-custom-functions
02f34cdaf6f3b4cade83741c7f5261f6f9f49560 6470aade5ea5c09945ce50bd28803ffc57a36545 Sim Zhen Quan <<EMAIL>> 1742449929 +0800	checkout: moving from feature/report-card-custom-functions to get-requestor-timeslots-enhancements
6470aade5ea5c09945ce50bd28803ffc57a36545 8327ef636d25d4c0058dc927634b4f155ee4a0b4 Sim Zhen Quan <<EMAIL>> 1742456290 +0800	commit: Added semester_class_id and subject_id filter
8327ef636d25d4c0058dc927634b4f155ee4a0b4 02f34cdaf6f3b4cade83741c7f5261f6f9f49560 Sim Zhen Quan <<EMAIL>> 1742460029 +0800	checkout: moving from get-requestor-timeslots-enhancements to feature/report-card-custom-functions
02f34cdaf6f3b4cade83741c7f5261f6f9f49560 0226d414158a2a51a842f789e43d8a1ce11dc528 Sim Zhen Quan <<EMAIL>> 1742460036 +0800	pull: Fast-forward
0226d414158a2a51a842f789e43d8a1ce11dc528 8327ef636d25d4c0058dc927634b4f155ee4a0b4 Sim Zhen Quan <<EMAIL>> 1742461035 +0800	checkout: moving from feature/report-card-custom-functions to get-requestor-timeslots-enhancements
8327ef636d25d4c0058dc927634b4f155ee4a0b4 c300f3c79ed869c41c06070078cb6b39cbb6aec7 Sim Zhen Quan <<EMAIL>> 1742461255 +0800	checkout: moving from get-requestor-timeslots-enhancements to dev
c300f3c79ed869c41c06070078cb6b39cbb6aec7 d7a77009b52525cd9fc5e2f8ccbe4a7f775ac7f8 Sim Zhen Quan <<EMAIL>> 1742461261 +0800	merge origin/main: Merge made by the 'ort' strategy.
d7a77009b52525cd9fc5e2f8ccbe4a7f775ac7f8 3d915c3e886a0e59f70e02a6b622ebdb0ab1eb10 Sim Zhen Quan <<EMAIL>> 1742461385 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
3d915c3e886a0e59f70e02a6b622ebdb0ab1eb10 7d84fd73a62e2842b6767a0a4fece0a96524c1a3 Sim Zhen Quan <<EMAIL>> 1742461783 +0800	commit: Deployed to DEV
7d84fd73a62e2842b6767a0a4fece0a96524c1a3 0226d414158a2a51a842f789e43d8a1ce11dc528 Sim Zhen Quan <<EMAIL>> 1742461825 +0800	checkout: moving from dev to feature/report-card-custom-functions
0226d414158a2a51a842f789e43d8a1ce11dc528 a981e25f07c2fd8e3fb291eac6b1bb18729affd0 Sim Zhen Quan <<EMAIL>> 1742462239 +0800	merge origin/main: Merge made by the 'ort' strategy.
a981e25f07c2fd8e3fb291eac6b1bb18729affd0 0c6d94ba521e1d85c67dd49170e2ba11b970bd9b Sim Zhen Quan <<EMAIL>> 1742464663 +0800	checkout: moving from feature/report-card-custom-functions to fix/class-permission-enhancement
0c6d94ba521e1d85c67dd49170e2ba11b970bd9b 7fd319be4b5f1a900a13c01317f4eaf4ec8e3583 Sim Zhen Quan <<EMAIL>> 1742464669 +0800	merge origin/main: Merge made by the 'ort' strategy.
7fd319be4b5f1a900a13c01317f4eaf4ec8e3583 68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 Sim Zhen Quan <<EMAIL>> 1742464712 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 Sim Zhen Quan <<EMAIL>> 1742464863 +0800	checkout: moving from fix/class-permission-enhancement to staging/2025-03-17
3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 157638539063a8fc4d44a4efaec02330a7cc2d55 Sim Zhen Quan <<EMAIL>> 1742464868 +0800	pull: Fast-forward
157638539063a8fc4d44a4efaec02330a7cc2d55 327e688514120980874496cb02d4e4bae4f7b006 Sim Zhen Quan <<EMAIL>> 1742464870 +0800	merge origin/main: Merge made by the 'ort' strategy.
327e688514120980874496cb02d4e4bae4f7b006 68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 Sim Zhen Quan <<EMAIL>> 1742464944 +0800	checkout: moving from staging/2025-03-17 to fix/class-permission-enhancement
68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 f94f5a581b18266024aa72bfa7b0f7957a8e4489 Sim Zhen Quan <<EMAIL>> 1742464966 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
f94f5a581b18266024aa72bfa7b0f7957a8e4489 be9acc61845ce1a320fd4a05eedb399ef3dfbebf Sim Zhen Quan <<EMAIL>> 1742465689 +0800	commit: Sorted permissions
be9acc61845ce1a320fd4a05eedb399ef3dfbebf 7d84fd73a62e2842b6767a0a4fece0a96524c1a3 Sim Zhen Quan <<EMAIL>> 1742466040 +0800	checkout: moving from fix/class-permission-enhancement to dev
7d84fd73a62e2842b6767a0a4fece0a96524c1a3 9b44657f26b90b4781e2101ddea870e6762feeb5 Sim Zhen Quan <<EMAIL>> 1742466057 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
9b44657f26b90b4781e2101ddea870e6762feeb5 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742468162 +0800	checkout: moving from dev to fix/library-report
5f05c526464c607f8e32125758f95730154afcb9 bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 Sim Zhen Quan <<EMAIL>> 1742468167 +0800	merge origin/main: Merge made by the 'ort' strategy.
bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 Sim Zhen Quan <<EMAIL>> 1742468277 +0800	checkout: moving from fix/library-report to fix/library-report
bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 3ef34fca91cc82bf9ab8e742ad5162f54afa8b52 Sim Zhen Quan <<EMAIL>> 1742523549 +0800	commit: Reviewed
3ef34fca91cc82bf9ab8e742ad5162f54afa8b52 0df790943aae84f72d31284d9bcb9eaff0522504 Sim Zhen Quan <<EMAIL>> 1742524697 +0800	checkout: moving from fix/library-report to substitute-record-fix-bug
0df790943aae84f72d31284d9bcb9eaff0522504 9ba692586cc65b3a760bca0960e2e04bde5700ab Sim Zhen Quan <<EMAIL>> 1742525225 +0800	commit: Reviewed
9ba692586cc65b3a760bca0960e2e04bde5700ab 9b44657f26b90b4781e2101ddea870e6762feeb5 Sim Zhen Quan <<EMAIL>> 1742525279 +0800	checkout: moving from substitute-record-fix-bug to dev
9b44657f26b90b4781e2101ddea870e6762feeb5 53b02539771243951344269e1a8e6c92d39e14d6 Sim Zhen Quan <<EMAIL>> 1742525296 +0800	merge origin/main: Merge made by the 'ort' strategy.
53b02539771243951344269e1a8e6c92d39e14d6 5a0939f5908f4d7d65d73e4928302d04ab1c28f9 Sim Zhen Quan <<EMAIL>> 1742525309 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
5a0939f5908f4d7d65d73e4928302d04ab1c28f9 a981e25f07c2fd8e3fb291eac6b1bb18729affd0 Sim Zhen Quan <<EMAIL>> 1742525688 +0800	checkout: moving from dev to feature/report-card-custom-functions
a981e25f07c2fd8e3fb291eac6b1bb18729affd0 78ec47586b2aba316dd8a07bbdc535e437d89040 Sim Zhen Quan <<EMAIL>> 1742525697 +0800	merge origin/main: Merge made by the 'ort' strategy.
78ec47586b2aba316dd8a07bbdc535e437d89040 78ec47586b2aba316dd8a07bbdc535e437d89040 Sim Zhen Quan <<EMAIL>> 1742525778 +0800	checkout: moving from feature/report-card-custom-functions to feature/report-card-custom-functions
78ec47586b2aba316dd8a07bbdc535e437d89040 7b0774741487bce02df6bd5811dfb5c06ba20bea Sim Zhen Quan <<EMAIL>> 1742532014 +0800	checkout: moving from feature/report-card-custom-functions to main
7b0774741487bce02df6bd5811dfb5c06ba20bea 8ea8456c9563005f1f68c09cd3aa50b1c6a21ed3 Sim Zhen Quan <<EMAIL>> 1742532019 +0800	pull: Fast-forward
8ea8456c9563005f1f68c09cd3aa50b1c6a21ed3 291204e3fab53d759922a1d3d0c2e5243b04f28c Sim Zhen Quan <<EMAIL>> 1742534965 +0800	commit: Remove sending of invoice via email
291204e3fab53d759922a1d3d0c2e5243b04f28c 8da27fecbb64f9ed10669251010a21416f132948 Sim Zhen Quan <<EMAIL>> 1742536141 +0800	checkout: moving from main to fix/timetable-teacher
8da27fecbb64f9ed10669251010a21416f132948 7c2a820a535d26f1afcdcf07e3790f493a17a985 Sim Zhen Quan <<EMAIL>> 1742536147 +0800	merge origin/main: Merge made by the 'ort' strategy.
7c2a820a535d26f1afcdcf07e3790f493a17a985 f2d663b17e7d8e079cc15ac34af20a99e63a5c8d Sim Zhen Quan <<EMAIL>> 1742538531 +0800	commit: Reviewed
f2d663b17e7d8e079cc15ac34af20a99e63a5c8d 5a0939f5908f4d7d65d73e4928302d04ab1c28f9 Sim Zhen Quan <<EMAIL>> 1742538913 +0800	checkout: moving from fix/timetable-teacher to dev
5a0939f5908f4d7d65d73e4928302d04ab1c28f9 0b5f64b93ea5c4e283918371397d2fadd9b3929c Sim Zhen Quan <<EMAIL>> 1742538919 +0800	merge origin/main: Merge made by the 'ort' strategy.
0b5f64b93ea5c4e283918371397d2fadd9b3929c 291204e3fab53d759922a1d3d0c2e5243b04f28c Sim Zhen Quan <<EMAIL>> 1742539525 +0800	checkout: moving from dev to main
291204e3fab53d759922a1d3d0c2e5243b04f28c 3dabd411866a1137188a44d028bc8be1eb398471 Sim Zhen Quan <<EMAIL>> 1742539530 +0800	pull: Fast-forward
3dabd411866a1137188a44d028bc8be1eb398471 1c6f001d71e5b6bb3c9ac56af292592dc814cfbb Sim Zhen Quan <<EMAIL>> 1742539540 +0800	commit: Update timetable permission dependency
1c6f001d71e5b6bb3c9ac56af292592dc814cfbb 26f57c15834a33a8a4d027c0f1c12c2326c2eed6 Sim Zhen Quan <<EMAIL>> 1742541197 +0800	checkout: moving from main to leave-application-enhancements
26f57c15834a33a8a4d027c0f1c12c2326c2eed6 fb6dc6aa7203da7cc78b417df57e3c908034761e Sim Zhen Quan <<EMAIL>> 1742541205 +0800	merge origin/main: Merge made by the 'ort' strategy.
fb6dc6aa7203da7cc78b417df57e3c908034761e 327e688514120980874496cb02d4e4bae4f7b006 Sim Zhen Quan <<EMAIL>> 1742541381 +0800	checkout: moving from leave-application-enhancements to staging/2025-03-17
327e688514120980874496cb02d4e4bae4f7b006 02a1f6be0c408c008969f46b4a17f1ff668f6f3d Sim Zhen Quan <<EMAIL>> 1742541386 +0800	pull: Fast-forward
02a1f6be0c408c008969f46b4a17f1ff668f6f3d 379ff82a1a5176f446ea571251a431ef878e0aa3 Sim Zhen Quan <<EMAIL>> 1742541386 +0800	merge origin/main: Merge made by the 'ort' strategy.
379ff82a1a5176f446ea571251a431ef878e0aa3 fb6dc6aa7203da7cc78b417df57e3c908034761e Sim Zhen Quan <<EMAIL>> 1742541563 +0800	checkout: moving from staging/2025-03-17 to leave-application-enhancements
fb6dc6aa7203da7cc78b417df57e3c908034761e 379ff82a1a5176f446ea571251a431ef878e0aa3 Sim Zhen Quan <<EMAIL>> 1742542492 +0800	checkout: moving from leave-application-enhancements to staging/2025-03-17
379ff82a1a5176f446ea571251a431ef878e0aa3 379ff82a1a5176f446ea571251a431ef878e0aa3 Sim Zhen Quan <<EMAIL>> 1742542505 +0800	checkout: moving from staging/2025-03-17 to staging/2025-03-17
379ff82a1a5176f446ea571251a431ef878e0aa3 fb6dc6aa7203da7cc78b417df57e3c908034761e Sim Zhen Quan <<EMAIL>> 1742542552 +0800	checkout: moving from staging/2025-03-17 to leave-application-enhancements
fb6dc6aa7203da7cc78b417df57e3c908034761e ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa Sim Zhen Quan <<EMAIL>> 1742542553 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa 1cf01439175a955d8852955112743e303c1158c0 Sim Zhen Quan <<EMAIL>> 1742546829 +0800	checkout: moving from leave-application-enhancements to fix/billing-document-enhancement
1cf01439175a955d8852955112743e303c1158c0 2dd51ccba8daaf64827a8eabbc1b830b8f96d90e Sim Zhen Quan <<EMAIL>> 1742546835 +0800	pull: Fast-forward
2dd51ccba8daaf64827a8eabbc1b830b8f96d90e ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa Sim Zhen Quan <<EMAIL>> 1742550615 +0800	checkout: moving from fix/billing-document-enhancement to leave-application-enhancements
ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa 2ca3e07e73244c2d992da4eff958905ca3558a6c Sim Zhen Quan <<EMAIL>> 1742550621 +0800	pull: Fast-forward
2ca3e07e73244c2d992da4eff958905ca3558a6c 9e18aff62d7190217e690f8c9beb3b3898a96243 Sim Zhen Quan <<EMAIL>> 1742738427 +0800	commit: Reviewed. Check comments on slack.
9e18aff62d7190217e690f8c9beb3b3898a96243 1a889c792bc9f7ed59b5312753b701fcaa2cc36a Sim Zhen Quan <<EMAIL>> 1742742256 +0800	commit: Added function in interface
1a889c792bc9f7ed59b5312753b701fcaa2cc36a 1c6f001d71e5b6bb3c9ac56af292592dc814cfbb Sim Zhen Quan <<EMAIL>> 1742742979 +0800	checkout: moving from leave-application-enhancements to main
1c6f001d71e5b6bb3c9ac56af292592dc814cfbb 07a23bfa559338fc93ebea54086d799ce9ece419 Sim Zhen Quan <<EMAIL>> 1742742984 +0800	pull: Fast-forward
07a23bfa559338fc93ebea54086d799ce9ece419 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742782975 +0800	commit: Deployed to prd
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce 1a889c792bc9f7ed59b5312753b701fcaa2cc36a Sim Zhen Quan <<EMAIL>> 1742783010 +0800	checkout: moving from main to leave-application-enhancements
1a889c792bc9f7ed59b5312753b701fcaa2cc36a 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742798368 +0800	checkout: moving from leave-application-enhancements to main
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce 0b5f64b93ea5c4e283918371397d2fadd9b3929c Sim Zhen Quan <<EMAIL>> 1742798409 +0800	checkout: moving from main to dev
0b5f64b93ea5c4e283918371397d2fadd9b3929c 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742798444 +0800	checkout: moving from dev to main
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce c0a666403e8e0e57eddfa9698dcb9281d07dd1b5 Sim Zhen Quan <<EMAIL>> 1742798500 +0800	checkout: moving from main to pos-check-charge-status
c0a666403e8e0e57eddfa9698dcb9281d07dd1b5 1ee2ecbb4f585cbd2c1f102444441415d9c09017 Sim Zhen Quan <<EMAIL>> 1742798767 +0800	commit (merge): Merge origin/main
1ee2ecbb4f585cbd2c1f102444441415d9c09017 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742799388 +0800	checkout: moving from pos-check-charge-status to main
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce 2054a6adcce6264d9de4e86aafd0e1706ed83834 Sim Zhen Quan <<EMAIL>> 1742799425 +0800	commit: Bug fix
2054a6adcce6264d9de4e86aafd0e1706ed83834 b7f41a58b5282cbf2f1958ba56ee366644340149 Sim Zhen Quan <<EMAIL>> 1742801788 +0800	checkout: moving from main to migrate-script-timetable
b7f41a58b5282cbf2f1958ba56ee366644340149 c333b0a4c006b75aef400b9d8cf1255c7338175e Sim Zhen Quan <<EMAIL>> 1742801796 +0800	merge origin/main: Merge made by the 'ort' strategy.
c333b0a4c006b75aef400b9d8cf1255c7338175e 1ee2ecbb4f585cbd2c1f102444441415d9c09017 Sim Zhen Quan <<EMAIL>> 1742833693 +0800	checkout: moving from migrate-script-timetable to pos-check-charge-status
1ee2ecbb4f585cbd2c1f102444441415d9c09017 4ddab932918ddc9c72edfc5f2a8e02265e9663ad Sim Zhen Quan <<EMAIL>> 1742833719 +0800	merge origin/main: Merge made by the 'ort' strategy.
4ddab932918ddc9c72edfc5f2a8e02265e9663ad 2054a6adcce6264d9de4e86aafd0e1706ed83834 Sim Zhen Quan <<EMAIL>> 1742833761 +0800	checkout: moving from pos-check-charge-status to main
2054a6adcce6264d9de4e86aafd0e1706ed83834 9ebc71b626650133146be5ba5aeeb39c922a87e3 Sim Zhen Quan <<EMAIL>> 1742833766 +0800	pull: Fast-forward
9ebc71b626650133146be5ba5aeeb39c922a87e3 8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 Sim Zhen Quan <<EMAIL>> 1742834036 +0800	commit: Deployed to prd
8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 c8e9d9fd65fd7f4cdef8979f52a92aed79949526 Sim Zhen Quan <<EMAIL>> 1742834045 +0800	checkout: moving from main to migrate-script-society-position
c8e9d9fd65fd7f4cdef8979f52a92aed79949526 2583f3eca374ae6dd6fa2520cded4e44bf254368 Sim Zhen Quan <<EMAIL>> 1742834267 +0800	commit (merge): Merge origin/main
2583f3eca374ae6dd6fa2520cded4e44bf254368 f014dce0edd250c29354a26ea40076de92ac888d Sim Zhen Quan <<EMAIL>> 1742838247 +0800	commit: Reviewed
f014dce0edd250c29354a26ea40076de92ac888d ab875585d341330ead57a665c9b8c9923e1657e0 Sim Zhen Quan <<EMAIL>> 1742838316 +0800	checkout: moving from migrate-script-society-position to migrate-script-comprehensive-assessment
ab875585d341330ead57a665c9b8c9923e1657e0 5de802f1384a587e521ad07c8d90a4c59413bd30 Sim Zhen Quan <<EMAIL>> 1742838382 +0800	commit (merge): Merge origin/main
5de802f1384a587e521ad07c8d90a4c59413bd30 f9514475c72faa081bee03e314723fcce442cb57 Sim Zhen Quan <<EMAIL>> 1742838589 +0800	commit: Reviewed
f9514475c72faa081bee03e314723fcce442cb57 78ec47586b2aba316dd8a07bbdc535e437d89040 Sim Zhen Quan <<EMAIL>> 1742838653 +0800	checkout: moving from migrate-script-comprehensive-assessment to feature/report-card-custom-functions
78ec47586b2aba316dd8a07bbdc535e437d89040 0aed1973b503e1426591ec1a68f8e6b90dcedd59 Sim Zhen Quan <<EMAIL>> 1742838657 +0800	pull: Fast-forward
0aed1973b503e1426591ec1a68f8e6b90dcedd59 f56a99196355190613de5ebfc51c5ee7d0859ff0 Sim Zhen Quan <<EMAIL>> 1742838658 +0800	merge origin/main: Merge made by the 'ort' strategy.
f56a99196355190613de5ebfc51c5ee7d0859ff0 0b5f64b93ea5c4e283918371397d2fadd9b3929c Sim Zhen Quan <<EMAIL>> 1742838710 +0800	checkout: moving from feature/report-card-custom-functions to dev
0b5f64b93ea5c4e283918371397d2fadd9b3929c eea49ddbfd71b206acd7b0b76d3bbd48ee55f091 Sim Zhen Quan <<EMAIL>> 1742838715 +0800	merge feature/report-card-custom-functions: Merge made by the 'ort' strategy.
eea49ddbfd71b206acd7b0b76d3bbd48ee55f091 d4d8517b3d88ba23a11d9a6f831e0a68b725d68a Sim Zhen Quan <<EMAIL>> 1742873506 +0800	commit: Deployed to dev
d4d8517b3d88ba23a11d9a6f831e0a68b725d68a 8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 Sim Zhen Quan <<EMAIL>> 1742874091 +0800	checkout: moving from dev to main
8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 12311d37bc6d65b2e1ddb8f7fe5495b970ac5f90 Sim Zhen Quan <<EMAIL>> 1742874099 +0800	pull: Fast-forward
12311d37bc6d65b2e1ddb8f7fe5495b970ac5f90 6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 Sim Zhen Quan <<EMAIL>> 1742874633 +0800	commit: Class subject added semester_setting_id
6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 Sim Zhen Quan <<EMAIL>> 1742888407 +0800	reset: moving to HEAD
6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 2dd51ccba8daaf64827a8eabbc1b830b8f96d90e Sim Zhen Quan <<EMAIL>> 1742888408 +0800	checkout: moving from main to fix/billing-document-enhancement
2dd51ccba8daaf64827a8eabbc1b830b8f96d90e 60ee8a404d40159728bcb702321b80b08e685c66 Sim Zhen Quan <<EMAIL>> 1742888418 +0800	merge origin/main: Merge made by the 'ort' strategy.
60ee8a404d40159728bcb702321b80b08e685c66 7ddcea58f310ae8c7dcae92050602be96322b64a Sim Zhen Quan <<EMAIL>> 1742892765 +0800	commit: Reviewed
7ddcea58f310ae8c7dcae92050602be96322b64a d4d8517b3d88ba23a11d9a6f831e0a68b725d68a Sim Zhen Quan <<EMAIL>> 1742892856 +0800	checkout: moving from fix/billing-document-enhancement to dev
d4d8517b3d88ba23a11d9a6f831e0a68b725d68a c26c3b0a603c9bde79a928eea8cb1eded149653f Sim Zhen Quan <<EMAIL>> 1742892902 +0800	commit (merge): Merge origin/main
c26c3b0a603c9bde79a928eea8cb1eded149653f d96f45ac51e55e673e004185c590783d195a51a9 Sim Zhen Quan <<EMAIL>> 1742892954 +0800	commit: Fix library import
d96f45ac51e55e673e004185c590783d195a51a9 3b9b54534a31a407cf6483673ea47561c7fe0ba5 Sim Zhen Quan <<EMAIL>> 1742893720 +0800	commit: Deployed to DEV
3b9b54534a31a407cf6483673ea47561c7fe0ba5 f36b21d8a37d18668ecae11c7f7839c8069e8a04 Sim Zhen Quan <<EMAIL>> 1742893740 +0800	checkout: moving from dev to migration-fees
f36b21d8a37d18668ecae11c7f7839c8069e8a04 ca988a8ff6e1fec22c4a39e031502e9cc7f015e3 Sim Zhen Quan <<EMAIL>> 1742893951 +0800	commit (merge): Merge origin/main
ca988a8ff6e1fec22c4a39e031502e9cc7f015e3 174367e2a96616e843c48f9a23b9086e26989f96 Sim Zhen Quan <<EMAIL>> 1742901274 +0800	commit: Reviewed
174367e2a96616e843c48f9a23b9086e26989f96 b11aefa89a0d2983e1847bdef6544c77517257d4 Sim Zhen Quan <<EMAIL>> 1742901304 +0800	checkout: moving from migration-fees to qas
b11aefa89a0d2983e1847bdef6544c77517257d4 15765ed8d07bd7f61fdff11e4137171282c88004 Sim Zhen Quan <<EMAIL>> 1742901312 +0800	merge origin/main: Merge made by the 'ort' strategy.
15765ed8d07bd7f61fdff11e4137171282c88004 0fca5ea266186bd2b4063e18eb1b5d4781894c8d Sim Zhen Quan <<EMAIL>> 1742922504 +0800	commit: Deployed to qas
0fca5ea266186bd2b4063e18eb1b5d4781894c8d 6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 Sim Zhen Quan <<EMAIL>> 1742922516 +0800	checkout: moving from qas to main
6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 5ab269abc7a5aef7179b69be49d6e7f86d8d63cb Sim Zhen Quan <<EMAIL>> 1742922527 +0800	merge origin/main: Fast-forward
5ab269abc7a5aef7179b69be49d6e7f86d8d63cb 430c99f3301d86ed4033559c6230fa847514b318 Sim Zhen Quan <<EMAIL>> 1742923317 +0800	commit: Fix test cases
430c99f3301d86ed4033559c6230fa847514b318 f56a99196355190613de5ebfc51c5ee7d0859ff0 Sim Zhen Quan <<EMAIL>> 1742955719 +0800	checkout: moving from main to feature/report-card-custom-functions
f56a99196355190613de5ebfc51c5ee7d0859ff0 f46c985ad25cd47346010486bae71379668a5164 Sim Zhen Quan <<EMAIL>> 1742955823 +0800	merge origin/main: Merge made by the 'ort' strategy.
f46c985ad25cd47346010486bae71379668a5164 2f078a4654f988427650fcb06e874f08867c85f2 Sim Zhen Quan <<EMAIL>> 1742957630 +0800	checkout: moving from feature/report-card-custom-functions to feature/qr-code-payment
2f078a4654f988427650fcb06e874f08867c85f2 2f078a4654f988427650fcb06e874f08867c85f2 Sim Zhen Quan <<EMAIL>> 1742957634 +0800	checkout: moving from feature/qr-code-payment to feature/qr-code-payment
2f078a4654f988427650fcb06e874f08867c85f2 c5abf670f1acba6de19c8850953f2884d82e5dc7 Sim Zhen Quan <<EMAIL>> 1742958313 +0800	commit (merge): Merge origin/main
c5abf670f1acba6de19c8850953f2884d82e5dc7 3784c3a3a6bc8e41bb4d8641a89865fb2e3c8efd Sim Zhen Quan <<EMAIL>> 1742958931 +0800	commit: Review WIP
3784c3a3a6bc8e41bb4d8641a89865fb2e3c8efd 8bd52234c0ea8613fecd734a6387f1f8b316044e Sim Zhen Quan <<EMAIL>> 1742959874 +0800	checkout: moving from feature/qr-code-payment to exam-leftovers
8bd52234c0ea8613fecd734a6387f1f8b316044e 88896b84e12120f68047de724ab56b5bac785cc0 Sim Zhen Quan <<EMAIL>> 1742959881 +0800	merge origin/main: Merge made by the 'ort' strategy.
88896b84e12120f68047de724ab56b5bac785cc0 46b32420ebbe8c00143bb769e4efa9838d67c608 Sim Zhen Quan <<EMAIL>> 1742963487 +0800	checkout: moving from exam-leftovers to migration-reward-punishment
46b32420ebbe8c00143bb769e4efa9838d67c608 eff54e0cf27659f21a66915b1902454e23f43616 Sim Zhen Quan <<EMAIL>> 1742963724 +0800	commit (merge): Merge origin/main
eff54e0cf27659f21a66915b1902454e23f43616 de36acf5350beb9cec97fbf5ea4bf13584ab83f6 Sim Zhen Quan <<EMAIL>> 1742972734 +0800	commit: Reviewed
de36acf5350beb9cec97fbf5ea4bf13584ab83f6 88896b84e12120f68047de724ab56b5bac785cc0 Sim Zhen Quan <<EMAIL>> 1742972805 +0800	checkout: moving from migration-reward-punishment to exam-leftovers
88896b84e12120f68047de724ab56b5bac785cc0 dd860ed1de261120125a09c88b8c62fad87bfb77 Sim Zhen Quan <<EMAIL>> 1742972811 +0800	pull: Fast-forward
dd860ed1de261120125a09c88b8c62fad87bfb77 72356de7262cd072f127a2774ba60ac2488dd3c3 Sim Zhen Quan <<EMAIL>> 1742972834 +0800	merge origin/main: Merge made by the 'ort' strategy.
72356de7262cd072f127a2774ba60ac2488dd3c3 ae3947bc0e586f850f80e5768062ed4f54c6b5e7 Sim Zhen Quan <<EMAIL>> 1742976082 +0800	commit: Review WIP
ae3947bc0e586f850f80e5768062ed4f54c6b5e7 3b9b54534a31a407cf6483673ea47561c7fe0ba5 Sim Zhen Quan <<EMAIL>> 1742976128 +0800	checkout: moving from exam-leftovers to dev
3b9b54534a31a407cf6483673ea47561c7fe0ba5 bbd8d29c6da0155a99fb82244b878de4e39c9171 Sim Zhen Quan <<EMAIL>> 1742976133 +0800	merge exam-leftovers: Merge made by the 'ort' strategy.
bbd8d29c6da0155a99fb82244b878de4e39c9171 bbd8d29c6da0155a99fb82244b878de4e39c9171 Sim Zhen Quan <<EMAIL>> 1742976162 +0800	checkout: moving from dev to dev
bbd8d29c6da0155a99fb82244b878de4e39c9171 cc6bd07539a9ccd2123c8a7ada767209c7b235e8 Sim Zhen Quan <<EMAIL>> 1742983518 +0800	commit: Deployed to dev
cc6bd07539a9ccd2123c8a7ada767209c7b235e8 b5defef3c60b3cc43d8dbe2f95cd1413f018f304 Sim Zhen Quan <<EMAIL>> 1742983522 +0800	checkout: moving from dev to fix-get-class-attendance-bug
b5defef3c60b3cc43d8dbe2f95cd1413f018f304 b5defef3c60b3cc43d8dbe2f95cd1413f018f304 Sim Zhen Quan <<EMAIL>> 1742983525 +0800	reset: moving to HEAD
b5defef3c60b3cc43d8dbe2f95cd1413f018f304 888b00033cdc8516eeab07bbaf7da04b3a3a0d71 Sim Zhen Quan <<EMAIL>> 1742983637 +0800	commit: Bug fix
888b00033cdc8516eeab07bbaf7da04b3a3a0d71 e0553b818a3b75f6e9dede8be350d461fe8a2749 Sim Zhen Quan <<EMAIL>> 1742983783 +0800	merge origin/main: Merge made by the 'ort' strategy.
e0553b818a3b75f6e9dede8be350d461fe8a2749 ae3947bc0e586f850f80e5768062ed4f54c6b5e7 Sim Zhen Quan <<EMAIL>> 1742984627 +0800	checkout: moving from fix-get-class-attendance-bug to exam-leftovers
ae3947bc0e586f850f80e5768062ed4f54c6b5e7 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1742984632 +0800	merge origin/main: Merge made by the 'ort' strategy.
a2a28ccce07d71a276d213e0a96987d36d7e2793 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1742996371 +0800	reset: moving to HEAD
a2a28ccce07d71a276d213e0a96987d36d7e2793 cc6bd07539a9ccd2123c8a7ada767209c7b235e8 Sim Zhen Quan <<EMAIL>> 1742996373 +0800	checkout: moving from exam-leftovers to dev
cc6bd07539a9ccd2123c8a7ada767209c7b235e8 0c8650f5cf44bfcd681ba525e80445214a33e7ef Sim Zhen Quan <<EMAIL>> 1742996394 +0800	merge origin/main: Merge made by the 'ort' strategy.
0c8650f5cf44bfcd681ba525e80445214a33e7ef 430c99f3301d86ed4033559c6230fa847514b318 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to main
430c99f3301d86ed4033559c6230fa847514b318 4c5623fb7be96611042c2e9a3020e8f9751b2740 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
4c5623fb7be96611042c2e9a3020e8f9751b2740 2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to fix/accounting-bugs
2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 2781d03486501d087f78a16d5f50051926d263a8 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/accounting-bugs to migrate-script-attendence
2781d03486501d087f78a16d5f50051926d263a8 2840309e28638c0c2728729e2377f1c7b2ac3e1a Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Added report card queue
2840309e28638c0c2728729e2377f1c7b2ac3e1a bddc8a7a5730cc857618f76576c35b9739b57110 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
bddc8a7a5730cc857618f76576c35b9739b57110 f8c25b32031e3bc64cf81365946acbc90c6561a1 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Remove late status
f8c25b32031e3bc64cf81365946acbc90c6561a1 710a70473dbcf09d411d2d43672adf3a0f16fbf9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from migrate-script-attendence to feature/conduct-setting-migration
710a70473dbcf09d411d2d43672adf3a0f16fbf9 f301bc42d0fec7cf077a52d687ec9238a2c1baba Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
f301bc42d0fec7cf077a52d687ec9238a2c1baba 72e4d06faab17cc054700b19fdbd76b1d1757f59 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Change conduct code
72e4d06faab17cc054700b19fdbd76b1d1757f59 9b58ea2efe29f79a9d314efc304d6c3f21778073 Sim Zhen Quan <<EMAIL>> 1743055274 +0800	commit: Reviewed
9b58ea2efe29f79a9d314efc304d6c3f21778073 f8c25b32031e3bc64cf81365946acbc90c6561a1 Sim Zhen Quan <<EMAIL>> 1743055316 +0800	checkout: moving from feature/conduct-setting-migration to migrate-script-attendence
f8c25b32031e3bc64cf81365946acbc90c6561a1 7b96bcd8394734cbdaad706266baf77f87be3bf6 Sim Zhen Quan <<EMAIL>> 1743055939 +0800	commit (merge): Merge origin/main
7b96bcd8394734cbdaad706266baf77f87be3bf6 28c8cd969dddeea1b335bc70ca92e12c3dfdb4f6 Sim Zhen Quan <<EMAIL>> 1743062154 +0800	commit: Reviewed
28c8cd969dddeea1b335bc70ca92e12c3dfdb4f6 0fca5ea266186bd2b4063e18eb1b5d4781894c8d Sim Zhen Quan <<EMAIL>> 1743068590 +0800	checkout: moving from migrate-script-attendence to qas
0fca5ea266186bd2b4063e18eb1b5d4781894c8d 06256fafdd1173b7078ee05be8cf7b08eee91002 Sim Zhen Quan <<EMAIL>> 1743068601 +0800	merge origin/main: Merge made by the 'ort' strategy.
06256fafdd1173b7078ee05be8cf7b08eee91002 64df3b6d18727060093551e92d5051fa1097e2d9 Sim Zhen Quan <<EMAIL>> 1743069877 +0800	commit: Deployed to qas
64df3b6d18727060093551e92d5051fa1097e2d9 4c5623fb7be96611042c2e9a3020e8f9751b2740 Sim Zhen Quan <<EMAIL>> 1743069905 +0800	checkout: moving from qas to main
4c5623fb7be96611042c2e9a3020e8f9751b2740 6122501c07bef394607b55ccf356bf0e78a52cc4 Sim Zhen Quan <<EMAIL>> 1743069910 +0800	pull: Fast-forward
6122501c07bef394607b55ccf356bf0e78a52cc4 b207bc9e50ef8b71be7038147d6d843d53acc80d Sim Zhen Quan <<EMAIL>> 1743070030 +0800	commit: Added posting in migration
b207bc9e50ef8b71be7038147d6d843d53acc80d 1a322d04123d307845730dff613f30a1ea9d66d2 Sim Zhen Quan <<EMAIL>> 1743070089 +0800	checkout: moving from main to attendance-enhancement-v2
1a322d04123d307845730dff613f30a1ea9d66d2 6d7cf2cb19aaefe92c0bcf599f5241f71a2ef08e Sim Zhen Quan <<EMAIL>> 1743070141 +0800	commit (merge): Merge origin/main
6d7cf2cb19aaefe92c0bcf599f5241f71a2ef08e 0756ad92e9c723415afc1cfdc652719eac729b8d Sim Zhen Quan <<EMAIL>> 1743073840 +0800	commit: Review WIP
0756ad92e9c723415afc1cfdc652719eac729b8d c333b0a4c006b75aef400b9d8cf1255c7338175e Sim Zhen Quan <<EMAIL>> 1743073863 +0800	checkout: moving from attendance-enhancement-v2 to migrate-script-timetable
c333b0a4c006b75aef400b9d8cf1255c7338175e b46aa10e59402bc90c8e151a7a8db9f40121b3b2 Sim Zhen Quan <<EMAIL>> 1743074085 +0800	commit (merge): Merge origin/main
b46aa10e59402bc90c8e151a7a8db9f40121b3b2 b207bc9e50ef8b71be7038147d6d843d53acc80d Sim Zhen Quan <<EMAIL>> 1743090104 +0800	checkout: moving from migrate-script-timetable to main
b207bc9e50ef8b71be7038147d6d843d53acc80d 7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 Sim Zhen Quan <<EMAIL>> 1743091037 +0800	commit: Temporarily update hostel reward punishment order
7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743091052 +0800	checkout: moving from main to exam-leftovers
a2a28ccce07d71a276d213e0a96987d36d7e2793 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743091075 +0800	checkout: moving from exam-leftovers to grading-framework-update-enhancements
a2a28ccce07d71a276d213e0a96987d36d7e2793 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743092340 +0800	reset: moving to HEAD
a2a28ccce07d71a276d213e0a96987d36d7e2793 7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 Sim Zhen Quan <<EMAIL>> 1743092340 +0800	checkout: moving from grading-framework-update-enhancements to main
7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 Sim Zhen Quan <<EMAIL>> 1743093087 +0800	commit: Temporarily update reward punishment order
9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743093097 +0800	checkout: moving from main to grading-framework-update-enhancements
a2a28ccce07d71a276d213e0a96987d36d7e2793 137c69a9b0a683558749fd024f7ccb086138bd46 Sim Zhen Quan <<EMAIL>> 1743100641 +0800	commit: Added update grading framework behavior
137c69a9b0a683558749fd024f7ccb086138bd46 59fc9005f877359c46cde96367966f4f8787a396 Sim Zhen Quan <<EMAIL>> 1743127567 +0800	commit: Added TODOs
59fc9005f877359c46cde96367966f4f8787a396 b46aa10e59402bc90c8e151a7a8db9f40121b3b2 Sim Zhen Quan <<EMAIL>> 1743131892 +0800	checkout: moving from grading-framework-update-enhancements to migrate-script-timetable
b46aa10e59402bc90c8e151a7a8db9f40121b3b2 b46aa10e59402bc90c8e151a7a8db9f40121b3b2 Sim Zhen Quan <<EMAIL>> 1743131898 +0800	merge origin/main: updating HEAD
b46aa10e59402bc90c8e151a7a8db9f40121b3b2 f288f43992e182b162459cb94ffdd88e88725194 Sim Zhen Quan <<EMAIL>> 1743133587 +0800	merge origin/main: Merge made by the 'ort' strategy.
f288f43992e182b162459cb94ffdd88e88725194 902d375ac276e3c357e4ee6f55a1997d852af31f Sim Zhen Quan <<EMAIL>> 1743148532 +0800	commit: Reviewed
902d375ac276e3c357e4ee6f55a1997d852af31f 0820a7ca2a7b81d043387ade2cb8e17d7323e24b Sim Zhen Quan <<EMAIL>> 1743149475 +0800	checkout: moving from migrate-script-timetable to announcement-index-reduce-response-size
0820a7ca2a7b81d043387ade2cb8e17d7323e24b 4f7102a9d777270f6859662d0fb3c6cd8eced1ad Sim Zhen Quan <<EMAIL>> 1743149480 +0800	merge origin/main: Merge made by the 'ort' strategy.
4f7102a9d777270f6859662d0fb3c6cd8eced1ad 811a2d22661d6dd9a74eebcb169944bffcad73c2 Sim Zhen Quan <<EMAIL>> 1743150802 +0800	commit: Fixed some test case
811a2d22661d6dd9a74eebcb169944bffcad73c2 9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 Sim Zhen Quan <<EMAIL>> 1743488538 +0800	checkout: moving from announcement-index-reduce-response-size to main
9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 a44125b11c443cb226dbc4db2954f4f85c25bdc0 Sim Zhen Quan <<EMAIL>> 1743488546 +0800	pull: Fast-forward
a44125b11c443cb226dbc4db2954f4f85c25bdc0 a44125b11c443cb226dbc4db2954f4f85c25bdc0 Sim Zhen Quan <<EMAIL>> 1743488560 +0800	reset: moving to HEAD
a44125b11c443cb226dbc4db2954f4f85c25bdc0 f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 Sim Zhen Quan <<EMAIL>> 1743488561 +0800	checkout: moving from main to migrate-script-period_attendances
f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 Sim Zhen Quan <<EMAIL>> 1743488576 +0800	reset: moving to HEAD
f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 8ec413c80b3f95ccc2d6118a0f128b7e8107479b Sim Zhen Quan <<EMAIL>> 1743488668 +0800	commit (merge): Merge origin/main
8ec413c80b3f95ccc2d6118a0f128b7e8107479b 0733ad4e7901275a56dbc232924d8ac8f381af02 Sim Zhen Quan <<EMAIL>> 1743488868 +0800	commit: Bug fixes
0733ad4e7901275a56dbc232924d8ac8f381af02 60f5f2fdfff04443e087b3e0db99b8e522a4ac3b Sim Zhen Quan <<EMAIL>> 1743519958 +0800	commit: Reviewed
60f5f2fdfff04443e087b3e0db99b8e522a4ac3b a44125b11c443cb226dbc4db2954f4f85c25bdc0 Sim Zhen Quan <<EMAIL>> 1743520045 +0800	checkout: moving from migrate-script-period_attendances to main
a44125b11c443cb226dbc4db2954f4f85c25bdc0 64bf2da30b0fec631722b5da4aba4ed3ad72989a Sim Zhen Quan <<EMAIL>> 1743520053 +0800	pull: Fast-forward
64bf2da30b0fec631722b5da4aba4ed3ad72989a 48ceb328926fd71a8c406012a2cccb4b4322779f Sim Zhen Quan <<EMAIL>> 1743520814 +0800	commit: Deployed to PRD
48ceb328926fd71a8c406012a2cccb4b4322779f 0756ad92e9c723415afc1cfdc652719eac729b8d Sim Zhen Quan <<EMAIL>> 1743520927 +0800	checkout: moving from main to attendance-enhancement-v2
0756ad92e9c723415afc1cfdc652719eac729b8d e6e145f4a71107bc8bea0b72b31f4f2ca461c269 Sim Zhen Quan <<EMAIL>> 1743520937 +0800	merge origin/main: Merge made by the 'ort' strategy.
e6e145f4a71107bc8bea0b72b31f4f2ca461c269 78dfa216994fbf3edefe02c8cfb644315b3540d3 Sim Zhen Quan <<EMAIL>> 1743529510 +0800	commit: Reviewed
78dfa216994fbf3edefe02c8cfb644315b3540d3 0c8650f5cf44bfcd681ba525e80445214a33e7ef Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from attendance-enhancement-v2 to dev
0c8650f5cf44bfcd681ba525e80445214a33e7ef 5a9fa996baa07ef859326166d87a42c00b6149b1 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
5a9fa996baa07ef859326166d87a42c00b6149b1 2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to fix/accounting-bugs
2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 1cd8e32af77dcb69b29b257d293d7086e7f20c41 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
1cd8e32af77dcb69b29b257d293d7086e7f20c41 5a9fa996baa07ef859326166d87a42c00b6149b1 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/accounting-bugs to dev
5a9fa996baa07ef859326166d87a42c00b6149b1 915ffca17506332c96d76482815d3839ca736a06 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
915ffca17506332c96d76482815d3839ca736a06 fe411aa37108d427bf91ea6223330264a5287e35 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
fe411aa37108d427bf91ea6223330264a5287e35 ef3d1d7f5124751fb3682aa06d873d04fa223e6f Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to feature/promotion-mark-CRUD
ef3d1d7f5124751fb3682aa06d873d04fa223e6f 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
63148ac69c3f6985a1ce56cb491c6346c6b116de 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/promotion-mark-CRUD to feature/promotion-mark-CRUD
63148ac69c3f6985a1ce56cb491c6346c6b116de 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> 1743561005 +0800	checkout: moving from feature/promotion-mark-CRUD to feature/promotion-mark-CRUD
63148ac69c3f6985a1ce56cb491c6346c6b116de faf096bf0be6aebe9e576a04bf2b49647f9a73e2 Sim Zhen Quan <<EMAIL>> 1743565464 +0800	checkout: moving from feature/promotion-mark-CRUD to SCRUM-256-Export-For-AutoCount
faf096bf0be6aebe9e576a04bf2b49647f9a73e2 85d4d73543d96d5b3cd372b58f1203a465ecb0cf Sim Zhen Quan <<EMAIL>> 1743565967 +0800	commit (merge): Merge origin/main
85d4d73543d96d5b3cd372b58f1203a465ecb0cf 85d4d73543d96d5b3cd372b58f1203a465ecb0cf Sim Zhen Quan <<EMAIL>> 1743609941 +0800	reset: moving to HEAD
85d4d73543d96d5b3cd372b58f1203a465ecb0cf 64df3b6d18727060093551e92d5051fa1097e2d9 Sim Zhen Quan <<EMAIL>> 1743609944 +0800	checkout: moving from SCRUM-256-Export-For-AutoCount to qas
64df3b6d18727060093551e92d5051fa1097e2d9 6a87aec64efe2453533311eefc21d39f8ac59cf0 Sim Zhen Quan <<EMAIL>> 1743609949 +0800	merge origin/main: Merge made by the 'ort' strategy.
6a87aec64efe2453533311eefc21d39f8ac59cf0 9c94108ee5a3a9c4cde63c8808009a42f8cea865 Sim Zhen Quan <<EMAIL>> 1743610082 +0800	commit: Deployed to qas
9c94108ee5a3a9c4cde63c8808009a42f8cea865 fe411aa37108d427bf91ea6223330264a5287e35 Sim Zhen Quan <<EMAIL>> 1743643191 +0800	checkout: moving from qas to dev
fe411aa37108d427bf91ea6223330264a5287e35 85d4d73543d96d5b3cd372b58f1203a465ecb0cf Sim Zhen Quan <<EMAIL>> 1743643456 +0800	checkout: moving from dev to SCRUM-256-Export-For-AutoCount
85d4d73543d96d5b3cd372b58f1203a465ecb0cf aa79db3e0977dfc87ad886354efda48f5dd717ae Sim Zhen Quan <<EMAIL>> 1743643635 +0800	merge origin/main: Merge made by the 'ort' strategy.
aa79db3e0977dfc87ad886354efda48f5dd717ae a9e12ce34d7323f3c56a68095c6190907e8e1c1e Sim Zhen Quan <<EMAIL>> 1743645826 +0800	commit: Reviewed
a9e12ce34d7323f3c56a68095c6190907e8e1c1e fe411aa37108d427bf91ea6223330264a5287e35 Sim Zhen Quan <<EMAIL>> 1743646111 +0800	checkout: moving from SCRUM-256-Export-For-AutoCount to dev
fe411aa37108d427bf91ea6223330264a5287e35 f3e21203d046a2943b24363018c44143386f9fae Sim Zhen Quan <<EMAIL>> 1743646116 +0800	merge origin/main: Merge made by the 'ort' strategy.
f3e21203d046a2943b24363018c44143386f9fae 7974add18d0988cf20949852621cf3b1abf3db98 Sim Zhen Quan <<EMAIL>> 1743647091 +0800	commit: Deployed to qas
7974add18d0988cf20949852621cf3b1abf3db98 fbb539e6f0d42a1b6507a672f5d939ed2074eda8 Sim Zhen Quan <<EMAIL>> 1743647102 +0800	checkout: moving from dev to fix/hostel-uat-feedback
fbb539e6f0d42a1b6507a672f5d939ed2074eda8 7a1e9a9634002503ab5aa57815aa691faa66316d Sim Zhen Quan <<EMAIL>> 1743649404 +0800	commit (merge): Merge origin/main
7a1e9a9634002503ab5aa57815aa691faa66316d b63b33bb135b2029609d19cfc1b3f6654fc5eba9 Sim Zhen Quan <<EMAIL>> 1743653742 +0800	commit: Reviewed
b63b33bb135b2029609d19cfc1b3f6654fc5eba9 7974add18d0988cf20949852621cf3b1abf3db98 Sim Zhen Quan <<EMAIL>> 1743653956 +0800	checkout: moving from fix/hostel-uat-feedback to dev
7974add18d0988cf20949852621cf3b1abf3db98 0bb74c2c0532fed403f9feaa148774434afdefdf Sim Zhen Quan <<EMAIL>> 1743660913 +0800	merge origin/main: Merge made by the 'ort' strategy.
0bb74c2c0532fed403f9feaa148774434afdefdf 2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 Sim Zhen Quan <<EMAIL>> 1743661975 +0800	commit: Deployed to dev
2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 b7ca76256d12f53cf0e6de1ad8751ddc232c0640 Sim Zhen Quan <<EMAIL>> 1743662099 +0800	checkout: moving from dev to migrate-script-leadership
b7ca76256d12f53cf0e6de1ad8751ddc232c0640 a80298275028d2146c1869a6626ef8389f09b937 Sim Zhen Quan <<EMAIL>> 1743662107 +0800	merge origin/main: Merge made by the 'ort' strategy.
a80298275028d2146c1869a6626ef8389f09b937 1a8e1a3554fd8c29b2e6628f6fc284d071042628 Sim Zhen Quan <<EMAIL>> 1743670137 +0800	commit: Reviewed
1a8e1a3554fd8c29b2e6628f6fc284d071042628 2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 Sim Zhen Quan <<EMAIL>> 1743670166 +0800	checkout: moving from migrate-script-leadership to dev
2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 72421c40ac864b308d0b56ff6b536e0fdbbcdcb2 Sim Zhen Quan <<EMAIL>> 1743670175 +0800	merge origin/main: Merge made by the 'ort' strategy.
72421c40ac864b308d0b56ff6b536e0fdbbcdcb2 11c71f912ed874bb5bb2990bf0ff7e27d81fe719 Sim Zhen Quan <<EMAIL>> 1743670551 +0800	commit: Deployed to dev
11c71f912ed874bb5bb2990bf0ff7e27d81fe719 11c71f912ed874bb5bb2990bf0ff7e27d81fe719 Sim Zhen Quan <<EMAIL>> 1743671374 +0800	reset: moving to HEAD
11c71f912ed874bb5bb2990bf0ff7e27d81fe719 48ceb328926fd71a8c406012a2cccb4b4322779f Sim Zhen Quan <<EMAIL>> 1743671377 +0800	checkout: moving from dev to main
48ceb328926fd71a8c406012a2cccb4b4322779f 9319430047f4d24df8d1783658fc189438c7c2eb Sim Zhen Quan <<EMAIL>> 1743671382 +0800	pull: Fast-forward
9319430047f4d24df8d1783658fc189438c7c2eb 41d6c3d5a9d1ff4611caea78bb136a813ba9e241 Sim Zhen Quan <<EMAIL>> 1743672617 +0800	commit: Added extra validation handling
41d6c3d5a9d1ff4611caea78bb136a813ba9e241 04eb3f1a2b71e6793e52c153a8672569ab1d1270 Sim Zhen Quan <<EMAIL>> 1743672651 +0800	checkout: moving from main to SCRUM-391-fix-unpaid-item-report-locale-for-product-name
04eb3f1a2b71e6793e52c153a8672569ab1d1270 82c5045e83fe727bcdc953752fd27c763008b54a Sim Zhen Quan <<EMAIL>> 1743672784 +0800	commit (merge): Merge origin/main
82c5045e83fe727bcdc953752fd27c763008b54a 41d6c3d5a9d1ff4611caea78bb136a813ba9e241 Sim Zhen Quan <<EMAIL>> 1743688645 +0800	checkout: moving from SCRUM-391-fix-unpaid-item-report-locale-for-product-name to main
41d6c3d5a9d1ff4611caea78bb136a813ba9e241 0ad7e44411d91dbb2ec01d083fec13bf755722a9 Sim Zhen Quan <<EMAIL>> 1743688686 +0800	commit: Fix student classes migration
0ad7e44411d91dbb2ec01d083fec13bf755722a9 82c5045e83fe727bcdc953752fd27c763008b54a Sim Zhen Quan <<EMAIL>> 1743693648 +0800	checkout: moving from main to SCRUM-391-fix-unpaid-item-report-locale-for-product-name
82c5045e83fe727bcdc953752fd27c763008b54a eab8d815774d50f962a7707d68cebd63d23b3e9e Sim Zhen Quan <<EMAIL>> 1743693701 +0800	merge origin/main: Merge made by the 'ort' strategy.
eab8d815774d50f962a7707d68cebd63d23b3e9e 33ac93b29c611355cd2a2cf963048f75ac9badec Sim Zhen Quan <<EMAIL>> 1743694480 +0800	checkout: moving from SCRUM-391-fix-unpaid-item-report-locale-for-product-name to feature/billing-document-report
33ac93b29c611355cd2a2cf963048f75ac9badec 1fdfba980ab816e9a052fdfd8131f920a881021d Sim Zhen Quan <<EMAIL>> 1743694485 +0800	merge origin/main: Merge made by the 'ort' strategy.
1fdfba980ab816e9a052fdfd8131f920a881021d cfdb2b623e25941927664eecf1bc4ae54bc69680 Sim Zhen Quan <<EMAIL>> 1743694902 +0800	commit: Clean up imports
cfdb2b623e25941927664eecf1bc4ae54bc69680 11c71f912ed874bb5bb2990bf0ff7e27d81fe719 Sim Zhen Quan <<EMAIL>> 1743694954 +0800	checkout: moving from feature/billing-document-report to dev
11c71f912ed874bb5bb2990bf0ff7e27d81fe719 ea3c13ff143d423fd8c5043ad70b3628218f99b9 Sim Zhen Quan <<EMAIL>> 1743694963 +0800	merge origin/main: Merge made by the 'ort' strategy.
ea3c13ff143d423fd8c5043ad70b3628218f99b9 218eecbd5e430a5703eee8e6721e2e194523b0ae Sim Zhen Quan <<EMAIL>> 1743695127 +0800	commit: Deployed to dev
218eecbd5e430a5703eee8e6721e2e194523b0ae 5578ecf8249469b5f479887eec53484111fe1337 Sim Zhen Quan <<EMAIL>> 1743695138 +0800	checkout: moving from dev to feature/conduct-mark-enhancement
5578ecf8249469b5f479887eec53484111fe1337 6312b8932760fc5cfc1619117c5862cc940b6987 Sim Zhen Quan <<EMAIL>> 1743695144 +0800	merge origin/main: Merge made by the 'ort' strategy.
6312b8932760fc5cfc1619117c5862cc940b6987 6312b8932760fc5cfc1619117c5862cc940b6987 Sim Zhen Quan <<EMAIL>> 1743695260 +0800	checkout: moving from feature/conduct-mark-enhancement to feature/conduct-mark-enhancement
6312b8932760fc5cfc1619117c5862cc940b6987 ad9d71d189fbdaacdd9ad4dea52b457bb145816c Sim Zhen Quan <<EMAIL>> 1743696901 +0800	commit: Reviewed
ad9d71d189fbdaacdd9ad4dea52b457bb145816c 218eecbd5e430a5703eee8e6721e2e194523b0ae Sim Zhen Quan <<EMAIL>> 1743697126 +0800	checkout: moving from feature/conduct-mark-enhancement to dev
218eecbd5e430a5703eee8e6721e2e194523b0ae 9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 Sim Zhen Quan <<EMAIL>> 1743697132 +0800	merge origin/main: Merge made by the 'ort' strategy.
9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 e39c74efea0bd94ea797c9ad9b9859599b37afbb Sim Zhen Quan <<EMAIL>> 1743697474 +0800	checkout: moving from dev to academy-student-analysis-report
e39c74efea0bd94ea797c9ad9b9859599b37afbb 30da17d76fae6ef08726f7882d3374d14470d0de Sim Zhen Quan <<EMAIL>> 1743698305 +0800	commit (merge): Merge origin/main
30da17d76fae6ef08726f7882d3374d14470d0de 1a889c792bc9f7ed59b5312753b701fcaa2cc36a Sim Zhen Quan <<EMAIL>> 1743698315 +0800	checkout: moving from academy-student-analysis-report to leave-application-enhancements
1a889c792bc9f7ed59b5312753b701fcaa2cc36a 8471ecb62421020f55fef07f3cd94d6c000b8246 Sim Zhen Quan <<EMAIL>> 1743698322 +0800	pull: Fast-forward
8471ecb62421020f55fef07f3cd94d6c000b8246 8f4a6e1b14bd4c016a39de9b2098159f40badd31 Sim Zhen Quan <<EMAIL>> 1743698459 +0800	commit (merge): Merge origin/main
8f4a6e1b14bd4c016a39de9b2098159f40badd31 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743761689 +0800	checkout: moving from leave-application-enhancements to exam-leftovers
a2a28ccce07d71a276d213e0a96987d36d7e2793 e7b65c028684ee443b61d0ae0a79a487fe4dc345 Sim Zhen Quan <<EMAIL>> 1743862624 +0800	pull: Fast-forward
e7b65c028684ee443b61d0ae0a79a487fe4dc345 0ad7e44411d91dbb2ec01d083fec13bf755722a9 Sim Zhen Quan <<EMAIL>> 1743862643 +0800	checkout: moving from exam-leftovers to main
0ad7e44411d91dbb2ec01d083fec13bf755722a9 ad4d6fae4d985515fbff52e7bfe51052351c087b Sim Zhen Quan <<EMAIL>> 1743862648 +0800	pull: Fast-forward
ad4d6fae4d985515fbff52e7bfe51052351c087b ad4d6fae4d985515fbff52e7bfe51052351c087b Sim Zhen Quan <<EMAIL>> 1743864312 +0800	checkout: moving from main to add-employee-timetables
ad4d6fae4d985515fbff52e7bfe51052351c087b 0629105ffe1a193f66a4e003e0d2aaa4df817706 Sim Zhen Quan <<EMAIL>> 1743872500 +0800	commit: Added employee timetable
0629105ffe1a193f66a4e003e0d2aaa4df817706 b619075c74750c367cb04eae83d06363b7f6b296 Sim Zhen Quan <<EMAIL>> 1743949668 +0800	checkout: moving from add-employee-timetables to fix/migrate-script-timetable
b619075c74750c367cb04eae83d06363b7f6b296 41b98191e0290de2a8bc99a6dac0a25dafc00531 Sim Zhen Quan <<EMAIL>> 1743949675 +0800	merge origin/main: Merge made by the 'ort' strategy.
41b98191e0290de2a8bc99a6dac0a25dafc00531 0de8167b53e2410be7a971e0a0aa24fdb27fd358 Sim Zhen Quan <<EMAIL>> 1743952364 +0800	commit: Reviewed
0de8167b53e2410be7a971e0a0aa24fdb27fd358 6058cc91f9b1cdd40b27e1b8202cabc290b188d5 Sim Zhen Quan <<EMAIL>> 1743952419 +0800	checkout: moving from fix/migrate-script-timetable to fix/migrate-script-hostel
6058cc91f9b1cdd40b27e1b8202cabc290b188d5 0a9a3bbf972a1b6f2751c2e1ffb203ae9d4330e6 Sim Zhen Quan <<EMAIL>> 1743952424 +0800	merge origin/main: Merge made by the 'ort' strategy.
0a9a3bbf972a1b6f2751c2e1ffb203ae9d4330e6 f949b2dea77697438b586be632b9e53fd8e67744 Sim Zhen Quan <<EMAIL>> 1743953645 +0800	commit: Reviewed
f949b2dea77697438b586be632b9e53fd8e67744 454c127c078459e48003659985ab189bf0d6ef76 Sim Zhen Quan <<EMAIL>> 1743954008 +0800	checkout: moving from fix/migrate-script-hostel to fix-migrate-script-class-subject-contractor
454c127c078459e48003659985ab189bf0d6ef76 54b100f00b807cc63a0dc6679153b3c111891756 Sim Zhen Quan <<EMAIL>> 1743954015 +0800	merge origin/main: Merge made by the 'ort' strategy.
54b100f00b807cc63a0dc6679153b3c111891756 132e861a524b4661380644b1e7543b0ac9e214d7 Sim Zhen Quan <<EMAIL>> 1743956457 +0800	commit: Reviewed
132e861a524b4661380644b1e7543b0ac9e214d7 ad4d6fae4d985515fbff52e7bfe51052351c087b Sim Zhen Quan <<EMAIL>> 1743956838 +0800	checkout: moving from fix-migrate-script-class-subject-contractor to main
ad4d6fae4d985515fbff52e7bfe51052351c087b 44d3715712a7d0b6d52b8cc4092959b8ef6ea79b Sim Zhen Quan <<EMAIL>> 1743956843 +0800	pull: Fast-forward
44d3715712a7d0b6d52b8cc4092959b8ef6ea79b 53cc56ed0c3775776460e6a57ed2ecd2ffcae188 Sim Zhen Quan <<EMAIL>> 1743990275 +0800	commit: Change invoice to receipt
53cc56ed0c3775776460e6a57ed2ecd2ffcae188 9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 Sim Zhen Quan <<EMAIL>> 1743990303 +0800	checkout: moving from main to dev
9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 12242d2a2732bcad79d40030bcdcd887371a2d1a Sim Zhen Quan <<EMAIL>> 1743990325 +0800	merge origin/main: Merge made by the 'ort' strategy.
12242d2a2732bcad79d40030bcdcd887371a2d1a a1b665b62aaaa56f355768012955581d989618ac Sim Zhen Quan <<EMAIL>> 1743990461 +0800	commit: Deployed to qas
a1b665b62aaaa56f355768012955581d989618ac 9c94108ee5a3a9c4cde63c8808009a42f8cea865 Sim Zhen Quan <<EMAIL>> 1743990480 +0800	checkout: moving from dev to qas
9c94108ee5a3a9c4cde63c8808009a42f8cea865 78ef23d2356b80fc814b000edd556d787f9bb6a5 Sim Zhen Quan <<EMAIL>> 1743990486 +0800	merge origin/main: Merge made by the 'ort' strategy.
78ef23d2356b80fc814b000edd556d787f9bb6a5 d957717e90b43b5026ffbc7d5dd4da3aeebfff47 Sim Zhen Quan <<EMAIL>> 1743995614 +0800	commit: Deployed to qas
d957717e90b43b5026ffbc7d5dd4da3aeebfff47 53cc56ed0c3775776460e6a57ed2ecd2ffcae188 Sim Zhen Quan <<EMAIL>> 1743997000 +0800	checkout: moving from qas to main
53cc56ed0c3775776460e6a57ed2ecd2ffcae188 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744000217 +0800	commit: Bug fixes
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 a1b665b62aaaa56f355768012955581d989618ac Sim Zhen Quan <<EMAIL>> 1744000240 +0800	checkout: moving from main to dev
a1b665b62aaaa56f355768012955581d989618ac de8422459ae83d08d7f26371c3c2310c986ce0f2 Sim Zhen Quan <<EMAIL>> 1744000246 +0800	merge origin/main: Merge made by the 'ort' strategy.
de8422459ae83d08d7f26371c3c2310c986ce0f2 b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 Sim Zhen Quan <<EMAIL>> 1744008085 +0800	commit: Deployed to dev
b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 e7b65c028684ee443b61d0ae0a79a487fe4dc345 Sim Zhen Quan <<EMAIL>> 1744009115 +0800	checkout: moving from dev to exam-leftovers
e7b65c028684ee443b61d0ae0a79a487fe4dc345 b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 Sim Zhen Quan <<EMAIL>> 1744011510 +0800	checkout: moving from exam-leftovers to dev
b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 e7b65c028684ee443b61d0ae0a79a487fe4dc345 Sim Zhen Quan <<EMAIL>> 1744013471 +0800	checkout: moving from dev to exam-leftovers
e7b65c028684ee443b61d0ae0a79a487fe4dc345 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744016422 +0800	checkout: moving from exam-leftovers to main
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744016447 +0800	checkout: moving from main to migrate-leave-application
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 ef8ac846735f625e6342856855e83d4a06062aff Sim Zhen Quan <<EMAIL>> 1744018667 +0800	commit: WIP
ef8ac846735f625e6342856855e83d4a06062aff b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 Sim Zhen Quan <<EMAIL>> 1744018693 +0800	checkout: moving from migrate-leave-application to dev
b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 7652012e7420db3fde0aedf5c9636e381fd0dc29 Sim Zhen Quan <<EMAIL>> 1744018703 +0800	merge origin/main: Merge made by the 'ort' strategy.
7652012e7420db3fde0aedf5c9636e381fd0dc29 ef8ac846735f625e6342856855e83d4a06062aff Sim Zhen Quan <<EMAIL>> 1744019128 +0800	checkout: moving from dev to migrate-leave-application
ef8ac846735f625e6342856855e83d4a06062aff 2e16e928abafa9123455cb18d7a2470385424dad Sim Zhen Quan <<EMAIL>> 1744081143 +0800	commit: WIP
2e16e928abafa9123455cb18d7a2470385424dad d957717e90b43b5026ffbc7d5dd4da3aeebfff47 Sim Zhen Quan <<EMAIL>> 1744081148 +0800	checkout: moving from migrate-leave-application to qas
d957717e90b43b5026ffbc7d5dd4da3aeebfff47 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744081170 +0800	checkout: moving from qas to main
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 499d565abe53914cf0beb84d7149947d1690fd27 Sim Zhen Quan <<EMAIL>> 1744081264 +0800	pull: Fast-forward
499d565abe53914cf0beb84d7149947d1690fd27 2e16e928abafa9123455cb18d7a2470385424dad Sim Zhen Quan <<EMAIL>> 1744082520 +0800	checkout: moving from main to migrate-leave-application
2e16e928abafa9123455cb18d7a2470385424dad 328ed68848ff1b00b5c371c1eac0788edf8fa6fc Sim Zhen Quan <<EMAIL>> 1744101405 +0800	commit: Completed Migration
328ed68848ff1b00b5c371c1eac0788edf8fa6fc 321ef321fce1982691bd5901bdcce887280fafd3 Sim Zhen Quan <<EMAIL>> 1744101831 +0800	commit: Added TODOs
321ef321fce1982691bd5901bdcce887280fafd3 7652012e7420db3fde0aedf5c9636e381fd0dc29 Sim Zhen Quan <<EMAIL>> 1744101991 +0800	checkout: moving from migrate-leave-application to dev
7652012e7420db3fde0aedf5c9636e381fd0dc29 9c0fc36b58a9d37c861674e9cf2dab71974923a7 Sim Zhen Quan <<EMAIL>> 1744101997 +0800	merge origin/main: Merge made by the 'ort' strategy.
9c0fc36b58a9d37c861674e9cf2dab71974923a7 a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744102139 +0800	commit: Deployed to dev
a089d8505c33afcd6a184d500ad7be14884f638a d957717e90b43b5026ffbc7d5dd4da3aeebfff47 Sim Zhen Quan <<EMAIL>> 1744102155 +0800	checkout: moving from dev to qas
d957717e90b43b5026ffbc7d5dd4da3aeebfff47 d4542b821cb1efec082e2e546cbca88f2bc0e768 Sim Zhen Quan <<EMAIL>> 1744102159 +0800	merge origin/main: Merge made by the 'ort' strategy.
d4542b821cb1efec082e2e546cbca88f2bc0e768 319f3abd80fa64453483e44b5c34ebeef4859264 Sim Zhen Quan <<EMAIL>> 1744107037 +0800	commit: Deployed to qas
319f3abd80fa64453483e44b5c34ebeef4859264 5bc58f9a530814b1e8716367c22a563adc144eea Sim Zhen Quan <<EMAIL>> 1744107066 +0800	checkout: moving from qas to calendar
5bc58f9a530814b1e8716367c22a563adc144eea 27a4dc9449008a6295b655324de88612beb1d7ff Sim Zhen Quan <<EMAIL>> 1744107073 +0800	pull: Fast-forward
27a4dc9449008a6295b655324de88612beb1d7ff 726bcc0fdc8f7d643c6dc259a2cad03f5e431187 Sim Zhen Quan <<EMAIL>> 1744107251 +0800	merge origin/main: Merge made by the 'ort' strategy.
726bcc0fdc8f7d643c6dc259a2cad03f5e431187 44e15271abe0cab0ce550929331bddd1f4ac8ad2 Sim Zhen Quan <<EMAIL>> 1744109335 +0800	pull: Fast-forward
44e15271abe0cab0ce550929331bddd1f4ac8ad2 499d565abe53914cf0beb84d7149947d1690fd27 Sim Zhen Quan <<EMAIL>> 1744171392 +0800	checkout: moving from calendar to main
499d565abe53914cf0beb84d7149947d1690fd27 793032f9240efbbdb6691aa07ea5eabb618c034b Sim Zhen Quan <<EMAIL>> 1744171402 +0800	pull: Fast-forward
793032f9240efbbdb6691aa07ea5eabb618c034b 44e15271abe0cab0ce550929331bddd1f4ac8ad2 Sim Zhen Quan <<EMAIL>> 1744171408 +0800	checkout: moving from main to calendar
44e15271abe0cab0ce550929331bddd1f4ac8ad2 793032f9240efbbdb6691aa07ea5eabb618c034b Sim Zhen Quan <<EMAIL>> 1744257803 +0800	checkout: moving from calendar to main
793032f9240efbbdb6691aa07ea5eabb618c034b b0cdfc67dd26d8ce10e3aa60d6b02ea652af4b42 Sim Zhen Quan <<EMAIL>> 1744265079 +0800	checkout: moving from main to feature/english-class-migration
b0cdfc67dd26d8ce10e3aa60d6b02ea652af4b42 cfcf736d3e762f1bb646f16018458b438a902cb4 Sim Zhen Quan <<EMAIL>> 1744267301 +0800	commit: Reviewed
cfcf736d3e762f1bb646f16018458b438a902cb4 a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744267348 +0800	checkout: moving from feature/english-class-migration to dev
a089d8505c33afcd6a184d500ad7be14884f638a 319f3abd80fa64453483e44b5c34ebeef4859264 Sim Zhen Quan <<EMAIL>> 1744267385 +0800	checkout: moving from dev to qas
319f3abd80fa64453483e44b5c34ebeef4859264 089d4898b0deff4b93aaa969913d3adab81d8838 Sim Zhen Quan <<EMAIL>> 1744267391 +0800	merge origin/main: Merge made by the 'ort' strategy.
089d4898b0deff4b93aaa969913d3adab81d8838 a49509a0a3bd97984e60886f54a33bd304a5ad01 Sim Zhen Quan <<EMAIL>> 1744267546 +0800	commit: Deployed to qas
a49509a0a3bd97984e60886f54a33bd304a5ad01 aaa8dc64560784ce7067a9e481b68bb755824ae1 Sim Zhen Quan <<EMAIL>> 1744268419 +0800	checkout: moving from qas to exam-elective-subjects
aaa8dc64560784ce7067a9e481b68bb755824ae1 aaa8dc64560784ce7067a9e481b68bb755824ae1 Sim Zhen Quan <<EMAIL>> 1744268428 +0800	checkout: moving from exam-elective-subjects to exam-elective-subjects
aaa8dc64560784ce7067a9e481b68bb755824ae1 dab450982fe056a51574fdb31fb8d3f1d1bcc604 Sim Zhen Quan <<EMAIL>> 1744268445 +0800	merge origin/main: Merge made by the 'ort' strategy.
dab450982fe056a51574fdb31fb8d3f1d1bcc604 f8312abacf62c708093e3100423c300477e3addc Sim Zhen Quan <<EMAIL>> 1744272877 +0800	pull: Fast-forward
f8312abacf62c708093e3100423c300477e3addc 17699f0dc3c74fe0fc9f39552719dbd15b424e10 Sim Zhen Quan <<EMAIL>> 1744272880 +0800	commit: Partially reviewed, gonna push to main and test from FE
17699f0dc3c74fe0fc9f39552719dbd15b424e10 63d4a65eb1114b6a0fb5d51c5dda0494b7931c82 Sim Zhen Quan <<EMAIL>> 1744273023 +0800	checkout: moving from exam-elective-subjects to SCRUM-392-exam-posting-dashboard
63d4a65eb1114b6a0fb5d51c5dda0494b7931c82 0054d22d5cb663c0fd7ab531845714bdf0d52bfe Sim Zhen Quan <<EMAIL>> 1744273031 +0800	merge origin/main: Merge made by the 'ort' strategy.
0054d22d5cb663c0fd7ab531845714bdf0d52bfe 0054d22d5cb663c0fd7ab531845714bdf0d52bfe Sim Zhen Quan <<EMAIL>> 1744273091 +0800	checkout: moving from SCRUM-392-exam-posting-dashboard to SCRUM-392-exam-posting-dashboard
0054d22d5cb663c0fd7ab531845714bdf0d52bfe 645b4c69b3de268843ae9aa50c9028f09cc3a0d5 Sim Zhen Quan <<EMAIL>> 1744273305 +0800	checkout: moving from SCRUM-392-exam-posting-dashboard to exam-output-component-output-type
645b4c69b3de268843ae9aa50c9028f09cc3a0d5 4fa81871bc3cd640b2c5d0e4617e81850873a88d Sim Zhen Quan <<EMAIL>> 1744273310 +0800	merge origin/main: Merge made by the 'ort' strategy.
4fa81871bc3cd640b2c5d0e4617e81850873a88d 0054d22d5cb663c0fd7ab531845714bdf0d52bfe Sim Zhen Quan <<EMAIL>> 1744274329 +0800	checkout: moving from exam-output-component-output-type to SCRUM-392-exam-posting-dashboard
0054d22d5cb663c0fd7ab531845714bdf0d52bfe c10523fb4f978dafb72c784eb09bf42247ff1a4c Sim Zhen Quan <<EMAIL>> 1744274334 +0800	merge origin/main: Merge made by the 'ort' strategy.
c10523fb4f978dafb72c784eb09bf42247ff1a4c a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744275571 +0800	checkout: moving from SCRUM-392-exam-posting-dashboard to dev
a089d8505c33afcd6a184d500ad7be14884f638a 793032f9240efbbdb6691aa07ea5eabb618c034b Sim Zhen Quan <<EMAIL>> 1744275581 +0800	checkout: moving from dev to main
793032f9240efbbdb6691aa07ea5eabb618c034b 09e817ea94ec1e34fa55118053cf604665bc2ae8 Sim Zhen Quan <<EMAIL>> 1744275586 +0800	pull: Fast-forward
09e817ea94ec1e34fa55118053cf604665bc2ae8 a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744275844 +0800	checkout: moving from main to dev
a089d8505c33afcd6a184d500ad7be14884f638a 5f5c5ce0d85041dd47d865f92d4dee1662215a5a Sim Zhen Quan <<EMAIL>> 1744275857 +0800	merge origin/main: Merge made by the 'ort' strategy.
5f5c5ce0d85041dd47d865f92d4dee1662215a5a d59b8b36ebee927bc3c606a41f2039ce2da070ea Sim Zhen Quan <<EMAIL>> 1744336345 +0800	commit: Deployed to dev
d59b8b36ebee927bc3c606a41f2039ce2da070ea 6a717749e84be47a1bff04c5496be538c7182e0e Sim Zhen Quan <<EMAIL>> 1744336354 +0800	checkout: moving from dev to feature/exam-publish-report-card
6a717749e84be47a1bff04c5496be538c7182e0e e41c3acfe0d0fc42424ff088089b75dd5ab356c7 Sim Zhen Quan <<EMAIL>> 1744336360 +0800	merge origin/main: Merge made by the 'ort' strategy.
e41c3acfe0d0fc42424ff088089b75dd5ab356c7 006019bc2b1987028737938b2cb0075994c6581d Sim Zhen Quan <<EMAIL>> 1744340750 +0800	commit: Review WIP
006019bc2b1987028737938b2cb0075994c6581d 4922df94e19b5d57361bc75d3ffdd3d8450a00aa Sim Zhen Quan <<EMAIL>> 1744346172 +0800	pull: Fast-forward
4922df94e19b5d57361bc75d3ffdd3d8450a00aa 44e15271abe0cab0ce550929331bddd1f4ac8ad2 Sim Zhen Quan <<EMAIL>> 1744346954 +0800	checkout: moving from feature/exam-publish-report-card to calendar
44e15271abe0cab0ce550929331bddd1f4ac8ad2 0c2bd77625c33fb5663bbaae797dd9febf957226 Sim Zhen Quan <<EMAIL>> 1744346961 +0800	pull: Fast-forward
0c2bd77625c33fb5663bbaae797dd9febf957226 d59b8b36ebee927bc3c606a41f2039ce2da070ea Sim Zhen Quan <<EMAIL>> 1744346962 +0800	checkout: moving from calendar to dev
d59b8b36ebee927bc3c606a41f2039ce2da070ea d724724a3321261d84cf6b359e2f0ada6bea70d3 Sim Zhen Quan <<EMAIL>> 1744347021 +0800	commit (merge): Merge calendar branch
d724724a3321261d84cf6b359e2f0ada6bea70d3 4922df94e19b5d57361bc75d3ffdd3d8450a00aa Sim Zhen Quan <<EMAIL>> 1744353760 +0800	checkout: moving from dev to feature/exam-publish-report-card
4922df94e19b5d57361bc75d3ffdd3d8450a00aa 54a8da3552cb96f24f3262eb8b9b10f9e05c788b Sim Zhen Quan <<EMAIL>> 1744353766 +0800	pull: Fast-forward
54a8da3552cb96f24f3262eb8b9b10f9e05c788b 58b56afd79d7f0d847973c5f1d1b0e2c7270ee91 Sim Zhen Quan <<EMAIL>> 1744356115 +0800	commit: Reviewed
58b56afd79d7f0d847973c5f1d1b0e2c7270ee91 d724724a3321261d84cf6b359e2f0ada6bea70d3 Sim Zhen Quan <<EMAIL>> 1744359564 +0800	checkout: moving from feature/exam-publish-report-card to dev
d724724a3321261d84cf6b359e2f0ada6bea70d3 0c2bd77625c33fb5663bbaae797dd9febf957226 Sim Zhen Quan <<EMAIL>> 1744360994 +0800	checkout: moving from dev to calendar
0c2bd77625c33fb5663bbaae797dd9febf957226 e883978af9e518397ad793f94239f63712b0e482 Sim Zhen Quan <<EMAIL>> 1744363687 +0800	commit: Fix migration
e883978af9e518397ad793f94239f63712b0e482 7e6d10130f9c0a19927d932d44fae9dc9816d756 Sim Zhen Quan <<EMAIL>> 1744470103 +0800	pull: Fast-forward
7e6d10130f9c0a19927d932d44fae9dc9816d756 893c34ad1ec8c6080ea940c4d0d9872de875cac4 Sim Zhen Quan <<EMAIL>> 1744547908 +0800	commit: Fix overload of data
893c34ad1ec8c6080ea940c4d0d9872de875cac4 6a45081c6e70582f01b79271730612e9b960fb29 Sim Zhen Quan <<EMAIL>> 1744560224 +0800	commit: 1. Calendar Targets index return StudentResource instead of Simple resource because class is needed
6a45081c6e70582f01b79271730612e9b960fb29 d9f0fc185f09cd5920ab7af564bed7c50b13433a Sim Zhen Quan <<EMAIL>> 1744560293 +0800	commit (merge): Merge origin/main
d9f0fc185f09cd5920ab7af564bed7c50b13433a d724724a3321261d84cf6b359e2f0ada6bea70d3 Sim Zhen Quan <<EMAIL>> 1744561737 +0800	checkout: moving from calendar to dev
d724724a3321261d84cf6b359e2f0ada6bea70d3 3c4b9b96f324c241eea49a82912bda480cce4804 Sim Zhen Quan <<EMAIL>> 1744561745 +0800	merge origin/main: Merge made by the 'ort' strategy.
3c4b9b96f324c241eea49a82912bda480cce4804 6815c789ae223360d7de478bf067867cc11e7aa7 Sim Zhen Quan <<EMAIL>> 1744562172 +0800	checkout: moving from dev to migration-english-timeslots
6815c789ae223360d7de478bf067867cc11e7aa7 054f0f954f21f0e34db885440b15e39140f06546 Sim Zhen Quan <<EMAIL>> 1744566151 +0800	commit: Reviewed
054f0f954f21f0e34db885440b15e39140f06546 57807a0d7f0d6ce0a815db6967fed8dd16c34c10 Sim Zhen Quan <<EMAIL>> 1744566372 +0800	checkout: moving from migration-english-timeslots to migrate-substitute-teachers
57807a0d7f0d6ce0a815db6967fed8dd16c34c10 5462493a0336b67c8eb20c583460aac3a8a2accb Sim Zhen Quan <<EMAIL>> 1744566471 +0800	commit (merge): Merge origin/main
5462493a0336b67c8eb20c583460aac3a8a2accb 0091e253b6ae79bb59f480193f895e4b9bcf968f Sim Zhen Quan <<EMAIL>> 1744568837 +0800	commit: Reviewed
0091e253b6ae79bb59f480193f895e4b9bcf968f 0ee8b6a9b60f0c63e3396453d6ae6692a25f37f8 Sim Zhen Quan <<EMAIL>> 1744597149 +0800	checkout: moving from migrate-substitute-teachers to enhancement/add-year-to-semester-setting
0ee8b6a9b60f0c63e3396453d6ae6692a25f37f8 0b52ea9aceb32cff1943c9a991175377d0390720 Sim Zhen Quan <<EMAIL>> 1744597181 +0800	merge origin/main: Merge made by the 'ort' strategy.
0b52ea9aceb32cff1943c9a991175377d0390720 c8be8a14843c12f4474dc36a09fca62f4354fe84 Sim Zhen Quan <<EMAIL>> 1744597203 +0800	checkout: moving from enhancement/add-year-to-semester-setting to migrate-script-cocu-timetable
c8be8a14843c12f4474dc36a09fca62f4354fe84 3e94add502e4ffc0a8f860190ffbc5b0bfc9b395 Sim Zhen Quan <<EMAIL>> 1744597208 +0800	merge origin/main: Merge made by the 'ort' strategy.
3e94add502e4ffc0a8f860190ffbc5b0bfc9b395 8be1acd8c9cb154b38171f747a1ec665c03a36e0 Sim Zhen Quan <<EMAIL>> 1744598869 +0800	commit: Reviewed
8be1acd8c9cb154b38171f747a1ec665c03a36e0 daba61e8bd751a796174edcadf3e4d93a1069998 Sim Zhen Quan <<EMAIL>> 1744599151 +0800	checkout: moving from migrate-script-cocu-timetable to bugfix/exam-exemption-json-decode-student-name
daba61e8bd751a796174edcadf3e4d93a1069998 1aa292d351dcef72bedbcdfd97d982102ef042cc Sim Zhen Quan <<EMAIL>> 1744599159 +0800	merge origin/main: Merge made by the 'ort' strategy.
1aa292d351dcef72bedbcdfd97d982102ef042cc 09e817ea94ec1e34fa55118053cf604665bc2ae8 Sim Zhen Quan <<EMAIL>> 1744599488 +0800	checkout: moving from bugfix/exam-exemption-json-decode-student-name to main
09e817ea94ec1e34fa55118053cf604665bc2ae8 d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744599493 +0800	pull: Fast-forward
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 3c4b9b96f324c241eea49a82912bda480cce4804 Sim Zhen Quan <<EMAIL>> 1744599501 +0800	checkout: moving from main to dev
3c4b9b96f324c241eea49a82912bda480cce4804 770711b39b693fe41e035ddcb0feaeed34d990b1 Sim Zhen Quan <<EMAIL>> 1744599510 +0800	merge origin/main: Merge made by the 'ort' strategy.
770711b39b693fe41e035ddcb0feaeed34d990b1 a49509a0a3bd97984e60886f54a33bd304a5ad01 Sim Zhen Quan <<EMAIL>> 1744599841 +0800	checkout: moving from dev to qas
a49509a0a3bd97984e60886f54a33bd304a5ad01 ca7a5597d42c0622e92912e356985de55712e7c0 Sim Zhen Quan <<EMAIL>> 1744599847 +0800	merge origin/main: Merge made by the 'ort' strategy.
ca7a5597d42c0622e92912e356985de55712e7c0 e33067de83501f4c10e047d3fab00f5f12b671f7 Sim Zhen Quan <<EMAIL>> 1744602502 +0800	commit: Deployed to qas
e33067de83501f4c10e047d3fab00f5f12b671f7 770711b39b693fe41e035ddcb0feaeed34d990b1 Sim Zhen Quan <<EMAIL>> 1744602508 +0800	checkout: moving from qas to dev
770711b39b693fe41e035ddcb0feaeed34d990b1 781c388090e5407e9dcb3050464a31a2f065dc67 Sim Zhen Quan <<EMAIL>> 1744602521 +0800	merge origin/calendar: Merge made by the 'ort' strategy.
781c388090e5407e9dcb3050464a31a2f065dc67 0b52ea9aceb32cff1943c9a991175377d0390720 Sim Zhen Quan <<EMAIL>> 1744605334 +0800	checkout: moving from dev to enhancement/add-year-to-semester-setting
0b52ea9aceb32cff1943c9a991175377d0390720 e43745e077001d542a96311dcc4d03b8720fcd20 Sim Zhen Quan <<EMAIL>> 1744605339 +0800	merge origin/main: Merge made by the 'ort' strategy.
e43745e077001d542a96311dcc4d03b8720fcd20 d9f0fc185f09cd5920ab7af564bed7c50b13433a Sim Zhen Quan <<EMAIL>> 1744607914 +0800	checkout: moving from enhancement/add-year-to-semester-setting to calendar
d9f0fc185f09cd5920ab7af564bed7c50b13433a 5eb91f3f4110c05c16806b401acd7f18feac1d16 Sim Zhen Quan <<EMAIL>> 1744617272 +0800	pull: Fast-forward
5eb91f3f4110c05c16806b401acd7f18feac1d16 b5a1eaed7ff0dac4f12d69f69f2ccc9f420a64bb Sim Zhen Quan <<EMAIL>> 1744617428 +0800	pull: Fast-forward
b5a1eaed7ff0dac4f12d69f69f2ccc9f420a64bb 12dce545c605417e6691632461cf7facbab2fdac Sim Zhen Quan <<EMAIL>> 1744622138 +0800	pull: Fast-forward
12dce545c605417e6691632461cf7facbab2fdac b1fcf18398670b2ca3d7f10307b26b343717e324 Sim Zhen Quan <<EMAIL>> 1744622268 +0800	commit: Added function to get all grouped periods
b1fcf18398670b2ca3d7f10307b26b343717e324 5db237e9f36b784e216e6551ab3064ec300b6b8c Sim Zhen Quan <<EMAIL>> 1744622587 +0800	pull: Fast-forward
5db237e9f36b784e216e6551ab3064ec300b6b8c 781c388090e5407e9dcb3050464a31a2f065dc67 Sim Zhen Quan <<EMAIL>> 1744622591 +0800	checkout: moving from calendar to dev
781c388090e5407e9dcb3050464a31a2f065dc67 3ea1ac0394fd1266e8e418176fe9605efab9a679 Sim Zhen Quan <<EMAIL>> 1744622607 +0800	merge origin/calendar: Merge made by the 'ort' strategy.
3ea1ac0394fd1266e8e418176fe9605efab9a679 5b35bda554ed2f31923466e1770207e29ddfa280 Sim Zhen Quan <<EMAIL>> 1744653800 +0800	commit: Deployed to dev
5b35bda554ed2f31923466e1770207e29ddfa280 5b35bda554ed2f31923466e1770207e29ddfa280 Sim Zhen Quan <<EMAIL>> 1744681468 +0800	checkout: moving from dev to dev
5b35bda554ed2f31923466e1770207e29ddfa280 d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744685715 +0800	checkout: moving from dev to main
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 5db237e9f36b784e216e6551ab3064ec300b6b8c Sim Zhen Quan <<EMAIL>> 1744687714 +0800	checkout: moving from main to calendar
5db237e9f36b784e216e6551ab3064ec300b6b8c e98924a99a7b57e7267279cda779087cf2564303 Sim Zhen Quan <<EMAIL>> 1744687720 +0800	pull: Fast-forward
e98924a99a7b57e7267279cda779087cf2564303 d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744687726 +0800	checkout: moving from calendar to main
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 5b35bda554ed2f31923466e1770207e29ddfa280 Sim Zhen Quan <<EMAIL>> 1744687733 +0800	checkout: moving from main to dev
5b35bda554ed2f31923466e1770207e29ddfa280 1a52e6f67d282b787ace999a9329554c379c2d72 Sim Zhen Quan <<EMAIL>> 1744687745 +0800	merge calendar: Merge made by the 'ort' strategy.
1a52e6f67d282b787ace999a9329554c379c2d72 e98924a99a7b57e7267279cda779087cf2564303 Sim Zhen Quan <<EMAIL>> 1744687996 +0800	checkout: moving from dev to calendar
e98924a99a7b57e7267279cda779087cf2564303 45926da1f8b355fa2390a19ccce34274f9f667ff Sim Zhen Quan <<EMAIL>> 1744688001 +0800	merge origin/main: Merge made by the 'ort' strategy.
45926da1f8b355fa2390a19ccce34274f9f667ff 258d1368ee5934033c8f7b85619cf0484950d0e6 Sim Zhen Quan <<EMAIL>> 1744707441 +0800	pull: Fast-forward
258d1368ee5934033c8f7b85619cf0484950d0e6 ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 Sim Zhen Quan <<EMAIL>> 1744707497 +0800	commit: Review WIP
ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 1a52e6f67d282b787ace999a9329554c379c2d72 Sim Zhen Quan <<EMAIL>> 1744707506 +0800	checkout: moving from calendar to dev
1a52e6f67d282b787ace999a9329554c379c2d72 f4798e9e7d68b4a430d6404919148a057e09789f Sim Zhen Quan <<EMAIL>> 1744707512 +0800	merge calendar: Merge made by the 'ort' strategy.
f4798e9e7d68b4a430d6404919148a057e09789f ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 Sim Zhen Quan <<EMAIL>> 1744712873 +0800	checkout: moving from dev to calendar
ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 0269b350fba060ae4649d9c474d5d83315b7f824 Sim Zhen Quan <<EMAIL>> 1744732823 +0800	merge origin/calendar: Fast-forward
0269b350fba060ae4649d9c474d5d83315b7f824 cd54b15787fd827b7b00205a4daae27ce709b9d9 Sim Zhen Quan <<EMAIL>> 1744733064 +0800	merge origin/main: Merge made by the 'ort' strategy.
cd54b15787fd827b7b00205a4daae27ce709b9d9 f1ef11919071669ee8b596cdd8e6d8110a2348c3 Sim Zhen Quan <<EMAIL>> 1744733405 +0800	commit: Reviewed 70% of the code, PR too big to go thru every line of code, will test manually on FE to make sure the scenarios are being handled correctly
f1ef11919071669ee8b596cdd8e6d8110a2348c3 c5419dc701185f3c0ac48b495bc1d39ec41a9574 Sim Zhen Quan <<EMAIL>> 1744733475 +0800	checkout: moving from calendar to feature/daily-attendance-report
c5419dc701185f3c0ac48b495bc1d39ec41a9574 33e4bcbedbd0b76731f5bec4c345f214436e233e Sim Zhen Quan <<EMAIL>> 1744733482 +0800	merge origin/main: Merge made by the 'ort' strategy.
33e4bcbedbd0b76731f5bec4c345f214436e233e 34cdb85868842546d21fb292c37bb55fb7d6ecf8 Sim Zhen Quan <<EMAIL>> 1744733533 +0800	checkout: moving from feature/daily-attendance-report to feature/class-attendance-taking-report
34cdb85868842546d21fb292c37bb55fb7d6ecf8 812a53178348da4d3174ca418e458ab1ecde30aa Sim Zhen Quan <<EMAIL>> 1744733667 +0800	commit (merge): Merge branch
812a53178348da4d3174ca418e458ab1ecde30aa f4798e9e7d68b4a430d6404919148a057e09789f Sim Zhen Quan <<EMAIL>> 1744733881 +0800	checkout: moving from feature/class-attendance-taking-report to dev
f4798e9e7d68b4a430d6404919148a057e09789f 51e61a789d2f0aa9f62442f1edfa0022fa3311f5 Sim Zhen Quan <<EMAIL>> 1744733890 +0800	merge origin/main: Merge made by the 'ort' strategy.
51e61a789d2f0aa9f62442f1edfa0022fa3311f5 109ae34986c593a41936018f14acebc4ac0815c0 Sim Zhen Quan <<EMAIL>> 1744733906 +0800	merge feature/daily-attendance-report: Merge made by the 'ort' strategy.
109ae34986c593a41936018f14acebc4ac0815c0 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744765039 +0800	commit: Deployed to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744765145 +0800	checkout: moving from dev to main
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 Sim Zhen Quan <<EMAIL>> 1744765150 +0800	pull: Fast-forward
6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744766080 +0800	checkout: moving from main to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e 6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 Sim Zhen Quan <<EMAIL>> 1744766102 +0800	checkout: moving from dev to main
6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 6b68588446a958e5b4195206a40e6e613e4a14f1 Sim Zhen Quan <<EMAIL>> 1744783083 +0800	commit: Migration script fixes
6b68588446a958e5b4195206a40e6e613e4a14f1 3fecbcbcf9458138d27db0f3d602c5a73efa9dcb Sim Zhen Quan <<EMAIL>> 1744783878 +0800	commit: Added Calendar Seeder
3fecbcbcf9458138d27db0f3d602c5a73efa9dcb 6d8b2459c9b2df678a11272c94278fe46a5d976d Sim Zhen Quan <<EMAIL>> 1744785126 +0800	checkout: moving from main to SCRUM-371-fix-scholarship-support-multiple-students
6d8b2459c9b2df678a11272c94278fe46a5d976d f1ba167a371877d796869f4d1bd30c418ecea695 Sim Zhen Quan <<EMAIL>> 1744785132 +0800	merge origin/main: Merge made by the 'ort' strategy.
f1ba167a371877d796869f4d1bd30c418ecea695 3fecbcbcf9458138d27db0f3d602c5a73efa9dcb Sim Zhen Quan <<EMAIL>> 1744785916 +0800	checkout: moving from SCRUM-371-fix-scholarship-support-multiple-students to main
3fecbcbcf9458138d27db0f3d602c5a73efa9dcb b55abc76e5f8409a62a43da6038aaf5d971a99c0 Sim Zhen Quan <<EMAIL>> 1744785921 +0800	pull: Fast-forward
b55abc76e5f8409a62a43da6038aaf5d971a99c0 f10b3a7a17a9869ff941d9e431fd0d88d18a7d21 Sim Zhen Quan <<EMAIL>> 1744788113 +0800	checkout: moving from main to SCRUM-413-Phase2-permission-seeder
f10b3a7a17a9869ff941d9e431fd0d88d18a7d21 0ab059f063ef26bba0df114112730dbd94e7262b Sim Zhen Quan <<EMAIL>> 1744788119 +0800	merge origin/main: Merge made by the 'ort' strategy.
0ab059f063ef26bba0df114112730dbd94e7262b 0fde93d4f93f80bc65a54ba70a40df43cc051e1e Sim Zhen Quan <<EMAIL>> 1744790457 +0800	commit: Updated readme
0fde93d4f93f80bc65a54ba70a40df43cc051e1e b55abc76e5f8409a62a43da6038aaf5d971a99c0 Sim Zhen Quan <<EMAIL>> 1744790480 +0800	checkout: moving from SCRUM-413-Phase2-permission-seeder to main
b55abc76e5f8409a62a43da6038aaf5d971a99c0 c3be00488d4a82ac0a68d5866e57107de7e708aa Sim Zhen Quan <<EMAIL>> 1744790486 +0800	pull: Fast-forward
c3be00488d4a82ac0a68d5866e57107de7e708aa db9be4750acbf70d29db214c60c7a808de6a8328 Sim Zhen Quan <<EMAIL>> 1744795863 +0800	checkout: moving from main to fix-attendance-posting-bug
db9be4750acbf70d29db214c60c7a808de6a8328 e33067de83501f4c10e047d3fab00f5f12b671f7 Sim Zhen Quan <<EMAIL>> 1744812208 +0800	checkout: moving from fix-attendance-posting-bug to qas
e33067de83501f4c10e047d3fab00f5f12b671f7 c0d1cf6d6c46b494c925824631fb5d3ac7170ffe Sim Zhen Quan <<EMAIL>> 1744812270 +0800	commit (merge): Merge origin/main
c0d1cf6d6c46b494c925824631fb5d3ac7170ffe ceba35dec9909dd52fdea1810240d0ab4452806b Sim Zhen Quan <<EMAIL>> 1744812635 +0800	commit: Deployed to qas
ceba35dec9909dd52fdea1810240d0ab4452806b a14d9eeef529798c7f592b87ee0573e83b9259d2 Sim Zhen Quan <<EMAIL>> 1744812687 +0800	checkout: moving from qas to SCRUM-418-Migrate-Hostel-PIC
a14d9eeef529798c7f592b87ee0573e83b9259d2 fabf8dc9313bfbbb0e158811d7d59f9408f31c0f Sim Zhen Quan <<EMAIL>> 1744812879 +0800	commit: Add in readme
fabf8dc9313bfbbb0e158811d7d59f9408f31c0f c3be00488d4a82ac0a68d5866e57107de7e708aa Sim Zhen Quan <<EMAIL>> 1744812939 +0800	checkout: moving from SCRUM-418-Migrate-Hostel-PIC to main
c3be00488d4a82ac0a68d5866e57107de7e708aa 9c278e106fffb99558f7161113e007fee3c2f66b Sim Zhen Quan <<EMAIL>> 1744812944 +0800	pull: Fast-forward
9c278e106fffb99558f7161113e007fee3c2f66b db9be4750acbf70d29db214c60c7a808de6a8328 Sim Zhen Quan <<EMAIL>> 1744812950 +0800	checkout: moving from main to fix-attendance-posting-bug
db9be4750acbf70d29db214c60c7a808de6a8328 d95562fc1db46a30a82929a51dac9736628caae4 Sim Zhen Quan <<EMAIL>> 1744812955 +0800	merge origin/main: Merge made by the 'ort' strategy.
d95562fc1db46a30a82929a51dac9736628caae4 2419c7a069a6dd24dd93e7a9b90edb37389be611 Sim Zhen Quan <<EMAIL>> 1744814540 +0800	commit: Fix to only get timeslot from active timetable
2419c7a069a6dd24dd93e7a9b90edb37389be611 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744814580 +0800	checkout: moving from fix-attendance-posting-bug to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e 9c278e106fffb99558f7161113e007fee3c2f66b Sim Zhen Quan <<EMAIL>> 1744814592 +0800	checkout: moving from dev to main
9c278e106fffb99558f7161113e007fee3c2f66b 7161685ae9b8c87340e5da023f20a64b2da94c33 Sim Zhen Quan <<EMAIL>> 1744814597 +0800	pull: Fast-forward
7161685ae9b8c87340e5da023f20a64b2da94c33 0be760e5a7634b219f8e5cdb1bfa36e7422050fe Sim Zhen Quan <<EMAIL>> 1744816141 +0800	commit: Update Kernel
0be760e5a7634b219f8e5cdb1bfa36e7422050fe ceba35dec9909dd52fdea1810240d0ab4452806b Sim Zhen Quan <<EMAIL>> 1744816151 +0800	checkout: moving from main to qas
ceba35dec9909dd52fdea1810240d0ab4452806b 3732fe66327d4fce862584a219baef31867d242c Sim Zhen Quan <<EMAIL>> 1744816159 +0800	merge origin/main: Merge made by the 'ort' strategy.
3732fe66327d4fce862584a219baef31867d242c 62103c6ba5d212a8bc4e0c2504d0153ce9441384 Sim Zhen Quan <<EMAIL>> 1744816358 +0800	commit: Deployed to qas
62103c6ba5d212a8bc4e0c2504d0153ce9441384 0be760e5a7634b219f8e5cdb1bfa36e7422050fe Sim Zhen Quan <<EMAIL>> 1744816437 +0800	checkout: moving from qas to main
0be760e5a7634b219f8e5cdb1bfa36e7422050fe 8d3479855bfaf0d000310c0d5ff76fea7e1a5585 Sim Zhen Quan <<EMAIL>> 1744817925 +0800	commit: Bug fix
8d3479855bfaf0d000310c0d5ff76fea7e1a5585 206b57fec963d4ab7272efd086487c76e2ef3670 Sim Zhen Quan <<EMAIL>> 1744817941 +0800	checkout: moving from main to SCRUM-418-Withdrawal-Reason
206b57fec963d4ab7272efd086487c76e2ef3670 7dc2dd6bb865efe4147567e150fa4c0c72c8c7f8 Sim Zhen Quan <<EMAIL>> 1744817998 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into SCRUM-418-Withdrawal-Reason
7dc2dd6bb865efe4147567e150fa4c0c72c8c7f8 98c3a0d422dbc42a77faf4cd980307f7717497fd Sim Zhen Quan <<EMAIL>> 1744818121 +0800	commit: Add migration step for withdrawal reasons in README
98c3a0d422dbc42a77faf4cd980307f7717497fd 6e0ac54e24a2df6d353cec55d01650d6804d0dd4 Sim Zhen Quan <<EMAIL>> 1744822315 +0800	checkout: moving from SCRUM-418-Withdrawal-Reason to SCRUM-404-Changes-to-autocount-daily-collection-report
6e0ac54e24a2df6d353cec55d01650d6804d0dd4 37f6029a7b36c8e6f62313a8380b07e14c2f929f Sim Zhen Quan <<EMAIL>> 1744822320 +0800	merge origin/main: Merge made by the 'ort' strategy.
37f6029a7b36c8e6f62313a8380b07e14c2f929f 8d3479855bfaf0d000310c0d5ff76fea7e1a5585 Sim Zhen Quan <<EMAIL>> 1744822646 +0800	checkout: moving from SCRUM-404-Changes-to-autocount-daily-collection-report to main
8d3479855bfaf0d000310c0d5ff76fea7e1a5585 6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 Sim Zhen Quan <<EMAIL>> 1744822651 +0800	pull: Fast-forward
6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 8bee32651fd50a7c67bcf81f012073380644ca58 Sim Zhen Quan <<EMAIL>> 1744822659 +0800	checkout: moving from main to api/duplicate-semester
8bee32651fd50a7c67bcf81f012073380644ca58 e37337c2af460ff13f232f172faa32d21aa4984b Sim Zhen Quan <<EMAIL>> 1744822669 +0800	merge origin/main: Merge made by the 'ort' strategy.
e37337c2af460ff13f232f172faa32d21aa4984b 4ab7f2c2937c563f5425f230b2033c55e8ba6b79 Sim Zhen Quan <<EMAIL>> 1744867552 +0800	commit: Add migration step for withdrawal reasons in README
4ab7f2c2937c563f5425f230b2033c55e8ba6b79 6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 Sim Zhen Quan <<EMAIL>> 1744873136 +0800	checkout: moving from api/duplicate-semester to main
6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 9134576a366118c20b091d05c398cd82f2d18bbf Sim Zhen Quan <<EMAIL>> 1744876855 +0800	commit: Updated discount to add in nullable source type and student id
9134576a366118c20b091d05c398cd82f2d18bbf 4fc250f3cc306c44baf860e8f6f864c7ac68636a Sim Zhen Quan <<EMAIL>> 1744880249 +0800	checkout: moving from main to attendance-bug-fix
4fc250f3cc306c44baf860e8f6f864c7ac68636a 1fbfbc0c2df0cbb2ec8a601323f4a79380855db2 Sim Zhen Quan <<EMAIL>> 1744880255 +0800	pull: Fast-forward
1fbfbc0c2df0cbb2ec8a601323f4a79380855db2 234897e99285a275354fde75df96d496e360b834 Sim Zhen Quan <<EMAIL>> 1744880271 +0800	merge origin/main: Merge made by the 'ort' strategy.
234897e99285a275354fde75df96d496e360b834 7fe0403e2949a022e1b0b869c2d72dceb979f0d2 Sim Zhen Quan <<EMAIL>> 1744881483 +0800	merge origin/main: Fast-forward
7fe0403e2949a022e1b0b869c2d72dceb979f0d2 36208c56bfe4504e5ca9b81071f775c90eaeccb9 Sim Zhen Quan <<EMAIL>> 1744881516 +0800	checkout: moving from attendance-bug-fix to fix/seat-setting-bug
36208c56bfe4504e5ca9b81071f775c90eaeccb9 4abaf12daf5c736273c7a206a0b9ae4b8c88f0dd Sim Zhen Quan <<EMAIL>> 1744881521 +0800	merge origin/main: Merge made by the 'ort' strategy.
4abaf12daf5c736273c7a206a0b9ae4b8c88f0dd 730866d93083b9b66612ed94ad7194025ef610db Sim Zhen Quan <<EMAIL>> 1744881684 +0800	checkout: moving from fix/seat-setting-bug to substitute-record-apis-add-allowance
730866d93083b9b66612ed94ad7194025ef610db 15132103924297d48c902c4c50ee8cc3d456fcf3 Sim Zhen Quan <<EMAIL>> 1744881797 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into substitute-record-apis-add-allowance
15132103924297d48c902c4c50ee8cc3d456fcf3 15132103924297d48c902c4c50ee8cc3d456fcf3 Sim Zhen Quan <<EMAIL>> 1744882295 +0800	checkout: moving from substitute-record-apis-add-allowance to substitute-record-apis-add-allowance
15132103924297d48c902c4c50ee8cc3d456fcf3 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744882634 +0800	checkout: moving from substitute-record-apis-add-allowance to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e 9134576a366118c20b091d05c398cd82f2d18bbf Sim Zhen Quan <<EMAIL>> 1744882689 +0800	checkout: moving from dev to main
9134576a366118c20b091d05c398cd82f2d18bbf bbdb50115935c27329bbc8848c23093c3d9eae5f Sim Zhen Quan <<EMAIL>> 1744882694 +0800	pull: Fast-forward
bbdb50115935c27329bbc8848c23093c3d9eae5f 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744882696 +0800	checkout: moving from main to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e d7bcb8a92579fce4fdf04569f48b1cbdf8aea034 Sim Zhen Quan <<EMAIL>> 1744882699 +0800	merge origin/main: Merge made by the 'ort' strategy.
d7bcb8a92579fce4fdf04569f48b1cbdf8aea034 49c9cbb50817402ae72b137340d9aee4efc076a1 Sim Zhen Quan <<EMAIL>> 1744885843 +0800	commit: Deployed to dev
49c9cbb50817402ae72b137340d9aee4efc076a1 bbdb50115935c27329bbc8848c23093c3d9eae5f Sim Zhen Quan <<EMAIL>> 1744887727 +0800	checkout: moving from dev to main
bbdb50115935c27329bbc8848c23093c3d9eae5f c052125d5ee560b2cabcd3dae700470f42b33981 Sim Zhen Quan <<EMAIL>> 1744887765 +0800	commit: Add support for pending payment status in billing resources
c052125d5ee560b2cabcd3dae700470f42b33981 18d11bba7014f6c61cf36b63ddca124e3bb0e9fc Sim Zhen Quan <<EMAIL>> 1744887849 +0800	checkout: moving from main to fix-migration-set-student-class-inactive-for-left-school
18d11bba7014f6c61cf36b63ddca124e3bb0e9fc 709b9c5710e7fe62593e22b7ab4c11cd0671f86b Sim Zhen Quan <<EMAIL>> 1744887854 +0800	merge origin/main: Merge made by the 'ort' strategy.
709b9c5710e7fe62593e22b7ab4c11cd0671f86b 709b9c5710e7fe62593e22b7ab4c11cd0671f86b Sim Zhen Quan <<EMAIL>> 1744903016 +0800	reset: moving to HEAD
709b9c5710e7fe62593e22b7ab4c11cd0671f86b 302922014e1c7eacf331ad151bbe4c417cd10099 Sim Zhen Quan <<EMAIL>> 1744903026 +0800	checkout: moving from fix-migration-set-student-class-inactive-for-left-school to mockrun-23-add-primary-class-to-api-response
302922014e1c7eacf331ad151bbe4c417cd10099 b0f4fe4cab3a6d33f4b1bfc38464a110a497845a Sim Zhen Quan <<EMAIL>> 1744907634 +0800	commit: - Fix english class migration
b0f4fe4cab3a6d33f4b1bfc38464a110a497845a a1c1c7288b970880405695798bff8e42bea0e9f5 Sim Zhen Quan <<EMAIL>> 1744907751 +0800	checkout: moving from mockrun-23-add-primary-class-to-api-response to add-show-fe-billing-document-endpoint
a1c1c7288b970880405695798bff8e42bea0e9f5 ff1bcac7ca3a6e3e20efe352c773ca781056e185 Sim Zhen Quan <<EMAIL>> 1744907756 +0800	merge origin/main: Merge made by the 'ort' strategy.
ff1bcac7ca3a6e3e20efe352c773ca781056e185 6cad78fc497e7d86cb916557de0b7b6e962c4706 Sim Zhen Quan <<EMAIL>> 1744908775 +0800	commit: Add unique index for student_timetable
6cad78fc497e7d86cb916557de0b7b6e962c4706 c052125d5ee560b2cabcd3dae700470f42b33981 Sim Zhen Quan <<EMAIL>> 1744908811 +0800	checkout: moving from add-show-fe-billing-document-endpoint to main
c052125d5ee560b2cabcd3dae700470f42b33981 03f1c1209a1746b28478c97627a803d451428f74 Sim Zhen Quan <<EMAIL>> 1744908817 +0800	pull: Fast-forward
03f1c1209a1746b28478c97627a803d451428f74 03f1c1209a1746b28478c97627a803d451428f74 Sim Zhen Quan <<EMAIL>> 1744908839 +0800	checkout: moving from main to change-refresh-view-table-to-job
03f1c1209a1746b28478c97627a803d451428f74 79f5a8288467b08c9345c03a92bb07779ef4be2e Sim Zhen Quan <<EMAIL>> 1744910163 +0800	commit: Refresh materialized view using job instead of sync
79f5a8288467b08c9345c03a92bb07779ef4be2e 03f1c1209a1746b28478c97627a803d451428f74 Sim Zhen Quan <<EMAIL>> 1744910330 +0800	checkout: moving from change-refresh-view-table-to-job to main
03f1c1209a1746b28478c97627a803d451428f74 3c41baaf6548a8d2fa4c81521e06a5190e97c44c Sim Zhen Quan <<EMAIL>> 1744910337 +0800	pull: Fast-forward
3c41baaf6548a8d2fa4c81521e06a5190e97c44c 49c9cbb50817402ae72b137340d9aee4efc076a1 Sim Zhen Quan <<EMAIL>> 1744910373 +0800	checkout: moving from main to dev
49c9cbb50817402ae72b137340d9aee4efc076a1 6c62425c9ef948efbcec48ec44ec562a273cf4e8 Sim Zhen Quan <<EMAIL>> 1744910384 +0800	merge origin/main: Merge made by the 'ort' strategy.
6c62425c9ef948efbcec48ec44ec562a273cf4e8 a29f668526e1f486792ad6b0af4f741df528e838 Sim Zhen Quan <<EMAIL>> 1744940209 +0800	commit: Deployed to dev
a29f668526e1f486792ad6b0af4f741df528e838 3c41baaf6548a8d2fa4c81521e06a5190e97c44c Sim Zhen Quan <<EMAIL>> 1744940218 +0800	checkout: moving from dev to main
3c41baaf6548a8d2fa4c81521e06a5190e97c44c 2cd3853b8402b7d26ca55fe009d607d127293aa7 Sim Zhen Quan <<EMAIL>> 1744942129 +0800	commit: Update materialized view queue config
2cd3853b8402b7d26ca55fe009d607d127293aa7 162abff90e53b343dc54f560ce44df8e073fac2e Sim Zhen Quan <<EMAIL>> 1744942163 +0800	checkout: moving from main to update-attendance-index-param
162abff90e53b343dc54f560ce44df8e073fac2e ff096dfffa368a12f6195a995b7858895d2e5e8f Sim Zhen Quan <<EMAIL>> 1744942173 +0800	merge origin/main: Merge made by the 'ort' strategy.
ff096dfffa368a12f6195a995b7858895d2e5e8f 2cd3853b8402b7d26ca55fe009d607d127293aa7 Sim Zhen Quan <<EMAIL>> 1744943938 +0800	checkout: moving from update-attendance-index-param to main
2cd3853b8402b7d26ca55fe009d607d127293aa7 312a6d9834663f7e57a023fe88bcedac4333b2c4 Sim Zhen Quan <<EMAIL>> 1744943950 +0800	commit: Change attendance posting to 7.31am
312a6d9834663f7e57a023fe88bcedac4333b2c4 080a8cbb6cd6f64dda7980812858f1866a12690b Sim Zhen Quan <<EMAIL>> 1744948165 +0800	commit: Add hostel bed assignment data to employee resource
080a8cbb6cd6f64dda7980812858f1866a12690b c6b4b6188765abfbaf9133a5af70ef31cb1ae091 Sim Zhen Quan <<EMAIL>> 1744948186 +0800	checkout: moving from main to bugfix/semester-class-society-sort
c6b4b6188765abfbaf9133a5af70ef31cb1ae091 660a0c73439d19eaba09b52008085004703bc075 Sim Zhen Quan <<EMAIL>> 1744948190 +0800	merge origin/main: Merge made by the 'ort' strategy.
660a0c73439d19eaba09b52008085004703bc075 080a8cbb6cd6f64dda7980812858f1866a12690b Sim Zhen Quan <<EMAIL>> 1744948469 +0800	checkout: moving from bugfix/semester-class-society-sort to main
080a8cbb6cd6f64dda7980812858f1866a12690b e39a6f32914904ce1042f8fef0cd1e6175050276 Sim Zhen Quan <<EMAIL>> 1744948474 +0800	pull: Fast-forward
e39a6f32914904ce1042f8fef0cd1e6175050276 616e62624a1c2071025cbdeb0f123f86781ef4a6 Sim Zhen Quan <<EMAIL>> 1744948495 +0800	commit: Fix test cases
616e62624a1c2071025cbdeb0f123f86781ef4a6 a29f668526e1f486792ad6b0af4f741df528e838 Sim Zhen Quan <<EMAIL>> 1744948533 +0800	checkout: moving from main to dev
a29f668526e1f486792ad6b0af4f741df528e838 e2b86e8d3438b8d5c362443e4193518ac5a65ec6 Sim Zhen Quan <<EMAIL>> 1744948535 +0800	merge main: Merge made by the 'ort' strategy.
e2b86e8d3438b8d5c362443e4193518ac5a65ec6 6092faf475f1ac267b67580c311b2eb8dfbbd983 Sim Zhen Quan <<EMAIL>> 1744953895 +0800	commit: Deployed to dev
6092faf475f1ac267b67580c311b2eb8dfbbd983 4abe8e5afc2dad95a188b04a1d93223a79101f46 Sim Zhen Quan <<EMAIL>> 1744953934 +0800	checkout: moving from dev to bug/trainer-detail-report
4abe8e5afc2dad95a188b04a1d93223a79101f46 19ef8944d6a63414895799ac7abe08c062956b42 Sim Zhen Quan <<EMAIL>> 1744953939 +0800	merge origin/main: Merge made by the 'ort' strategy.
19ef8944d6a63414895799ac7abe08c062956b42 ff096dfffa368a12f6195a995b7858895d2e5e8f Sim Zhen Quan <<EMAIL>> 1744954340 +0800	checkout: moving from bug/trainer-detail-report to update-attendance-index-param
ff096dfffa368a12f6195a995b7858895d2e5e8f 1cab1476ce316b3d4b199652433c0f0b16ac572b Sim Zhen Quan <<EMAIL>> 1744954346 +0800	pull: Fast-forward
1cab1476ce316b3d4b199652433c0f0b16ac572b 68632fa2ec7a2340aa80b96da465834084bebc4f Sim Zhen Quan <<EMAIL>> 1744954346 +0800	merge origin/main: Merge made by the 'ort' strategy.
68632fa2ec7a2340aa80b96da465834084bebc4f 616e62624a1c2071025cbdeb0f123f86781ef4a6 Sim Zhen Quan <<EMAIL>> 1744955205 +0800	checkout: moving from update-attendance-index-param to main
616e62624a1c2071025cbdeb0f123f86781ef4a6 2efe9e640e9f253df4f7a41dd8c7944644a4fb8c Sim Zhen Quan <<EMAIL>> 1744955213 +0800	pull: Fast-forward
2efe9e640e9f253df4f7a41dd8c7944644a4fb8c 6092faf475f1ac267b67580c311b2eb8dfbbd983 Sim Zhen Quan <<EMAIL>> 1744955213 +0800	checkout: moving from main to dev
6092faf475f1ac267b67580c311b2eb8dfbbd983 acc24ecf57c87714d53c9b5f5a75a953a3816fb9 Sim Zhen Quan <<EMAIL>> 1744955217 +0800	merge origin/main: Merge made by the 'ort' strategy.
acc24ecf57c87714d53c9b5f5a75a953a3816fb9 711f344e9e63614d3dd1011fd429a46ffeb8976c Sim Zhen Quan <<EMAIL>> 1744955471 +0800	commit: Deployed to dev
711f344e9e63614d3dd1011fd429a46ffeb8976c 6a2ca3d30a11f11e791a8255ec76c0b95948f41c Sim Zhen Quan <<EMAIL>> 1744955505 +0800	checkout: moving from dev to fix/conduct-mark-entry-after-deadline
6a2ca3d30a11f11e791a8255ec76c0b95948f41c 22c80052105e744788824cc43c506960ab63dfcb Sim Zhen Quan <<EMAIL>> 1744955515 +0800	merge origin/main: Merge made by the 'ort' strategy.
22c80052105e744788824cc43c506960ab63dfcb df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 Sim Zhen Quan <<EMAIL>> 1744957769 +0800	commit: Reviewed
df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 Sim Zhen Quan <<EMAIL>> 1744957956 +0800	checkout: moving from fix/conduct-mark-entry-after-deadline to fix/conduct-mark-entry-after-deadline
df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 dd0de29611fe4d48e8f9236193eb3382b42d2600 Sim Zhen Quan <<EMAIL>> 1744957962 +0800	merge origin/main: Fast-forward
dd0de29611fe4d48e8f9236193eb3382b42d2600 f9347416dc68b4e471bc5c759cda8d971026db5d Sim Zhen Quan <<EMAIL>> 1744958005 +0800	checkout: moving from fix/conduct-mark-entry-after-deadline to fix/create-fee-month-validation
f9347416dc68b4e471bc5c759cda8d971026db5d 2fa333baba219c4ef097c579a4127442729e4864 Sim Zhen Quan <<EMAIL>> 1744958011 +0800	merge origin/main: Merge made by the 'ort' strategy.
2fa333baba219c4ef097c579a4127442729e4864 a9fee8e1411cafefda9d97acc80031c151c332c4 Sim Zhen Quan <<EMAIL>> 1744958624 +0800	commit: Reviewed
a9fee8e1411cafefda9d97acc80031c151c332c4 2efe9e640e9f253df4f7a41dd8c7944644a4fb8c Sim Zhen Quan <<EMAIL>> 1744958686 +0800	checkout: moving from fix/create-fee-month-validation to main
2efe9e640e9f253df4f7a41dd8c7944644a4fb8c 242f255099a17a2074eb79700dfa8b4b31dd00c7 Sim Zhen Quan <<EMAIL>> 1744958692 +0800	pull: Fast-forward
242f255099a17a2074eb79700dfa8b4b31dd00c7 711f344e9e63614d3dd1011fd429a46ffeb8976c Sim Zhen Quan <<EMAIL>> 1744958698 +0800	checkout: moving from main to dev
711f344e9e63614d3dd1011fd429a46ffeb8976c fd092df7a8ed07aeb4248220f499b1b15fe590ef Sim Zhen Quan <<EMAIL>> 1744959034 +0800	merge origin/main: Merge made by the 'ort' strategy.
fd092df7a8ed07aeb4248220f499b1b15fe590ef e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 Sim Zhen Quan <<EMAIL>> 1744959929 +0800	commit: Deployed to dev
e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 Sim Zhen Quan <<EMAIL>> 1744960326 +0800	reset: moving to HEAD
e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 242f255099a17a2074eb79700dfa8b4b31dd00c7 Sim Zhen Quan <<EMAIL>> 1744960328 +0800	checkout: moving from dev to main
242f255099a17a2074eb79700dfa8b4b31dd00c7 ee25d4b9229913f152347093bc304179e6dd83db Sim Zhen Quan <<EMAIL>> 1744964200 +0800	pull: Fast-forward
ee25d4b9229913f152347093bc304179e6dd83db 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744964235 +0800	commit: Fix discount typo
31c87146e23539e7aae60611b43616d1300d676a 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744964247 +0800	checkout: moving from main to permission-changes
31c87146e23539e7aae60611b43616d1300d676a 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744972766 +0800	reset: moving to HEAD
31c87146e23539e7aae60611b43616d1300d676a 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744973201 +0800	commit: Added permissions
3d28b2377d28514ecf9ea2296dc753057e58d01c 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744973293 +0800	checkout: moving from permission-changes to main
31c87146e23539e7aae60611b43616d1300d676a 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744973312 +0800	checkout: moving from main to permission-changes
3d28b2377d28514ecf9ea2296dc753057e58d01c 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744973325 +0800	checkout: moving from permission-changes to main
31c87146e23539e7aae60611b43616d1300d676a 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744973325 +0800	merge permission-changes: Fast-forward
3d28b2377d28514ecf9ea2296dc753057e58d01c e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 Sim Zhen Quan <<EMAIL>> 1744973361 +0800	checkout: moving from main to dev
e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 7fefd42f994e70336706ba4930c9bcf2d4caf9a1 Sim Zhen Quan <<EMAIL>> 1744973523 +0800	commit: Deployed to dev
7fefd42f994e70336706ba4930c9bcf2d4caf9a1 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744983130 +0800	checkout: moving from dev to main
3d28b2377d28514ecf9ea2296dc753057e58d01c 0d9401098843914569a56226dbbacf0edd683d5f Sim Zhen Quan <<EMAIL>> 1744983307 +0800	cherry-pick: Add migration step for withdrawal reasons in README
0d9401098843914569a56226dbbacf0edd683d5f 362cdeac8db6ce95f7daccc8b6251ae222da1617 Sim Zhen Quan <<EMAIL>> 1744984069 +0800	commit: Updated README.md
362cdeac8db6ce95f7daccc8b6251ae222da1617 c89d1e4dac7d52e6374ce88c030e7a57147f40f7 Sim Zhen Quan <<EMAIL>> 1745031251 +0800	commit: Minor fixes
c89d1e4dac7d52e6374ce88c030e7a57147f40f7 1de8cf744fce8c76fdfae4a7560618937b449edb Sim Zhen Quan <<EMAIL>> 1745031267 +0800	merge origin/main: Merge made by the 'ort' strategy.
1de8cf744fce8c76fdfae4a7560618937b449edb f04d1ba6674bfb8ff9353e4583bd5c87d2e8f25a Sim Zhen Quan <<EMAIL>> 1745031283 +0800	checkout: moving from main to library-report-by-school-rate-of-borrow-bug-fix
f04d1ba6674bfb8ff9353e4583bd5c87d2e8f25a 201483e214c305799b6f7912853df5bc74ae0a15 Sim Zhen Quan <<EMAIL>> 1745031289 +0800	merge origin/main: Merge made by the 'ort' strategy.
201483e214c305799b6f7912853df5bc74ae0a15 6f905748f094e589f2f75c3e8af33cd48b8dfdcd Sim Zhen Quan <<EMAIL>> 1745031621 +0800	commit: Remove unused imports
6f905748f094e589f2f75c3e8af33cd48b8dfdcd 9ece82160e297eed0c2e7fc705d0ffa720ef73b6 Sim Zhen Quan <<EMAIL>> 1745031716 +0800	checkout: moving from library-report-by-school-rate-of-borrow-bug-fix to bug/comprehensive-assessment-question
9ece82160e297eed0c2e7fc705d0ffa720ef73b6 9206b3913e2bc1ee322b964c53c33175b6fc33cb Sim Zhen Quan <<EMAIL>> 1745031729 +0800	merge origin/main: Merge made by the 'ort' strategy.
9206b3913e2bc1ee322b964c53c33175b6fc33cb 98ad096ba5e242f583fa4a8b930f5d8583528f37 Sim Zhen Quan <<EMAIL>> 1745032060 +0800	commit: Reviewed
98ad096ba5e242f583fa4a8b930f5d8583528f37 1d10afda9972850987210fdc8b9b425705ae40e9 Sim Zhen Quan <<EMAIL>> 1745032090 +0800	merge origin/main: Merge made by the 'ort' strategy.
1d10afda9972850987210fdc8b9b425705ae40e9 d8e9af1546a91b84f34c7e45745e94224e1eaf25 Sim Zhen Quan <<EMAIL>> 1745032131 +0800	checkout: moving from bug/comprehensive-assessment-question to bug/class-subject
d8e9af1546a91b84f34c7e45745e94224e1eaf25 39f0b36fba1c332d65361c25fc09d6b5fa87ed63 Sim Zhen Quan <<EMAIL>> 1745032138 +0800	merge origin/main: Merge made by the 'ort' strategy.
39f0b36fba1c332d65361c25fc09d6b5fa87ed63 cd3f6655a928910784f49c4b7f5989245f74b325 Sim Zhen Quan <<EMAIL>> 1745032355 +0800	commit: Reviewed
cd3f6655a928910784f49c4b7f5989245f74b325 39b0e7d220858416bf404810ffea3db93c1b50a5 Sim Zhen Quan <<EMAIL>> 1745032398 +0800	checkout: moving from bug/class-subject to bugfix/substitute-management-and-period-sorting
39b0e7d220858416bf404810ffea3db93c1b50a5 06afb10c4e54d4f288cb7f012f9cdad1066e2336 Sim Zhen Quan <<EMAIL>> 1745032409 +0800	merge origin/main: Merge made by the 'ort' strategy.
06afb10c4e54d4f288cb7f012f9cdad1066e2336 dbff68fec903b6fa79c2b38f1820c97bac88702f Sim Zhen Quan <<EMAIL>> 1745033815 +0800	commit: Reviewed Timetable Period
dbff68fec903b6fa79c2b38f1820c97bac88702f 0a9794a758412d019673392224983c491b92befe Sim Zhen Quan <<EMAIL>> 1745033861 +0800	checkout: moving from bugfix/substitute-management-and-period-sorting to update-book-sub-classification-resource
0a9794a758412d019673392224983c491b92befe 9d06d907dec94d3bda45b75ba4c3a373ccf976fa Sim Zhen Quan <<EMAIL>> 1745033867 +0800	merge origin/main: Merge made by the 'ort' strategy.
9d06d907dec94d3bda45b75ba4c3a373ccf976fa dadb4aa61aa39f3e5104dcdbe78a472b3dca4adf Sim Zhen Quan <<EMAIL>> 1745034129 +0800	commit: Reviewed
dadb4aa61aa39f3e5104dcdbe78a472b3dca4adf 1de8cf744fce8c76fdfae4a7560618937b449edb Sim Zhen Quan <<EMAIL>> 1745034275 +0800	checkout: moving from update-book-sub-classification-resource to main
1de8cf744fce8c76fdfae4a7560618937b449edb b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 Sim Zhen Quan <<EMAIL>> 1745034282 +0800	pull: Fast-forward
b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 7fefd42f994e70336706ba4930c9bcf2d4caf9a1 Sim Zhen Quan <<EMAIL>> 1745034283 +0800	checkout: moving from main to dev
7fefd42f994e70336706ba4930c9bcf2d4caf9a1 9e3d97f4cd466e649bd6eaa098fca9b2341e089e Sim Zhen Quan <<EMAIL>> 1745034293 +0800	merge origin/main: Merge made by the 'ort' strategy.
9e3d97f4cd466e649bd6eaa098fca9b2341e089e 4c51c41f4c3ee04fe9a939ca1df820fa685af51f Sim Zhen Quan <<EMAIL>> 1745034587 +0800	commit: Deployed to dev
4c51c41f4c3ee04fe9a939ca1df820fa685af51f 104de8ae951e4b3ce8057d9d6087e1a1c2ec311c Sim Zhen Quan <<EMAIL>> 1745034699 +0800	checkout: moving from dev to bug/substitute-record
104de8ae951e4b3ce8057d9d6087e1a1c2ec311c d55c5c6f02cc64adb8fbf44338923ee7f1de6393 Sim Zhen Quan <<EMAIL>> 1745034707 +0800	merge origin/main: Merge made by the 'ort' strategy.
d55c5c6f02cc64adb8fbf44338923ee7f1de6393 0738542dd798e2aab8d0bbdcf052c81c34739429 Sim Zhen Quan <<EMAIL>> 1745035362 +0800	commit: Reviewed
0738542dd798e2aab8d0bbdcf052c81c34739429 dbff68fec903b6fa79c2b38f1820c97bac88702f Sim Zhen Quan <<EMAIL>> 1745035491 +0800	checkout: moving from bug/substitute-record to bugfix/substitute-management-and-period-sorting
dbff68fec903b6fa79c2b38f1820c97bac88702f 31535d00ef16c1d1f2cc4460f9fb3bc1f644f607 Sim Zhen Quan <<EMAIL>> 1745035500 +0800	pull: Fast-forward
31535d00ef16c1d1f2cc4460f9fb3bc1f644f607 27412297c8ec38ed9320982c4654b037195f8630 Sim Zhen Quan <<EMAIL>> 1745035577 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into bugfix/substitute-management-and-period-sorting
27412297c8ec38ed9320982c4654b037195f8630 cee31b61e2c8d90f33d1b07730feb8624ee5cb48 Sim Zhen Quan <<EMAIL>> 1745035840 +0800	merge origin/main: Fast-forward
cee31b61e2c8d90f33d1b07730feb8624ee5cb48 b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 Sim Zhen Quan <<EMAIL>> 1745035883 +0800	checkout: moving from bugfix/substitute-management-and-period-sorting to main
b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 cee31b61e2c8d90f33d1b07730feb8624ee5cb48 Sim Zhen Quan <<EMAIL>> 1745035887 +0800	pull: Fast-forward
cee31b61e2c8d90f33d1b07730feb8624ee5cb48 e536d2c7d350b594202c0612f56d48803a263a0e Sim Zhen Quan <<EMAIL>> 1745035899 +0800	checkout: moving from main to class-attendance-taking-display-period-label-name
e536d2c7d350b594202c0612f56d48803a263a0e cee31b61e2c8d90f33d1b07730feb8624ee5cb48 Sim Zhen Quan <<EMAIL>> 1745036743 +0800	checkout: moving from class-attendance-taking-display-period-label-name to main
cee31b61e2c8d90f33d1b07730feb8624ee5cb48 f8ab0638253a82d9fe23a52889a0df4b9d7fe42a Sim Zhen Quan <<EMAIL>> 1745036748 +0800	pull: Fast-forward
f8ab0638253a82d9fe23a52889a0df4b9d7fe42a 4c51c41f4c3ee04fe9a939ca1df820fa685af51f Sim Zhen Quan <<EMAIL>> 1745036749 +0800	checkout: moving from main to dev
4c51c41f4c3ee04fe9a939ca1df820fa685af51f fe65c30244051064701abcd411022091c46a3995 Sim Zhen Quan <<EMAIL>> 1745036755 +0800	merge origin/main: Merge made by the 'ort' strategy.
fe65c30244051064701abcd411022091c46a3995 f601b6ba67521136a0bb15eaf4451900c995925e Sim Zhen Quan <<EMAIL>> 1745041581 +0800	commit: Deployed to dev
f601b6ba67521136a0bb15eaf4451900c995925e dee179be8780fcff4019e50257aedd457550a8fa Sim Zhen Quan <<EMAIL>> 1745041588 +0800	checkout: moving from dev to fix-batch-delete
dee179be8780fcff4019e50257aedd457550a8fa 2007f2dd1f6aef85238228341ab83bfc3af658cc Sim Zhen Quan <<EMAIL>> 1745041962 +0800	commit: Added validation for timeslot override batch id delete
2007f2dd1f6aef85238228341ab83bfc3af658cc df40fcbaccdb2b7ab0388c186fc8cad22a50ead9 Sim Zhen Quan <<EMAIL>> 1745041985 +0800	merge origin/fix-batch-delete: Merge made by the 'ort' strategy.
df40fcbaccdb2b7ab0388c186fc8cad22a50ead9 27a81db5288bdea14ee3d8e1ca83cff161373e95 Sim Zhen Quan <<EMAIL>> 1745043199 +0800	pull: Fast-forward
27a81db5288bdea14ee3d8e1ca83cff161373e95 c8260c126fd6e8d0966ca8332ec347f6f071b7c3 Sim Zhen Quan <<EMAIL>> 1745043432 +0800	commit: Added validation for user inbox batch id delete
c8260c126fd6e8d0966ca8332ec347f6f071b7c3 6acbfdd82be4e29c6a2d12d269937f43db4fc6cb Sim Zhen Quan <<EMAIL>> 1745043444 +0800	merge origin/fix-batch-delete: Merge made by the 'ort' strategy.
6acbfdd82be4e29c6a2d12d269937f43db4fc6cb f8ab0638253a82d9fe23a52889a0df4b9d7fe42a Sim Zhen Quan <<EMAIL>> 1745043726 +0800	checkout: moving from fix-batch-delete to main
f8ab0638253a82d9fe23a52889a0df4b9d7fe42a 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745043732 +0800	pull: Fast-forward
1750e710732442cc41e8574cb50ad01d7e9889e2 f601b6ba67521136a0bb15eaf4451900c995925e Sim Zhen Quan <<EMAIL>> 1745043732 +0800	checkout: moving from main to dev
f601b6ba67521136a0bb15eaf4451900c995925e 615bf0022cbe7d251f39eb8bca77673f22fc7b65 Sim Zhen Quan <<EMAIL>> 1745043739 +0800	merge origin/main: Merge made by the 'ort' strategy.
615bf0022cbe7d251f39eb8bca77673f22fc7b65 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745046301 +0800	checkout: moving from dev to main
1750e710732442cc41e8574cb50ad01d7e9889e2 33e4bcbedbd0b76731f5bec4c345f214436e233e Sim Zhen Quan <<EMAIL>> 1745117051 +0800	checkout: moving from main to feature/daily-attendance-report
33e4bcbedbd0b76731f5bec4c345f214436e233e c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 Sim Zhen Quan <<EMAIL>> 1745117061 +0800	merge origin/main: Merge made by the 'ort' strategy.
c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745121593 +0800	checkout: moving from feature/daily-attendance-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745121670 +0800	checkout: moving from main to fix-attendance-posting
1750e710732442cc41e8574cb50ad01d7e9889e2 3f75e5dce55307444f1756fb55b88e646b63c66d Sim Zhen Quan <<EMAIL>> 1745121718 +0800	commit: Enhance service for attendance posting
3f75e5dce55307444f1756fb55b88e646b63c66d 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745123623 +0800	checkout: moving from fix-attendance-posting to main
1750e710732442cc41e8574cb50ad01d7e9889e2 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745123632 +0800	checkout: moving from main to staging/2025-04-20
1750e710732442cc41e8574cb50ad01d7e9889e2 3f75e5dce55307444f1756fb55b88e646b63c66d Sim Zhen Quan <<EMAIL>> 1745123646 +0800	merge fix-attendance-posting: Fast-forward
3f75e5dce55307444f1756fb55b88e646b63c66d c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 Sim Zhen Quan <<EMAIL>> 1745123992 +0800	checkout: moving from staging/2025-04-20 to feature/daily-attendance-report
c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 3378f3a1d86b024fc9067a93418dbe537a601834 Sim Zhen Quan <<EMAIL>> 1745124004 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
3378f3a1d86b024fc9067a93418dbe537a601834 615bf0022cbe7d251f39eb8bca77673f22fc7b65 Sim Zhen Quan <<EMAIL>> 1745125074 +0800	checkout: moving from feature/daily-attendance-report to dev
615bf0022cbe7d251f39eb8bca77673f22fc7b65 34b49a7c584632b9864ed5436c23f9a04c968f2f Sim Zhen Quan <<EMAIL>> 1745125085 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
34b49a7c584632b9864ed5436c23f9a04c968f2f f491b6a11ed14b39908d6c9dceb74350b8e64985 Sim Zhen Quan <<EMAIL>> 1745129484 +0800	commit: Deploy to dev
f491b6a11ed14b39908d6c9dceb74350b8e64985 812a53178348da4d3174ca418e458ab1ecde30aa Sim Zhen Quan <<EMAIL>> 1745129502 +0800	checkout: moving from dev to feature/class-attendance-taking-report
812a53178348da4d3174ca418e458ab1ecde30aa c6b1e520f1a9b04882346b46bc3e885c033265f4 Sim Zhen Quan <<EMAIL>> 1745129514 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
c6b1e520f1a9b04882346b46bc3e885c033265f4 72dc0eb09fca223dbddfef6664e8810d12dc4664 Sim Zhen Quan <<EMAIL>> 1745131354 +0800	commit: Need to implement for timeslot override
72dc0eb09fca223dbddfef6664e8810d12dc4664 f491b6a11ed14b39908d6c9dceb74350b8e64985 Sim Zhen Quan <<EMAIL>> 1745131425 +0800	checkout: moving from feature/class-attendance-taking-report to dev
f491b6a11ed14b39908d6c9dceb74350b8e64985 18516b8d64ca2b232fa9f55688ec7d72b2d56a41 Sim Zhen Quan <<EMAIL>> 1745131437 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
18516b8d64ca2b232fa9f55688ec7d72b2d56a41 c9ae5a5a8f06c7890004e1fb4d269214537e31a4 Sim Zhen Quan <<EMAIL>> 1745131504 +0800	checkout: moving from dev to feature/attendance-mark-deduction-report
c9ae5a5a8f06c7890004e1fb4d269214537e31a4 18516b8d64ca2b232fa9f55688ec7d72b2d56a41 Sim Zhen Quan <<EMAIL>> 1745131514 +0800	checkout: moving from feature/attendance-mark-deduction-report to dev
18516b8d64ca2b232fa9f55688ec7d72b2d56a41 ddaaf168a25c2270ad2b028c4006ec017525c71b Sim Zhen Quan <<EMAIL>> 1745131812 +0800	commit: Deployed to dev
ddaaf168a25c2270ad2b028c4006ec017525c71b ddaaf168a25c2270ad2b028c4006ec017525c71b Sim Zhen Quan <<EMAIL>> 1745131965 +0800	reset: moving to HEAD
ddaaf168a25c2270ad2b028c4006ec017525c71b c9ae5a5a8f06c7890004e1fb4d269214537e31a4 Sim Zhen Quan <<EMAIL>> 1745131966 +0800	checkout: moving from dev to feature/attendance-mark-deduction-report
c9ae5a5a8f06c7890004e1fb4d269214537e31a4 df666e4804ff8675ee545245974d6d06c4b8f7e5 Sim Zhen Quan <<EMAIL>> 1745131970 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
df666e4804ff8675ee545245974d6d06c4b8f7e5 3cb7bec7e653b60a26bbc67d75374a4a584bd1de Sim Zhen Quan <<EMAIL>> 1745136247 +0800	commit: Review WIP
3cb7bec7e653b60a26bbc67d75374a4a584bd1de 3f75e5dce55307444f1756fb55b88e646b63c66d Sim Zhen Quan <<EMAIL>> 1745136277 +0800	checkout: moving from feature/attendance-mark-deduction-report to staging/2025-04-20
3f75e5dce55307444f1756fb55b88e646b63c66d 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745136301 +0800	commit: Added relationship include for new flag
5e37d45a25313bec7345c02b405a5c02488c6a0b 2123dfed7dfb4d9e58249cb0bb7134f729100bbc Sim Zhen Quan <<EMAIL>> 1745136403 +0800	checkout: moving from staging/2025-04-20 to report/attendance-report
2123dfed7dfb4d9e58249cb0bb7134f729100bbc ebd177ab08593a33f2108591c59a09f6a8c3e8bc Sim Zhen Quan <<EMAIL>> 1745136738 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-20' into report/attendance-report
ebd177ab08593a33f2108591c59a09f6a8c3e8bc ebd177ab08593a33f2108591c59a09f6a8c3e8bc Sim Zhen Quan <<EMAIL>> 1745136790 +0800	checkout: moving from report/attendance-report to report/attendance-report
ebd177ab08593a33f2108591c59a09f6a8c3e8bc b54fb741c760ccc3892c6748d8af300a316fb332 Sim Zhen Quan <<EMAIL>> 1745139867 +0800	commit: Reviewed
b54fb741c760ccc3892c6748d8af300a316fb332 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745139933 +0800	checkout: moving from report/attendance-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 ddaaf168a25c2270ad2b028c4006ec017525c71b Sim Zhen Quan <<EMAIL>> 1745139940 +0800	checkout: moving from main to dev
ddaaf168a25c2270ad2b028c4006ec017525c71b de5a454d5662962c30b26f21d39c3d8e3ba070b8 Sim Zhen Quan <<EMAIL>> 1745139951 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
de5a454d5662962c30b26f21d39c3d8e3ba070b8 370d9a5661e6cdac120218da6bbade3c4eacd050 Sim Zhen Quan <<EMAIL>> 1745140108 +0800	commit: Deployed to DEV
370d9a5661e6cdac120218da6bbade3c4eacd050 30da17d76fae6ef08726f7882d3374d14470d0de Sim Zhen Quan <<EMAIL>> 1745140631 +0800	checkout: moving from dev to academy-student-analysis-report
30da17d76fae6ef08726f7882d3374d14470d0de 47944782ad2c7ead7cf1076b1120b5458e0ea7c6 Sim Zhen Quan <<EMAIL>> 1745140813 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-20' into academy-student-analysis-report
47944782ad2c7ead7cf1076b1120b5458e0ea7c6 47944782ad2c7ead7cf1076b1120b5458e0ea7c6 Sim Zhen Quan <<EMAIL>> 1745140950 +0800	checkout: moving from academy-student-analysis-report to academy-student-analysis-report
47944782ad2c7ead7cf1076b1120b5458e0ea7c6 47944782ad2c7ead7cf1076b1120b5458e0ea7c6 Sim Zhen Quan <<EMAIL>> 1745142076 +0800	checkout: moving from academy-student-analysis-report to academy-student-analysis-report
47944782ad2c7ead7cf1076b1120b5458e0ea7c6 907312674a34edd23b29554ccdb6b283c9037377 Sim Zhen Quan <<EMAIL>> 1745142768 +0800	commit: Reviewed
907312674a34edd23b29554ccdb6b283c9037377 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745142789 +0800	checkout: moving from academy-student-analysis-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 370d9a5661e6cdac120218da6bbade3c4eacd050 Sim Zhen Quan <<EMAIL>> 1745142794 +0800	checkout: moving from main to dev
370d9a5661e6cdac120218da6bbade3c4eacd050 2d8e5dcf65d5fdc117ce6f4a8a4ec8c0729142f0 Sim Zhen Quan <<EMAIL>> 1745142861 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
2d8e5dcf65d5fdc117ce6f4a8a4ec8c0729142f0 8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d Sim Zhen Quan <<EMAIL>> 1745143054 +0800	commit: Deployed to dev
8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d 3a0d7fc253da027d7f03351a6e923745121bf683 Sim Zhen Quan <<EMAIL>> 1745143059 +0800	checkout: moving from dev to SCRUM-178-training-attendance-report
3a0d7fc253da027d7f03351a6e923745121bf683 45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 Sim Zhen Quan <<EMAIL>> 1745143290 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-20' into SCRUM-178-training-attendance-report
45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 Sim Zhen Quan <<EMAIL>> 1745148150 +0800	reset: moving to HEAD
45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745148152 +0800	checkout: moving from SCRUM-178-training-attendance-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745148161 +0800	checkout: moving from main to migration-fixes
1750e710732442cc41e8574cb50ad01d7e9889e2 0deb82910fcfac54bbea47b0218b597329864d92 Sim Zhen Quan <<EMAIL>> 1745148197 +0800	commit: Added migratePeriodAttendancePlaceholderOnly
0deb82910fcfac54bbea47b0218b597329864d92 0deb82910fcfac54bbea47b0218b597329864d92 Sim Zhen Quan <<EMAIL>> 1745150417 +0800	reset: moving to HEAD
0deb82910fcfac54bbea47b0218b597329864d92 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150418 +0800	checkout: moving from migration-fixes to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150478 +0800	checkout: moving from staging/2025-04-20 to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150514 +0800	checkout: moving from staging/2025-04-20 to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745150687 +0800	checkout: moving from staging/2025-04-20 to main
1750e710732442cc41e8574cb50ad01d7e9889e2 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150695 +0800	checkout: moving from main to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150722 +0800	reset: moving to HEAD
5e37d45a25313bec7345c02b405a5c02488c6a0b c8b2d2872a4e22d180530043264761bb28e046d1 Sim Zhen Quan <<EMAIL>> 1745150886 +0800	pull: Merge made by the 'ort' strategy.
c8b2d2872a4e22d180530043264761bb28e046d1 1530f28359caf1c251a2a5179b781c4ce45bd192 Sim Zhen Quan <<EMAIL>> 1745151994 +0800	checkout: moving from staging/2025-04-20 to path-leave-application-individual-override
1530f28359caf1c251a2a5179b781c4ce45bd192 98af6e2060522aec6c7b9606c1caee5357b248e3 Sim Zhen Quan <<EMAIL>> 1745152012 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
98af6e2060522aec6c7b9606c1caee5357b248e3 8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d Sim Zhen Quan <<EMAIL>> 1745161914 +0800	checkout: moving from path-leave-application-individual-override to dev
8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d 402a1a25364b8703c6d3c618147b780972db6f1d Sim Zhen Quan <<EMAIL>> 1745161935 +0800	merge migration-fixes: Merge made by the 'ort' strategy.
402a1a25364b8703c6d3c618147b780972db6f1d ec6465f0fa959f65f69d84254564eb704b115043 Sim Zhen Quan <<EMAIL>> 1745162167 +0800	merge origin/migration-fixes: Merge made by the 'ort' strategy.
ec6465f0fa959f65f69d84254564eb704b115043 ffec0219f1dbfc5487691af800de32428c8faa91 Sim Zhen Quan <<EMAIL>> 1745165092 +0800	commit: Deployed to DEV
ffec0219f1dbfc5487691af800de32428c8faa91 98af6e2060522aec6c7b9606c1caee5357b248e3 Sim Zhen Quan <<EMAIL>> 1745165337 +0800	checkout: moving from dev to path-leave-application-individual-override
98af6e2060522aec6c7b9606c1caee5357b248e3 c8b2d2872a4e22d180530043264761bb28e046d1 Sim Zhen Quan <<EMAIL>> 1745166052 +0800	checkout: moving from path-leave-application-individual-override to staging/2025-04-20
c8b2d2872a4e22d180530043264761bb28e046d1 d1ddd1693ec52399af00a51ef91056351344b702 Sim Zhen Quan <<EMAIL>> 1745166059 +0800	merge origin/main: Merge made by the 'ort' strategy.
d1ddd1693ec52399af00a51ef91056351344b702 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745167631 +0800	checkout: moving from staging/2025-04-20 to main
1750e710732442cc41e8574cb50ad01d7e9889e2 92db3a8929dbf699e904d58aac529e8dd8602f3d Sim Zhen Quan <<EMAIL>> 1745167637 +0800	pull: Fast-forward
92db3a8929dbf699e904d58aac529e8dd8602f3d bd27fadffd2273ee5897a23741b6cda3a0ba3692 Sim Zhen Quan <<EMAIL>> 1745201063 +0800	pull: Fast-forward
bd27fadffd2273ee5897a23741b6cda3a0ba3692 d8df21279f1f0bd502546519f2a4eb3b28086aab Sim Zhen Quan <<EMAIL>> 1745201814 +0800	commit: Allow filter by late attendance also
d8df21279f1f0bd502546519f2a4eb3b28086aab ffec0219f1dbfc5487691af800de32428c8faa91 Sim Zhen Quan <<EMAIL>> 1745201824 +0800	checkout: moving from main to dev
ffec0219f1dbfc5487691af800de32428c8faa91 c98f77f26232894d0719910f90060fe1631b6b4c Sim Zhen Quan <<EMAIL>> 1745201838 +0800	merge origin/main: Merge made by the 'ort' strategy.
c98f77f26232894d0719910f90060fe1631b6b4c 2e53166ce929c38f5490acadcaf01e28b8888205 Sim Zhen Quan <<EMAIL>> 1745204423 +0800	commit: Deployed to dev
2e53166ce929c38f5490acadcaf01e28b8888205 d8df21279f1f0bd502546519f2a4eb3b28086aab Sim Zhen Quan <<EMAIL>> 1745204451 +0800	checkout: moving from dev to main
d8df21279f1f0bd502546519f2a4eb3b28086aab 7d1807feb8e037d564a1f9169b2184db18167cb7 Sim Zhen Quan <<EMAIL>> 1745204456 +0800	pull: Fast-forward
7d1807feb8e037d564a1f9169b2184db18167cb7 edb1e9a85bdbebf5df03babb3d2dbeef4d827623 Sim Zhen Quan <<EMAIL>> 1745228186 +0800	pull: Fast-forward
edb1e9a85bdbebf5df03babb3d2dbeef4d827623 dad60aa80fef74f84ff2c7cf750edbfcd649272f Sim Zhen Quan <<EMAIL>> 1745228189 +0800	commit: Deployed to prd
dad60aa80fef74f84ff2c7cf750edbfcd649272f dad60aa80fef74f84ff2c7cf750edbfcd649272f Sim Zhen Quan <<EMAIL>> 1745228212 +0800	checkout: moving from main to fix-migration-english-class
dad60aa80fef74f84ff2c7cf750edbfcd649272f 2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca Sim Zhen Quan <<EMAIL>> 1745231168 +0800	commit: Added patch for english class and primary class placeholder
2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca 2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca Sim Zhen Quan <<EMAIL>> 1745238724 +0800	checkout: moving from fix-migration-english-class to staging/2025-04-21
2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca 429b34422c35b2be30dafbe7756043e127ff7d63 Sim Zhen Quan <<EMAIL>> 1745238771 +0800	checkout: moving from staging/2025-04-21 to class-attendance-taking-is-editable-check-school-attendance-status
429b34422c35b2be30dafbe7756043e127ff7d63 a2c3f5b75ed2db8006e8d5b775627c157c516af5 Sim Zhen Quan <<EMAIL>> 1745238783 +0800	merge origin/main: Merge made by the 'ort' strategy.
a2c3f5b75ed2db8006e8d5b775627c157c516af5 f451a18c90c71936e3ea882f1d3eaeb31aaf6422 Sim Zhen Quan <<EMAIL>> 1745239376 +0800	checkout: moving from class-attendance-taking-is-editable-check-school-attendance-status to substitute-teacher-timeslot-display-period-label
f451a18c90c71936e3ea882f1d3eaeb31aaf6422 2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca Sim Zhen Quan <<EMAIL>> 1745239386 +0800	checkout: moving from substitute-teacher-timeslot-display-period-label to staging/2025-04-21
2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca 853c77ca7f2658924cbe984060bbae3935ed8dfd Sim Zhen Quan <<EMAIL>> 1745239391 +0800	pull: Fast-forward
853c77ca7f2658924cbe984060bbae3935ed8dfd f451a18c90c71936e3ea882f1d3eaeb31aaf6422 Sim Zhen Quan <<EMAIL>> 1745239397 +0800	checkout: moving from staging/2025-04-21 to substitute-teacher-timeslot-display-period-label
f451a18c90c71936e3ea882f1d3eaeb31aaf6422 853c77ca7f2658924cbe984060bbae3935ed8dfd Sim Zhen Quan <<EMAIL>> 1745239542 +0800	checkout: moving from substitute-teacher-timeslot-display-period-label to staging/2025-04-21
853c77ca7f2658924cbe984060bbae3935ed8dfd f451a18c90c71936e3ea882f1d3eaeb31aaf6422 Sim Zhen Quan <<EMAIL>> 1745239551 +0800	checkout: moving from staging/2025-04-21 to substitute-teacher-timeslot-display-period-label
f451a18c90c71936e3ea882f1d3eaeb31aaf6422 5355bf9ddd07f4769ac8b8b59151ec9e3967f012 Sim Zhen Quan <<EMAIL>> 1745239569 +0800	merge origin/staging/2025-04-21: Merge made by the 'ort' strategy.
5355bf9ddd07f4769ac8b8b59151ec9e3967f012 046fb2133ff9e821364b471792815ca3af275e4a Sim Zhen Quan <<EMAIL>> 1745244888 +0800	commit: Reviewed
046fb2133ff9e821364b471792815ca3af275e4a 853c77ca7f2658924cbe984060bbae3935ed8dfd Sim Zhen Quan <<EMAIL>> 1745244991 +0800	checkout: moving from substitute-teacher-timeslot-display-period-label to staging/2025-04-21
853c77ca7f2658924cbe984060bbae3935ed8dfd a941e967b7366dcc0282360ae6ea0fa38192ab7d Sim Zhen Quan <<EMAIL>> 1745244996 +0800	pull: Fast-forward
a941e967b7366dcc0282360ae6ea0fa38192ab7d 095bc07fe294ba92bb9759a8308739a007a4d05f Sim Zhen Quan <<EMAIL>> 1745244997 +0800	merge origin/main: Merge made by the 'ort' strategy.
095bc07fe294ba92bb9759a8308739a007a4d05f 2e53166ce929c38f5490acadcaf01e28b8888205 Sim Zhen Quan <<EMAIL>> 1745245073 +0800	checkout: moving from staging/2025-04-21 to dev
2e53166ce929c38f5490acadcaf01e28b8888205 d987dc10b90d16fa9b5ac8338a9dd98d312c5423 Sim Zhen Quan <<EMAIL>> 1745245089 +0800	merge staging/2025-04-21: Merge made by the 'ort' strategy.
d987dc10b90d16fa9b5ac8338a9dd98d312c5423 f27433f58c1eb53b8ffb1c39c8a59a33e449a9fd Sim Zhen Quan <<EMAIL>> 1745245623 +0800	commit: Deployed to dev
f27433f58c1eb53b8ffb1c39c8a59a33e449a9fd 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745246206 +0800	merge origin/main: Merge made by the 'ort' strategy.
65e84b42c8dac823e70c1c83416f5fcffb8db432 095bc07fe294ba92bb9759a8308739a007a4d05f Sim Zhen Quan <<EMAIL>> 1745248532 +0800	checkout: moving from dev to staging/2025-04-21
095bc07fe294ba92bb9759a8308739a007a4d05f bdad9f65d99324a6b2a425167fcb16b8a28bd35d Sim Zhen Quan <<EMAIL>> 1745248538 +0800	merge origin/main: Merge made by the 'ort' strategy.
bdad9f65d99324a6b2a425167fcb16b8a28bd35d 7475f6310adb76ace109c59a70b8bbe07e6a8037 Sim Zhen Quan <<EMAIL>> 1745249717 +0800	commit: Revert eager loading
7475f6310adb76ace109c59a70b8bbe07e6a8037 dad60aa80fef74f84ff2c7cf750edbfcd649272f Sim Zhen Quan <<EMAIL>> 1745249740 +0800	checkout: moving from staging/2025-04-21 to main
dad60aa80fef74f84ff2c7cf750edbfcd649272f 3a552cd0f666ccc554d1c6174e4c131dba90a27e Sim Zhen Quan <<EMAIL>> 1745249748 +0800	pull: Fast-forward
3a552cd0f666ccc554d1c6174e4c131dba90a27e a420605c2b6183706543c11dcdb3c20156e80498 Sim Zhen Quan <<EMAIL>> 1745249959 +0800	commit: Deployed to PRD
a420605c2b6183706543c11dcdb3c20156e80498 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745289216 +0800	pull: Fast-forward
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 991072c655211e4651bda0577cf7957591038bb2 Sim Zhen Quan <<EMAIL>> 1745289220 +0800	checkout: moving from main to exam-gross-average
991072c655211e4651bda0577cf7957591038bb2 fa52e9b003c24908f5bc870f9a352a5c51b44819 Sim Zhen Quan <<EMAIL>> 1745289229 +0800	merge origin/main: Merge made by the 'ort' strategy.
fa52e9b003c24908f5bc870f9a352a5c51b44819 fa52e9b003c24908f5bc870f9a352a5c51b44819 Sim Zhen Quan <<EMAIL>> 1745289380 +0800	checkout: moving from exam-gross-average to exam-module-changes
fa52e9b003c24908f5bc870f9a352a5c51b44819 3044f5b5dc070cf2f0edd5fdc2c43a39357ea9b9 Sim Zhen Quan <<EMAIL>> 1745289507 +0800	checkout: moving from exam-module-changes to exam-expire-student-framework
3044f5b5dc070cf2f0edd5fdc2c43a39357ea9b9 eb5ef264e51faafbf53ef0c8c2be55f0a8e9ff5e Sim Zhen Quan <<EMAIL>> 1745289515 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
eb5ef264e51faafbf53ef0c8c2be55f0a8e9ff5e 385f173a542fb05b8a66ee1917253c4737de00ca Sim Zhen Quan <<EMAIL>> 1745292843 +0800	commit: Reviewed
385f173a542fb05b8a66ee1917253c4737de00ca b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745298890 +0800	checkout: moving from exam-expire-student-framework to main
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745300055 +0800	checkout: moving from main to main
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745300069 +0800	checkout: moving from main to staging/2025-04-22
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 a4f94999e656203b7b592a8fbb11c547313dc485 Sim Zhen Quan <<EMAIL>> 1745300163 +0800	checkout: moving from staging/2025-04-22 to daily-collection-report-total-and-net-amount
a4f94999e656203b7b592a8fbb11c547313dc485 8e7379d9fea58d7b23632a6df372e57ad8481e22 Sim Zhen Quan <<EMAIL>> 1745300571 +0800	commit: Reviewed
8e7379d9fea58d7b23632a6df372e57ad8481e22 3d2b1b219762f4376edd406637a55996b4625025 Sim Zhen Quan <<EMAIL>> 1745306659 +0800	checkout: moving from daily-collection-report-total-and-net-amount to student-absent-report-enhancement
3d2b1b219762f4376edd406637a55996b4625025 45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 Sim Zhen Quan <<EMAIL>> 1745307317 +0800	checkout: moving from student-absent-report-enhancement to SCRUM-178-training-attendance-report
45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 8c4ee3133e3d79ca4fa5ce67cd1c40c0ae607d0a Sim Zhen Quan <<EMAIL>> 1745307386 +0800	commit (merge): Merge branch 'staging/2025-04-22' into SCRUM-178-training-attendance-report
8c4ee3133e3d79ca4fa5ce67cd1c40c0ae607d0a 3eae46667bcf88f256351a3e26531b2c15fe0c3c Sim Zhen Quan <<EMAIL>> 1745307410 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
3eae46667bcf88f256351a3e26531b2c15fe0c3c 2e13f1d59f403b83e6f5de1d51ab5a4c1671c61b Sim Zhen Quan <<EMAIL>> 1745307458 +0800	checkout: moving from SCRUM-178-training-attendance-report to bugfix/library-book-loan-report-employee-class-relation
2e13f1d59f403b83e6f5de1d51ab5a4c1671c61b 3d8efb33aabf1e91600830691028954488208cfb Sim Zhen Quan <<EMAIL>> 1745307480 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
3d8efb33aabf1e91600830691028954488208cfb 754cc351c16360487b5e5cfc74483adba3fa7c87 Sim Zhen Quan <<EMAIL>> 1745308651 +0800	commit: Fix query
754cc351c16360487b5e5cfc74483adba3fa7c87 c969d3c6a10297a8fd28d194d2a93e0bed39c764 Sim Zhen Quan <<EMAIL>> 1745309078 +0800	checkout: moving from bugfix/library-book-loan-report-employee-class-relation to issuelog-40-prevent-void-paid-invoices
c969d3c6a10297a8fd28d194d2a93e0bed39c764 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745310455 +0800	checkout: moving from issuelog-40-prevent-void-paid-invoices to staging/2025-04-22
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 aff45193ef22474a6ccebe4e1994effa40cd201f Sim Zhen Quan <<EMAIL>> 1745310462 +0800	pull: Fast-forward
aff45193ef22474a6ccebe4e1994effa40cd201f 687cc351d2a63b3afcbd40f7be09a9c406cba247 Sim Zhen Quan <<EMAIL>> 1745310526 +0800	commit: Add student_number_wildcard to form request
687cc351d2a63b3afcbd40f7be09a9c406cba247 3eae46667bcf88f256351a3e26531b2c15fe0c3c Sim Zhen Quan <<EMAIL>> 1745310696 +0800	checkout: moving from staging/2025-04-22 to SCRUM-178-training-attendance-report
3eae46667bcf88f256351a3e26531b2c15fe0c3c 8f746462bafc1dcea7240b76582a0b0b834a8b30 Sim Zhen Quan <<EMAIL>> 1745310705 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
8f746462bafc1dcea7240b76582a0b0b834a8b30 6ec487d3972bc31b1988835154d429d2f5303af1 Sim Zhen Quan <<EMAIL>> 1745312926 +0800	commit: Reviewed
6ec487d3972bc31b1988835154d429d2f5303af1 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745313082 +0800	checkout: moving from SCRUM-178-training-attendance-report to dev
65e84b42c8dac823e70c1c83416f5fcffb8db432 687cc351d2a63b3afcbd40f7be09a9c406cba247 Sim Zhen Quan <<EMAIL>> 1745313092 +0800	checkout: moving from dev to staging/2025-04-22
687cc351d2a63b3afcbd40f7be09a9c406cba247 b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 Sim Zhen Quan <<EMAIL>> 1745313098 +0800	pull: Fast-forward
b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745313107 +0800	checkout: moving from staging/2025-04-22 to dev
65e84b42c8dac823e70c1c83416f5fcffb8db432 b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 Sim Zhen Quan <<EMAIL>> 1745313120 +0800	checkout: moving from dev to staging/2025-04-22
b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745313127 +0800	checkout: moving from staging/2025-04-22 to dev
65e84b42c8dac823e70c1c83416f5fcffb8db432 7035b3e11b37d08f57322c8c935b99873592b2e4 Sim Zhen Quan <<EMAIL>> 1745313138 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
7035b3e11b37d08f57322c8c935b99873592b2e4 52eb30d8571996330da5d5c33a26d357f26539af Sim Zhen Quan <<EMAIL>> 1745317081 +0800	commit: Deployed to dev
52eb30d8571996330da5d5c33a26d357f26539af 0b1eccd1255b4e4ddc8a1010c8f0fbf9a3e19eff Sim Zhen Quan <<EMAIL>> 1745317268 +0800	checkout: moving from dev to discount-userable-add-more-details
0b1eccd1255b4e4ddc8a1010c8f0fbf9a3e19eff 164774b881ddaffb932950749049b815650b38ee Sim Zhen Quan <<EMAIL>> 1745317282 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
164774b881ddaffb932950749049b815650b38ee c7ae49aa14cca4d8df97f1d6aff38d082ae9c63e Sim Zhen Quan <<EMAIL>> 1745331046 +0800	commit: Reviewed
c7ae49aa14cca4d8df97f1d6aff38d082ae9c63e b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745335794 +0800	checkout: moving from discount-userable-add-more-details to main
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 b0e418323088db9153b22b408d4834221452118c Sim Zhen Quan <<EMAIL>> 1745335806 +0800	pull: Fast-forward
b0e418323088db9153b22b408d4834221452118c 889610d2cbcea65247e5cf9d30f79bfb8ec99834 Sim Zhen Quan <<EMAIL>> 1745335924 +0800	commit: Added patch for leave early period correction
889610d2cbcea65247e5cf9d30f79bfb8ec99834 e90ce7169e07685db944c78bc6632c38c73813f7 Sim Zhen Quan <<EMAIL>> 1745336118 +0800	commit: Added patch period 15 incorrect attendance status
e90ce7169e07685db944c78bc6632c38c73813f7 4e4000feda7663f097bf766a7c9e56d2a3cf7c9c Sim Zhen Quan <<EMAIL>> 1745336134 +0800	checkout: moving from main to attendance-posting-auto-create-leave-application
4e4000feda7663f097bf766a7c9e56d2a3cf7c9c d1ddd1693ec52399af00a51ef91056351344b702 Sim Zhen Quan <<EMAIL>> 1745336148 +0800	checkout: moving from attendance-posting-auto-create-leave-application to staging/2025-04-20
d1ddd1693ec52399af00a51ef91056351344b702 e90ce7169e07685db944c78bc6632c38c73813f7 Sim Zhen Quan <<EMAIL>> 1745336162 +0800	merge origin/main: Fast-forward
e90ce7169e07685db944c78bc6632c38c73813f7 4e4000feda7663f097bf766a7c9e56d2a3cf7c9c Sim Zhen Quan <<EMAIL>> 1745336172 +0800	checkout: moving from staging/2025-04-20 to attendance-posting-auto-create-leave-application
4e4000feda7663f097bf766a7c9e56d2a3cf7c9c ecd65e631007eff3be75d9705e288f98de8a2df9 Sim Zhen Quan <<EMAIL>> 1745336187 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
ecd65e631007eff3be75d9705e288f98de8a2df9 46ad5b291d5bc2e320a61af615fc19713b1932e9 Sim Zhen Quan <<EMAIL>> 1745341445 +0800	commit: Reviewed
46ad5b291d5bc2e320a61af615fc19713b1932e9 e90ce7169e07685db944c78bc6632c38c73813f7 Sim Zhen Quan <<EMAIL>> 1745341814 +0800	checkout: moving from attendance-posting-auto-create-leave-application to main
e90ce7169e07685db944c78bc6632c38c73813f7 e843c2078c1cf7147bc4b1fbccfb3384be06444c Sim Zhen Quan <<EMAIL>> 1745341820 +0800	pull: Fast-forward
e843c2078c1cf7147bc4b1fbccfb3384be06444c 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745343230 +0800	commit: Deployed to PRD
071181cc25fbce84151d14a6c95c2ea8acbf57c8 3cb7bec7e653b60a26bbc67d75374a4a584bd1de Sim Zhen Quan <<EMAIL>> 1745393081 +0800	checkout: moving from main to feature/attendance-mark-deduction-report
3cb7bec7e653b60a26bbc67d75374a4a584bd1de 6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 Sim Zhen Quan <<EMAIL>> 1745393090 +0800	pull: Fast-forward
6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 Sim Zhen Quan <<EMAIL>> 1745395098 +0800	reset: moving to HEAD
6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 31fd5f63530721dced288c228c419af1e7b89154 Sim Zhen Quan <<EMAIL>> 1745401262 +0800	commit: Added view table
31fd5f63530721dced288c228c419af1e7b89154 ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 Sim Zhen Quan <<EMAIL>> 1745402076 +0800	checkout: moving from feature/attendance-mark-deduction-report to fix/billing-doc-payment-ref-no-filter
ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745402087 +0800	checkout: moving from fix/billing-doc-payment-ref-no-filter to main
071181cc25fbce84151d14a6c95c2ea8acbf57c8 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745402103 +0800	checkout: moving from main to staging/2025-04-23
071181cc25fbce84151d14a6c95c2ea8acbf57c8 ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 Sim Zhen Quan <<EMAIL>> 1745402117 +0800	checkout: moving from staging/2025-04-23 to fix/billing-doc-payment-ref-no-filter
ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 31fd5f63530721dced288c228c419af1e7b89154 Sim Zhen Quan <<EMAIL>> 1745407259 +0800	checkout: moving from fix/billing-doc-payment-ref-no-filter to feature/attendance-mark-deduction-report
31fd5f63530721dced288c228c419af1e7b89154 57e9c88ee17fe913e197a2986ec4399a4695a548 Sim Zhen Quan <<EMAIL>> 1745419487 +0800	pull: Fast-forward
57e9c88ee17fe913e197a2986ec4399a4695a548 bbdb9c5dbca3b195d0c1eb3e6c498d16e695c0b6 Sim Zhen Quan <<EMAIL>> 1745421915 +0800	commit: Reviewed
bbdb9c5dbca3b195d0c1eb3e6c498d16e695c0b6 52eb30d8571996330da5d5c33a26d357f26539af Sim Zhen Quan <<EMAIL>> 1745422086 +0800	checkout: moving from feature/attendance-mark-deduction-report to dev
52eb30d8571996330da5d5c33a26d357f26539af 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745422146 +0800	checkout: moving from dev to staging/2025-04-23
071181cc25fbce84151d14a6c95c2ea8acbf57c8 748b75b177af53897c02b95f08d2216fa0619db4 Sim Zhen Quan <<EMAIL>> 1745422154 +0800	pull: Fast-forward
748b75b177af53897c02b95f08d2216fa0619db4 52eb30d8571996330da5d5c33a26d357f26539af Sim Zhen Quan <<EMAIL>> 1745422173 +0800	checkout: moving from staging/2025-04-23 to dev
52eb30d8571996330da5d5c33a26d357f26539af 34cb12d30995821f9d675823c415c9febbb52c73 Sim Zhen Quan <<EMAIL>> 1745422177 +0800	merge staging/2025-04-23: Merge made by the 'ort' strategy.
34cb12d30995821f9d675823c415c9febbb52c73 5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 Sim Zhen Quan <<EMAIL>> 1745422891 +0800	commit: Deployed to dev
5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 3d2b1b219762f4376edd406637a55996b4625025 Sim Zhen Quan <<EMAIL>> 1745422924 +0800	checkout: moving from dev to student-absent-report-enhancement
3d2b1b219762f4376edd406637a55996b4625025 e60a81ef6a71701de390ee3679a4665539d68c7d Sim Zhen Quan <<EMAIL>> 1745422929 +0800	pull: Fast-forward
e60a81ef6a71701de390ee3679a4665539d68c7d bd05212173c3d1c60f8bf2368a8aba5ebd2cb674 Sim Zhen Quan <<EMAIL>> 1745423009 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into student-absent-report-enhancement
bd05212173c3d1c60f8bf2368a8aba5ebd2cb674 95a90de239d77148f27c822b8edf689252998681 Sim Zhen Quan <<EMAIL>> 1745424341 +0800	commit: Reviewed
95a90de239d77148f27c822b8edf689252998681 748b75b177af53897c02b95f08d2216fa0619db4 Sim Zhen Quan <<EMAIL>> 1745424360 +0800	checkout: moving from student-absent-report-enhancement to staging/2025-04-23
748b75b177af53897c02b95f08d2216fa0619db4 988f3ef3db7742061eea5751b67885aca4035e32 Sim Zhen Quan <<EMAIL>> 1745424524 +0800	commit (merge): Merge branch 'student-absent-report-enhancement' into staging/2025-04-23
988f3ef3db7742061eea5751b67885aca4035e32 5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 Sim Zhen Quan <<EMAIL>> 1745424570 +0800	checkout: moving from staging/2025-04-23 to dev
5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 cb54783092d12a4512bff421352fcd037764a2d5 Sim Zhen Quan <<EMAIL>> 1745424579 +0800	merge origin/staging/2025-04-23: Merge made by the 'ort' strategy.
cb54783092d12a4512bff421352fcd037764a2d5 1694df83f96224936f6bb7c8c1b55f6826bd4493 Sim Zhen Quan <<EMAIL>> 1745424977 +0800	commit: Deployed to dev
1694df83f96224936f6bb7c8c1b55f6826bd4493 44292e6439cf8301cc1cb8796be57969b3648941 Sim Zhen Quan <<EMAIL>> 1745424984 +0800	checkout: moving from dev to tap-card-trigger-attendance-posting
44292e6439cf8301cc1cb8796be57969b3648941 5a9b5edba7111685bd4566663f48ea53da1d16f0 Sim Zhen Quan <<EMAIL>> 1745424997 +0800	merge origin/staging/2025-04-23: Merge made by the 'ort' strategy.
5a9b5edba7111685bd4566663f48ea53da1d16f0 762896bf1f9fe9e63850d59c2fcd27f89bb59086 Sim Zhen Quan <<EMAIL>> 1745428022 +0800	commit: Reviewed
762896bf1f9fe9e63850d59c2fcd27f89bb59086 1694df83f96224936f6bb7c8c1b55f6826bd4493 Sim Zhen Quan <<EMAIL>> 1745428218 +0800	checkout: moving from tap-card-trigger-attendance-posting to dev
1694df83f96224936f6bb7c8c1b55f6826bd4493 823bbc3907d1f1271e3a2be624feb8b37443f51e Sim Zhen Quan <<EMAIL>> 1745428231 +0800	merge origin/staging/2025-04-23: Merge made by the 'ort' strategy.
823bbc3907d1f1271e3a2be624feb8b37443f51e 9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd Sim Zhen Quan <<EMAIL>> 1745429685 +0800	commit: Deployed to dev
9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745429823 +0800	checkout: moving from dev to main
071181cc25fbce84151d14a6c95c2ea8acbf57c8 a04fbf5406e7bbd01735edd7e00cd4083a6941b8 Sim Zhen Quan <<EMAIL>> 1745429829 +0800	pull: Fast-forward
a04fbf5406e7bbd01735edd7e00cd4083a6941b8 173d0a084e0186f413078de50eafaa7246430390 Sim Zhen Quan <<EMAIL>> 1745460720 +0800	commit: Deployed to prd
173d0a084e0186f413078de50eafaa7246430390 d1e5efb5e2cd091c13bd3e63e8808a5c2ed50824 Sim Zhen Quan <<EMAIL>> 1745461026 +0800	checkout: moving from main to fix/billing-document-report-for-product
d1e5efb5e2cd091c13bd3e63e8808a5c2ed50824 698138a3a71845c7f75e729d1e869e431563b2bd Sim Zhen Quan <<EMAIL>> 1745461032 +0800	merge origin/main: Merge made by the 'ort' strategy.
698138a3a71845c7f75e729d1e869e431563b2bd 1392e6567ef60464a2539ab9a4e716e97ea82958 Sim Zhen Quan <<EMAIL>> 1745463926 +0800	checkout: moving from fix/billing-document-report-for-product to fix/attendance-mark-deduction-report
1392e6567ef60464a2539ab9a4e716e97ea82958 175dddb259244892ea2be8072a62e141c5cd61e8 Sim Zhen Quan <<EMAIL>> 1745483535 +0800	checkout: moving from fix/attendance-mark-deduction-report to issuelog-25-hostel-savings-see-transaction-balance
175dddb259244892ea2be8072a62e141c5cd61e8 749a7523adf4fad36bb5a988cdd1af7ab862bbfb Sim Zhen Quan <<EMAIL>> 1745483547 +0800	merge origin/main: Merge made by the 'ort' strategy.
749a7523adf4fad36bb5a988cdd1af7ab862bbfb 173d0a084e0186f413078de50eafaa7246430390 Sim Zhen Quan <<EMAIL>> 1745483562 +0800	checkout: moving from issuelog-25-hostel-savings-see-transaction-balance to main
173d0a084e0186f413078de50eafaa7246430390 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745483568 +0800	pull: Fast-forward
72b23a17fc9dae7ec7cf713667cd83b546d8f123 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745483583 +0800	checkout: moving from main to staging/2025-04-24
72b23a17fc9dae7ec7cf713667cd83b546d8f123 749a7523adf4fad36bb5a988cdd1af7ab862bbfb Sim Zhen Quan <<EMAIL>> 1745483597 +0800	checkout: moving from staging/2025-04-24 to issuelog-25-hostel-savings-see-transaction-balance
749a7523adf4fad36bb5a988cdd1af7ab862bbfb 698138a3a71845c7f75e729d1e869e431563b2bd Sim Zhen Quan <<EMAIL>> 1745484200 +0800	checkout: moving from issuelog-25-hostel-savings-see-transaction-balance to fix/billing-document-report-for-product
698138a3a71845c7f75e729d1e869e431563b2bd 922ebd290b6653b48abb07ba9d6a668e92023083 Sim Zhen Quan <<EMAIL>> 1745484215 +0800	merge origin/main: Merge made by the 'ort' strategy.
922ebd290b6653b48abb07ba9d6a668e92023083 0c452f5ff76989c5f4019ff71111627f0bdc4ff7 Sim Zhen Quan <<EMAIL>> 1745485868 +0800	commit: Reviewed
0c452f5ff76989c5f4019ff71111627f0bdc4ff7 7518f410774c1e45fa58a457aff51373a6ef34a8 Sim Zhen Quan <<EMAIL>> 1745486205 +0800	checkout: moving from fix/billing-document-report-for-product to fix/load-merit-demerit-relationship
7518f410774c1e45fa58a457aff51373a6ef34a8 2d84297d6f2bfcfdb3b88e2f6ade55ad21b01c84 Sim Zhen Quan <<EMAIL>> 1745486217 +0800	merge origin/staging/2025-04-24: Merge made by the 'ort' strategy.
2d84297d6f2bfcfdb3b88e2f6ade55ad21b01c84 b6ae480b43db5d3b8d54f8507e93ff62d7972151 Sim Zhen Quan <<EMAIL>> 1745508038 +0800	commit: WIP
b6ae480b43db5d3b8d54f8507e93ff62d7972151 f11efac3340f7e281040695c4c097cfb15de79dc Sim Zhen Quan <<EMAIL>> 1745509160 +0800	commit: Reviewed
f11efac3340f7e281040695c4c097cfb15de79dc 70795960c20425989b2deb4da3f8f4712235057f Sim Zhen Quan <<EMAIL>> 1745509433 +0800	commit: Reviewed
70795960c20425989b2deb4da3f8f4712235057f 957e956d9634b4877c397ec38eb54a20d066208b Sim Zhen Quan <<EMAIL>> 1745509924 +0800	commit: Fix test case
957e956d9634b4877c397ec38eb54a20d066208b 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745509992 +0800	checkout: moving from fix/load-merit-demerit-relationship to staging/2025-04-24
72b23a17fc9dae7ec7cf713667cd83b546d8f123 32b1999066e93ed9f0ccae7fbddac66566b09aed Sim Zhen Quan <<EMAIL>> 1745509997 +0800	pull: Fast-forward
32b1999066e93ed9f0ccae7fbddac66566b09aed 1fd23a442951953fcd6814bd1f0e92749b50945d Sim Zhen Quan <<EMAIL>> 1745510065 +0800	merge origin/main: Merge made by the 'ort' strategy.
1fd23a442951953fcd6814bd1f0e92749b50945d 92d31de92e4c81c6f8b0bb58dc2e6c40906ede84 Sim Zhen Quan <<EMAIL>> 1745510331 +0800	commit: Delete unwanted files
92d31de92e4c81c6f8b0bb58dc2e6c40906ede84 de74ea31391a6e52359977d1d1c4910458335872 Sim Zhen Quan <<EMAIL>> 1745510348 +0800	checkout: moving from staging/2025-04-24 to migration/patch-student-guardian
de74ea31391a6e52359977d1d1c4910458335872 bde19f8651e6f4d13a5d120a4f717b187a1a6e35 Sim Zhen Quan <<EMAIL>> 1745510356 +0800	merge origin/staging/2025-04-24: Merge made by the 'ort' strategy.
bde19f8651e6f4d13a5d120a4f717b187a1a6e35 1dd49861a591251abe9bd6a5ecff62ea85c814af Sim Zhen Quan <<EMAIL>> 1745512718 +0800	commit: Reviewed
1dd49861a591251abe9bd6a5ecff62ea85c814af ccb289281e40b5840f40931d589c03c81e8bf843 Sim Zhen Quan <<EMAIL>> 1745512832 +0800	checkout: moving from migration/patch-student-guardian to issuelog40-void-invoice-validation-updates
ccb289281e40b5840f40931d589c03c81e8bf843 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745513477 +0800	checkout: moving from issuelog40-void-invoice-validation-updates to main
72b23a17fc9dae7ec7cf713667cd83b546d8f123 bec9fb2412c4cd22c8377a322e3beb76ea4bb2f6 Sim Zhen Quan <<EMAIL>> 1745513483 +0800	pull: Fast-forward
bec9fb2412c4cd22c8377a322e3beb76ea4bb2f6 c4b674b8d67df017ff476f0698671e831fd20d1f Sim Zhen Quan <<EMAIL>> 1745513843 +0800	merge origin/staging/2025-04-24: Fast-forward
c4b674b8d67df017ff476f0698671e831fd20d1f a078170d63d012ae73cccd8d74b5c6a1278b949e Sim Zhen Quan <<EMAIL>> 1745514100 +0800	commit: Remove comments
a078170d63d012ae73cccd8d74b5c6a1278b949e 6cbc3dd7880b8505008adc868ff38012343292a4 Sim Zhen Quan <<EMAIL>> 1745514849 +0800	commit: Deployed to prd
6cbc3dd7880b8505008adc868ff38012343292a4 6cbc3dd7880b8505008adc868ff38012343292a4 Sim Zhen Quan <<EMAIL>> 1745546384 +0800	checkout: moving from main to staging/2025-04-25
6cbc3dd7880b8505008adc868ff38012343292a4 6c1123819e8edba0a9d0e0df4b6aaadfb1d170a5 Sim Zhen Quan <<EMAIL>> 1745547499 +0800	checkout: moving from staging/2025-04-25 to discount-add-description-column
6c1123819e8edba0a9d0e0df4b6aaadfb1d170a5 96b900a45f83c40c3c06481e37691e6931e3bf9c Sim Zhen Quan <<EMAIL>> 1745547508 +0800	merge origin/main: Merge made by the 'ort' strategy.
96b900a45f83c40c3c06481e37691e6931e3bf9c 7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb Sim Zhen Quan <<EMAIL>> 1745548049 +0800	commit: Reviewed
7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb 9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd Sim Zhen Quan <<EMAIL>> 1745548187 +0800	checkout: moving from discount-add-description-column to dev
9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd b1bf4e2003431f813adaa3dbd22f86ab18ca8570 Sim Zhen Quan <<EMAIL>> 1745548195 +0800	merge discount-add-description-column: Merge made by the 'ort' strategy.
b1bf4e2003431f813adaa3dbd22f86ab18ca8570 09acb715b02c3aab7231791534924cfb476b3a4a Sim Zhen Quan <<EMAIL>> 1745548445 +0800	commit: Deployed to dev
09acb715b02c3aab7231791534924cfb476b3a4a 3111b95d45b645ae729d21c4286f3ae204e286ff Sim Zhen Quan <<EMAIL>> 1745548630 +0800	checkout: moving from dev to fix/billing-doc-creation-validation
3111b95d45b645ae729d21c4286f3ae204e286ff c98f66c21f2930cec3e002ad1a313f11ff5f7fe0 Sim Zhen Quan <<EMAIL>> 1745554340 +0800	checkout: moving from fix/billing-doc-creation-validation to issuelog-80-return-scholarship-details-in-discount-api
c98f66c21f2930cec3e002ad1a313f11ff5f7fe0 5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf Sim Zhen Quan <<EMAIL>> 1745554347 +0800	merge origin/main: Merge made by the 'ort' strategy.
5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf 3111b95d45b645ae729d21c4286f3ae204e286ff Sim Zhen Quan <<EMAIL>> 1745558924 +0800	checkout: moving from issuelog-80-return-scholarship-details-in-discount-api to fix/billing-doc-creation-validation
3111b95d45b645ae729d21c4286f3ae204e286ff 7753d3bbb0473c73785edf22127da5587de11456 Sim Zhen Quan <<EMAIL>> 1745558929 +0800	pull: Fast-forward
7753d3bbb0473c73785edf22127da5587de11456 a94be2308575c2aa0599183afeb63a2b926c13fe Sim Zhen Quan <<EMAIL>> 1745562120 +0800	commit: Reviewed
a94be2308575c2aa0599183afeb63a2b926c13fe 6cbc3dd7880b8505008adc868ff38012343292a4 Sim Zhen Quan <<EMAIL>> 1745563399 +0800	checkout: moving from fix/billing-doc-creation-validation to main
6cbc3dd7880b8505008adc868ff38012343292a4 d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 Sim Zhen Quan <<EMAIL>> 1745563840 +0800	commit: Fix test case
d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 09acb715b02c3aab7231791534924cfb476b3a4a Sim Zhen Quan <<EMAIL>> 1745563864 +0800	checkout: moving from main to dev
09acb715b02c3aab7231791534924cfb476b3a4a b0915c7856d64be231682dd1ad197dadb90c1e5b Sim Zhen Quan <<EMAIL>> 1745563869 +0800	merge fix/billing-doc-creation-validation: Merge made by the 'ort' strategy.
b0915c7856d64be231682dd1ad197dadb90c1e5b 1d55ad122bfe8175f6dc1cd75fd75ef0070229ac Sim Zhen Quan <<EMAIL>> 1745563912 +0800	merge origin/main: Merge made by the 'ort' strategy.
1d55ad122bfe8175f6dc1cd75fd75ef0070229ac 6b10423db10ad656a49ec4d54c1667e617144e85 Sim Zhen Quan <<EMAIL>> 1745564210 +0800	commit: Deployed to DEV
6b10423db10ad656a49ec4d54c1667e617144e85 ccb289281e40b5840f40931d589c03c81e8bf843 Sim Zhen Quan <<EMAIL>> 1745564215 +0800	checkout: moving from dev to issuelog40-void-invoice-validation-updates
ccb289281e40b5840f40931d589c03c81e8bf843 7899d466d80f2097ceaff0db8d0d154c4554087a Sim Zhen Quan <<EMAIL>> 1745564221 +0800	merge origin/main: Merge made by the 'ort' strategy.
7899d466d80f2097ceaff0db8d0d154c4554087a 6b10423db10ad656a49ec4d54c1667e617144e85 Sim Zhen Quan <<EMAIL>> 1745564740 +0800	checkout: moving from issuelog40-void-invoice-validation-updates to dev
6b10423db10ad656a49ec4d54c1667e617144e85 ad07ac10237d15aef5ed5a428b82d9c79de520ea Sim Zhen Quan <<EMAIL>> 1745564750 +0800	merge issuelog40-void-invoice-validation-updates: Merge made by the 'ort' strategy.
ad07ac10237d15aef5ed5a428b82d9c79de520ea ed64b080fa22d9ff20d70af124cf06e119a959ca Sim Zhen Quan <<EMAIL>> 1745565157 +0800	commit: Deployed to DEV
ed64b080fa22d9ff20d70af124cf06e119a959ca 5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf Sim Zhen Quan <<EMAIL>> 1745565162 +0800	checkout: moving from dev to issuelog-80-return-scholarship-details-in-discount-api
5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 Sim Zhen Quan <<EMAIL>> 1745565167 +0800	checkout: moving from issuelog-80-return-scholarship-details-in-discount-api to main
d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf Sim Zhen Quan <<EMAIL>> 1745565262 +0800	checkout: moving from main to issuelog-80-return-scholarship-details-in-discount-api
5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf f21b963180a27d613bc7c0b7a436ff5a053cf228 Sim Zhen Quan <<EMAIL>> 1745565268 +0800	merge origin/main: Merge made by the 'ort' strategy.
f21b963180a27d613bc7c0b7a436ff5a053cf228 ed64b080fa22d9ff20d70af124cf06e119a959ca Sim Zhen Quan <<EMAIL>> 1745565916 +0800	checkout: moving from issuelog-80-return-scholarship-details-in-discount-api to dev
ed64b080fa22d9ff20d70af124cf06e119a959ca 20d43c84e1574d54cf1af854f7a8fde2a1c971f0 Sim Zhen Quan <<EMAIL>> 1745565924 +0800	merge issuelog-80-return-scholarship-details-in-discount-api: Merge made by the 'ort' strategy.
20d43c84e1574d54cf1af854f7a8fde2a1c971f0 f982e0bd08dd0e272d664e879c8f5937c6fb1091 Sim Zhen Quan <<EMAIL>> 1745566442 +0800	commit: Deployed to DEV
f982e0bd08dd0e272d664e879c8f5937c6fb1091 f563478c88744cd885e71836949e2852cebc576b Sim Zhen Quan <<EMAIL>> 1745566450 +0800	checkout: moving from dev to api/book-recovery
f563478c88744cd885e71836949e2852cebc576b 51edf881237bc0bb4631973260f1cd9bfc630448 Sim Zhen Quan <<EMAIL>> 1745566501 +0800	merge origin/main: Merge made by the 'ort' strategy.
51edf881237bc0bb4631973260f1cd9bfc630448 e9a8acdee0c5151c27250af973477ef83371bf1f Sim Zhen Quan <<EMAIL>> 1745567375 +0800	commit: Reviewed
e9a8acdee0c5151c27250af973477ef83371bf1f f982e0bd08dd0e272d664e879c8f5937c6fb1091 Sim Zhen Quan <<EMAIL>> 1745567548 +0800	checkout: moving from api/book-recovery to dev
f982e0bd08dd0e272d664e879c8f5937c6fb1091 cd1a1e62a5d9122d325be81b6b903314316fbb93 Sim Zhen Quan <<EMAIL>> 1745567555 +0800	merge api/book-recovery: Merge made by the 'ort' strategy.
cd1a1e62a5d9122d325be81b6b903314316fbb93 f6833694686913c4a2d67b947673e117b2ea18f4 Sim Zhen Quan <<EMAIL>> 1745568352 +0800	commit: Deployed to dev
f6833694686913c4a2d67b947673e117b2ea18f4 d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 Sim Zhen Quan <<EMAIL>> 1745768295 +0800	checkout: moving from dev to main
d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 3ffc52d4692fd8453b111d1dda104daa74d5b6f9 Sim Zhen Quan <<EMAIL>> 1745768300 +0800	pull: Fast-forward
3ffc52d4692fd8453b111d1dda104daa74d5b6f9 51fac5149bc89ba2d4091db64aeb6baa185c5052 Sim Zhen Quan <<EMAIL>> 1745768591 +0800	checkout: moving from main to issue_86_employee_view_permission_dependency
51fac5149bc89ba2d4091db64aeb6baa185c5052 3ffc52d4692fd8453b111d1dda104daa74d5b6f9 Sim Zhen Quan <<EMAIL>> 1745768602 +0800	checkout: moving from issue_86_employee_view_permission_dependency to main
3ffc52d4692fd8453b111d1dda104daa74d5b6f9 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745768605 +0800	commit: Deployed to PRD
28320bba79056630c5a09dc205dccc8140d57700 51fac5149bc89ba2d4091db64aeb6baa185c5052 Sim Zhen Quan <<EMAIL>> 1745768666 +0800	checkout: moving from main to issue_86_employee_view_permission_dependency
51fac5149bc89ba2d4091db64aeb6baa185c5052 cf03d6db27b06cb6d378c82d918b0a67214b653e Sim Zhen Quan <<EMAIL>> 1745768671 +0800	merge origin/main: Merge made by the 'ort' strategy.
cf03d6db27b06cb6d378c82d918b0a67214b653e b97a88edb577baf318aa04909857614a150cc654 Sim Zhen Quan <<EMAIL>> 1745855954 +0800	commit: Review WIP
b97a88edb577baf318aa04909857614a150cc654 9dd559121f69cd6a84b6be36616b15ca074b2056 Sim Zhen Quan <<EMAIL>> 1745855992 +0800	checkout: moving from issue_86_employee_view_permission_dependency to report/teacher-attendance-report
9dd559121f69cd6a84b6be36616b15ca074b2056 89bec0acd99d67a84b03017f1d81f037aefcffb6 Sim Zhen Quan <<EMAIL>> 1745856340 +0800	checkout: moving from report/teacher-attendance-report to bug/book_no_wildcard
89bec0acd99d67a84b03017f1d81f037aefcffb6 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745856425 +0800	checkout: moving from bug/book_no_wildcard to main
28320bba79056630c5a09dc205dccc8140d57700 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745856439 +0800	checkout: moving from main to staging/2025-04-29
28320bba79056630c5a09dc205dccc8140d57700 8ef039b68f184c8bce6a9a6a17e5e027a7a0861c Sim Zhen Quan <<EMAIL>> 1745856622 +0800	checkout: moving from staging/2025-04-29 to bug/library-member-add-class
8ef039b68f184c8bce6a9a6a17e5e027a7a0861c 938300a81125921932c54c91fd088dd4a49222b1 Sim Zhen Quan <<EMAIL>> 1745858186 +0800	commit: Reviewed
938300a81125921932c54c91fd088dd4a49222b1 7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb Sim Zhen Quan <<EMAIL>> 1745895164 +0800	checkout: moving from bug/library-member-add-class to discount-add-description-column
7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb 02b53a00fa25b83983f5c4d49d18c53662f13953 Sim Zhen Quan <<EMAIL>> 1745895168 +0800	pull: Fast-forward
02b53a00fa25b83983f5c4d49d18c53662f13953 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745895645 +0800	checkout: moving from discount-add-description-column to main
28320bba79056630c5a09dc205dccc8140d57700 6d18473cc47c4bcf274e0c3ed7a3b082513a5409 Sim Zhen Quan <<EMAIL>> 1745895657 +0800	merge origin/staging/2025-04-29: Fast-forward
6d18473cc47c4bcf274e0c3ed7a3b082513a5409 f6833694686913c4a2d67b947673e117b2ea18f4 Sim Zhen Quan <<EMAIL>> 1745895679 +0800	checkout: moving from main to dev
f6833694686913c4a2d67b947673e117b2ea18f4 875465344dc01e3cf0ee107e6c6a3ec6e3ba99b4 Sim Zhen Quan <<EMAIL>> 1745895687 +0800	merge origin/staging/2025-04-29: Merge made by the 'ort' strategy.
875465344dc01e3cf0ee107e6c6a3ec6e3ba99b4 d69a3c7b827df4097a6f76846103415784947146 Sim Zhen Quan <<EMAIL>> 1745896765 +0800	commit: Deployed to dev
d69a3c7b827df4097a6f76846103415784947146 9ff50a01d03ab13dd8ae5e3d37bec240be6f3875 Sim Zhen Quan <<EMAIL>> 1745896798 +0800	checkout: moving from dev to issue-105-daily-collection-report-formula
9ff50a01d03ab13dd8ae5e3d37bec240be6f3875 61668f05594f534b993a1aec47a51b2263fed6dc Sim Zhen Quan <<EMAIL>> 1745896805 +0800	merge origin/main: Merge made by the 'ort' strategy.
61668f05594f534b993a1aec47a51b2263fed6dc d69a3c7b827df4097a6f76846103415784947146 Sim Zhen Quan <<EMAIL>> 1745897833 +0800	checkout: moving from issue-105-daily-collection-report-formula to dev
d69a3c7b827df4097a6f76846103415784947146 89c57d48138f326b39a580a4161268e97d63693a Sim Zhen Quan <<EMAIL>> 1745897836 +0800	merge issue-105-daily-collection-report-formula: Merge made by the 'ort' strategy.
89c57d48138f326b39a580a4161268e97d63693a 8e0cf6accadf0ea00003bde7b5d9ba42d687a43f Sim Zhen Quan <<EMAIL>> 1745899021 +0800	commit: Deployed to dev
8e0cf6accadf0ea00003bde7b5d9ba42d687a43f eb759c444c4987c589e32f25f296899275d7e11b Sim Zhen Quan <<EMAIL>> 1745899030 +0800	checkout: moving from dev to fix/departure-optional-guardian
eb759c444c4987c589e32f25f296899275d7e11b fd8b07e09825081f6c6a5cfed0a673f80f8ce5fc Sim Zhen Quan <<EMAIL>> 1745899140 +0800	merge origin/main: Merge made by the 'ort' strategy.
fd8b07e09825081f6c6a5cfed0a673f80f8ce5fc 8e0cf6accadf0ea00003bde7b5d9ba42d687a43f Sim Zhen Quan <<EMAIL>> 1745899328 +0800	checkout: moving from fix/departure-optional-guardian to dev
8e0cf6accadf0ea00003bde7b5d9ba42d687a43f e8948fae63aae8151e6a037ef601c326f8ca92df Sim Zhen Quan <<EMAIL>> 1745899334 +0800	merge origin/fix/departure-optional-guardian: Merge made by the 'ort' strategy.
e8948fae63aae8151e6a037ef601c326f8ca92df a979be1d3e737c1b01424ffac5d1b0d9ffb9430c Sim Zhen Quan <<EMAIL>> 1745899611 +0800	commit: Deployed to dev
a979be1d3e737c1b01424ffac5d1b0d9ffb9430c ecd89224effc0b8a5caad928408a2418136b3dfc Sim Zhen Quan <<EMAIL>> 1745899615 +0800	checkout: moving from dev to issue-124-employee-resigned-with-hostel-bed
ecd89224effc0b8a5caad928408a2418136b3dfc 93bf3035915633ce13deeb3b76133ef3f2c746aa Sim Zhen Quan <<EMAIL>> 1745899620 +0800	merge origin/main: Merge made by the 'ort' strategy.
93bf3035915633ce13deeb3b76133ef3f2c746aa 6a4d139fff82852848a8a777f2497b53e2b7d25c Sim Zhen Quan <<EMAIL>> 1745917521 +0800	checkout: moving from issue-124-employee-resigned-with-hostel-bed to class-attendance-taking-page-add-student-photo-employee-details
6a4d139fff82852848a8a777f2497b53e2b7d25c 9fa65916dead4cd8b1a5f80207c96cafb4629a3a Sim Zhen Quan <<EMAIL>> 1745917527 +0800	merge origin/main: Merge made by the 'ort' strategy.
9fa65916dead4cd8b1a5f80207c96cafb4629a3a 003581e500e1a3c7d3f61c3d09b2d8fccbea744e Sim Zhen Quan <<EMAIL>> 1745918222 +0800	commit: Reviewed
003581e500e1a3c7d3f61c3d09b2d8fccbea744e a979be1d3e737c1b01424ffac5d1b0d9ffb9430c Sim Zhen Quan <<EMAIL>> 1745918492 +0800	checkout: moving from class-attendance-taking-page-add-student-photo-employee-details to dev
a979be1d3e737c1b01424ffac5d1b0d9ffb9430c eb53dd5ed62dfe977e392443d13b8800ccbca30c Sim Zhen Quan <<EMAIL>> 1745918501 +0800	merge class-attendance-taking-page-add-student-photo-employee-details: Merge made by the 'ort' strategy.
eb53dd5ed62dfe977e392443d13b8800ccbca30c eb7c4eef6f0423bed8b1370b1aabb87db4f52e63 Sim Zhen Quan <<EMAIL>> 1745925519 +0800	commit: Deployed to Dev
eb7c4eef6f0423bed8b1370b1aabb87db4f52e63 d5e3b337c994d16bfb0a86d550f787b88e9d170e Sim Zhen Quan <<EMAIL>> 1745925536 +0800	commit: Add default sort for id
d5e3b337c994d16bfb0a86d550f787b88e9d170e 61668f05594f534b993a1aec47a51b2263fed6dc Sim Zhen Quan <<EMAIL>> 1745925585 +0800	checkout: moving from dev to issue-105-daily-collection-report-formula
61668f05594f534b993a1aec47a51b2263fed6dc 3a1203998f53c87efff7758ebc89006319baf384 Sim Zhen Quan <<EMAIL>> 1745925606 +0800	cherry-pick: Add default sort for id
3a1203998f53c87efff7758ebc89006319baf384 a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745925670 +0800	checkout: moving from issue-105-daily-collection-report-formula to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 d5e3b337c994d16bfb0a86d550f787b88e9d170e Sim Zhen Quan <<EMAIL>> 1745925681 +0800	checkout: moving from report/teacher-attendance to dev
d5e3b337c994d16bfb0a86d550f787b88e9d170e 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745925686 +0800	merge report/teacher-attendance: Merge made by the 'ort' strategy.
055fb20c052bdc84f7a46542a210a27ca7168dae a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745926130 +0800	checkout: moving from dev to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745926178 +0800	checkout: moving from report/teacher-attendance to dev
055fb20c052bdc84f7a46542a210a27ca7168dae f2433953e1fa8fe3032e3a765061708357064bfd Sim Zhen Quan <<EMAIL>> 1745926179 +0800	checkout: moving from dev to issue-50-cocu-student-absent-report
f2433953e1fa8fe3032e3a765061708357064bfd 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745926188 +0800	checkout: moving from issue-50-cocu-student-absent-report to dev
055fb20c052bdc84f7a46542a210a27ca7168dae 6d18473cc47c4bcf274e0c3ed7a3b082513a5409 Sim Zhen Quan <<EMAIL>> 1745927001 +0800	checkout: moving from dev to main
6d18473cc47c4bcf274e0c3ed7a3b082513a5409 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745927063 +0800	checkout: moving from main to dev
055fb20c052bdc84f7a46542a210a27ca7168dae 5c5e3192aaeabac874c7ec7310fe222ace096179 Sim Zhen Quan <<EMAIL>> 1745927164 +0800	commit (merge): Merge to dev
5c5e3192aaeabac874c7ec7310fe222ace096179 5f0025c08a143363b8fe158d2373a111f9906214 Sim Zhen Quan <<EMAIL>> 1745940952 +0800	commit: Deployed to dev
5f0025c08a143363b8fe158d2373a111f9906214 6d18473cc47c4bcf274e0c3ed7a3b082513a5409 Sim Zhen Quan <<EMAIL>> 1745941387 +0800	checkout: moving from dev to main
6d18473cc47c4bcf274e0c3ed7a3b082513a5409 63bd88fbe09bdfe9776f2b42235296a414631d12 Sim Zhen Quan <<EMAIL>> 1745941393 +0800	pull: Fast-forward
63bd88fbe09bdfe9776f2b42235296a414631d12 a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745942337 +0800	checkout: moving from main to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 63bd88fbe09bdfe9776f2b42235296a414631d12 Sim Zhen Quan <<EMAIL>> 1745942346 +0800	checkout: moving from report/teacher-attendance to main
63bd88fbe09bdfe9776f2b42235296a414631d12 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745942354 +0800	commit: Deployed to PRD
38b120e9982b5022885bdc8d8958a2a445dc24ce a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745942363 +0800	checkout: moving from main to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 ecb7073ed0883dac04469a80e0bab5714d90d023 Sim Zhen Quan <<EMAIL>> 1745942371 +0800	merge origin/main: Merge made by the 'ort' strategy.
ecb7073ed0883dac04469a80e0bab5714d90d023 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745943778 +0800	checkout: moving from report/teacher-attendance to main
38b120e9982b5022885bdc8d8958a2a445dc24ce 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745943785 +0800	checkout: moving from main to pagination-fix
38b120e9982b5022885bdc8d8958a2a445dc24ce c4a4fc242088fe0647ff819828a4298a2eddc7ed Sim Zhen Quan <<EMAIL>> 1745943813 +0800	commit: Added flag to control default order by status
c4a4fc242088fe0647ff819828a4298a2eddc7ed ecb7073ed0883dac04469a80e0bab5714d90d023 Sim Zhen Quan <<EMAIL>> 1745943828 +0800	checkout: moving from pagination-fix to report/teacher-attendance
ecb7073ed0883dac04469a80e0bab5714d90d023 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745944033 +0800	checkout: moving from report/teacher-attendance to main
38b120e9982b5022885bdc8d8958a2a445dc24ce 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745944046 +0800	commit: Temporary disable code
397cd210416fc3aa207241c1d586208f09c0907a ecb7073ed0883dac04469a80e0bab5714d90d023 Sim Zhen Quan <<EMAIL>> 1745978283 +0800	checkout: moving from main to report/teacher-attendance
ecb7073ed0883dac04469a80e0bab5714d90d023 1ae867bae57fab26a466a179b02457c9fdb7e019 Sim Zhen Quan <<EMAIL>> 1745978289 +0800	merge origin/main: Merge made by the 'ort' strategy.
1ae867bae57fab26a466a179b02457c9fdb7e019 f2433953e1fa8fe3032e3a765061708357064bfd Sim Zhen Quan <<EMAIL>> 1745980090 +0800	checkout: moving from report/teacher-attendance to issue-50-cocu-student-absent-report
f2433953e1fa8fe3032e3a765061708357064bfd 6cb82509c967d0da8f905d6277bda7f10212b94d Sim Zhen Quan <<EMAIL>> 1745980096 +0800	pull: Fast-forward
6cb82509c967d0da8f905d6277bda7f10212b94d 2e96c006ec995c89a7dfe15e731be019d0a66741 Sim Zhen Quan <<EMAIL>> 1745980110 +0800	merge origin/main: Merge made by the 'ort' strategy.
2e96c006ec995c89a7dfe15e731be019d0a66741 b97a88edb577baf318aa04909857614a150cc654 Sim Zhen Quan <<EMAIL>> 1745984195 +0800	checkout: moving from issue-50-cocu-student-absent-report to issue_86_employee_view_permission_dependency
b97a88edb577baf318aa04909857614a150cc654 0e7560821c7803beeff9a992901fb0e212883012 Sim Zhen Quan <<EMAIL>> 1745984251 +0800	merge origin/main: Merge made by the 'ort' strategy.
0e7560821c7803beeff9a992901fb0e212883012 0e7560821c7803beeff9a992901fb0e212883012 Sim Zhen Quan <<EMAIL>> 1745985516 +0800	reset: moving to HEAD
0e7560821c7803beeff9a992901fb0e212883012 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745985518 +0800	checkout: moving from issue_86_employee_view_permission_dependency to main
397cd210416fc3aa207241c1d586208f09c0907a 0e7560821c7803beeff9a992901fb0e212883012 Sim Zhen Quan <<EMAIL>> 1745985557 +0800	checkout: moving from main to issue_86_employee_view_permission_dependency
0e7560821c7803beeff9a992901fb0e212883012 9bbc9487f47f3d9e08b3756738e050d21acbe4ff Sim Zhen Quan <<EMAIL>> 1745986271 +0800	commit: Reviewed
9bbc9487f47f3d9e08b3756738e050d21acbe4ff 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745986297 +0800	checkout: moving from issue_86_employee_view_permission_dependency to main
397cd210416fc3aa207241c1d586208f09c0907a 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745986307 +0800	checkout: moving from main to staging/2025-04-30
397cd210416fc3aa207241c1d586208f09c0907a 63f03d221d26503e9bfa4e36e56a3a5b5c46e96b Sim Zhen Quan <<EMAIL>> 1745986433 +0800	checkout: moving from staging/2025-04-30 to attendance-module-enhancements
63f03d221d26503e9bfa4e36e56a3a5b5c46e96b 978127b852ac1d37e422b99c79c39a58b6637e89 Sim Zhen Quan <<EMAIL>> 1745986442 +0800	merge origin/main: Merge made by the 'ort' strategy.
978127b852ac1d37e422b99c79c39a58b6637e89 5f0025c08a143363b8fe158d2373a111f9906214 Sim Zhen Quan <<EMAIL>> 1745988222 +0800	checkout: moving from attendance-module-enhancements to dev
5f0025c08a143363b8fe158d2373a111f9906214 7fa5c3c05f823ebc98963beb09ea8f03b3f36b74 Sim Zhen Quan <<EMAIL>> 1745988528 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-30' into dev
7fa5c3c05f823ebc98963beb09ea8f03b3f36b74 0d5be9fd83d2f9c32ae706bae43efa5dda593756 Sim Zhen Quan <<EMAIL>> 1745988557 +0800	commit (merge): Merge remote-tracking branch 'origin/pagination-fix' into dev
0d5be9fd83d2f9c32ae706bae43efa5dda593756 0f2825224a2fc81ae2365bb9145f2ceba56cdef5 Sim Zhen Quan <<EMAIL>> 1745988575 +0800	merge attendance-module-enhancements: Merge made by the 'ort' strategy.
0f2825224a2fc81ae2365bb9145f2ceba56cdef5 b4910c79a1f00fb55069f508b576ae14d61c0824 Sim Zhen Quan <<EMAIL>> 1745995068 +0800	commit: Deployed to DEV
b4910c79a1f00fb55069f508b576ae14d61c0824 7fa9038f2b35b108ba96a1480e3db9ef39b3f642 Sim Zhen Quan <<EMAIL>> 1745995080 +0800	checkout: moving from dev to fix/attendance-mark-deduction-styling
7fa9038f2b35b108ba96a1480e3db9ef39b3f642 e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e Sim Zhen Quan <<EMAIL>> 1745996481 +0800	commit: Updated report with translations
e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e b4910c79a1f00fb55069f508b576ae14d61c0824 Sim Zhen Quan <<EMAIL>> 1745996693 +0800	checkout: moving from fix/attendance-mark-deduction-styling to dev
b4910c79a1f00fb55069f508b576ae14d61c0824 fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 Sim Zhen Quan <<EMAIL>> 1745996706 +0800	merge origin/fix/attendance-mark-deduction-styling: Merge made by the 'ort' strategy.
fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1746006300 +0800	checkout: moving from dev to main
397cd210416fc3aa207241c1d586208f09c0907a 2e96c006ec995c89a7dfe15e731be019d0a66741 Sim Zhen Quan <<EMAIL>> 1746108865 +0800	checkout: moving from main to issue-50-cocu-student-absent-report
2e96c006ec995c89a7dfe15e731be019d0a66741 1bd1927347a6313ce168ca1c1e786603dabbace8 Sim Zhen Quan <<EMAIL>> 1746108870 +0800	pull: Fast-forward
1bd1927347a6313ce168ca1c1e786603dabbace8 77ed2aeba83558b71c4416d08a60b8615eed4567 Sim Zhen Quan <<EMAIL>> 1746110943 +0800	commit: Reviewed
77ed2aeba83558b71c4416d08a60b8615eed4567 c4a4fc242088fe0647ff819828a4298a2eddc7ed Sim Zhen Quan <<EMAIL>> 1746111375 +0800	checkout: moving from issue-50-cocu-student-absent-report to pagination-fix
c4a4fc242088fe0647ff819828a4298a2eddc7ed c4a4fc242088fe0647ff819828a4298a2eddc7ed Sim Zhen Quan <<EMAIL>> 1746111460 +0800	reset: moving to HEAD
c4a4fc242088fe0647ff819828a4298a2eddc7ed d80da291cee185fdafed89f24b3427e4e314ae54 Sim Zhen Quan <<EMAIL>> 1746111491 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into pagination-fix
d80da291cee185fdafed89f24b3427e4e314ae54 d1bb6d212819ae867b82a7599471e1d122b2339f Sim Zhen Quan <<EMAIL>> 1746114306 +0800	commit: Only fixed affected placed for now
d1bb6d212819ae867b82a7599471e1d122b2339f c00b315343747031888c6a7008f8e199c4a4c787 Sim Zhen Quan <<EMAIL>> 1746114605 +0800	checkout: moving from pagination-fix to fix/billing-document-daily-report-collection-column-changes
c00b315343747031888c6a7008f8e199c4a4c787 c00b315343747031888c6a7008f8e199c4a4c787 Sim Zhen Quan <<EMAIL>> 1746114924 +0800	checkout: moving from fix/billing-document-daily-report-collection-column-changes to fix/billing-document-daily-report-collection-column-changes
c00b315343747031888c6a7008f8e199c4a4c787 c00b315343747031888c6a7008f8e199c4a4c787 Sim Zhen Quan <<EMAIL>> 1746114943 +0800	checkout: moving from fix/billing-document-daily-report-collection-column-changes to issue-116-fix-student-search-by-class
c00b315343747031888c6a7008f8e199c4a4c787 77ed2aeba83558b71c4416d08a60b8615eed4567 Sim Zhen Quan <<EMAIL>> 1746115084 +0800	checkout: moving from issue-116-fix-student-search-by-class to issue-50-cocu-student-absent-report
77ed2aeba83558b71c4416d08a60b8615eed4567 77ed2aeba83558b71c4416d08a60b8615eed4567 Sim Zhen Quan <<EMAIL>> 1746115106 +0800	checkout: moving from issue-50-cocu-student-absent-report to issue-116-fix-student-search-by-class
77ed2aeba83558b71c4416d08a60b8615eed4567 d4f04721c1fa688f436cf5bbbdd471dc601a45c8 Sim Zhen Quan <<EMAIL>> 1746115523 +0800	commit: Fix student class search
d4f04721c1fa688f436cf5bbbdd471dc601a45c8 ef10ef194e6cc807601c41088983a57a8c52e992 Sim Zhen Quan <<EMAIL>> 1746115779 +0800	commit: Fix student class search
ef10ef194e6cc807601c41088983a57a8c52e992 fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 Sim Zhen Quan <<EMAIL>> 1746115816 +0800	checkout: moving from issue-116-fix-student-search-by-class to dev
fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 dd7fa25da0eeac0ef3b8d314fecdbe5e32fb3643 Sim Zhen Quan <<EMAIL>> 1746115855 +0800	merge origin/issue-116-fix-student-search-by-class: Merge made by the 'ort' strategy.
dd7fa25da0eeac0ef3b8d314fecdbe5e32fb3643 0f94a77adf6701dd7e7d6c1f0b0112575859d367 Sim Zhen Quan <<EMAIL>> 1746115884 +0800	merge origin/fix/billing-document-daily-report-collection-column-changes: Merge made by the 'ort' strategy.
0f94a77adf6701dd7e7d6c1f0b0112575859d367 77c4953918b83cee09b2ea36397743ce7bb493c6 Sim Zhen Quan <<EMAIL>> 1746115917 +0800	commit (merge): Merge remote-tracking branch 'origin/pagination-fix' into dev
77c4953918b83cee09b2ea36397743ce7bb493c6 dc5a77ebfebcce2e5c44475784fb02c7eb0d833a Sim Zhen Quan <<EMAIL>> 1746150531 +0800	commit: Deployed to dev
dc5a77ebfebcce2e5c44475784fb02c7eb0d833a 9c68ef33cbe79be2392de186f62e726ec24ecb3e Sim Zhen Quan <<EMAIL>> 1746150539 +0800	checkout: moving from dev to fix-get-attendance-period-for-student-bug
9c68ef33cbe79be2392de186f62e726ec24ecb3e ce8d3200ac00c264d7872cfac7796664f5becb4a Sim Zhen Quan <<EMAIL>> 1746151151 +0800	commit: Reviewed
ce8d3200ac00c264d7872cfac7796664f5becb4a dc5a77ebfebcce2e5c44475784fb02c7eb0d833a Sim Zhen Quan <<EMAIL>> 1746151185 +0800	checkout: moving from fix-get-attendance-period-for-student-bug to dev
dc5a77ebfebcce2e5c44475784fb02c7eb0d833a 3210b6f12157d81d07c25d5740902e360d43d485 Sim Zhen Quan <<EMAIL>> 1746151193 +0800	merge origin/fix-get-attendance-period-for-student-bug: Merge made by the 'ort' strategy.
3210b6f12157d81d07c25d5740902e360d43d485 1ae867bae57fab26a466a179b02457c9fdb7e019 Sim Zhen Quan <<EMAIL>> 1746151383 +0800	checkout: moving from dev to report/teacher-attendance
1ae867bae57fab26a466a179b02457c9fdb7e019 4c0d73b4ba4d457813c1c5c29e5a213a50800a52 Sim Zhen Quan <<EMAIL>> 1746151388 +0800	pull: Fast-forward
4c0d73b4ba4d457813c1c5c29e5a213a50800a52 54e6fe1cb545e395778ae96a8404839e36410082 Sim Zhen Quan <<EMAIL>> 1746153206 +0800	commit: Review WIP
54e6fe1cb545e395778ae96a8404839e36410082 3210b6f12157d81d07c25d5740902e360d43d485 Sim Zhen Quan <<EMAIL>> 1746153745 +0800	checkout: moving from report/teacher-attendance to dev
3210b6f12157d81d07c25d5740902e360d43d485 11e1b6a68fd9dc415f9973445fc1f4fe77fcd455 Sim Zhen Quan <<EMAIL>> 1746153861 +0800	commit (merge): Merge branch 'report/teacher-attendance' into dev
11e1b6a68fd9dc415f9973445fc1f4fe77fcd455 ba03526edf927f0e18986f2b3fd25379ae4de94f Sim Zhen Quan <<EMAIL>> 1746154865 +0800	commit: Deployed to dev
ba03526edf927f0e18986f2b3fd25379ae4de94f 54e6fe1cb545e395778ae96a8404839e36410082 Sim Zhen Quan <<EMAIL>> 1746154882 +0800	checkout: moving from dev to report/teacher-attendance
54e6fe1cb545e395778ae96a8404839e36410082 bd29c837e318880960946986bd5820b70eb43103 Sim Zhen Quan <<EMAIL>> 1746154892 +0800	commit: Bug fix
bd29c837e318880960946986bd5820b70eb43103 ba03526edf927f0e18986f2b3fd25379ae4de94f Sim Zhen Quan <<EMAIL>> 1746154998 +0800	checkout: moving from report/teacher-attendance to dev
ba03526edf927f0e18986f2b3fd25379ae4de94f bd29c837e318880960946986bd5820b70eb43103 Sim Zhen Quan <<EMAIL>> 1746155414 +0800	checkout: moving from dev to report/teacher-attendance
bd29c837e318880960946986bd5820b70eb43103 2b16984401ed62ce8b1b44ea4906a3e8606becd4 Sim Zhen Quan <<EMAIL>> 1746155452 +0800	commit: Bug fix
2b16984401ed62ce8b1b44ea4906a3e8606becd4 ba03526edf927f0e18986f2b3fd25379ae4de94f Sim Zhen Quan <<EMAIL>> 1746155924 +0800	checkout: moving from report/teacher-attendance to dev
ba03526edf927f0e18986f2b3fd25379ae4de94f 8f0d3bdffd38ca9ea3f7965e3524078a87c94440 Sim Zhen Quan <<EMAIL>> 1746155924 +0800	merge report/teacher-attendance: Merge made by the 'ort' strategy.
8f0d3bdffd38ca9ea3f7965e3524078a87c94440 5f2817407fa569320b20a530a37a1d25979bcf1f Sim Zhen Quan <<EMAIL>> 1746156086 +0800	checkout: moving from dev to tap-card-throw-error-if-inactive-card
5f2817407fa569320b20a530a37a1d25979bcf1f 8f0d3bdffd38ca9ea3f7965e3524078a87c94440 Sim Zhen Quan <<EMAIL>> 1746156422 +0800	checkout: moving from tap-card-throw-error-if-inactive-card to dev
8f0d3bdffd38ca9ea3f7965e3524078a87c94440 5f2817407fa569320b20a530a37a1d25979bcf1f Sim Zhen Quan <<EMAIL>> 1746156536 +0800	checkout: moving from dev to tap-card-throw-error-if-inactive-card
5f2817407fa569320b20a530a37a1d25979bcf1f e895a9ab6eec235cafa9cd7059f09ab94f8f9c06 Sim Zhen Quan <<EMAIL>> 1746156608 +0800	commit: Fix error code
e895a9ab6eec235cafa9cd7059f09ab94f8f9c06 8f0d3bdffd38ca9ea3f7965e3524078a87c94440 Sim Zhen Quan <<EMAIL>> 1746156623 +0800	checkout: moving from tap-card-throw-error-if-inactive-card to dev
8f0d3bdffd38ca9ea3f7965e3524078a87c94440 fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 Sim Zhen Quan <<EMAIL>> 1746156650 +0800	commit (merge): Merge remote-tracking branch 'origin/tap-card-throw-error-if-inactive-card' into dev
fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 42f04cdba2a60f7721170b8d469b2068267aa7e0 Sim Zhen Quan <<EMAIL>> 1746156696 +0800	checkout: moving from dev to substitute-records-index-show-period-label-name
42f04cdba2a60f7721170b8d469b2068267aa7e0 229e1730e999e3ddb0c3f364879d7e8a68ebeaa7 Sim Zhen Quan <<EMAIL>> 1746157729 +0800	commit: Added new column instead of reusing old one
229e1730e999e3ddb0c3f364879d7e8a68ebeaa7 fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 Sim Zhen Quan <<EMAIL>> 1746157837 +0800	checkout: moving from substitute-records-index-show-period-label-name to dev
fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 b5ccdbbdb02f4693a266419593798a14ee396e00 Sim Zhen Quan <<EMAIL>> 1746157840 +0800	merge origin/substitute-records-index-show-period-label-name: Merge made by the 'ort' strategy.
b5ccdbbdb02f4693a266419593798a14ee396e00 c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 Sim Zhen Quan <<EMAIL>> 1746371072 +0800	commit: Deployed to DEV
c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1746371077 +0800	checkout: moving from dev to staging/2025-04-30
397cd210416fc3aa207241c1d586208f09c0907a b3db8e0780d3f2aebb8535e8d4b433516e5a7b38 Sim Zhen Quan <<EMAIL>> 1746371087 +0800	pull: Fast-forward
b3db8e0780d3f2aebb8535e8d4b433516e5a7b38 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1746371778 +0800	checkout: moving from staging/2025-04-30 to main
397cd210416fc3aa207241c1d586208f09c0907a 485681a98d509da56c42a70062e3f9d9f9cb8801 Sim Zhen Quan <<EMAIL>> 1746371784 +0800	pull: Fast-forward
485681a98d509da56c42a70062e3f9d9f9cb8801 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746375217 +0800	commit: Test case fixes
a136c9367666564da506b17abe51c02b6b724182 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746583386 +0800	checkout: moving from main to staging/2025-05-07
a136c9367666564da506b17abe51c02b6b724182 59924c6323da866b867c09cd1782b4c3d21c73a1 Sim Zhen Quan <<EMAIL>> 1746583490 +0800	checkout: moving from staging/2025-05-07 to attendance-posting-direct-update-instead-of-delete-create
59924c6323da866b867c09cd1782b4c3d21c73a1 31ef9153ca148078f1586038d82a28b5d59da628 Sim Zhen Quan <<EMAIL>> 1746583498 +0800	commit: Deployed to PRD
31ef9153ca148078f1586038d82a28b5d59da628 003581e500e1a3c7d3f61c3d09b2d8fccbea744e Sim Zhen Quan <<EMAIL>> 1746585419 +0800	checkout: moving from attendance-posting-direct-update-instead-of-delete-create to class-attendance-taking-page-add-student-photo-employee-details
003581e500e1a3c7d3f61c3d09b2d8fccbea744e 04fbf0ff1f7befa7b37936a2a930cae4ed6c4c6c Sim Zhen Quan <<EMAIL>> 1746585627 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-05-07' into class-attendance-taking-page-add-student-photo-employee-details
04fbf0ff1f7befa7b37936a2a930cae4ed6c4c6c e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e Sim Zhen Quan <<EMAIL>> 1746586238 +0800	checkout: moving from class-attendance-taking-page-add-student-photo-employee-details to fix/attendance-mark-deduction-styling
e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e 6e9d38e52b8e31dbc5cd43f55da6dff47271cb6e Sim Zhen Quan <<EMAIL>> 1746586251 +0800	merge origin/main: Merge made by the 'ort' strategy.
6e9d38e52b8e31dbc5cd43f55da6dff47271cb6e 1716f2cfea5b1f9ba0eea4e1a7a3ea416f04b4c7 Sim Zhen Quan <<EMAIL>> 1746586442 +0800	commit: Fix import bug
1716f2cfea5b1f9ba0eea4e1a7a3ea416f04b4c7 c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 Sim Zhen Quan <<EMAIL>> 1746586467 +0800	checkout: moving from fix/attendance-mark-deduction-styling to dev
c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 d09a66a45e447671975e82b27c9d0800d95a4cca Sim Zhen Quan <<EMAIL>> 1746586470 +0800	pull: Fast-forward
d09a66a45e447671975e82b27c9d0800d95a4cca 2f82c478e4598f8f5d9575c519d43f914624ad37 Sim Zhen Quan <<EMAIL>> 1746586470 +0800	merge origin/fix/attendance-mark-deduction-styling: Merge made by the 'ort' strategy.
2f82c478e4598f8f5d9575c519d43f914624ad37 978127b852ac1d37e422b99c79c39a58b6637e89 Sim Zhen Quan <<EMAIL>> 1746589421 +0800	checkout: moving from dev to attendance-module-enhancements
978127b852ac1d37e422b99c79c39a58b6637e89 6015a49e492376531fa99f66914b54a3b74bf7db Sim Zhen Quan <<EMAIL>> 1746589426 +0800	pull: Fast-forward
6015a49e492376531fa99f66914b54a3b74bf7db 48501637c4a0da8a03f164439000550cdecc035f Sim Zhen Quan <<EMAIL>> 1746591026 +0800	pull: Fast-forward
48501637c4a0da8a03f164439000550cdecc035f 907d08ad87bba51f05577cca4bbe4548e59da1e4 Sim Zhen Quan <<EMAIL>> 1746591213 +0800	checkout: moving from attendance-module-enhancements to attendance-posting-update-first-period-class-attendance-if-late
907d08ad87bba51f05577cca4bbe4548e59da1e4 48501637c4a0da8a03f164439000550cdecc035f Sim Zhen Quan <<EMAIL>> 1746594351 +0800	checkout: moving from attendance-posting-update-first-period-class-attendance-if-late to attendance-module-enhancements
48501637c4a0da8a03f164439000550cdecc035f 4f5bb0dde583b8e5a29923a15173d9a710624852 Sim Zhen Quan <<EMAIL>> 1746594357 +0800	pull: Fast-forward
4f5bb0dde583b8e5a29923a15173d9a710624852 bf99f48ea2286efe2befce62464a4b21845465d3 Sim Zhen Quan <<EMAIL>> 1746594744 +0800	commit: Test case fix
bf99f48ea2286efe2befce62464a4b21845465d3 2f82c478e4598f8f5d9575c519d43f914624ad37 Sim Zhen Quan <<EMAIL>> 1746594764 +0800	checkout: moving from attendance-module-enhancements to dev
2f82c478e4598f8f5d9575c519d43f914624ad37 432034724ee3a58517b1f46ac0d060aa14aff6f6 Sim Zhen Quan <<EMAIL>> 1746594910 +0800	commit (merge): Merge branch 'attendance-module-enhancements' into dev
432034724ee3a58517b1f46ac0d060aa14aff6f6 f0891fa227cf7ae99faaba8eee4059c3c2a19470 Sim Zhen Quan <<EMAIL>> 1746607491 +0800	commit: Deployed to DEV
f0891fa227cf7ae99faaba8eee4059c3c2a19470 2a0df02698ace2c00bb5911727c1e4b658a1e1a6 Sim Zhen Quan <<EMAIL>> 1746607510 +0800	checkout: moving from dev to bug/library-loan-book-overdue-amount
2a0df02698ace2c00bb5911727c1e4b658a1e1a6 dc09faeddf1ba46c414e4660d4d030a05b11aef9 Sim Zhen Quan <<EMAIL>> 1746608980 +0800	commit: Reviewed
dc09faeddf1ba46c414e4660d4d030a05b11aef9 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746612241 +0800	checkout: moving from bug/library-loan-book-overdue-amount to main
a136c9367666564da506b17abe51c02b6b724182 e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 Sim Zhen Quan <<EMAIL>> 1746612302 +0800	commit: Deployed to PRD at 4 May 2025
e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746612978 +0800	checkout: moving from main to staging/2025-05-07
a136c9367666564da506b17abe51c02b6b724182 60d58226a7e53167af17fa176261d62344b91def Sim Zhen Quan <<EMAIL>> 1746612985 +0800	pull: Fast-forward
60d58226a7e53167af17fa176261d62344b91def dc52f344c93875e4e35f212120251cd63b988a59 Sim Zhen Quan <<EMAIL>> 1746613170 +0800	checkout: moving from staging/2025-05-07 to bug/call_no_wildcard
dc52f344c93875e4e35f212120251cd63b988a59 767b445dd8563ce094315d096223b3488ab55c4c Sim Zhen Quan <<EMAIL>> 1746613178 +0800	merge origin/main: Merge made by the 'ort' strategy.
767b445dd8563ce094315d096223b3488ab55c4c 2b16984401ed62ce8b1b44ea4906a3e8606becd4 Sim Zhen Quan <<EMAIL>> 1746619790 +0800	checkout: moving from bug/call_no_wildcard to report/teacher-attendance
2b16984401ed62ce8b1b44ea4906a3e8606becd4 9064f236ad2cb1960ce10effdf418c51c783a94d Sim Zhen Quan <<EMAIL>> 1746619796 +0800	pull: Fast-forward
9064f236ad2cb1960ce10effdf418c51c783a94d ff63f34ea6de22b5972365083e2c65b0814d10ad Sim Zhen Quan <<EMAIL>> 1746620265 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into report/teacher-attendance
ff63f34ea6de22b5972365083e2c65b0814d10ad 558d6f0a1aa62a1f018565ff459b18416e0af20e Sim Zhen Quan <<EMAIL>> 1746623813 +0800	commit: Remove unnecessary changes.
558d6f0a1aa62a1f018565ff459b18416e0af20e bd1c69b712f93f3c048dfd6791c8ab9fda004788 Sim Zhen Quan <<EMAIL>> 1746624354 +0800	commit: Fix test case
bd1c69b712f93f3c048dfd6791c8ab9fda004788 f0891fa227cf7ae99faaba8eee4059c3c2a19470 Sim Zhen Quan <<EMAIL>> 1746624388 +0800	checkout: moving from report/teacher-attendance to dev
f0891fa227cf7ae99faaba8eee4059c3c2a19470 2b09d6740555f56166b54ad00d215c71e032eb36 Sim Zhen Quan <<EMAIL>> 1746624436 +0800	commit (merge): Merge branch 'report/teacher-attendance' into dev
2b09d6740555f56166b54ad00d215c71e032eb36 9df33b591f3f06f2c797576867f5c521c44ebaa8 Sim Zhen Quan <<EMAIL>> 1746625257 +0800	commit: Deployed to DEV
9df33b591f3f06f2c797576867f5c521c44ebaa8 e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 Sim Zhen Quan <<EMAIL>> 1746625265 +0800	checkout: moving from dev to main
e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 be74f807e7710c62e1a48e22b07f45cd74fd83b7 Sim Zhen Quan <<EMAIL>> 1746625269 +0800	pull: Fast-forward
be74f807e7710c62e1a48e22b07f45cd74fd83b7 9df33b591f3f06f2c797576867f5c521c44ebaa8 Sim Zhen Quan <<EMAIL>> 1746625319 +0800	checkout: moving from main to dev
9df33b591f3f06f2c797576867f5c521c44ebaa8 bd1c69b712f93f3c048dfd6791c8ab9fda004788 Sim Zhen Quan <<EMAIL>> 1746625501 +0800	checkout: moving from dev to report/teacher-attendance
bd1c69b712f93f3c048dfd6791c8ab9fda004788 babd8b48f4d8a49a521d5468bc2bcfbad7e788da Sim Zhen Quan <<EMAIL>> 1746625798 +0800	commit: Remove unused test case
babd8b48f4d8a49a521d5468bc2bcfbad7e788da be74f807e7710c62e1a48e22b07f45cd74fd83b7 Sim Zhen Quan <<EMAIL>> 1746628663 +0800	checkout: moving from report/teacher-attendance to main
be74f807e7710c62e1a48e22b07f45cd74fd83b7 546e08449ac6fd710135dd220067691e671eecd5 Sim Zhen Quan <<EMAIL>> 1746688381 +0800	commit: Deployed to prd
546e08449ac6fd710135dd220067691e671eecd5 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688400 +0800	merge origin/main: Merge made by the 'ort' strategy.
15e2d16e802e9e2690986d55c2c2445ca5750878 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688459 +0800	checkout: moving from main to issue-83-auto-post-reward-punishment-records
15e2d16e802e9e2690986d55c2c2445ca5750878 a880327d04f36982f287e3dc99b2d242d7440b5c Sim Zhen Quan <<EMAIL>> 1746688865 +0800	commit: Update reward punishment record status to POSTED and adjust test cases
a880327d04f36982f287e3dc99b2d242d7440b5c 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688883 +0800	checkout: moving from issue-83-auto-post-reward-punishment-records to main
15e2d16e802e9e2690986d55c2c2445ca5750878 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688891 +0800	checkout: moving from main to staging/2025-05-08
15e2d16e802e9e2690986d55c2c2445ca5750878 9df33b591f3f06f2c797576867f5c521c44ebaa8 Sim Zhen Quan <<EMAIL>> 1746688945 +0800	checkout: moving from staging/2025-05-08 to dev
9df33b591f3f06f2c797576867f5c521c44ebaa8 5b5e0d688855355e182df301c128e3c1627bf6c9 Sim Zhen Quan <<EMAIL>> 1746688953 +0800	merge issue-83-auto-post-reward-punishment-records: Merge made by the 'ort' strategy.
5b5e0d688855355e182df301c128e3c1627bf6c9 0d82371920dd2e21c61405e54df44869ca96a675 Sim Zhen Quan <<EMAIL>> 1746689020 +0800	checkout: moving from dev to bug/product-index-enhancement
0d82371920dd2e21c61405e54df44869ca96a675 07349e5b07993602c5a44eef374bf2e037a5cb61 Sim Zhen Quan <<EMAIL>> 1746689026 +0800	merge origin/main: Merge made by the 'ort' strategy.
07349e5b07993602c5a44eef374bf2e037a5cb61 3f8c1f2c67a48283d9fa8faf580575618a0e5aa9 Sim Zhen Quan <<EMAIL>> 1746689185 +0800	commit: Reviewed
3f8c1f2c67a48283d9fa8faf580575618a0e5aa9 5b5e0d688855355e182df301c128e3c1627bf6c9 Sim Zhen Quan <<EMAIL>> 1746689206 +0800	checkout: moving from bug/product-index-enhancement to dev
5b5e0d688855355e182df301c128e3c1627bf6c9 e6bddf114391a56b32d3d74bdb04b4ccda5fdadc Sim Zhen Quan <<EMAIL>> 1746689215 +0800	merge origin/bug/product-index-enhancement: Merge made by the 'ort' strategy.
e6bddf114391a56b32d3d74bdb04b4ccda5fdadc 99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 Sim Zhen Quan <<EMAIL>> 1746689554 +0800	commit: Deployed to DEV
99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 6442802399f7955ff3537b8c1b1af9e3942e0d5f Sim Zhen Quan <<EMAIL>> 1746689562 +0800	checkout: moving from dev to issue-149-wallet-top-up-max-limit
6442802399f7955ff3537b8c1b1af9e3942e0d5f ddd135bd0a1479d803602f163fd6c33c99b7aec8 Sim Zhen Quan <<EMAIL>> 1746689567 +0800	merge origin/main: Merge made by the 'ort' strategy.
ddd135bd0a1479d803602f163fd6c33c99b7aec8 1eeeab6d3010ab868e4544c2d0c771bf7f18f1b0 Sim Zhen Quan <<EMAIL>> 1746692047 +0800	commit: Reviewed
1eeeab6d3010ab868e4544c2d0c771bf7f18f1b0 99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 Sim Zhen Quan <<EMAIL>> 1746692094 +0800	checkout: moving from issue-149-wallet-top-up-max-limit to dev
99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 bef2e0c304caa42d315667e91779cfd04276a75c Sim Zhen Quan <<EMAIL>> 1746692100 +0800	merge origin/issue-149-wallet-top-up-max-limit: Merge made by the 'ort' strategy.
bef2e0c304caa42d315667e91779cfd04276a75c 767b445dd8563ce094315d096223b3488ab55c4c Sim Zhen Quan <<EMAIL>> 1746692221 +0800	checkout: moving from dev to bug/call_no_wildcard
767b445dd8563ce094315d096223b3488ab55c4c 0aeb2e99462cf7713c6d6d1838f05851461ea11f Sim Zhen Quan <<EMAIL>> 1746692227 +0800	merge origin/main: Merge made by the 'ort' strategy.
0aeb2e99462cf7713c6d6d1838f05851461ea11f bef2e0c304caa42d315667e91779cfd04276a75c Sim Zhen Quan <<EMAIL>> 1746692318 +0800	checkout: moving from bug/call_no_wildcard to dev
bef2e0c304caa42d315667e91779cfd04276a75c fcf697c1b08d822957a75e3dbfffd7adc8edb042 Sim Zhen Quan <<EMAIL>> 1746692323 +0800	merge bug/call_no_wildcard: Merge made by the 'ort' strategy.
fcf697c1b08d822957a75e3dbfffd7adc8edb042 20783b1488c426437a92f16f779aa270fe67dc64 Sim Zhen Quan <<EMAIL>> 1746692522 +0800	commit: Deployed to DEV
20783b1488c426437a92f16f779aa270fe67dc64 362cfe59b031923e4f495820729641a049a89d34 Sim Zhen Quan <<EMAIL>> 1746692529 +0800	checkout: moving from dev to fix-substitute-record-query-bug
362cfe59b031923e4f495820729641a049a89d34 a526ca72edfc6787f28cbf6470dddcc7f2ff0d3a Sim Zhen Quan <<EMAIL>> 1746692536 +0800	merge origin/main: Merge made by the 'ort' strategy.
a526ca72edfc6787f28cbf6470dddcc7f2ff0d3a 563a1cdcae45305c83db779a828d8fcd4a869690 Sim Zhen Quan <<EMAIL>> 1746692815 +0800	checkout: moving from fix-substitute-record-query-bug to student-statistic-report-added-class-code-column
563a1cdcae45305c83db779a828d8fcd4a869690 7d8b09e638c5550500bda4660c8188269a633c02 Sim Zhen Quan <<EMAIL>> 1746692820 +0800	merge origin/main: Merge made by the 'ort' strategy.
7d8b09e638c5550500bda4660c8188269a633c02 20783b1488c426437a92f16f779aa270fe67dc64 Sim Zhen Quan <<EMAIL>> 1746694936 +0800	checkout: moving from student-statistic-report-added-class-code-column to dev
20783b1488c426437a92f16f779aa270fe67dc64 e9a6efc740153bb50d199725c9ced5b00a1d6af8 Sim Zhen Quan <<EMAIL>> 1746694942 +0800	merge origin/student-statistic-report-added-class-code-column: Merge made by the 'ort' strategy.
e9a6efc740153bb50d199725c9ced5b00a1d6af8 256f70facf8209441441ea4869fbff461ff393a9 Sim Zhen Quan <<EMAIL>> 1746698562 +0800	checkout: moving from dev to fix/attendance-mark-deduction-type-filters
256f70facf8209441441ea4869fbff461ff393a9 281a59844b2d2f65895a881d04baa76149229efc Sim Zhen Quan <<EMAIL>> 1746698568 +0800	merge origin/main: Merge made by the 'ort' strategy.
281a59844b2d2f65895a881d04baa76149229efc e9a6efc740153bb50d199725c9ced5b00a1d6af8 Sim Zhen Quan <<EMAIL>> 1746700001 +0800	checkout: moving from fix/attendance-mark-deduction-type-filters to dev
e9a6efc740153bb50d199725c9ced5b00a1d6af8 c8d264bce7770084e3ec39ffc9ad3f5336ba70c1 Sim Zhen Quan <<EMAIL>> 1746700045 +0800	commit (merge): Merge branch 'fix/attendance-mark-deduction-type-filters' into dev
c8d264bce7770084e3ec39ffc9ad3f5336ba70c1 f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 Sim Zhen Quan <<EMAIL>> 1746713338 +0800	commit: Deployed to DEV
f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746713913 +0800	checkout: moving from dev to main
15e2d16e802e9e2690986d55c2c2445ca5750878 534a9e177e126eab7fe33c6f9a60613b4806ff8f Sim Zhen Quan <<EMAIL>> 1746713918 +0800	pull: Fast-forward
534a9e177e126eab7fe33c6f9a60613b4806ff8f adb122bcaddf54bae0d5f3bec8a6dcd5b11ce3f2 Sim Zhen Quan <<EMAIL>> 1746714101 +0800	pull: Fast-forward
adb122bcaddf54bae0d5f3bec8a6dcd5b11ce3f2 f02f1656f453475c0f5ed9c33c6e32e872dd5338 Sim Zhen Quan <<EMAIL>> 1746756057 +0800	commit: Deployed to PRD
f02f1656f453475c0f5ed9c33c6e32e872dd5338 7ba7281f8e2546f3de8442ac078d4e88dec06df3 Sim Zhen Quan <<EMAIL>> 1746756279 +0800	checkout: moving from main to timeslot-and-period-attendance-add-has-mark-deduction-column
7ba7281f8e2546f3de8442ac078d4e88dec06df3 64c58efd25474ab741c5ffebffda57024eda742f Sim Zhen Quan <<EMAIL>> 1746756443 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into timeslot-and-period-attendance-add-has-mark-deduction-column
64c58efd25474ab741c5ffebffda57024eda742f f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 Sim Zhen Quan <<EMAIL>> 1746757799 +0800	checkout: moving from timeslot-and-period-attendance-add-has-mark-deduction-column to dev
f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 2959d413f162e223335e80ebada0013defb07aef Sim Zhen Quan <<EMAIL>> 1746757806 +0800	merge timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
2959d413f162e223335e80ebada0013defb07aef 50e8c408e4a982de04d223e358509e8c08ac8f70 Sim Zhen Quan <<EMAIL>> 1746758714 +0800	commit: Deployed to DEV
50e8c408e4a982de04d223e358509e8c08ac8f70 729327b11d1b4f7cdcf4955d81f57a6ed7d2c9e3 Sim Zhen Quan <<EMAIL>> 1746758743 +0800	checkout: moving from dev to period-attendance-patch-script
729327b11d1b4f7cdcf4955d81f57a6ed7d2c9e3 9b5f5a10b427cb34da0cc803ce44f27580b2cd57 Sim Zhen Quan <<EMAIL>> 1746758776 +0800	merge timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
9b5f5a10b427cb34da0cc803ce44f27580b2cd57 3e6f3c7503404d030be0a03a03d08145faa68703 Sim Zhen Quan <<EMAIL>> 1746761164 +0800	pull: Fast-forward
3e6f3c7503404d030be0a03a03d08145faa68703 50e8c408e4a982de04d223e358509e8c08ac8f70 Sim Zhen Quan <<EMAIL>> 1746761753 +0800	checkout: moving from period-attendance-patch-script to dev
50e8c408e4a982de04d223e358509e8c08ac8f70 235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 Sim Zhen Quan <<EMAIL>> 1746761758 +0800	merge origin/timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 f02f1656f453475c0f5ed9c33c6e32e872dd5338 Sim Zhen Quan <<EMAIL>> 1746768091 +0800	checkout: moving from dev to main
f02f1656f453475c0f5ed9c33c6e32e872dd5338 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1746768106 +0800	commit: Pagination issue fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> 1746768114 +0800	checkout: moving from main to feature/promotion-mark-CRUD
63148ac69c3f6985a1ce56cb491c6346c6b116de 59d83d2c163b87348e5eaef7c058ae280047d301 Sim Zhen Quan <<EMAIL>> 1746768124 +0800	pull: Fast-forward
59d83d2c163b87348e5eaef7c058ae280047d301 c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1746768125 +0800	merge origin/main: Merge made by the 'ort' strategy.
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 Sim Zhen Quan <<EMAIL>> 1746769678 +0800	checkout: moving from feature/promotion-mark-CRUD to dev
235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1746769829 +0800	checkout: moving from dev to feature/promotion-mark-CRUD
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 fa52e9b003c24908f5bc870f9a352a5c51b44819 Sim Zhen Quan <<EMAIL>> 1746770001 +0800	checkout: moving from feature/promotion-mark-CRUD to exam-module-changes
fa52e9b003c24908f5bc870f9a352a5c51b44819 b59f95520d9c1d9f426ff4de550dcf96fe96fd4f Sim Zhen Quan <<EMAIL>> 1746770006 +0800	pull: Fast-forward
b59f95520d9c1d9f426ff4de550dcf96fe96fd4f ee7eae97a01d66f8a9bce57ef46d885652900f0a Sim Zhen Quan <<EMAIL>> 1746770006 +0800	merge origin/main: Merge made by the 'ort' strategy.
ee7eae97a01d66f8a9bce57ef46d885652900f0a 53dfe21d547ae62f35479a28cd8c5911a77da9a4 Sim Zhen Quan <<EMAIL>> 1746770034 +0800	checkout: moving from exam-module-changes to exam-validate-subject-by-semester-setting
53dfe21d547ae62f35479a28cd8c5911a77da9a4 52bf53dd30a8788bac7abae5325624a516373d9b Sim Zhen Quan <<EMAIL>> 1746770056 +0800	merge origin/main: Merge made by the 'ort' strategy.
52bf53dd30a8788bac7abae5325624a516373d9b ee7eae97a01d66f8a9bce57ef46d885652900f0a Sim Zhen Quan <<EMAIL>> 1746770113 +0800	checkout: moving from exam-validate-subject-by-semester-setting to exam-module-changes
ee7eae97a01d66f8a9bce57ef46d885652900f0a 52bf53dd30a8788bac7abae5325624a516373d9b Sim Zhen Quan <<EMAIL>> 1746770125 +0800	checkout: moving from exam-module-changes to exam-validate-subject-by-semester-setting
52bf53dd30a8788bac7abae5325624a516373d9b c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1746771966 +0800	checkout: moving from exam-validate-subject-by-semester-setting to feature/promotion-mark-CRUD
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 d8a3385d63c500d7268f6a4144e32608fe89f2fb Sim Zhen Quan <<EMAIL>> 1746772576 +0800	checkout: moving from feature/promotion-mark-CRUD to assign-class-to-student-using-new-latest-class-in-semester
d8a3385d63c500d7268f6a4144e32608fe89f2fb 53c565074f4c0a048f4de977e53fc2767993f779 Sim Zhen Quan <<EMAIL>> 1746772584 +0800	merge origin/main: Merge made by the 'ort' strategy.
53c565074f4c0a048f4de977e53fc2767993f779 676f91d95a9ba5e161b255500333626fb75f32e0 Sim Zhen Quan <<EMAIL>> 1746773462 +0800	commit: Reviewed
676f91d95a9ba5e161b255500333626fb75f32e0 235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 Sim Zhen Quan <<EMAIL>> 1746773567 +0800	checkout: moving from assign-class-to-student-using-new-latest-class-in-semester to dev
235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 Sim Zhen Quan <<EMAIL>> 1746773573 +0800	merge assign-class-to-student-using-new-latest-class-in-semester: Merge made by the 'ort' strategy.
1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 749a7523adf4fad36bb5a988cdd1af7ab862bbfb Sim Zhen Quan <<EMAIL>> 1746773675 +0800	checkout: moving from dev to issuelog-25-hostel-savings-see-transaction-balance
749a7523adf4fad36bb5a988cdd1af7ab862bbfb c840b15d86e8b3130c47984dc3f48d5f9c8e9ff3 Sim Zhen Quan <<EMAIL>> 1746773681 +0800	pull: Fast-forward
c840b15d86e8b3130c47984dc3f48d5f9c8e9ff3 1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from issuelog-25-hostel-savings-see-transaction-balance to dev
1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 0ae4284adc43da2aac16da31f3ebc02ee8ec358f Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
0ae4284adc43da2aac16da31f3ebc02ee8ec358f 7c75c11992201c58adf5876152477e10f5129247 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to report/hostel-report-saving-account
7c75c11992201c58adf5876152477e10f5129247 4fada80be3a64d965a991284cfe31ef6aa8897c6 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/main' into report/hostel-report-saving-account
4fada80be3a64d965a991284cfe31ef6aa8897c6 53fe959d80ddf8a794c53ab4bb1865308855aea5 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Review WIP
53fe959d80ddf8a794c53ab4bb1865308855aea5 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to main
bb9eef3886a046805ccb3d3216e4f106c593f53b bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to lucas/repository-fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 5bee99ef0f40fedc66adfa0f91abafcaf1407df0 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: WIP
5bee99ef0f40fedc66adfa0f91abafcaf1407df0 3e53500f680264aa6a1a02118e5a73df24b6c694 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Done
3e53500f680264aa6a1a02118e5a73df24b6c694 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from lucas/repository-fix to main
bb9eef3886a046805ccb3d3216e4f106c593f53b bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to repository-fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 3e0b41aa1f6d9702082cfe676600d5202cd34fb8 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from repository-fix to noah-fix-repo
3e0b41aa1f6d9702082cfe676600d5202cd34fb8 7062f274992402077fabb3cf7a676339cef4a4c8 Sim Zhen Quan <<EMAIL>> 1747068360 +0800	commit: Reviewed
7062f274992402077fabb3cf7a676339cef4a4c8 eb26fb35298bac85e81e16f51b1b9d388226767a Sim Zhen Quan <<EMAIL>> 1747068404 +0800	checkout: moving from noah-fix-repo to fix-form-request
eb26fb35298bac85e81e16f51b1b9d388226767a 1d585bce2ea286d000cfcb7f2fba6dc1e2094055 Sim Zhen Quan <<EMAIL>> 1747068428 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
1d585bce2ea286d000cfcb7f2fba6dc1e2094055 f81b706f15dc7f65ca47998ed28d2a5a2560a28e Sim Zhen Quan <<EMAIL>> 1747069115 +0800	checkout: moving from fix-form-request to alvin-update-repository
f81b706f15dc7f65ca47998ed28d2a5a2560a28e 56412647dddf909f83c32155000c7bca43332086 Sim Zhen Quan <<EMAIL>> 1747069123 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
56412647dddf909f83c32155000c7bca43332086 7374507d91c10c777bddd2fc8560a83481a3db46 Sim Zhen Quan <<EMAIL>> 1747069402 +0800	checkout: moving from alvin-update-repository to ryan-repository-fix
7374507d91c10c777bddd2fc8560a83481a3db46 47651212951046eca4a2f9f1b861a5a656f4c9a7 Sim Zhen Quan <<EMAIL>> 1747069461 +0800	commit (merge): Merge remote-tracking branch 'origin/repository-fix' into ryan-repository-fix
47651212951046eca4a2f9f1b861a5a656f4c9a7 41cb4ce0d99f5eb90b99f4571479070b67e78ac1 Sim Zhen Quan <<EMAIL>> 1747100604 +0800	checkout: moving from ryan-repository-fix to fix/repository-to-isset-kimi
41cb4ce0d99f5eb90b99f4571479070b67e78ac1 5d1ee3bbcac05327ef26c393a89b3709236e4749 Sim Zhen Quan <<EMAIL>> 1747100611 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
5d1ee3bbcac05327ef26c393a89b3709236e4749 0ae4284adc43da2aac16da31f3ebc02ee8ec358f Sim Zhen Quan <<EMAIL>> 1747101736 +0800	checkout: moving from fix/repository-to-isset-kimi to dev
0ae4284adc43da2aac16da31f3ebc02ee8ec358f 6eb648b216596044023cde0cfe5b06685b80bf82 Sim Zhen Quan <<EMAIL>> 1747101784 +0800	commit (merge): Merge remote-tracking branch 'origin/repository-fix' into dev
6eb648b216596044023cde0cfe5b06685b80bf82 1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e Sim Zhen Quan <<EMAIL>> 1747102605 +0800	commit: Deployed to dev
1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1747102630 +0800	checkout: moving from dev to feature/promotion-mark-CRUD
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 5c573a486435225f81ab51bd6fc47a5ae194527b Sim Zhen Quan <<EMAIL>> 1747102635 +0800	pull: Fast-forward
5c573a486435225f81ab51bd6fc47a5ae194527b 561b61a90422f80ab2f5993ed68ea9fcc89a4673 Sim Zhen Quan <<EMAIL>> 1747103649 +0800	commit: Review WIP
561b61a90422f80ab2f5993ed68ea9fcc89a4673 af2fc5b5340faf8755b8bb1971a2f543b3b4ce4a Sim Zhen Quan <<EMAIL>> 1747104399 +0800	commit: Reviewed
af2fc5b5340faf8755b8bb1971a2f543b3b4ce4a ee7eae97a01d66f8a9bce57ef46d885652900f0a Sim Zhen Quan <<EMAIL>> 1747104443 +0800	checkout: moving from feature/promotion-mark-CRUD to exam-module-changes
ee7eae97a01d66f8a9bce57ef46d885652900f0a 25e4b0a40ccb90240216c47d2b188fb4d469a8e2 Sim Zhen Quan <<EMAIL>> 1747104449 +0800	pull: Fast-forward
25e4b0a40ccb90240216c47d2b188fb4d469a8e2 60bd431e5dd3f83b1d20c12fafdb725355b793da Sim Zhen Quan <<EMAIL>> 1747104719 +0800	checkout: moving from exam-module-changes to jira-380-exam-uat-feedback
60bd431e5dd3f83b1d20c12fafdb725355b793da 126c6b1b2582cce943d378cb9a42590695c7fba8 Sim Zhen Quan <<EMAIL>> 1747104727 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
126c6b1b2582cce943d378cb9a42590695c7fba8 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747106181 +0800	checkout: moving from jira-380-exam-uat-feedback to repository-fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 18772bd26098aaba1dafd350f7ea823c3f736954 Sim Zhen Quan <<EMAIL>> 1747106185 +0800	pull: Fast-forward
18772bd26098aaba1dafd350f7ea823c3f736954 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747106882 +0800	checkout: moving from repository-fix to main
bb9eef3886a046805ccb3d3216e4f106c593f53b bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747106928 +0800	checkout: moving from main to staging/2025-05-13
bb9eef3886a046805ccb3d3216e4f106c593f53b 18772bd26098aaba1dafd350f7ea823c3f736954 Sim Zhen Quan <<EMAIL>> 1747107115 +0800	checkout: moving from staging/2025-05-13 to repository-fix
18772bd26098aaba1dafd350f7ea823c3f736954 0b60906babcc41ed25a89077bf99ee7a0f070331 Sim Zhen Quan <<EMAIL>> 1747107121 +0800	merge origin/staging/2025-05-13: Merge made by the 'ort' strategy.
0b60906babcc41ed25a89077bf99ee7a0f070331 41f3fe006ae4c0a95122f7191c6cf9b5bdd27afa Sim Zhen Quan <<EMAIL>> 1747109044 +0800	commit: WIP
41f3fe006ae4c0a95122f7191c6cf9b5bdd27afa 8bf5152d5d434dfb0aa929f31ba262665a61e5cf Sim Zhen Quan <<EMAIL>> 1747109907 +0800	commit: Bug fix
8bf5152d5d434dfb0aa929f31ba262665a61e5cf 1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e Sim Zhen Quan <<EMAIL>> 1747110478 +0800	checkout: moving from repository-fix to dev
1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e 1bb9d8f94f7861e0673e16408d30d0db6c16c1df Sim Zhen Quan <<EMAIL>> 1747110485 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
1bb9d8f94f7861e0673e16408d30d0db6c16c1df 87eaa5344e47680a5dea7dccc8b245af9c6bacb2 Sim Zhen Quan <<EMAIL>> 1747110504 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
87eaa5344e47680a5dea7dccc8b245af9c6bacb2 87eaa5344e47680a5dea7dccc8b245af9c6bacb2 Sim Zhen Quan <<EMAIL>> 1747119893 +0800	checkout: moving from dev to dev
87eaa5344e47680a5dea7dccc8b245af9c6bacb2 1b4355bfa32f47615ce4bd69d0e42a6139213640 Sim Zhen Quan <<EMAIL>> 1747119901 +0800	commit: Deployed to dev
1b4355bfa32f47615ce4bd69d0e42a6139213640 bfd041b9e094aa5450623c713abdc497b0c210c3 Sim Zhen Quan <<EMAIL>> 1747119979 +0800	commit (merge): Merge remote-tracking branch 'origin/seed-exam-data' into dev
bfd041b9e094aa5450623c713abdc497b0c210c3 126c6b1b2582cce943d378cb9a42590695c7fba8 Sim Zhen Quan <<EMAIL>> 1747195209 +0800	checkout: moving from dev to jira-380-exam-uat-feedback
126c6b1b2582cce943d378cb9a42590695c7fba8 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747195224 +0800	checkout: moving from jira-380-exam-uat-feedback to main
bb9eef3886a046805ccb3d3216e4f106c593f53b 6c0cdcd307ead6c259d2c32a12252476769b5228 Sim Zhen Quan <<EMAIL>> 1747195254 +0800	pull: Fast-forward
6c0cdcd307ead6c259d2c32a12252476769b5228 30fe292833e03147752506546cec0bea4f8275a0 Sim Zhen Quan <<EMAIL>> 1747195255 +0800	commit: Added report card worker
30fe292833e03147752506546cec0bea4f8275a0 126c6b1b2582cce943d378cb9a42590695c7fba8 Sim Zhen Quan <<EMAIL>> 1747195263 +0800	checkout: moving from main to jira-380-exam-uat-feedback
126c6b1b2582cce943d378cb9a42590695c7fba8 ad29d18d5735be9d944dfbaeaca3b6670fcb2392 Sim Zhen Quan <<EMAIL>> 1747195268 +0800	pull: Fast-forward
ad29d18d5735be9d944dfbaeaca3b6670fcb2392 ad29d18d5735be9d944dfbaeaca3b6670fcb2392 Sim Zhen Quan <<EMAIL>> 1747195288 +0800	checkout: moving from jira-380-exam-uat-feedback to jira-380-exam-uat-feedback
ad29d18d5735be9d944dfbaeaca3b6670fcb2392 6f1b47ad9b96c19036ab01124bd1731385e7c47a Sim Zhen Quan <<EMAIL>> 1747195641 +0800	checkout: moving from jira-380-exam-uat-feedback to JIRA-436-exam-changes
6f1b47ad9b96c19036ab01124bd1731385e7c47a 654c76d1b26396a24ff51143a6dd73f044c75b99 Sim Zhen Quan <<EMAIL>> 1747196827 +0800	pull: Fast-forward
654c76d1b26396a24ff51143a6dd73f044c75b99 bfd041b9e094aa5450623c713abdc497b0c210c3 Sim Zhen Quan <<EMAIL>> 1747196830 +0800	checkout: moving from JIRA-436-exam-changes to dev
bfd041b9e094aa5450623c713abdc497b0c210c3 2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 Sim Zhen Quan <<EMAIL>> 1747196834 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 Sim Zhen Quan <<EMAIL>> 1747198068 +0800	reset: moving to HEAD
2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 654c76d1b26396a24ff51143a6dd73f044c75b99 Sim Zhen Quan <<EMAIL>> 1747198070 +0800	checkout: moving from dev to JIRA-436-exam-changes
654c76d1b26396a24ff51143a6dd73f044c75b99 cbad4bb56dce0b3ee3950a43cb23904ced376ab5 Sim Zhen Quan <<EMAIL>> 1747198077 +0800	pull: Fast-forward
cbad4bb56dce0b3ee3950a43cb23904ced376ab5 c25bad2f033e93bb6e3b34cace32af8c0e6f3882 Sim Zhen Quan <<EMAIL>> 1747198099 +0800	commit: Undo accidental comment
c25bad2f033e93bb6e3b34cace32af8c0e6f3882 2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 Sim Zhen Quan <<EMAIL>> 1747198131 +0800	checkout: moving from JIRA-436-exam-changes to dev
2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 8d787cefa1ecd0cb5e2de6f56788ab56a044a90a Sim Zhen Quan <<EMAIL>> 1747198137 +0800	merge origin/main: Merge made by the 'ort' strategy.
8d787cefa1ecd0cb5e2de6f56788ab56a044a90a f07cd64cdb45f9a279810c55bc82535297f4792e Sim Zhen Quan <<EMAIL>> 1747198508 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
f07cd64cdb45f9a279810c55bc82535297f4792e f07cd64cdb45f9a279810c55bc82535297f4792e Sim Zhen Quan <<EMAIL>> 1747203628 +0800	checkout: moving from dev to lucas-testing
f07cd64cdb45f9a279810c55bc82535297f4792e bf2a3905d23b95a779658ee560f16a4408221cde Sim Zhen Quan <<EMAIL>> 1747203638 +0800	commit: Test
bf2a3905d23b95a779658ee560f16a4408221cde c25bad2f033e93bb6e3b34cace32af8c0e6f3882 Sim Zhen Quan <<EMAIL>> 1747204456 +0800	checkout: moving from lucas-testing to JIRA-436-exam-changes
c25bad2f033e93bb6e3b34cace32af8c0e6f3882 537f2e9ae6bcb95c19327000d5a384200989b8cb Sim Zhen Quan <<EMAIL>> 1747204467 +0800	pull: Fast-forward
537f2e9ae6bcb95c19327000d5a384200989b8cb 0829aefac2c607301acdaf0990aca3f83456a0ce Sim Zhen Quan <<EMAIL>> 1747204493 +0800	commit: Uncomment queue
0829aefac2c607301acdaf0990aca3f83456a0ce 1ecdbbcd780d1b5760190f11262f84409be1069f Sim Zhen Quan <<EMAIL>> 1747204653 +0800	commit: Updated grading framework
1ecdbbcd780d1b5760190f11262f84409be1069f f07cd64cdb45f9a279810c55bc82535297f4792e Sim Zhen Quan <<EMAIL>> 1747204712 +0800	checkout: moving from JIRA-436-exam-changes to dev
f07cd64cdb45f9a279810c55bc82535297f4792e 7be6877a04390b6aaa1788cbee3b58f15dac1aff Sim Zhen Quan <<EMAIL>> 1747204712 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
7be6877a04390b6aaa1788cbee3b58f15dac1aff 1ecdbbcd780d1b5760190f11262f84409be1069f Sim Zhen Quan <<EMAIL>> 1747215053 +0800	checkout: moving from dev to JIRA-436-exam-changes
1ecdbbcd780d1b5760190f11262f84409be1069f e69be91393589fbe1e3822be078383e30ce397ac Sim Zhen Quan <<EMAIL>> 1747215110 +0800	commit: Update report formatting
e69be91393589fbe1e3822be078383e30ce397ac 7be6877a04390b6aaa1788cbee3b58f15dac1aff Sim Zhen Quan <<EMAIL>> 1747215135 +0800	checkout: moving from JIRA-436-exam-changes to dev
7be6877a04390b6aaa1788cbee3b58f15dac1aff 83f69259b4c23c823843909bec0df5595b2c45d7 Sim Zhen Quan <<EMAIL>> 1747215159 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
83f69259b4c23c823843909bec0df5595b2c45d7 e69be91393589fbe1e3822be078383e30ce397ac Sim Zhen Quan <<EMAIL>> 1747242924 +0800	checkout: moving from dev to JIRA-436-exam-changes
e69be91393589fbe1e3822be078383e30ce397ac 0d985738524f1954b0731373ec6b9ab4fd72268a Sim Zhen Quan <<EMAIL>> 1747242942 +0800	pull: Fast-forward
0d985738524f1954b0731373ec6b9ab4fd72268a 1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 Sim Zhen Quan <<EMAIL>> 1747242957 +0800	commit: Update report formatting
1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 30fe292833e03147752506546cec0bea4f8275a0 Sim Zhen Quan <<EMAIL>> 1747242978 +0800	checkout: moving from JIRA-436-exam-changes to main
30fe292833e03147752506546cec0bea4f8275a0 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747243010 +0800	checkout: moving from main to staging/2025-05-13
bb9eef3886a046805ccb3d3216e4f106c593f53b 07fa86eefc1acd5a7cc8c47dda2d342054a92e8a Sim Zhen Quan <<EMAIL>> 1747243015 +0800	pull: Fast-forward
07fa86eefc1acd5a7cc8c47dda2d342054a92e8a 3b06ce1f1b3c6ef8a84c7c468130956fb695bd8a Sim Zhen Quan <<EMAIL>> 1747243054 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into staging/2025-05-13
3b06ce1f1b3c6ef8a84c7c468130956fb695bd8a 30fe292833e03147752506546cec0bea4f8275a0 Sim Zhen Quan <<EMAIL>> 1747243079 +0800	checkout: moving from staging/2025-05-13 to main
30fe292833e03147752506546cec0bea4f8275a0 56e4364b063771a8dfdc26af56781b90d349fbf7 Sim Zhen Quan <<EMAIL>> 1747243084 +0800	pull: Fast-forward
56e4364b063771a8dfdc26af56781b90d349fbf7 39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef Sim Zhen Quan <<EMAIL>> 1747244346 +0800	commit: Deployed to prd
39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef 01afb39fae367143b8ff01aededd7d3e3b4467d5 Sim Zhen Quan <<EMAIL>> 1747297670 +0800	checkout: moving from main to exam-posting-prechecks-handle-is-exempted
01afb39fae367143b8ff01aededd7d3e3b4467d5 f16f7c160de8d14c59865988f326c030e0906587 Sim Zhen Quan <<EMAIL>> 1747297683 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
f16f7c160de8d14c59865988f326c030e0906587 849a4c285bf78e085ca9bb6ac403122bfe565dec Sim Zhen Quan <<EMAIL>> 1747298057 +0800	checkout: moving from exam-posting-prechecks-handle-is-exempted to seed-exam-data
849a4c285bf78e085ca9bb6ac403122bfe565dec 9888834d088462cfcf9c7c4141266274c95f9e8a Sim Zhen Quan <<EMAIL>> 1747298605 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes' into seed-exam-data
9888834d088462cfcf9c7c4141266274c95f9e8a 25e4b0a40ccb90240216c47d2b188fb4d469a8e2 Sim Zhen Quan <<EMAIL>> 1747299146 +0800	checkout: moving from seed-exam-data to exam-module-changes
25e4b0a40ccb90240216c47d2b188fb4d469a8e2 09f6350ab4a41159556210187528303750604f0e Sim Zhen Quan <<EMAIL>> 1747299152 +0800	pull: Fast-forward
09f6350ab4a41159556210187528303750604f0e 9fecece139c77f54607e0ee5599d530dff75ec7f Sim Zhen Quan <<EMAIL>> 1747299153 +0800	merge origin/main: Merge made by the 'ort' strategy.
9fecece139c77f54607e0ee5599d530dff75ec7f 1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 Sim Zhen Quan <<EMAIL>> 1747299653 +0800	checkout: moving from exam-module-changes to JIRA-436-exam-changes
1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 f026caff06b56f89f6cdd0f591a5e3228a0b8bef Sim Zhen Quan <<EMAIL>> 1747299658 +0800	pull: Fast-forward
f026caff06b56f89f6cdd0f591a5e3228a0b8bef c4f5b02c4e2edaf6ddb16fd5c4f392fdf98d61ec Sim Zhen Quan <<EMAIL>> 1747299659 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
c4f5b02c4e2edaf6ddb16fd5c4f392fdf98d61ec 978dbc014a5f6a1ce3037d64de2764b2fd7811ba Sim Zhen Quan <<EMAIL>> 1747299829 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes' into JIRA-436-exam-changes
978dbc014a5f6a1ce3037d64de2764b2fd7811ba 83f69259b4c23c823843909bec0df5595b2c45d7 Sim Zhen Quan <<EMAIL>> 1747299943 +0800	checkout: moving from JIRA-436-exam-changes to dev
83f69259b4c23c823843909bec0df5595b2c45d7 6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc Sim Zhen Quan <<EMAIL>> 1747299955 +0800	merge origin/JIRA-436-exam-changes: Merge made by the 'ort' strategy.
6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc 978dbc014a5f6a1ce3037d64de2764b2fd7811ba Sim Zhen Quan <<EMAIL>> 1747391456 +0800	checkout: moving from dev to JIRA-436-exam-changes
978dbc014a5f6a1ce3037d64de2764b2fd7811ba 180f8fd8a2842524ad0a383b10d52ed6afc5154e Sim Zhen Quan <<EMAIL>> 1747391462 +0800	pull: Fast-forward
180f8fd8a2842524ad0a383b10d52ed6afc5154e 39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef Sim Zhen Quan <<EMAIL>> 1747590091 +0800	checkout: moving from JIRA-436-exam-changes to main
39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef c6194433e2e1256da72317db5aefd7ed1fb88269 Sim Zhen Quan <<EMAIL>> 1747590119 +0800	commit: Changed report header
c6194433e2e1256da72317db5aefd7ed1fb88269 6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc Sim Zhen Quan <<EMAIL>> 1747625746 +0800	checkout: moving from main to dev
6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc 9d44782f69d9cb5e2753f304e4220962efb5dcf7 Sim Zhen Quan <<EMAIL>> 1747625939 +0800	commit: Fix merge conflicts
9d44782f69d9cb5e2753f304e4220962efb5dcf7 c6194433e2e1256da72317db5aefd7ed1fb88269 Sim Zhen Quan <<EMAIL>> 1747637535 +0800	checkout: moving from dev to main
c6194433e2e1256da72317db5aefd7ed1fb88269 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747637704 +0800	commit: Hotfix hostel in out report repository
8536e64e3681503deda5c860fc678553f430eede 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747638360 +0800	checkout: moving from main to staging/2025-05-19
8536e64e3681503deda5c860fc678553f430eede 281a59844b2d2f65895a881d04baa76149229efc Sim Zhen Quan <<EMAIL>> 1747638385 +0800	checkout: moving from staging/2025-05-19 to fix/attendance-mark-deduction-type-filters
281a59844b2d2f65895a881d04baa76149229efc eefd2d30dbc77a5e6d762f51a3b78f722abc7303 Sim Zhen Quan <<EMAIL>> 1747638465 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into fix/attendance-mark-deduction-type-filters
eefd2d30dbc77a5e6d762f51a3b78f722abc7303 180f8fd8a2842524ad0a383b10d52ed6afc5154e Sim Zhen Quan <<EMAIL>> 1747638548 +0800	checkout: moving from fix/attendance-mark-deduction-type-filters to JIRA-436-exam-changes
180f8fd8a2842524ad0a383b10d52ed6afc5154e 9fecece139c77f54607e0ee5599d530dff75ec7f Sim Zhen Quan <<EMAIL>> 1747638565 +0800	checkout: moving from JIRA-436-exam-changes to exam-module-changes
9fecece139c77f54607e0ee5599d530dff75ec7f 4c8178d7531d3a183fdafa4568ab8d77bf7a7d9a Sim Zhen Quan <<EMAIL>> 1747638570 +0800	pull: Fast-forward
4c8178d7531d3a183fdafa4568ab8d77bf7a7d9a a8d582bd8d23ad224ebb889e1855a6cd393f1348 Sim Zhen Quan <<EMAIL>> 1747638571 +0800	merge origin/main: Merge made by the 'ort' strategy.
a8d582bd8d23ad224ebb889e1855a6cd393f1348 180f8fd8a2842524ad0a383b10d52ed6afc5154e Sim Zhen Quan <<EMAIL>> 1747638583 +0800	checkout: moving from exam-module-changes to JIRA-436-exam-changes
180f8fd8a2842524ad0a383b10d52ed6afc5154e 17de40081e310c31245c05dc8fda3f96a2209dd2 Sim Zhen Quan <<EMAIL>> 1747638593 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
17de40081e310c31245c05dc8fda3f96a2209dd2 55003cca80ad2eba1437f543be0558616b9f844d Sim Zhen Quan <<EMAIL>> 1747640281 +0800	commit: Reviewed
55003cca80ad2eba1437f543be0558616b9f844d 9d44782f69d9cb5e2753f304e4220962efb5dcf7 Sim Zhen Quan <<EMAIL>> 1747640695 +0800	checkout: moving from JIRA-436-exam-changes to dev
9d44782f69d9cb5e2753f304e4220962efb5dcf7 7f0a3d140a091241d5e2e1e89c25a4d001da6916 Sim Zhen Quan <<EMAIL>> 1747640708 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
7f0a3d140a091241d5e2e1e89c25a4d001da6916 68748fa7bd793a544696060b324c9c46f4f6425d Sim Zhen Quan <<EMAIL>> 1747641514 +0800	commit: Deployed to dev
68748fa7bd793a544696060b324c9c46f4f6425d 300720c1f1866ff011b1ef912d441915bb2fd598 Sim Zhen Quan <<EMAIL>> 1747641530 +0800	checkout: moving from dev to feature/enrollment-session-CRUD
300720c1f1866ff011b1ef912d441915bb2fd598 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747644209 +0800	checkout: moving from feature/enrollment-session-CRUD to main
8536e64e3681503deda5c860fc678553f430eede 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747644225 +0800	checkout: moving from main to add-guardian-resource-to-hostel-in-out-record
8536e64e3681503deda5c860fc678553f430eede 68748fa7bd793a544696060b324c9c46f4f6425d Sim Zhen Quan <<EMAIL>> 1747644385 +0800	checkout: moving from add-guardian-resource-to-hostel-in-out-record to dev
68748fa7bd793a544696060b324c9c46f4f6425d 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747644393 +0800	checkout: moving from dev to add-guardian-resource-to-hostel-in-out-record
8536e64e3681503deda5c860fc678553f430eede 166a0055dfa4606d5bd2ceb7d91e37d5dd7c0fd7 Sim Zhen Quan <<EMAIL>> 1747644524 +0800	commit: Added guardian and student includes into hostel in out
166a0055dfa4606d5bd2ceb7d91e37d5dd7c0fd7 68748fa7bd793a544696060b324c9c46f4f6425d Sim Zhen Quan <<EMAIL>> 1747644536 +0800	checkout: moving from add-guardian-resource-to-hostel-in-out-record to dev
68748fa7bd793a544696060b324c9c46f4f6425d 7c76fb71e37c86de371fb1c23c596ca0e6d65527 Sim Zhen Quan <<EMAIL>> 1747644539 +0800	merge add-guardian-resource-to-hostel-in-out-record: Merge made by the 'ort' strategy.
7c76fb71e37c86de371fb1c23c596ca0e6d65527 43406cbb1b40e40908b07c7c34aec8d833ed8793 Sim Zhen Quan <<EMAIL>> 1747646585 +0800	checkout: moving from dev to fix/update-validation-when-create-billing-doc
43406cbb1b40e40908b07c7c34aec8d833ed8793 edffd32b155e5ef606b1bb99bfc6b0a337aafadb Sim Zhen Quan <<EMAIL>> 1747646591 +0800	merge origin/main: Merge made by the 'ort' strategy.
edffd32b155e5ef606b1bb99bfc6b0a337aafadb 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747707943 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to main
8536e64e3681503deda5c860fc678553f430eede 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747708359 +0800	commit: Added refresh view table for exam posting pre check mat view every 5 mins
7068f7efbcc996503b57996f24b4260a06b10d6a edffd32b155e5ef606b1bb99bfc6b0a337aafadb Sim Zhen Quan <<EMAIL>> 1747708989 +0800	checkout: moving from main to fix/update-validation-when-create-billing-doc
edffd32b155e5ef606b1bb99bfc6b0a337aafadb 22ef14452f35905e4c388acf0362d52ca9eaef0b Sim Zhen Quan <<EMAIL>> 1747708995 +0800	merge origin/main: Merge made by the 'ort' strategy.
22ef14452f35905e4c388acf0362d52ca9eaef0b a8d582bd8d23ad224ebb889e1855a6cd393f1348 Sim Zhen Quan <<EMAIL>> 1747715179 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to exam-module-changes
a8d582bd8d23ad224ebb889e1855a6cd393f1348 5d3e61cf1fc926c2876a99caf8760932b7fc79db Sim Zhen Quan <<EMAIL>> 1747715185 +0800	pull: Fast-forward
5d3e61cf1fc926c2876a99caf8760932b7fc79db 22ef14452f35905e4c388acf0362d52ca9eaef0b Sim Zhen Quan <<EMAIL>> 1747725770 +0800	checkout: moving from exam-module-changes to fix/update-validation-when-create-billing-doc
22ef14452f35905e4c388acf0362d52ca9eaef0b 5d3e61cf1fc926c2876a99caf8760932b7fc79db Sim Zhen Quan <<EMAIL>> 1747726158 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to exam-module-changes
5d3e61cf1fc926c2876a99caf8760932b7fc79db ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 Sim Zhen Quan <<EMAIL>> 1747726161 +0800	merge origin/main: Merge made by the 'ort' strategy.
ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 91b9385b03e321169210ce82eaa9a75bf0d2bfd6 Sim Zhen Quan <<EMAIL>> 1747726171 +0800	checkout: moving from exam-module-changes to JIRA-449-exam-module-changes-v2
91b9385b03e321169210ce82eaa9a75bf0d2bfd6 2c61ebe9514d9212b5711b9428197c731cd8489e Sim Zhen Quan <<EMAIL>> 1747726178 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
2c61ebe9514d9212b5711b9428197c731cd8489e 7c76fb71e37c86de371fb1c23c596ca0e6d65527 Sim Zhen Quan <<EMAIL>> 1747734072 +0800	checkout: moving from JIRA-449-exam-module-changes-v2 to dev
7c76fb71e37c86de371fb1c23c596ca0e6d65527 64c63d1e3f946d9299abe45175cc5bc7d53c23b5 Sim Zhen Quan <<EMAIL>> 1747734075 +0800	merge JIRA-449-exam-module-changes-v2: Merge made by the 'ort' strategy.
64c63d1e3f946d9299abe45175cc5bc7d53c23b5 3f1e67b1966f69be52845926314df0f1bb64c613 Sim Zhen Quan <<EMAIL>> 1747791957 +0800	commit: Deployed to dev
3f1e67b1966f69be52845926314df0f1bb64c613 ad58f6d6f37fc6574a21ce586a1a6a05131f1ade Sim Zhen Quan <<EMAIL>> 1747791966 +0800	commit: Fix kernel issue
ad58f6d6f37fc6574a21ce586a1a6a05131f1ade ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 Sim Zhen Quan <<EMAIL>> 1747791985 +0800	checkout: moving from dev to exam-module-changes
ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab Sim Zhen Quan <<EMAIL>> 1747791991 +0800	cherry-pick: Fix kernel issue
d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab 300720c1f1866ff011b1ef912d441915bb2fd598 Sim Zhen Quan <<EMAIL>> 1747792016 +0800	checkout: moving from exam-module-changes to feature/enrollment-session-CRUD
300720c1f1866ff011b1ef912d441915bb2fd598 db2093540287ae4fef0480d5a14c461b9bae2322 Sim Zhen Quan <<EMAIL>> 1747792022 +0800	pull: Fast-forward
db2093540287ae4fef0480d5a14c461b9bae2322 1b6f03c76c85c7fc9898704cb306e1f5acfc91f7 Sim Zhen Quan <<EMAIL>> 1747792023 +0800	merge origin/main: Merge made by the 'ort' strategy.
1b6f03c76c85c7fc9898704cb306e1f5acfc91f7 5321b822662e92244c0717f314c963f9a8f02982 Sim Zhen Quan <<EMAIL>> 1747798770 +0800	commit: Reviewed
5321b822662e92244c0717f314c963f9a8f02982 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1747798782 +0800	checkout: moving from feature/enrollment-session-CRUD to enrollment
397cd210416fc3aa207241c1d586208f09c0907a 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747798793 +0800	checkout: moving from enrollment to main
7068f7efbcc996503b57996f24b4260a06b10d6a 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747798800 +0800	checkout: moving from main to enrollment-v2
7068f7efbcc996503b57996f24b4260a06b10d6a 5321b822662e92244c0717f314c963f9a8f02982 Sim Zhen Quan <<EMAIL>> 1747798814 +0800	checkout: moving from enrollment-v2 to feature/enrollment-session-CRUD
5321b822662e92244c0717f314c963f9a8f02982 ac0e2f184cf3cd7ee05d7d3d2474c21194f22c31 Sim Zhen Quan <<EMAIL>> 1747799074 +0800	checkout: moving from feature/enrollment-session-CRUD to JIRA-448-grading-framework-changes
ac0e2f184cf3cd7ee05d7d3d2474c21194f22c31 9e730eda9be1a933cf3abeb30e60c92b0448345c Sim Zhen Quan <<EMAIL>> 1747800000 +0800	pull: Fast-forward
9e730eda9be1a933cf3abeb30e60c92b0448345c a399992aaf361fb77915da4ef6b9aac03f037595 Sim Zhen Quan <<EMAIL>> 1747800801 +0800	commit: Tidy up
a399992aaf361fb77915da4ef6b9aac03f037595 ad58f6d6f37fc6574a21ce586a1a6a05131f1ade Sim Zhen Quan <<EMAIL>> 1747801244 +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
ad58f6d6f37fc6574a21ce586a1a6a05131f1ade 76273652849bf61f787d139d14b50937f62c07f9 Sim Zhen Quan <<EMAIL>> 1747801244 +0800	merge JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
76273652849bf61f787d139d14b50937f62c07f9 7c6eb1c3d78e72c052f611ebab19d943e49d8b86 Sim Zhen Quan <<EMAIL>> 1747806727 +0800	commit: Deployed to dev
7c6eb1c3d78e72c052f611ebab19d943e49d8b86 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747807708 +0800	checkout: moving from dev to main
7068f7efbcc996503b57996f24b4260a06b10d6a 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747807715 +0800	pull: Fast-forward
98cd6430fbec4914d05d8a4f4505416b22f74daa d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab Sim Zhen Quan <<EMAIL>> 1747812911 +0800	checkout: moving from main to exam-module-changes
d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab 7dac28fc46b3ed716652711020d5b22c95554a63 Sim Zhen Quan <<EMAIL>> 1747812920 +0800	commit: Deployed to prd
7dac28fc46b3ed716652711020d5b22c95554a63 cdce7767e2fa72aefb8b334bed5a277cda2ef652 Sim Zhen Quan <<EMAIL>> 1747813596 +0800	checkout: moving from exam-module-changes to feature/enrollment-migration
cdce7767e2fa72aefb8b334bed5a277cda2ef652 68cdc6c0d5aa212ac959475ad4ced081a5273e15 Sim Zhen Quan <<EMAIL>> 1747813605 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
68cdc6c0d5aa212ac959475ad4ced081a5273e15 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747813615 +0800	checkout: moving from feature/enrollment-migration to enrollment-v2
7068f7efbcc996503b57996f24b4260a06b10d6a beb8f10d4f8fc6f8ef665b1d068997d4779b92ae Sim Zhen Quan <<EMAIL>> 1747813625 +0800	pull: Fast-forward
beb8f10d4f8fc6f8ef665b1d068997d4779b92ae 856b53f9cb69dc528e1b8661aa367b1e83c192d6 Sim Zhen Quan <<EMAIL>> 1747813625 +0800	merge origin/main: Merge made by the 'ort' strategy.
856b53f9cb69dc528e1b8661aa367b1e83c192d6 68cdc6c0d5aa212ac959475ad4ced081a5273e15 Sim Zhen Quan <<EMAIL>> 1747813642 +0800	checkout: moving from enrollment-v2 to feature/enrollment-migration
68cdc6c0d5aa212ac959475ad4ced081a5273e15 92b6bf3a135b0e4ffbb889eb961100cebe6c43c1 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
92b6bf3a135b0e4ffbb889eb961100cebe6c43c1 02c0349fff9055dd3f3b8988e6833a644e1571d6 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
02c0349fff9055dd3f3b8988e6833a644e1571d6 53fe959d80ddf8a794c53ab4bb1865308855aea5 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/enrollment-migration to report/hostel-report-saving-account
53fe959d80ddf8a794c53ab4bb1865308855aea5 c910ccfe063384f24762978184a5987d7853c42c Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
c910ccfe063384f24762978184a5987d7853c42c 6c29e9e774b831817dccbae5069b439ad583aba7 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
6c29e9e774b831817dccbae5069b439ad583aba7 7c6eb1c3d78e72c052f611ebab19d943e49d8b86 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to dev
7c6eb1c3d78e72c052f611ebab19d943e49d8b86 bbc902b3da8200fd7e32297f9bbbdae9809d7cc7 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/report/hostel-report-saving-account: Merge made by the 'ort' strategy.
bbc902b3da8200fd7e32297f9bbbdae9809d7cc7 ab25508b437c2462bfe4a2b4d8706eff90f1ea13 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
ab25508b437c2462bfe4a2b4d8706eff90f1ea13 a399992aaf361fb77915da4ef6b9aac03f037595 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to JIRA-448-grading-framework-changes
a399992aaf361fb77915da4ef6b9aac03f037595 762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e ab25508b437c2462bfe4a2b4d8706eff90f1ea13 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
ab25508b437c2462bfe4a2b4d8706eff90f1ea13 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747878516 +0800	reset: moving to HEAD
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747878518 +0800	checkout: moving from dev to main
98cd6430fbec4914d05d8a4f4505416b22f74daa 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747882212 +0800	checkout: moving from main to dev
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747897786 +0800	reset: moving to HEAD
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747897788 +0800	checkout: moving from dev to main
98cd6430fbec4914d05d8a4f4505416b22f74daa 7dac28fc46b3ed716652711020d5b22c95554a63 Sim Zhen Quan <<EMAIL>> 1747897886 +0800	checkout: moving from main to exam-module-changes
7dac28fc46b3ed716652711020d5b22c95554a63 b432ed0353f9f28b8969eba6efc59d745e7fc583 Sim Zhen Quan <<EMAIL>> 1747897924 +0800	reset: moving to origin/exam-module-changes
b432ed0353f9f28b8969eba6efc59d745e7fc583 2d66131e3f9e2e5bd1ea8926e825145b71188228 Sim Zhen Quan <<EMAIL>> 1747897962 +0800	commit: Update README.md
2d66131e3f9e2e5bd1ea8926e825145b71188228 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747897989 +0800	checkout: moving from exam-module-changes to main
98cd6430fbec4914d05d8a4f4505416b22f74daa 7b569b520e54c0071477014207bdcb7143911d2d Sim Zhen Quan <<EMAIL>> 1747897995 +0800	pull: Fast-forward
7b569b520e54c0071477014207bdcb7143911d2d d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 Sim Zhen Quan <<EMAIL>> 1747898889 +0800	commit: Fix kernel issue
d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 Sim Zhen Quan <<EMAIL>> 1747900517 +0800	checkout: moving from main to feature/enrollment-import-template
4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 856b53f9cb69dc528e1b8661aa367b1e83c192d6 Sim Zhen Quan <<EMAIL>> 1747900529 +0800	checkout: moving from feature/enrollment-import-template to enrollment-v2
856b53f9cb69dc528e1b8661aa367b1e83c192d6 dc71c69291e10cc9a82508eb5d0051aa5a6b2b21 Sim Zhen Quan <<EMAIL>> 1747900534 +0800	pull: Fast-forward
dc71c69291e10cc9a82508eb5d0051aa5a6b2b21 8e3a834e1638dc286a3df46d786c8279ac8ff807 Sim Zhen Quan <<EMAIL>> 1747900534 +0800	merge origin/main: Merge made by the 'ort' strategy.
8e3a834e1638dc286a3df46d786c8279ac8ff807 4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 Sim Zhen Quan <<EMAIL>> 1747900546 +0800	checkout: moving from enrollment-v2 to feature/enrollment-import-template
4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 d78398df81f6c2d9ea42c8de2840dfb6d55b0093 Sim Zhen Quan <<EMAIL>> 1747900551 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
d78398df81f6c2d9ea42c8de2840dfb6d55b0093 3cdddcc251f82d9a252de0fc4686309d900bbd1b Sim Zhen Quan <<EMAIL>> 1747908940 +0800	commit: Reviewed
3cdddcc251f82d9a252de0fc4686309d900bbd1b d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 Sim Zhen Quan <<EMAIL>> 1747933573 +0800	checkout: moving from feature/enrollment-import-template to main
d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 11a18edb615809acc364116d77b5c404614b21dc Sim Zhen Quan <<EMAIL>> 1747933846 +0800	commit: Remove duplicated scheduler
11a18edb615809acc364116d77b5c404614b21dc 2d66131e3f9e2e5bd1ea8926e825145b71188228 Sim Zhen Quan <<EMAIL>> 1747966167 +0800	checkout: moving from main to exam-module-changes
2d66131e3f9e2e5bd1ea8926e825145b71188228 762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e Sim Zhen Quan <<EMAIL>> 1747969899 +0800	checkout: moving from exam-module-changes to JIRA-448-grading-framework-changes
762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e 11a18edb615809acc364116d77b5c404614b21dc Sim Zhen Quan <<EMAIL>> 1747969902 +0800	checkout: moving from JIRA-448-grading-framework-changes to main
11a18edb615809acc364116d77b5c404614b21dc 9873f23d406d7770d65aec65c428b7a1020aa6d4 Sim Zhen Quan <<EMAIL>> 1747969911 +0800	commit: Deployed to PRD
9873f23d406d7770d65aec65c428b7a1020aa6d4 762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e Sim Zhen Quan <<EMAIL>> 1747969921 +0800	checkout: moving from main to JIRA-448-grading-framework-changes
762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e 109e98ed3651978fa64d795670ca3783c0246a6d Sim Zhen Quan <<EMAIL>> 1747969930 +0800	pull: Fast-forward
109e98ed3651978fa64d795670ca3783c0246a6d 9873f23d406d7770d65aec65c428b7a1020aa6d4 Sim Zhen Quan <<EMAIL>> 1747970678 +0800	checkout: moving from JIRA-448-grading-framework-changes to main
9873f23d406d7770d65aec65c428b7a1020aa6d4 109e98ed3651978fa64d795670ca3783c0246a6d Sim Zhen Quan <<EMAIL>> 1747971702 +0800	checkout: moving from main to JIRA-448-grading-framework-changes
109e98ed3651978fa64d795670ca3783c0246a6d 635f6c7c5588db7ec12d4bd408dc974b9cdfff12 Sim Zhen Quan <<EMAIL>> 1747972062 +0800	commit: Reviewed
635f6c7c5588db7ec12d4bd408dc974b9cdfff12 ffcd384f5b2cadbb68564bf59f1471411e403a95 Sim Zhen Quan <<EMAIL>> 1747972086 +0800	commit: Updated readme
ffcd384f5b2cadbb68564bf59f1471411e403a95 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747972115 +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747972125 +0800	checkout: moving from dev to dev
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 a97c9465f7413260e12863a2bc549ebe71c80cd2 Sim Zhen Quan <<EMAIL>> 1747972129 +0800	merge JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
a97c9465f7413260e12863a2bc549ebe71c80cd2 40b6d4230ccd35b4b1f6a7471f62556461ee12e1 Sim Zhen Quan <<EMAIL>> 1747988767 +0800	commit: Deployed to dev
40b6d4230ccd35b4b1f6a7471f62556461ee12e1 497b9f71101802d93c83b776b907f440e88b2dc4 Sim Zhen Quan <<EMAIL>> 1747988777 +0800	checkout: moving from dev to feature/enrollment-import-and-save-APIs
497b9f71101802d93c83b776b907f440e88b2dc4 20d2115fca2342a079ee866c1484bca8075cf117 Sim Zhen Quan <<EMAIL>> 1747991332 +0800	commit: Reviewed
20d2115fca2342a079ee866c1484bca8075cf117 40b6d4230ccd35b4b1f6a7471f62556461ee12e1 Sim Zhen Quan <<EMAIL>> 1747991353 +0800	checkout: moving from feature/enrollment-import-and-save-APIs to dev
40b6d4230ccd35b4b1f6a7471f62556461ee12e1 d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 Sim Zhen Quan <<EMAIL>> 1747991359 +0800	merge feature/enrollment-import-and-save-APIs: Merge made by the 'ort' strategy.
d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 b452964bfedca7f7ee2cc84cb4e64fc1f36533df Sim Zhen Quan <<EMAIL>> 1747991956 +0800	checkout: moving from dev to feature/enrollment-save-imported-excel-API
b452964bfedca7f7ee2cc84cb4e64fc1f36533df 4c4acc7ab548d5dab3c052006e028289847a1062 Sim Zhen Quan <<EMAIL>> 1748109474 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to enrollment-user
4c4acc7ab548d5dab3c052006e028289847a1062 a38524901503f2d985120ada1b7e06ab73ebd682 Sim Zhen Quan <<EMAIL>> 1748109485 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
a38524901503f2d985120ada1b7e06ab73ebd682 cfe02e2c7c8193b303e8be3497a041674b3a09ee Sim Zhen Quan <<EMAIL>> 1748109896 +0800	commit: Revert changes
cfe02e2c7c8193b303e8be3497a041674b3a09ee 9873f23d406d7770d65aec65c428b7a1020aa6d4 Sim Zhen Quan <<EMAIL>> 1748110082 +0800	checkout: moving from enrollment-user to main
9873f23d406d7770d65aec65c428b7a1020aa6d4 7ec7fef357689ad9216b97a6e88aff2a48de217d Sim Zhen Quan <<EMAIL>> 1748110090 +0800	pull: Fast-forward
7ec7fef357689ad9216b97a6e88aff2a48de217d 35c5b053bb9bba723e533bae27e61ba3fc61862c Sim Zhen Quan <<EMAIL>> 1748183784 +0800	commit: Deployed to prd
35c5b053bb9bba723e533bae27e61ba3fc61862c cfe02e2c7c8193b303e8be3497a041674b3a09ee Sim Zhen Quan <<EMAIL>> 1748183794 +0800	checkout: moving from main to enrollment-user
cfe02e2c7c8193b303e8be3497a041674b3a09ee ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 Sim Zhen Quan <<EMAIL>> 1748188131 +0800	commit: Reviewed
ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 b452964bfedca7f7ee2cc84cb4e64fc1f36533df Sim Zhen Quan <<EMAIL>> 1748188235 +0800	checkout: moving from enrollment-user to feature/enrollment-save-imported-excel-API
b452964bfedca7f7ee2cc84cb4e64fc1f36533df 395266aea364f7dfad6919859ac8f51861e17955 Sim Zhen Quan <<EMAIL>> 1748188240 +0800	pull: Fast-forward
395266aea364f7dfad6919859ac8f51861e17955 20d2115fca2342a079ee866c1484bca8075cf117 Sim Zhen Quan <<EMAIL>> 1748188257 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to feature/enrollment-import-and-save-APIs
20d2115fca2342a079ee866c1484bca8075cf117 b58c610ffe55b1ea23f95dc9ea687f992216d260 Sim Zhen Quan <<EMAIL>> 1748188264 +0800	merge origin/enrollment-user: Merge made by the 'ort' strategy.
b58c610ffe55b1ea23f95dc9ea687f992216d260 395266aea364f7dfad6919859ac8f51861e17955 Sim Zhen Quan <<EMAIL>> 1748188278 +0800	checkout: moving from feature/enrollment-import-and-save-APIs to feature/enrollment-save-imported-excel-API
395266aea364f7dfad6919859ac8f51861e17955 711c133b634480c25da7440571cd64c5beb636bd Sim Zhen Quan <<EMAIL>> 1748188298 +0800	merge feature/enrollment-import-and-save-APIs: Merge made by the 'ort' strategy.
711c133b634480c25da7440571cd64c5beb636bd 18798f5709324c48024dccc123016ef6961097c6 Sim Zhen Quan <<EMAIL>> 1748192152 +0800	commit: Review WIP
18798f5709324c48024dccc123016ef6961097c6 d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 Sim Zhen Quan <<EMAIL>> 1748192168 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to dev
d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 0e8ca983ff28eb1485adbcefc8c8855815962ac7 Sim Zhen Quan <<EMAIL>> 1748192172 +0800	merge feature/enrollment-save-imported-excel-API: Merge made by the 'ort' strategy.
0e8ca983ff28eb1485adbcefc8c8855815962ac7 48d7376f79053e60baedc78824ea9a0b8f056ba6 Sim Zhen Quan <<EMAIL>> 1748192492 +0800	commit: Deployed to dev
48d7376f79053e60baedc78824ea9a0b8f056ba6 35c5b053bb9bba723e533bae27e61ba3fc61862c Sim Zhen Quan <<EMAIL>> 1748192576 +0800	checkout: moving from dev to main
35c5b053bb9bba723e533bae27e61ba3fc61862c 143e26bd9e386b5fb5bd056d199f57202890c488 Sim Zhen Quan <<EMAIL>> 1748193680 +0800	commit: Added patch for reward punishment record date of sign
143e26bd9e386b5fb5bd056d199f57202890c488 48d7376f79053e60baedc78824ea9a0b8f056ba6 Sim Zhen Quan <<EMAIL>> 1748193692 +0800	checkout: moving from main to dev
48d7376f79053e60baedc78824ea9a0b8f056ba6 d265574c0a6b2d187517830e9899ed79ff0d2cc8 Sim Zhen Quan <<EMAIL>> 1748193742 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into dev
d265574c0a6b2d187517830e9899ed79ff0d2cc8 e0445ac34ff872bcbd9ab73f16c09af22ceb54bd Sim Zhen Quan <<EMAIL>> 1748193960 +0800	checkout: moving from dev to conduct-report
e0445ac34ff872bcbd9ab73f16c09af22ceb54bd da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 Sim Zhen Quan <<EMAIL>> 1748194029 +0800	merge origin/JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 Sim Zhen Quan <<EMAIL>> 1748225704 +0800	reset: moving to HEAD
da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 4c4acc7ab548d5dab3c052006e028289847a1062 Sim Zhen Quan <<EMAIL>> 1748225714 +0800	checkout: moving from conduct-report to 4c4acc7
4c4acc7ab548d5dab3c052006e028289847a1062 da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 Sim Zhen Quan <<EMAIL>> 1748226117 +0800	checkout: moving from 4c4acc7ab548d5dab3c052006e028289847a1062 to conduct-report
da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 83753fa213af0a915a870316d41413396e557ed8 Sim Zhen Quan <<EMAIL>> 1748232092 +0800	commit: Review WIP
83753fa213af0a915a870316d41413396e557ed8 ffcd384f5b2cadbb68564bf59f1471411e403a95 Sim Zhen Quan <<EMAIL>> 1748232106 +0800	checkout: moving from conduct-report to JIRA-448-grading-framework-changes
ffcd384f5b2cadbb68564bf59f1471411e403a95 e870ac3849ada8755376e0c53d032a7dbddc1199 Sim Zhen Quan <<EMAIL>> 1748235438 +0800	pull: Fast-forward
e870ac3849ada8755376e0c53d032a7dbddc1199 f326b208d630f302d9fc80da07a441eef9e62402 Sim Zhen Quan <<EMAIL>> 1748235471 +0800	commit: Exam posting check enhancement
f326b208d630f302d9fc80da07a441eef9e62402 5010d39494c4789df8d50ceaf667026cd7987a41 Sim Zhen Quan <<EMAIL>> 1748235498 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
5010d39494c4789df8d50ceaf667026cd7987a41 5703e9cb3a48aa0f57c839d1ce7fdebb4642464c Sim Zhen Quan <<EMAIL>> 1748236778 +0800	commit: Reviewed
5703e9cb3a48aa0f57c839d1ce7fdebb4642464c d265574c0a6b2d187517830e9899ed79ff0d2cc8 Sim Zhen Quan <<EMAIL>> 1748236961 +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
d265574c0a6b2d187517830e9899ed79ff0d2cc8 a99916e0208b837a6de64b7fea83e7b98ed68448 Sim Zhen Quan <<EMAIL>> 1748236971 +0800	merge JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
a99916e0208b837a6de64b7fea83e7b98ed68448 5703e9cb3a48aa0f57c839d1ce7fdebb4642464c Sim Zhen Quan <<EMAIL>> 1748244280 +0800	checkout: moving from dev to JIRA-448-grading-framework-changes
5703e9cb3a48aa0f57c839d1ce7fdebb4642464c b69166b9727b67854f4196c1c193f48bd5e05d8e Sim Zhen Quan <<EMAIL>> 1748244290 +0800	commit: Reviewed
b69166b9727b67854f4196c1c193f48bd5e05d8e 9299376d944ae9634734a7809ed736739ed63e59 Sim Zhen Quan <<EMAIL>> 1748246914 +0800	commit: Bug fix
9299376d944ae9634734a7809ed736739ed63e59 2d66131e3f9e2e5bd1ea8926e825145b71188228 Sim Zhen Quan <<EMAIL>> 1748248023 +0800	checkout: moving from JIRA-448-grading-framework-changes to exam-module-changes
2d66131e3f9e2e5bd1ea8926e825145b71188228 757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f Sim Zhen Quan <<EMAIL>> 1748248030 +0800	pull: Fast-forward
757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f 757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f Sim Zhen Quan <<EMAIL>> 1748248032 +0800	checkout: moving from exam-module-changes to patch-elective-courses
757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f 83753fa213af0a915a870316d41413396e557ed8 Sim Zhen Quan <<EMAIL>> 1748250289 +0800	checkout: moving from patch-elective-courses to conduct-report
83753fa213af0a915a870316d41413396e557ed8 4606f2f5c4bd96356131e7751d611599067fad50 Sim Zhen Quan <<EMAIL>> 1748250300 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
4606f2f5c4bd96356131e7751d611599067fad50 4606f2f5c4bd96356131e7751d611599067fad50 Sim Zhen Quan <<EMAIL>> 1748250474 +0800	checkout: moving from conduct-report to conduct-report
4606f2f5c4bd96356131e7751d611599067fad50 710b0a5aed67fa9b81b067e02912ab35e453ef8b Sim Zhen Quan <<EMAIL>> 1748254295 +0800	commit: Review WIP
710b0a5aed67fa9b81b067e02912ab35e453ef8b a99916e0208b837a6de64b7fea83e7b98ed68448 Sim Zhen Quan <<EMAIL>> 1748254351 +0800	checkout: moving from conduct-report to dev
a99916e0208b837a6de64b7fea83e7b98ed68448 fe40d42f014e709fd09b77de15fd85eb348e310c Sim Zhen Quan <<EMAIL>> 1748254402 +0800	commit (merge): Merge branch 'conduct-report' into dev
fe40d42f014e709fd09b77de15fd85eb348e310c 6c30098d4b2d78eece31ec6725ef5fd0387f90ce Sim Zhen Quan <<EMAIL>> 1748258592 +0800	commit: Deployed to DEV
6c30098d4b2d78eece31ec6725ef5fd0387f90ce 757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f Sim Zhen Quan <<EMAIL>> 1748258599 +0800	checkout: moving from dev to exam-module-changes
757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f 569d4d903196b4ccfde995446792d9eb087dde63 Sim Zhen Quan <<EMAIL>> 1748269258 +0800	commit: Added patch for elective subjects
569d4d903196b4ccfde995446792d9eb087dde63 4a2d125e5a8326ea922c698da1ab643c2712f405 Sim Zhen Quan <<EMAIL>> 1748269273 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
4a2d125e5a8326ea922c698da1ab643c2712f405 6c30098d4b2d78eece31ec6725ef5fd0387f90ce Sim Zhen Quan <<EMAIL>> 1748269284 +0800	checkout: moving from exam-module-changes to dev
6c30098d4b2d78eece31ec6725ef5fd0387f90ce 23a639ce366b3da32de0fb6513fba0e306a28dbc Sim Zhen Quan <<EMAIL>> 1748269296 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
23a639ce366b3da32de0fb6513fba0e306a28dbc 23a639ce366b3da32de0fb6513fba0e306a28dbc Sim Zhen Quan <<EMAIL>> 1748272361 +0800	reset: moving to HEAD
23a639ce366b3da32de0fb6513fba0e306a28dbc 4a2d125e5a8326ea922c698da1ab643c2712f405 Sim Zhen Quan <<EMAIL>> 1748272362 +0800	checkout: moving from dev to exam-module-changes
4a2d125e5a8326ea922c698da1ab643c2712f405 fc45cfb094b051303ec13559c80c4ad6237985e1 Sim Zhen Quan <<EMAIL>> 1748272402 +0800	commit: Update permissions
fc45cfb094b051303ec13559c80c4ad6237985e1 a78234d3c3b3823bf0680650769dd000393b63e6 Sim Zhen Quan <<EMAIL>> 1748274298 +0800	commit: Added numprocs
a78234d3c3b3823bf0680650769dd000393b63e6 710b0a5aed67fa9b81b067e02912ab35e453ef8b Sim Zhen Quan <<EMAIL>> 1748274307 +0800	checkout: moving from exam-module-changes to conduct-report
710b0a5aed67fa9b81b067e02912ab35e453ef8b b9605fc46a393ddd9034f078565d49245c7df5a3 Sim Zhen Quan <<EMAIL>> 1748274474 +0800	commit: Added patch exam semester settings
b9605fc46a393ddd9034f078565d49245c7df5a3 ac13a133fce88880be3170d50147de6415818ba5 Sim Zhen Quan <<EMAIL>> 1748274493 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
ac13a133fce88880be3170d50147de6415818ba5 15e6324b641b5a13128dde28be393e77b0bde43d Sim Zhen Quan <<EMAIL>> 1748274832 +0800	pull: Fast-forward
15e6324b641b5a13128dde28be393e77b0bde43d c5fd549a32e2710347ab473883d66461c629d7d5 Sim Zhen Quan <<EMAIL>> 1748274854 +0800	commit: Updated README.md
c5fd549a32e2710347ab473883d66461c629d7d5 23a639ce366b3da32de0fb6513fba0e306a28dbc Sim Zhen Quan <<EMAIL>> 1748274876 +0800	checkout: moving from conduct-report to dev
23a639ce366b3da32de0fb6513fba0e306a28dbc 3414571d9b5146cb346b556ed6b96784e2c906db Sim Zhen Quan <<EMAIL>> 1748274881 +0800	merge conduct-report: Merge made by the 'ort' strategy.
3414571d9b5146cb346b556ed6b96784e2c906db a78234d3c3b3823bf0680650769dd000393b63e6 Sim Zhen Quan <<EMAIL>> 1748276171 +0800	checkout: moving from dev to exam-module-changes
a78234d3c3b3823bf0680650769dd000393b63e6 7048764b7701f7dd9578a501bd0be3fcd9b853d9 Sim Zhen Quan <<EMAIL>> 1748276177 +0800	pull: Fast-forward
7048764b7701f7dd9578a501bd0be3fcd9b853d9 f197c309692b190d38b46f95759ed1f5be27342f Sim Zhen Quan <<EMAIL>> 1748276239 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into exam-module-changes
f197c309692b190d38b46f95759ed1f5be27342f 51148f092a51484dc2ff11515a91bab8a7e7917d Sim Zhen Quan <<EMAIL>> 1748276284 +0800	checkout: moving from exam-module-changes to fix-wallet-trx-ref-no-unique
51148f092a51484dc2ff11515a91bab8a7e7917d 2c5c3c0f52109277f21c495114d3d3fb8b630c4b Sim Zhen Quan <<EMAIL>> 1748276291 +0800	merge origin/main: Merge made by the 'ort' strategy.
2c5c3c0f52109277f21c495114d3d3fb8b630c4b 143e26bd9e386b5fb5bd056d199f57202890c488 Sim Zhen Quan <<EMAIL>> 1748276548 +0800	checkout: moving from fix-wallet-trx-ref-no-unique to main
143e26bd9e386b5fb5bd056d199f57202890c488 7cc66fe79d7ff0bcbf330a1680a22efa420c445c Sim Zhen Quan <<EMAIL>> 1748276558 +0800	pull: Fast-forward
7cc66fe79d7ff0bcbf330a1680a22efa420c445c 73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb Sim Zhen Quan <<EMAIL>> 1748279942 +0800	commit: Deployed to prd
73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb b58c610ffe55b1ea23f95dc9ea687f992216d260 Sim Zhen Quan <<EMAIL>> 1748310103 +0800	checkout: moving from main to feature/enrollment-import-and-save-APIs
b58c610ffe55b1ea23f95dc9ea687f992216d260 18798f5709324c48024dccc123016ef6961097c6 Sim Zhen Quan <<EMAIL>> 1748310137 +0800	checkout: moving from feature/enrollment-import-and-save-APIs to feature/enrollment-save-imported-excel-API
18798f5709324c48024dccc123016ef6961097c6 874e81726ef1c5968f9973614072aa7c0a7980ff Sim Zhen Quan <<EMAIL>> 1748310148 +0800	pull: Fast-forward
874e81726ef1c5968f9973614072aa7c0a7980ff 874e81726ef1c5968f9973614072aa7c0a7980ff Sim Zhen Quan <<EMAIL>> 1748310150 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to feature/enrollment-save-imported-excel-API
874e81726ef1c5968f9973614072aa7c0a7980ff 31107403881f89ede7aaa28657a0790a6631ac71 Sim Zhen Quan <<EMAIL>> 1748312311 +0800	commit: Reviewed
31107403881f89ede7aaa28657a0790a6631ac71 ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 Sim Zhen Quan <<EMAIL>> 1748312379 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to enrollment-user
ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 df75ebc8fcde4e5e8cba98c7cbae1c15231a8516 Sim Zhen Quan <<EMAIL>> 1748312385 +0800	pull: Fast-forward
df75ebc8fcde4e5e8cba98c7cbae1c15231a8516 c4f9ea754ab63ef141b87778044318a535618701 Sim Zhen Quan <<EMAIL>> 1748313019 +0800	checkout: moving from enrollment-user to feature/enrollment-setting-fee
c4f9ea754ab63ef141b87778044318a535618701 b9582e82310cd10765ee1438cc64089e4da4081d Sim Zhen Quan <<EMAIL>> 1748313037 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
b9582e82310cd10765ee1438cc64089e4da4081d 3630491947bc7dcee006e555c8af6825da2a97af Sim Zhen Quan <<EMAIL>> 1748317035 +0800	commit: Reviewed
3630491947bc7dcee006e555c8af6825da2a97af 3414571d9b5146cb346b556ed6b96784e2c906db Sim Zhen Quan <<EMAIL>> 1748317086 +0800	checkout: moving from feature/enrollment-setting-fee to dev
3414571d9b5146cb346b556ed6b96784e2c906db c42f3f28df9926354cc606e70ebdb872a717bd22 Sim Zhen Quan <<EMAIL>> 1748317093 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
c42f3f28df9926354cc606e70ebdb872a717bd22 d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> 1748324439 +0800	commit: Deployed to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb Sim Zhen Quan <<EMAIL>> 1748324465 +0800	checkout: moving from dev to main
73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb 645ec3379f9b4756f514445330ed938f25b04fa2 Sim Zhen Quan <<EMAIL>> 1748327702 +0800	commit: Bug fix
645ec3379f9b4756f514445330ed938f25b04fa2 ba06a6f4ee9f9a134b1bddbb6fcd8028b7143a03 Sim Zhen Quan <<EMAIL>> 1748327797 +0800	checkout: moving from main to feature/function-to-determine-fees
ba06a6f4ee9f9a134b1bddbb6fcd8028b7143a03 ee70b18226006ca7e945dab9fea1174bffa58512 Sim Zhen Quan <<EMAIL>> 1748327952 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/function-to-determine-fees
ee70b18226006ca7e945dab9fea1174bffa58512 645ec3379f9b4756f514445330ed938f25b04fa2 Sim Zhen Quan <<EMAIL>> 1748330457 +0800	checkout: moving from feature/function-to-determine-fees to main
645ec3379f9b4756f514445330ed938f25b04fa2 8e3a834e1638dc286a3df46d786c8279ac8ff807 Sim Zhen Quan <<EMAIL>> 1748330488 +0800	checkout: moving from main to enrollment-v2
8e3a834e1638dc286a3df46d786c8279ac8ff807 79144436a7dc52ded6e85cf936043147dad955fb Sim Zhen Quan <<EMAIL>> 1748330494 +0800	pull: Fast-forward
79144436a7dc52ded6e85cf936043147dad955fb ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 Sim Zhen Quan <<EMAIL>> 1748330495 +0800	merge origin/main: Merge made by the 'ort' strategy.
ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 ee70b18226006ca7e945dab9fea1174bffa58512 Sim Zhen Quan <<EMAIL>> 1748330518 +0800	checkout: moving from enrollment-v2 to feature/function-to-determine-fees
ee70b18226006ca7e945dab9fea1174bffa58512 160c54b94011b2faf886cf6ef5f9c6c08ec6921b Sim Zhen Quan <<EMAIL>> 1748330528 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
160c54b94011b2faf886cf6ef5f9c6c08ec6921b 03b6b3cc4fe8506c59ee20b1746fdfd5e4fbe205 Sim Zhen Quan <<EMAIL>> 1748332011 +0800	commit: Reviewed
03b6b3cc4fe8506c59ee20b1746fdfd5e4fbe205 d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> 1748332454 +0800	checkout: moving from feature/function-to-determine-fees to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 0640fb965085ab6950978bf3b0ec76ea365b67ab Sim Zhen Quan <<EMAIL>> 1748332633 +0800	checkout: moving from dev to fix/student-mark-deduction-report
0640fb965085ab6950978bf3b0ec76ea365b67ab 0f4732a4b61b8d8ef01b740230597bc17376d44d Sim Zhen Quan <<EMAIL>> 1748332780 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into fix/student-mark-deduction-report
0f4732a4b61b8d8ef01b740230597bc17376d44d e423ba0a3dbd95c421652bd075124b4c49bcb3d3 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Bug fix
e423ba0a3dbd95c421652bd075124b4c49bcb3d3 d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/student-mark-deduction-report to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 6c29e9e774b831817dccbae5069b439ad583aba7 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to report/hostel-report-saving-account
6c29e9e774b831817dccbae5069b439ad583aba7 b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Bug fix
b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e f5cf00ece1af5e4599205418ee4e1d454213036b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to feature/enrollment-make-payment
f5cf00ece1af5e4599205418ee4e1d454213036b f5a470366e46123e9c547e0aefedf065f218d762 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-make-payment
f5a470366e46123e9c547e0aefedf065f218d762 0e83fa3c59b505d2b514ef0625083c41e0f14fa7 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Review WIP
0e83fa3c59b505d2b514ef0625083c41e0f14fa7 645ec3379f9b4756f514445330ed938f25b04fa2 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/enrollment-make-payment to main
645ec3379f9b4756f514445330ed938f25b04fa2 281e528dd1026b9906ecdd280cb1b7deb8611671 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
281e528dd1026b9906ecdd280cb1b7deb8611671 2e077008480ece690a1aa18259a90709d3b9f47d Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Test case fix
2e077008480ece690a1aa18259a90709d3b9f47d dda13ce2f01c1c42db4eb12942a19ee34c55ed47 Sim Zhen Quan <<EMAIL>> *********0 +0800	commit: Deployed to prd
dda13ce2f01c1c42db4eb12942a19ee34c55ed47 0e83fa3c59b505d2b514ef0625083c41e0f14fa7 Sim Zhen Quan <<EMAIL>> 1748399328 +0800	checkout: moving from main to feature/enrollment-make-payment
0e83fa3c59b505d2b514ef0625083c41e0f14fa7 ccab57ccf2dfdd2dc1151f0617affb175355341b Sim Zhen Quan <<EMAIL>> 1748402181 +0800	commit: Review WIP
ccab57ccf2dfdd2dc1151f0617affb175355341b a4741765aa8ef98672e7ce03fca5abc5ca6d8448 Sim Zhen Quan <<EMAIL>> 1748402189 +0800	checkout: moving from feature/enrollment-make-payment to exam-module-changes-v2
a4741765aa8ef98672e7ce03fca5abc5ca6d8448 157c1b5f697b6dcaa04de948bb08e38c980ccdbe Sim Zhen Quan <<EMAIL>> 1748402200 +0800	merge origin/main: Merge made by the 'ort' strategy.
157c1b5f697b6dcaa04de948bb08e38c980ccdbe 157c1b5f697b6dcaa04de948bb08e38c980ccdbe Sim Zhen Quan <<EMAIL>> 1748404226 +0800	checkout: moving from exam-module-changes-v2 to exam-module-changes-v2
157c1b5f697b6dcaa04de948bb08e38c980ccdbe d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> 1748404248 +0800	checkout: moving from exam-module-changes-v2 to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 3a72f500626fd0eee58a90ab5de2f7952ea20c23 Sim Zhen Quan <<EMAIL>> 1748404255 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
3a72f500626fd0eee58a90ab5de2f7952ea20c23 d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 Sim Zhen Quan <<EMAIL>> 1748423542 +0800	commit: Deployed to dev
d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 ccab57ccf2dfdd2dc1151f0617affb175355341b Sim Zhen Quan <<EMAIL>> 1748423552 +0800	checkout: moving from dev to feature/enrollment-make-payment
ccab57ccf2dfdd2dc1151f0617affb175355341b ac3616781e229cf8966cf290324c78584e912457 Sim Zhen Quan <<EMAIL>> 1748423560 +0800	pull: Fast-forward
ac3616781e229cf8966cf290324c78584e912457 6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f Sim Zhen Quan <<EMAIL>> 1748452298 +0800	commit: Reviewed
6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 Sim Zhen Quan <<EMAIL>> 1748452305 +0800	checkout: moving from feature/enrollment-make-payment to enrollment-v2
ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 c37fd465e6da8c6454b6a37aaf810afa70d42db3 Sim Zhen Quan <<EMAIL>> 1748452311 +0800	pull: Fast-forward
c37fd465e6da8c6454b6a37aaf810afa70d42db3 62f461e72d023531472aad0c73ea999df975f154 Sim Zhen Quan <<EMAIL>> 1748452311 +0800	merge origin/main: Merge made by the 'ort' strategy.
62f461e72d023531472aad0c73ea999df975f154 6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f Sim Zhen Quan <<EMAIL>> 1748452325 +0800	checkout: moving from enrollment-v2 to feature/enrollment-make-payment
6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f bc7d0de8c42f708b6e425a65c3cf9cd92561d752 Sim Zhen Quan <<EMAIL>> 1748452393 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
bc7d0de8c42f708b6e425a65c3cf9cd92561d752 a056772fa047e799a8fc988b5e809209d82ffa64 Sim Zhen Quan <<EMAIL>> 1748484107 +0800	checkout: moving from feature/enrollment-make-payment to feature/enrollment-update-APIs
a056772fa047e799a8fc988b5e809209d82ffa64 877e4289691ebcf453c3dcdd6caa0547ed4d5d56 Sim Zhen Quan <<EMAIL>> 1748484114 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
877e4289691ebcf453c3dcdd6caa0547ed4d5d56 877e4289691ebcf453c3dcdd6caa0547ed4d5d56 Sim Zhen Quan <<EMAIL>> 1748485306 +0800	reset: moving to HEAD
877e4289691ebcf453c3dcdd6caa0547ed4d5d56 373502496b3a8feef95995b4dd1ab833952e2798 Sim Zhen Quan <<EMAIL>> 1748485308 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
373502496b3a8feef95995b4dd1ab833952e2798 a5b9cb0913769db9c9f3b164307519c75c155b90 Sim Zhen Quan <<EMAIL>> 1748485350 +0800	commit: Review WIP
a5b9cb0913769db9c9f3b164307519c75c155b90 baf28720f8b2bf2a74ebd514824af5679d1fade4 Sim Zhen Quan <<EMAIL>> 1748486395 +0800	commit: Review WIP
baf28720f8b2bf2a74ebd514824af5679d1fade4 8c39ea8747f816d2cc9df3e480a6cdc4e24cbf80 Sim Zhen Quan <<EMAIL>> 1748491006 +0800	checkout: moving from feature/enrollment-update-APIs to script-remove-elective-subject-from-primary-class
8c39ea8747f816d2cc9df3e480a6cdc4e24cbf80 7a2b73f7acc20ae42b3036d1dae63986441b545e Sim Zhen Quan <<EMAIL>> 1748491014 +0800	merge origin/main: Merge made by the 'ort' strategy.
7a2b73f7acc20ae42b3036d1dae63986441b545e ea060032ed951dd23f1eeec8733dfde75614a54c Sim Zhen Quan <<EMAIL>> 1748491203 +0800	commit: Reviewed
ea060032ed951dd23f1eeec8733dfde75614a54c d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 Sim Zhen Quan <<EMAIL>> 1748491223 +0800	checkout: moving from script-remove-elective-subject-from-primary-class to dev
d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 617d4b2c3576122a591a313be0bf4c09f4c1aaf3 Sim Zhen Quan <<EMAIL>> 1748491233 +0800	merge origin/main: Merge made by the 'ort' strategy.
617d4b2c3576122a591a313be0bf4c09f4c1aaf3 826b1588f7f205a1d794d6223e5f6f662cf51af8 Sim Zhen Quan <<EMAIL>> 1748491257 +0800	merge origin/exam-module-changes-v2: Merge made by the 'ort' strategy.
826b1588f7f205a1d794d6223e5f6f662cf51af8 826b1588f7f205a1d794d6223e5f6f662cf51af8 Sim Zhen Quan <<EMAIL>> 1748492148 +0800	reset: moving to HEAD
826b1588f7f205a1d794d6223e5f6f662cf51af8 dda13ce2f01c1c42db4eb12942a19ee34c55ed47 Sim Zhen Quan <<EMAIL>> 1748492151 +0800	checkout: moving from dev to main
dda13ce2f01c1c42db4eb12942a19ee34c55ed47 43b71abc11a33490f84b755c65323e6550fae4ed Sim Zhen Quan <<EMAIL>> 1748492155 +0800	pull: Fast-forward
43b71abc11a33490f84b755c65323e6550fae4ed 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748492168 +0800	commit: Corrected translations
91a7dd5eaad921da64cd24a06941424b480c604f 22ef14452f35905e4c388acf0362d52ca9eaef0b Sim Zhen Quan <<EMAIL>> 1748493144 +0800	checkout: moving from main to fix/update-validation-when-create-billing-doc
22ef14452f35905e4c388acf0362d52ca9eaef0b 80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 Sim Zhen Quan <<EMAIL>> 1748493151 +0800	merge origin/main: Merge made by the 'ort' strategy.
80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 baf28720f8b2bf2a74ebd514824af5679d1fade4 Sim Zhen Quan <<EMAIL>> 1748500554 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to feature/enrollment-update-APIs
baf28720f8b2bf2a74ebd514824af5679d1fade4 da61737008ddd7047f1267136d50067610b6b2e8 Sim Zhen Quan <<EMAIL>> 1748500560 +0800	pull: Fast-forward
da61737008ddd7047f1267136d50067610b6b2e8 0d2746d7bde15d12d0cb7b2365bd08339d51b230 Sim Zhen Quan <<EMAIL>> 1748501495 +0800	commit: Reviewed
0d2746d7bde15d12d0cb7b2365bd08339d51b230 ced1099535f15642cf55a201ef1705ec31275fad Sim Zhen Quan <<EMAIL>> 1748501762 +0800	commit: Reviewed
ced1099535f15642cf55a201ef1705ec31275fad 62f461e72d023531472aad0c73ea999df975f154 Sim Zhen Quan <<EMAIL>> 1748501855 +0800	checkout: moving from feature/enrollment-update-APIs to enrollment-v2
62f461e72d023531472aad0c73ea999df975f154 77e91b2f7c435ad057144a70e765f759812527ba Sim Zhen Quan <<EMAIL>> 1748501861 +0800	pull: Fast-forward
77e91b2f7c435ad057144a70e765f759812527ba 4e903f91a461b99ffe644035cebe4844296cacef Sim Zhen Quan <<EMAIL>> 1748501865 +0800	merge origin/main: Merge made by the 'ort' strategy.
4e903f91a461b99ffe644035cebe4844296cacef 826b1588f7f205a1d794d6223e5f6f662cf51af8 Sim Zhen Quan <<EMAIL>> 1748501894 +0800	checkout: moving from enrollment-v2 to dev
826b1588f7f205a1d794d6223e5f6f662cf51af8 724b4b744a4de16f516604da9fcb380035b0b455 Sim Zhen Quan <<EMAIL>> 1748501899 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
724b4b744a4de16f516604da9fcb380035b0b455 724b4b744a4de16f516604da9fcb380035b0b455 Sim Zhen Quan <<EMAIL>> 1748502811 +0800	reset: moving to HEAD
724b4b744a4de16f516604da9fcb380035b0b455 4e903f91a461b99ffe644035cebe4844296cacef Sim Zhen Quan <<EMAIL>> 1748502815 +0800	checkout: moving from dev to enrollment-v2
4e903f91a461b99ffe644035cebe4844296cacef f28c1f368300854c07ac9a420274b60de19fe364 Sim Zhen Quan <<EMAIL>> 1748502902 +0800	commit: Added master data routes
f28c1f368300854c07ac9a420274b60de19fe364 724b4b744a4de16f516604da9fcb380035b0b455 Sim Zhen Quan <<EMAIL>> 1748502928 +0800	checkout: moving from enrollment-v2 to dev
724b4b744a4de16f516604da9fcb380035b0b455 c78f370d25277add07b366d87cc0cffc61522af6 Sim Zhen Quan <<EMAIL>> 1748502935 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
c78f370d25277add07b366d87cc0cffc61522af6 7bf4d65c645dd877b048b2966a1ed73dafdff461 Sim Zhen Quan <<EMAIL>> 1748507658 +0800	commit: Deployed to dev
7bf4d65c645dd877b048b2966a1ed73dafdff461 a54d138cfac1f3ff48c8e274180b5fd61fa3d44c Sim Zhen Quan <<EMAIL>> 1748507667 +0800	commit: Updated api route
a54d138cfac1f3ff48c8e274180b5fd61fa3d44c f28c1f368300854c07ac9a420274b60de19fe364 Sim Zhen Quan <<EMAIL>> 1748507676 +0800	checkout: moving from dev to enrollment-v2
f28c1f368300854c07ac9a420274b60de19fe364 bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748507720 +0800	cherry-pick: Updated api route
bc0c7d24185b5a70058fb423681586324ebd2a9d a54d138cfac1f3ff48c8e274180b5fd61fa3d44c Sim Zhen Quan <<EMAIL>> 1748508278 +0800	checkout: moving from enrollment-v2 to dev
a54d138cfac1f3ff48c8e274180b5fd61fa3d44c b0e672e8136503902ba74f44b29868c5c60607c4 Sim Zhen Quan <<EMAIL>> 1748508284 +0800	pull: Fast-forward
b0e672e8136503902ba74f44b29868c5c60607c4 4a3a7921de3d9623ba6b12e673ee1176c1ae2599 Sim Zhen Quan <<EMAIL>> 1748508858 +0800	commit: Deployed to dev
4a3a7921de3d9623ba6b12e673ee1176c1ae2599 bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748570987 +0800	checkout: moving from dev to enrollment-v2
bc0c7d24185b5a70058fb423681586324ebd2a9d bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748570996 +0800	checkout: moving from enrollment-v2 to enrollment-enhancements
bc0c7d24185b5a70058fb423681586324ebd2a9d e849b832797d579a1de1742d60296ddb21204e79 Sim Zhen Quan <<EMAIL>> 1748572624 +0800	commit: Updated enrollment session
e849b832797d579a1de1742d60296ddb21204e79 8252973b39acd8d125bb1b55bd39c327f17c0b24 Sim Zhen Quan <<EMAIL>> 1748581081 +0800	commit: Update code to cater if no need to pay for enrollment fees
8252973b39acd8d125bb1b55bd39c327f17c0b24 da3c1526c648a371985c2a8d55195cded1c06931 Sim Zhen Quan <<EMAIL>> 1748834100 +0800	commit: Files cleanup
da3c1526c648a371985c2a8d55195cded1c06931 63ee1fb2f70393155b02be4049a5e680b9882a52 Sim Zhen Quan <<EMAIL>> 1748835027 +0800	commit: Files cleanup
63ee1fb2f70393155b02be4049a5e680b9882a52 148360da5b2ce54347f7b14fe5569f1f5e6f4571 Sim Zhen Quan <<EMAIL>> 1748836370 +0800	checkout: moving from enrollment-enhancements to feature/post-payment-enrollment-import
148360da5b2ce54347f7b14fe5569f1f5e6f4571 130817759d2de6cac757693fcb6a4a7891a6fd9e Sim Zhen Quan <<EMAIL>> 1748836377 +0800	pull: Fast-forward
130817759d2de6cac757693fcb6a4a7891a6fd9e 8eb13506a457e8e6ce3e04b3ff2b369506b06acb Sim Zhen Quan <<EMAIL>> 1748836435 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/post-payment-enrollment-import
8eb13506a457e8e6ce3e04b3ff2b369506b06acb 841c188290d1fa9e5f669168df056043a2ed0959 Sim Zhen Quan <<EMAIL>> 1748839802 +0800	commit: Updated code to allow excel upload without exam slip number
841c188290d1fa9e5f669168df056043a2ed0959 5c8680f1e32d1b604de8dae1ec61677b194ef59a Sim Zhen Quan <<EMAIL>> 1748841548 +0800	commit: - Added auto create guardian when import
5c8680f1e32d1b604de8dae1ec61677b194ef59a 4a3a7921de3d9623ba6b12e673ee1176c1ae2599 Sim Zhen Quan <<EMAIL>> 1748841564 +0800	checkout: moving from feature/post-payment-enrollment-import to dev
4a3a7921de3d9623ba6b12e673ee1176c1ae2599 4b367509a6541221febb939ec0d2f9854f9abfde Sim Zhen Quan <<EMAIL>> 1748841573 +0800	merge origin/feature/post-payment-enrollment-import: Merge made by the 'ort' strategy.
4b367509a6541221febb939ec0d2f9854f9abfde 5c8680f1e32d1b604de8dae1ec61677b194ef59a Sim Zhen Quan <<EMAIL>> 1748849391 +0800	checkout: moving from dev to feature/post-payment-enrollment-import
5c8680f1e32d1b604de8dae1ec61677b194ef59a e72b72ab62523082abc8a6ca5256577896b6a938 Sim Zhen Quan <<EMAIL>> 1748849564 +0800	commit: Removed student number
e72b72ab62523082abc8a6ca5256577896b6a938 86409d49c6e4274416f8c5a8902c03338c35f805 Sim Zhen Quan <<EMAIL>> 1748852386 +0800	commit: Reviewed
86409d49c6e4274416f8c5a8902c03338c35f805 4b367509a6541221febb939ec0d2f9854f9abfde Sim Zhen Quan <<EMAIL>> 1748852487 +0800	checkout: moving from feature/post-payment-enrollment-import to dev
4b367509a6541221febb939ec0d2f9854f9abfde 358b592db4cd8d6278248ae0a0fb205ce07766a7 Sim Zhen Quan <<EMAIL>> 1748852493 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
358b592db4cd8d6278248ae0a0fb205ce07766a7 bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748852953 +0800	checkout: moving from dev to enrollment-v2
bc0c7d24185b5a70058fb423681586324ebd2a9d e7deb8bfc29b90f85d1193b99fcf947a91e6a338 Sim Zhen Quan <<EMAIL>> 1748852959 +0800	pull: Fast-forward
e7deb8bfc29b90f85d1193b99fcf947a91e6a338 f8d66016cc8694fed618b657f19312c2ee486ea7 Sim Zhen Quan <<EMAIL>> 1748854375 +0800	commit: Added redirect_url to enrollment
f8d66016cc8694fed618b657f19312c2ee486ea7 358b592db4cd8d6278248ae0a0fb205ce07766a7 Sim Zhen Quan <<EMAIL>> 1748854389 +0800	checkout: moving from enrollment-v2 to dev
358b592db4cd8d6278248ae0a0fb205ce07766a7 80aade318b9526815a0f22bb088055288b30fc74 Sim Zhen Quan <<EMAIL>> 1748854405 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
80aade318b9526815a0f22bb088055288b30fc74 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857062 +0800	checkout: moving from dev to main
91a7dd5eaad921da64cd24a06941424b480c604f 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857075 +0800	checkout: moving from main to billing-doc-report-enhancement
91a7dd5eaad921da64cd24a06941424b480c604f f8d66016cc8694fed618b657f19312c2ee486ea7 Sim Zhen Quan <<EMAIL>> 1748857104 +0800	checkout: moving from billing-doc-report-enhancement to enrollment-v2
f8d66016cc8694fed618b657f19312c2ee486ea7 81734f64bc6f60c6a716f89ead22af47f6abbf58 Sim Zhen Quan <<EMAIL>> 1748857527 +0800	commit: Minor enhancements
81734f64bc6f60c6a716f89ead22af47f6abbf58 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857535 +0800	checkout: moving from enrollment-v2 to billing-doc-report-enhancement
91a7dd5eaad921da64cd24a06941424b480c604f 81734f64bc6f60c6a716f89ead22af47f6abbf58 Sim Zhen Quan <<EMAIL>> 1748857736 +0800	checkout: moving from billing-doc-report-enhancement to enrollment-v2
81734f64bc6f60c6a716f89ead22af47f6abbf58 2c520c68677edecb3f6d3b502465951a07884b53 Sim Zhen Quan <<EMAIL>> 1748857812 +0800	commit: Minor enhancements
2c520c68677edecb3f6d3b502465951a07884b53 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857817 +0800	checkout: moving from enrollment-v2 to billing-doc-report-enhancement
91a7dd5eaad921da64cd24a06941424b480c604f 482e3e6454b5648d9b0900e9f82160f32576d3f0 Sim Zhen Quan <<EMAIL>> 1748883206 +0800	commit: Excel enhancements
482e3e6454b5648d9b0900e9f82160f32576d3f0 80aade318b9526815a0f22bb088055288b30fc74 Sim Zhen Quan <<EMAIL>> 1748920255 +0800	checkout: moving from billing-doc-report-enhancement to dev
80aade318b9526815a0f22bb088055288b30fc74 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748938224 +0800	checkout: moving from dev to main
91a7dd5eaad921da64cd24a06941424b480c604f 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748938234 +0800	checkout: moving from main to staging/2025-06-03
91a7dd5eaad921da64cd24a06941424b480c604f 7c65e0426842307d9a696aeb13d680f36e3f0cd7 Sim Zhen Quan <<EMAIL>> 1748939812 +0800	checkout: moving from staging/2025-06-03 to issue-186-mark-deduction-report-exclude-inactive-students
7c65e0426842307d9a696aeb13d680f36e3f0cd7 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748940319 +0800	checkout: moving from issue-186-mark-deduction-report-exclude-inactive-students to staging/2025-06-03
91a7dd5eaad921da64cd24a06941424b480c604f 80aade318b9526815a0f22bb088055288b30fc74 Sim Zhen Quan <<EMAIL>> 1748940458 +0800	checkout: moving from staging/2025-06-03 to dev
80aade318b9526815a0f22bb088055288b30fc74 fc8c90328045e4239373de52470bcb266574c5ef Sim Zhen Quan <<EMAIL>> 1748940468 +0800	merge billing-doc-report-enhancement: Merge made by the 'ort' strategy.
fc8c90328045e4239373de52470bcb266574c5ef 8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 Sim Zhen Quan <<EMAIL>> 1748964244 +0800	commit: Deployed to dev
8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748964250 +0800	checkout: moving from dev to main
91a7dd5eaad921da64cd24a06941424b480c604f 29eadf6f28a047f121b21b39bc1e37f070dbceb8 Sim Zhen Quan <<EMAIL>> 1748964257 +0800	pull: Fast-forward
29eadf6f28a047f121b21b39bc1e37f070dbceb8 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> 1748965393 +0800	commit: Deployed to prd
b256a94df313192188826a85009a7fbcd5b43b97 bf985836c3ae0d111f91af71d075ccb81913ebb5 Sim Zhen Quan <<EMAIL>> 1748965922 +0800	checkout: moving from main to issue-140-by-student-in-class-coco-and-english
bf985836c3ae0d111f91af71d075ccb81913ebb5 6a3308368438b927003c819fc960093e34f69be6 Sim Zhen Quan <<EMAIL>> 1748965928 +0800	merge origin/main: Merge made by the 'ort' strategy.
6a3308368438b927003c819fc960093e34f69be6 8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 Sim Zhen Quan <<EMAIL>> 1748966679 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 defb071d90447e6da5ac433c99215dd7dc15fa15 Sim Zhen Quan <<EMAIL>> 1748966688 +0800	merge origin/issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
defb071d90447e6da5ac433c99215dd7dc15fa15 680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b Sim Zhen Quan <<EMAIL>> 1749002776 +0800	commit: Deployed to dev
680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b f0600e777644f58eed098be0a186b1c32a389fd4 Sim Zhen Quan <<EMAIL>> 1749003055 +0800	checkout: moving from dev to feature/import-validation-changes
f0600e777644f58eed098be0a186b1c32a389fd4 6a3308368438b927003c819fc960093e34f69be6 Sim Zhen Quan <<EMAIL>> 1749005515 +0800	checkout: moving from feature/import-validation-changes to issue-140-by-student-in-class-coco-and-english
6a3308368438b927003c819fc960093e34f69be6 b67db56352b1a1770d61778f1662873143524989 Sim Zhen Quan <<EMAIL>> 1749005915 +0800	commit: Added permission
b67db56352b1a1770d61778f1662873143524989 680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b Sim Zhen Quan <<EMAIL>> 1749005932 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b 47ee8a33d641abe2a90970860c1bc509f431306c Sim Zhen Quan <<EMAIL>> 1749005937 +0800	merge issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
47ee8a33d641abe2a90970860c1bc509f431306c b67db56352b1a1770d61778f1662873143524989 Sim Zhen Quan <<EMAIL>> 1749006357 +0800	checkout: moving from dev to issue-140-by-student-in-class-coco-and-english
b67db56352b1a1770d61778f1662873143524989 27ebd33fa4440f695aa1e3f37051e8aab1997cdf Sim Zhen Quan <<EMAIL>> 1749006363 +0800	commit: Fix blade issue
27ebd33fa4440f695aa1e3f37051e8aab1997cdf 47ee8a33d641abe2a90970860c1bc509f431306c Sim Zhen Quan <<EMAIL>> 1749006396 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
47ee8a33d641abe2a90970860c1bc509f431306c 6b75b220d75484cafc53afc66cf02283d0cf4f45 Sim Zhen Quan <<EMAIL>> 1749006408 +0800	merge issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
6b75b220d75484cafc53afc66cf02283d0cf4f45 f0600e777644f58eed098be0a186b1c32a389fd4 Sim Zhen Quan <<EMAIL>> 1749006529 +0800	checkout: moving from dev to feature/import-validation-changes
f0600e777644f58eed098be0a186b1c32a389fd4 3f935ac82e1d50ada0960e1d491517c5fac49b23 Sim Zhen Quan <<EMAIL>> 1749006533 +0800	pull: Fast-forward
3f935ac82e1d50ada0960e1d491517c5fac49b23 274a99e55108aedf5f4f856f5f94b1c72d38d9fb Sim Zhen Quan <<EMAIL>> 1749006678 +0800	commit: Fix typo
274a99e55108aedf5f4f856f5f94b1c72d38d9fb 313f0cd59ecdb18725d5d207113bee1700d56929 Sim Zhen Quan <<EMAIL>> 1749006752 +0800	checkout: moving from feature/import-validation-changes to enrollment-register-student-report
313f0cd59ecdb18725d5d207113bee1700d56929 25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 Sim Zhen Quan <<EMAIL>> 1749006905 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into enrollment-register-student-report
25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 27ebd33fa4440f695aa1e3f37051e8aab1997cdf Sim Zhen Quan <<EMAIL>> 1749008678 +0800	checkout: moving from enrollment-register-student-report to issue-140-by-student-in-class-coco-and-english
27ebd33fa4440f695aa1e3f37051e8aab1997cdf 50a90045d1e4d06708e400bc12a6db614e56401b Sim Zhen Quan <<EMAIL>> 1749009664 +0800	commit: Fix report format
50a90045d1e4d06708e400bc12a6db614e56401b 25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 Sim Zhen Quan <<EMAIL>> 1749010013 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to enrollment-register-student-report
25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 f4c428f6d86b9a782b4dad15fb79f5a6f0830150 Sim Zhen Quan <<EMAIL>> 1749012553 +0800	commit: Reviewed
f4c428f6d86b9a782b4dad15fb79f5a6f0830150 f4c428f6d86b9a782b4dad15fb79f5a6f0830150 Sim Zhen Quan <<EMAIL>> 1749012771 +0800	reset: moving to HEAD
f4c428f6d86b9a782b4dad15fb79f5a6f0830150 2c520c68677edecb3f6d3b502465951a07884b53 Sim Zhen Quan <<EMAIL>> 1749012773 +0800	checkout: moving from enrollment-register-student-report to enrollment-v2
2c520c68677edecb3f6d3b502465951a07884b53 650c83688b1d8854d7e8691374028121ecabf3b3 Sim Zhen Quan <<EMAIL>> 1749012780 +0800	pull: Fast-forward
650c83688b1d8854d7e8691374028121ecabf3b3 a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 Sim Zhen Quan <<EMAIL>> 1749012808 +0800	commit: Reviewed
a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 Sim Zhen Quan <<EMAIL>> 1749012934 +0800	checkout: moving from enrollment-v2 to add-expiry-date-and-register-date
a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 1fdf5f17b19285d1b72bc4630f918aec07ae6445 Sim Zhen Quan <<EMAIL>> 1749013210 +0800	commit: Added expiry date and register date
1fdf5f17b19285d1b72bc4630f918aec07ae6445 6b75b220d75484cafc53afc66cf02283d0cf4f45 Sim Zhen Quan <<EMAIL>> 1749016677 +0800	checkout: moving from add-expiry-date-and-register-date to dev
6b75b220d75484cafc53afc66cf02283d0cf4f45 1fdf5f17b19285d1b72bc4630f918aec07ae6445 Sim Zhen Quan <<EMAIL>> 1749016830 +0800	checkout: moving from dev to add-expiry-date-and-register-date
1fdf5f17b19285d1b72bc4630f918aec07ae6445 484f5033f3756a8850d79befde70c6cdfe501e15 Sim Zhen Quan <<EMAIL>> 1749016873 +0800	commit: Added register_date and expiry_date into excel template
484f5033f3756a8850d79befde70c6cdfe501e15 687ed9b1084790f85b791ca06ebabb0072e7569e Sim Zhen Quan <<EMAIL>> 1749017266 +0800	commit: Added register_date and expiry_date into excel template
687ed9b1084790f85b791ca06ebabb0072e7569e 6b75b220d75484cafc53afc66cf02283d0cf4f45 Sim Zhen Quan <<EMAIL>> 1749017288 +0800	checkout: moving from add-expiry-date-and-register-date to dev
6b75b220d75484cafc53afc66cf02283d0cf4f45 d9a91372f2c1cb66a67ce417edd439863325547d Sim Zhen Quan <<EMAIL>> 1749017298 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
d9a91372f2c1cb66a67ce417edd439863325547d c916efd4a66d99258a0e10b0ad081c5e7f9b515c Sim Zhen Quan <<EMAIL>> 1749019105 +0800	commit: Deployed to dev
c916efd4a66d99258a0e10b0ad081c5e7f9b515c c916efd4a66d99258a0e10b0ad081c5e7f9b515c Sim Zhen Quan <<EMAIL>> 1749020244 +0800	reset: moving to HEAD
c916efd4a66d99258a0e10b0ad081c5e7f9b515c a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 Sim Zhen Quan <<EMAIL>> 1749020244 +0800	checkout: moving from dev to enrollment-v2
a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 cbdb1e374720b93c88554d590ab7b5f09973dd06 Sim Zhen Quan <<EMAIL>> 1749020251 +0800	pull: Fast-forward
cbdb1e374720b93c88554d590ab7b5f09973dd06 7ac2f04fd6a54891dd34f15638bc94a551a750b6 Sim Zhen Quan <<EMAIL>> 1749020378 +0800	commit: Fixed date issue
7ac2f04fd6a54891dd34f15638bc94a551a750b6 50a90045d1e4d06708e400bc12a6db614e56401b Sim Zhen Quan <<EMAIL>> 1749020385 +0800	checkout: moving from enrollment-v2 to issue-140-by-student-in-class-coco-and-english
50a90045d1e4d06708e400bc12a6db614e56401b e1d8a48d620e0e5664741ec0d8abda7b70e895e1 Sim Zhen Quan <<EMAIL>> 1749022471 +0800	commit: Fix report
e1d8a48d620e0e5664741ec0d8abda7b70e895e1 c916efd4a66d99258a0e10b0ad081c5e7f9b515c Sim Zhen Quan <<EMAIL>> 1749022495 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
c916efd4a66d99258a0e10b0ad081c5e7f9b515c 9df3d915091740196d03032635400f8aab605d39 Sim Zhen Quan <<EMAIL>> 1749022499 +0800	merge issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
9df3d915091740196d03032635400f8aab605d39 7ac2f04fd6a54891dd34f15638bc94a551a750b6 Sim Zhen Quan <<EMAIL>> 1749022518 +0800	checkout: moving from dev to enrollment-v2
7ac2f04fd6a54891dd34f15638bc94a551a750b6 d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a Sim Zhen Quan <<EMAIL>> 1749022715 +0800	commit: Fix EnrollmentPrePaymentImport date
d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a 9df3d915091740196d03032635400f8aab605d39 Sim Zhen Quan <<EMAIL>> 1749022724 +0800	checkout: moving from enrollment-v2 to dev
9df3d915091740196d03032635400f8aab605d39 9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 Sim Zhen Quan <<EMAIL>> 1749022724 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a Sim Zhen Quan <<EMAIL>> 1749023303 +0800	checkout: moving from dev to enrollment-v2
d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a 241bcb01229bc7edbd40d3c8d9706f8189820f28 Sim Zhen Quan <<EMAIL>> 1749023614 +0800	checkout: moving from enrollment-v2 to feature/retry-enrollment-payment-API
241bcb01229bc7edbd40d3c8d9706f8189820f28 0511b950a8c7ab5e138c20a691cb4fa4810ea22a Sim Zhen Quan <<EMAIL>> 1749023958 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/retry-enrollment-payment-API
0511b950a8c7ab5e138c20a691cb4fa4810ea22a d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a Sim Zhen Quan <<EMAIL>> 1749024052 +0800	checkout: moving from feature/retry-enrollment-payment-API to enrollment-v2
d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a d0a4529d3f89fd0b39f1f5b872c60893386c2545 Sim Zhen Quan <<EMAIL>> 1749024057 +0800	merge origin/main: Merge made by the 'ort' strategy.
d0a4529d3f89fd0b39f1f5b872c60893386c2545 0511b950a8c7ab5e138c20a691cb4fa4810ea22a Sim Zhen Quan <<EMAIL>> 1749024092 +0800	checkout: moving from enrollment-v2 to feature/retry-enrollment-payment-API
0511b950a8c7ab5e138c20a691cb4fa4810ea22a 445a95c29c91d16db3131f009c23a34c714364c1 Sim Zhen Quan <<EMAIL>> 1749024231 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/retry-enrollment-payment-API
445a95c29c91d16db3131f009c23a34c714364c1 445a95c29c91d16db3131f009c23a34c714364c1 Sim Zhen Quan <<EMAIL>> 1749027709 +0800	reset: moving to HEAD
445a95c29c91d16db3131f009c23a34c714364c1 9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 Sim Zhen Quan <<EMAIL>> 1749027710 +0800	checkout: moving from feature/retry-enrollment-payment-API to dev
9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> 1749027721 +0800	checkout: moving from dev to main
b256a94df313192188826a85009a7fbcd5b43b97 445a95c29c91d16db3131f009c23a34c714364c1 Sim Zhen Quan <<EMAIL>> 1749027735 +0800	checkout: moving from main to feature/retry-enrollment-payment-API
445a95c29c91d16db3131f009c23a34c714364c1 a182e5dfacef79b181b6fdb0b6e32f8116a5d8b9 Sim Zhen Quan <<EMAIL>> 1749028233 +0800	commit: Reviewed
a182e5dfacef79b181b6fdb0b6e32f8116a5d8b9 9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 Sim Zhen Quan <<EMAIL>> 1749028241 +0800	checkout: moving from feature/retry-enrollment-payment-API to dev
9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 dfba6b8e8bee69b06832ded1af88867d8a233cb9 Sim Zhen Quan <<EMAIL>> 1749028251 +0800	merge feature/retry-enrollment-payment-API: Merge made by the 'ort' strategy.
dfba6b8e8bee69b06832ded1af88867d8a233cb9 d0e4c8fc8fc4f51136eeddd7611c3fc20adefe9f Sim Zhen Quan <<EMAIL>> 1749031308 +0800	commit: Deployed to dev
d0e4c8fc8fc4f51136eeddd7611c3fc20adefe9f 185e8481d44a4254c0fa78699c36698369c8656a Sim Zhen Quan <<EMAIL>> 1749031347 +0800	commit: Fixes
185e8481d44a4254c0fa78699c36698369c8656a b280a63c991755f13a4665f9f00728ad5fa2f2fc Sim Zhen Quan <<EMAIL>> 1749033265 +0800	checkout: moving from dev to feature/enrollment-autocount-report
b280a63c991755f13a4665f9f00728ad5fa2f2fc 1f1b019b1da953b6238427c03ecdb760b61503c1 Sim Zhen Quan <<EMAIL>> 1749033300 +0800	merge feature/retry-enrollment-payment-API: Merge made by the 'ort' strategy.
1f1b019b1da953b6238427c03ecdb760b61503c1 cdb511b21f3a13eb863045e0ff1bf4f7504230aa Sim Zhen Quan <<EMAIL>> 1749034434 +0800	commit: Added queue for enrollment
cdb511b21f3a13eb863045e0ff1bf4f7504230aa 185e8481d44a4254c0fa78699c36698369c8656a Sim Zhen Quan <<EMAIL>> 1749034446 +0800	checkout: moving from feature/enrollment-autocount-report to dev
185e8481d44a4254c0fa78699c36698369c8656a 197ebe792107fe497e822beba41a7d0e902e2327 Sim Zhen Quan <<EMAIL>> 1749034447 +0800	merge feature/enrollment-autocount-report: Merge made by the 'ort' strategy.
197ebe792107fe497e822beba41a7d0e902e2327 a88993cbbdaa1023998aed5745d00ebaed2babec Sim Zhen Quan <<EMAIL>> 1749092729 +0800	commit: Deployed to dev
a88993cbbdaa1023998aed5745d00ebaed2babec b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to main
b256a94df313192188826a85009a7fbcd5b43b97 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to staging/2025-06-05
b256a94df313192188826a85009a7fbcd5b43b97 b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from staging/2025-06-05 to report/hostel-report-saving-account
b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e 4b36d3ab59234b09328a88b7d52838f9c17a5047 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/main' into report/hostel-report-saving-account
4b36d3ab59234b09328a88b7d52838f9c17a5047 e652ad79345b0594222b3a5a5522d6d9d4997f13 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to allow-take-class-attendance-5-mins-earlier
e652ad79345b0594222b3a5a5522d6d9d4997f13 c250154f5cc1d7b2fca1d0a932b7df7a5ade3d4a Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
c250154f5cc1d7b2fca1d0a932b7df7a5ade3d4a 43cf8fe3511d07b7f3f45fb7c8ea9715eb43489d Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
43cf8fe3511d07b7f3f45fb7c8ea9715eb43489d a88993cbbdaa1023998aed5745d00ebaed2babec Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from allow-take-class-attendance-5-mins-earlier to dev
a88993cbbdaa1023998aed5745d00ebaed2babec 510c1cc4fb1115fe1442059608f0999065f2f363 Sim Zhen Quan <<EMAIL>> ********** +0800	merge allow-take-class-attendance-5-mins-earlier: Merge made by the 'ort' strategy.
510c1cc4fb1115fe1442059608f0999065f2f363 80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to fix/update-validation-when-create-billing-doc
80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 cb379a17b4ec9a8261e39123d721226d62b6d0d9 Sim Zhen Quan <<EMAIL>> 1749093540 +0800	merge origin/main: Merge made by the 'ort' strategy.
cb379a17b4ec9a8261e39123d721226d62b6d0d9 d00d80383e1784ea180cc44a2751f8bd280fcb8f Sim Zhen Quan <<EMAIL>> 1749094825 +0800	commit: Reviewed
d00d80383e1784ea180cc44a2751f8bd280fcb8f 510c1cc4fb1115fe1442059608f0999065f2f363 Sim Zhen Quan <<EMAIL>> 1749094934 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to dev
510c1cc4fb1115fe1442059608f0999065f2f363 d262f3885d1649141dfcddace142bcae843b31a1 Sim Zhen Quan <<EMAIL>> 1749094940 +0800	merge fix/update-validation-when-create-billing-doc: Merge made by the 'ort' strategy.
d262f3885d1649141dfcddace142bcae843b31a1 19168b635157ad77386aef2fd950ba2149298802 Sim Zhen Quan <<EMAIL>> 1749095324 +0800	checkout: moving from dev to enhancement/student-attendance-report
19168b635157ad77386aef2fd950ba2149298802 07fed8b495cf493ed09872b30be0a774d57694d0 Sim Zhen Quan <<EMAIL>> 1749095382 +0800	merge origin/main: Merge made by the 'ort' strategy.
07fed8b495cf493ed09872b30be0a774d57694d0 84f043941dafbab95c1eeb6861c71def21e6311b Sim Zhen Quan <<EMAIL>> 1749097975 +0800	commit: Reviewed
84f043941dafbab95c1eeb6861c71def21e6311b d262f3885d1649141dfcddace142bcae843b31a1 Sim Zhen Quan <<EMAIL>> 1749098011 +0800	checkout: moving from enhancement/student-attendance-report to dev
d262f3885d1649141dfcddace142bcae843b31a1 05623284ad8d56cda06315c5ea7d531ab32d3d63 Sim Zhen Quan <<EMAIL>> 1749098016 +0800	merge enhancement/student-attendance-report: Merge made by the 'ort' strategy.
05623284ad8d56cda06315c5ea7d531ab32d3d63 b18bced93dc57256ec45979081ce6fb7d7b7d1a5 Sim Zhen Quan <<EMAIL>> 1749107012 +0800	commit: Deployed to dev
b18bced93dc57256ec45979081ce6fb7d7b7d1a5 157c1b5f697b6dcaa04de948bb08e38c980ccdbe Sim Zhen Quan <<EMAIL>> 1749107018 +0800	checkout: moving from dev to exam-module-changes-v2
157c1b5f697b6dcaa04de948bb08e38c980ccdbe 512967e21a4e460a61a44448e23bec3940f19908 Sim Zhen Quan <<EMAIL>> 1749107024 +0800	pull: Fast-forward
512967e21a4e460a61a44448e23bec3940f19908 c524bbe3d220e2d81e93fa0539b4499efcc0a251 Sim Zhen Quan <<EMAIL>> 1749110989 +0800	pull: Fast-forward
c524bbe3d220e2d81e93fa0539b4499efcc0a251 956f870947f6a90e76df46ae15264eb3d07971b5 Sim Zhen Quan <<EMAIL>> 1749117956 +0800	commit: Reviewed
956f870947f6a90e76df46ae15264eb3d07971b5 b18bced93dc57256ec45979081ce6fb7d7b7d1a5 Sim Zhen Quan <<EMAIL>> 1749118055 +0800	checkout: moving from exam-module-changes-v2 to dev
b18bced93dc57256ec45979081ce6fb7d7b7d1a5 b90e99ce757c36e565913ac7f066a91f0f33f51d Sim Zhen Quan <<EMAIL>> 1749118069 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
b90e99ce757c36e565913ac7f066a91f0f33f51d 63e02574e3e8f6d7299c4acf94c7c19d365d5486 Sim Zhen Quan <<EMAIL>> 1749142894 +0800	commit: Deployed to dev
63e02574e3e8f6d7299c4acf94c7c19d365d5486 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> 1749143016 +0800	checkout: moving from dev to main
b256a94df313192188826a85009a7fbcd5b43b97 184846544bd4350e51b93f87e9677376cff9dab0 Sim Zhen Quan <<EMAIL>> 1749143021 +0800	pull: Fast-forward
184846544bd4350e51b93f87e9677376cff9dab0 3ef477c3b379761e6f5ab9b501b037411a3e55bf Sim Zhen Quan <<EMAIL>> 1749174880 +0800	commit: Deployed to prd
3ef477c3b379761e6f5ab9b501b037411a3e55bf 3891980d6276ab79276844e0ee0b70146379b29e Sim Zhen Quan <<EMAIL>> 1749175211 +0800	commit: Bug fix
3891980d6276ab79276844e0ee0b70146379b29e 50a1b341a214ca5fee1117a4e561d9e05fe147d6 Sim Zhen Quan <<EMAIL>> 1749176098 +0800	checkout: moving from main to feature/enrollment-user-login-via-email
50a1b341a214ca5fee1117a4e561d9e05fe147d6 c7404aa6fc86c8c261e571035b000ca069b2e50c Sim Zhen Quan <<EMAIL>> 1749176195 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-user-login-via-email
c7404aa6fc86c8c261e571035b000ca069b2e50c 3d215d40ae58b0a0adb0a2a69e9e3cbe79f455a7 Sim Zhen Quan <<EMAIL>> 1749177000 +0800	commit: Reviewed
3d215d40ae58b0a0adb0a2a69e9e3cbe79f455a7 63e02574e3e8f6d7299c4acf94c7c19d365d5486 Sim Zhen Quan <<EMAIL>> 1749177054 +0800	checkout: moving from feature/enrollment-user-login-via-email to dev
63e02574e3e8f6d7299c4acf94c7c19d365d5486 5c9598ec1aa4a8c7a8d84cae2e73b9943a8248ff Sim Zhen Quan <<EMAIL>> 1749177064 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
5c9598ec1aa4a8c7a8d84cae2e73b9943a8248ff 432e483ee58571b3f284d8ce006a4476bf533d8c Sim Zhen Quan <<EMAIL>> 1749177304 +0800	commit: Deployed to dev
432e483ee58571b3f284d8ce006a4476bf533d8c 1b567825f655907552b8d5d02075467d782e4e76 Sim Zhen Quan <<EMAIL>> 1749177315 +0800	checkout: moving from dev to exam-module-passing-marks
1b567825f655907552b8d5d02075467d782e4e76 ccf10ebc87bafb5f2b4339cd3d4789aa41f87146 Sim Zhen Quan <<EMAIL>> 1749178722 +0800	commit: Added a patch for student grading framework
ccf10ebc87bafb5f2b4339cd3d4789aa41f87146 432e483ee58571b3f284d8ce006a4476bf533d8c Sim Zhen Quan <<EMAIL>> 1749178791 +0800	checkout: moving from exam-module-passing-marks to dev
432e483ee58571b3f284d8ce006a4476bf533d8c a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 Sim Zhen Quan <<EMAIL>> 1749178798 +0800	merge origin/exam-module-passing-marks: Merge made by the 'ort' strategy.
a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 Sim Zhen Quan <<EMAIL>> 1749184851 +0800	reset: moving to HEAD
a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 956f870947f6a90e76df46ae15264eb3d07971b5 Sim Zhen Quan <<EMAIL>> 1749184853 +0800	checkout: moving from dev to exam-module-changes-v2
956f870947f6a90e76df46ae15264eb3d07971b5 e289a9641641cc427ed62489980a26d8a62b122f Sim Zhen Quan <<EMAIL>> 1749184884 +0800	commit: Updated supervisord.conf
e289a9641641cc427ed62489980a26d8a62b122f 70d17e9fc5c84a87e9db13f6b9d625c421995426 Sim Zhen Quan <<EMAIL>> 1749184912 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into exam-module-changes-v2
70d17e9fc5c84a87e9db13f6b9d625c421995426 70d17e9fc5c84a87e9db13f6b9d625c421995426 Sim Zhen Quan <<EMAIL>> 1749184942 +0800	checkout: moving from exam-module-changes-v2 to exam-module-changes-v2
70d17e9fc5c84a87e9db13f6b9d625c421995426 6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 Sim Zhen Quan <<EMAIL>> 1749184963 +0800	merge origin/main: Merge made by the 'ort' strategy.
6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 d0a4529d3f89fd0b39f1f5b872c60893386c2545 Sim Zhen Quan <<EMAIL>> 1749186074 +0800	checkout: moving from exam-module-changes-v2 to enrollment-v2
d0a4529d3f89fd0b39f1f5b872c60893386c2545 17e96e22e5a6a798917c554a34bcc072bf6e3d1b Sim Zhen Quan <<EMAIL>> 1749186082 +0800	pull: Fast-forward
17e96e22e5a6a798917c554a34bcc072bf6e3d1b 6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 Sim Zhen Quan <<EMAIL>> 1749186258 +0800	checkout: moving from enrollment-v2 to exam-module-changes-v2
6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 d4c68b3f136abd1a954210bb880cd5e593921e27 Sim Zhen Quan <<EMAIL>> 1749191583 +0800	commit: Updated report card queue timeout
d4c68b3f136abd1a954210bb880cd5e593921e27 17e96e22e5a6a798917c554a34bcc072bf6e3d1b Sim Zhen Quan <<EMAIL>> 1749191593 +0800	checkout: moving from exam-module-changes-v2 to enrollment-v2
17e96e22e5a6a798917c554a34bcc072bf6e3d1b 0d4d5f0a808ef1772b28fae32ca158e270965077 Sim Zhen Quan <<EMAIL>> 1749192581 +0800	checkout: moving from enrollment-v2 to jira-180-examination-result-by-semester-class
0d4d5f0a808ef1772b28fae32ca158e270965077 415557cf78c5aa30918aa6640ca6a7ad1c964cf2 Sim Zhen Quan <<EMAIL>> 1749192587 +0800	merge origin/main: Merge made by the 'ort' strategy.
415557cf78c5aa30918aa6640ca6a7ad1c964cf2 9d51fc2b0b6daf6c2f756f1e709abe28e49598a8 Sim Zhen Quan <<EMAIL>> 1749193352 +0800	checkout: moving from jira-180-examination-result-by-semester-class to examination-result-by-exam-report
9d51fc2b0b6daf6c2f756f1e709abe28e49598a8 08176ee16648d710438380c7903ff72f0401221b Sim Zhen Quan <<EMAIL>> 1749193372 +0800	merge origin/main: Merge made by the 'ort' strategy.
08176ee16648d710438380c7903ff72f0401221b e410147af75bb6860bd0ef3a51d8ee3fbc21948c Sim Zhen Quan <<EMAIL>> 1749193676 +0800	merge origin/exam-module-changes-v2: Merge made by the 'ort' strategy.
e410147af75bb6860bd0ef3a51d8ee3fbc21948c 7a3d67fc22c83d6329bdc083a5c0c295d0a65286 Sim Zhen Quan <<EMAIL>> 1749196228 +0800	commit: Reviewed
7a3d67fc22c83d6329bdc083a5c0c295d0a65286 0da92a71a294c65a5154de8f80f6be595dd494cf Sim Zhen Quan <<EMAIL>> 1749196468 +0800	checkout: moving from examination-result-by-exam-report to jira-183-exam-result-by-student
0da92a71a294c65a5154de8f80f6be595dd494cf a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 Sim Zhen Quan <<EMAIL>> 1749196507 +0800	checkout: moving from jira-183-exam-result-by-student to dev
a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 341e73c1178e3d3384cb599534b93cb846fc8805 Sim Zhen Quan <<EMAIL>> 1749196604 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into dev
341e73c1178e3d3384cb599534b93cb846fc8805 c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd Sim Zhen Quan <<EMAIL>> 1749197030 +0800	commit: Deployed to dev
c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd 0da92a71a294c65a5154de8f80f6be595dd494cf Sim Zhen Quan <<EMAIL>> 1749197040 +0800	checkout: moving from dev to jira-183-exam-result-by-student
0da92a71a294c65a5154de8f80f6be595dd494cf 4c2bbe340efc2db64b3ae98e0536ae4a18994db6 Sim Zhen Quan <<EMAIL>> 1749197048 +0800	merge origin/main: Merge made by the 'ort' strategy.
4c2bbe340efc2db64b3ae98e0536ae4a18994db6 b767828495c6063b6ea6f2fec339b7aa0bbf8837 Sim Zhen Quan <<EMAIL>> 1749197696 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into jira-183-exam-result-by-student
b767828495c6063b6ea6f2fec339b7aa0bbf8837 5b7b77747720acb4416442daa3516dd27cff73d5 Sim Zhen Quan <<EMAIL>> 1749199896 +0800	commit: Reviewed
5b7b77747720acb4416442daa3516dd27cff73d5 c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd Sim Zhen Quan <<EMAIL>> 1749199946 +0800	checkout: moving from jira-183-exam-result-by-student to dev
c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd 183d66c8487ab3d73761c817917de201ee73fdae Sim Zhen Quan <<EMAIL>> 1749199994 +0800	merge origin/jira-183-exam-result-by-student: Merge made by the 'ort' strategy.
183d66c8487ab3d73761c817917de201ee73fdae 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749202378 +0800	commit: Deployed to dev
45926c0881e62433b5733b05d750c1f84243f1db 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749202408 +0800	reset: moving to HEAD
45926c0881e62433b5733b05d750c1f84243f1db d4c68b3f136abd1a954210bb880cd5e593921e27 Sim Zhen Quan <<EMAIL>> 1749202409 +0800	checkout: moving from dev to exam-module-changes-v2
d4c68b3f136abd1a954210bb880cd5e593921e27 4cba4c5c953acab9bb85363b0c725bd931dc822e Sim Zhen Quan <<EMAIL>> 1749202417 +0800	pull: Fast-forward
4cba4c5c953acab9bb85363b0c725bd931dc822e cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749202552 +0800	commit: Bug fix
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749202597 +0800	reset: moving to HEAD
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e 17e96e22e5a6a798917c554a34bcc072bf6e3d1b Sim Zhen Quan <<EMAIL>> 1749202598 +0800	checkout: moving from exam-module-changes-v2 to enrollment-v2
17e96e22e5a6a798917c554a34bcc072bf6e3d1b a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 Sim Zhen Quan <<EMAIL>> 1749203010 +0800	commit: Fix form request
a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749203019 +0800	checkout: moving from enrollment-v2 to exam-module-changes-v2
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e 124d7e3c09cbcd9402d41db883810bea72ac3e98 Sim Zhen Quan <<EMAIL>> 1749203212 +0800	checkout: moving from exam-module-changes-v2 to feature/enrollment-update-marks-API
124d7e3c09cbcd9402d41db883810bea72ac3e98 d27d32ecd16b65007f3b17b75d2a5fca0b59a12d Sim Zhen Quan <<EMAIL>> 1749203253 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-update-marks-API
d27d32ecd16b65007f3b17b75d2a5fca0b59a12d 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749203502 +0800	checkout: moving from feature/enrollment-update-marks-API to dev
45926c0881e62433b5733b05d750c1f84243f1db 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749204709 +0800	reset: moving to HEAD
45926c0881e62433b5733b05d750c1f84243f1db cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749204712 +0800	checkout: moving from dev to exam-module-changes-v2
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e e08941ab2d58db6a6223428f77f8f7102d1b2e20 Sim Zhen Quan <<EMAIL>> 1749205200 +0800	commit: Route update
e08941ab2d58db6a6223428f77f8f7102d1b2e20 d27d32ecd16b65007f3b17b75d2a5fca0b59a12d Sim Zhen Quan <<EMAIL>> 1749205362 +0800	checkout: moving from exam-module-changes-v2 to feature/enrollment-update-marks-API
d27d32ecd16b65007f3b17b75d2a5fca0b59a12d 833a9cd3e3dd925a56a36296d9613ae98f5eac84 Sim Zhen Quan <<EMAIL>> 1749206398 +0800	commit: Reviewed
833a9cd3e3dd925a56a36296d9613ae98f5eac84 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749206435 +0800	checkout: moving from feature/enrollment-update-marks-API to dev
45926c0881e62433b5733b05d750c1f84243f1db bcb61f8762bfafbbb5c3d6932141d2575d770877 Sim Zhen Quan <<EMAIL>> 1749206445 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
bcb61f8762bfafbbb5c3d6932141d2575d770877 467392527dc655a67f342eda375d11cbfa299e63 Sim Zhen Quan <<EMAIL>> 1749206456 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
467392527dc655a67f342eda375d11cbfa299e63 04312cf50bf5c30f8d6e4f169027afa4b03a4488 Sim Zhen Quan <<EMAIL>> 1749206492 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
04312cf50bf5c30f8d6e4f169027afa4b03a4488 1fb717853b9cba1a0f2e44760391de29a9d9ee93 Sim Zhen Quan <<EMAIL>> 1749206916 +0800	commit: Deployed to dev
1fb717853b9cba1a0f2e44760391de29a9d9ee93 8bbd8a51633cfdc768950b47f622f02535f15249 Sim Zhen Quan <<EMAIL>> 1749206944 +0800	checkout: moving from dev to feature/enrollment-delete-API
8bbd8a51633cfdc768950b47f622f02535f15249 ac36558587de2e1715829c7a0428a891b9bd9811 Sim Zhen Quan <<EMAIL>> 1749206950 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
ac36558587de2e1715829c7a0428a891b9bd9811 aad8d1a859653acf493c4fa74c9eba4a0e5a61cb Sim Zhen Quan <<EMAIL>> 1749208196 +0800	commit: Reviewed
aad8d1a859653acf493c4fa74c9eba4a0e5a61cb 1fb717853b9cba1a0f2e44760391de29a9d9ee93 Sim Zhen Quan <<EMAIL>> 1749208205 +0800	checkout: moving from feature/enrollment-delete-API to dev
1fb717853b9cba1a0f2e44760391de29a9d9ee93 1fb717853b9cba1a0f2e44760391de29a9d9ee93 Sim Zhen Quan <<EMAIL>> 1749208465 +0800	reset: moving to HEAD
1fb717853b9cba1a0f2e44760391de29a9d9ee93 585c373e9abbf4adff0241e434895f2a6dead323 Sim Zhen Quan <<EMAIL>> 1749208559 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
585c373e9abbf4adff0241e434895f2a6dead323 12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 Sim Zhen Quan <<EMAIL>> 1749208932 +0800	commit: Deployed to dev
12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 e08941ab2d58db6a6223428f77f8f7102d1b2e20 Sim Zhen Quan <<EMAIL>> 1749208968 +0800	checkout: moving from dev to exam-module-changes-v2
e08941ab2d58db6a6223428f77f8f7102d1b2e20 5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d Sim Zhen Quan <<EMAIL>> 1749208982 +0800	commit: Fix excel generate error
5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d 098ab02355cd8a7c356486e2cf0d8beeea4c6511 Sim Zhen Quan <<EMAIL>> 1749231886 +0800	checkout: moving from exam-module-changes-v2 to feature/manual-payment-enrollment
098ab02355cd8a7c356486e2cf0d8beeea4c6511 7733914c9f10f564c5c4b7be7514cf2b96bd4498 Sim Zhen Quan <<EMAIL>> 1749231893 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
7733914c9f10f564c5c4b7be7514cf2b96bd4498 c160a27028b3faafcafd69a22e591ef84e3c81d7 Sim Zhen Quan <<EMAIL>> 1749279790 +0800	checkout: moving from feature/manual-payment-enrollment to feature/enrollment-feedback
c160a27028b3faafcafd69a22e591ef84e3c81d7 2f20ddb9c310d36a7207894bf7cbf945564afe08 Sim Zhen Quan <<EMAIL>> 1749280171 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-feedback
2f20ddb9c310d36a7207894bf7cbf945564afe08 cf03a60ef01b0b435687f34085e0b73466eaef5e Sim Zhen Quan <<EMAIL>> 1749281380 +0800	commit: Reviewed
cf03a60ef01b0b435687f34085e0b73466eaef5e 2c54e08bbba99dfbaa2cd60fc5daafd7cb536734 Sim Zhen Quan <<EMAIL>> 1749281415 +0800	checkout: moving from feature/enrollment-feedback to feature/enrollment-feedback-2
2c54e08bbba99dfbaa2cd60fc5daafd7cb536734 fd8807f5fa3bb8dc4fccac9711bfa50826417163 Sim Zhen Quan <<EMAIL>> 1749281490 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-feedback-2
fd8807f5fa3bb8dc4fccac9711bfa50826417163 dc89d72894814c9a87b13144b0fdcd9c723ecb09 Sim Zhen Quan <<EMAIL>> 1749282590 +0800	commit: Reviewed
dc89d72894814c9a87b13144b0fdcd9c723ecb09 53dda110bf3fd307f7fb6f377a54650f922ae6cc Sim Zhen Quan <<EMAIL>> 1749282620 +0800	commit: Reviewed
53dda110bf3fd307f7fb6f377a54650f922ae6cc a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 Sim Zhen Quan <<EMAIL>> 1749282771 +0800	checkout: moving from feature/enrollment-feedback-2 to enrollment-v2
a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 df4163d24aa7c0b321b0623221f64d0743dce1d3 Sim Zhen Quan <<EMAIL>> 1749282776 +0800	pull: Fast-forward
df4163d24aa7c0b321b0623221f64d0743dce1d3 3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e Sim Zhen Quan <<EMAIL>> 1749282781 +0800	merge origin/feature/enrollment-feedback-2: Merge made by the 'ort' strategy.
3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e 12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 Sim Zhen Quan <<EMAIL>> 1749283035 +0800	checkout: moving from enrollment-v2 to dev
12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 c64a74e7b13c9a91e466c3916205e11c9e1d28cb Sim Zhen Quan <<EMAIL>> 1749283048 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
c64a74e7b13c9a91e466c3916205e11c9e1d28cb 40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 Sim Zhen Quan <<EMAIL>> 1749308650 +0800	commit: Deployed to dev
40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e Sim Zhen Quan <<EMAIL>> 1749308689 +0800	checkout: moving from dev to enrollment-v2
3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e c4f6a96edf31bfadf5360a5a51c026613d05a638 Sim Zhen Quan <<EMAIL>> 1749308701 +0800	commit: Enhancement
c4f6a96edf31bfadf5360a5a51c026613d05a638 40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 Sim Zhen Quan <<EMAIL>> 1749308714 +0800	checkout: moving from enrollment-v2 to dev
40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 bc71f6affc2f2c9756f899482d40ca0795fd2c12 Sim Zhen Quan <<EMAIL>> 1749308720 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
bc71f6affc2f2c9756f899482d40ca0795fd2c12 c4f6a96edf31bfadf5360a5a51c026613d05a638 Sim Zhen Quan <<EMAIL>> 1749372603 +0800	checkout: moving from dev to enrollment-v2
c4f6a96edf31bfadf5360a5a51c026613d05a638 c8f44ad752ca4130999d85e854a558b09bf683a9 Sim Zhen Quan <<EMAIL>> 1749372674 +0800	commit: Added more filters
c8f44ad752ca4130999d85e854a558b09bf683a9 bc71f6affc2f2c9756f899482d40ca0795fd2c12 Sim Zhen Quan <<EMAIL>> 1749372679 +0800	checkout: moving from enrollment-v2 to dev
bc71f6affc2f2c9756f899482d40ca0795fd2c12 40edf440dcadcd40b36aefd9620689a49d4ac480 Sim Zhen Quan <<EMAIL>> 1749372694 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
40edf440dcadcd40b36aefd9620689a49d4ac480 c8f44ad752ca4130999d85e854a558b09bf683a9 Sim Zhen Quan <<EMAIL>> 1749434559 +0800	checkout: moving from dev to enrollment-v2
c8f44ad752ca4130999d85e854a558b09bf683a9 a5da7c1e6c25a72c0df212bb5e55e131ee3145a7 Sim Zhen Quan <<EMAIL>> 1749434588 +0800	commit: Validation enhancements
a5da7c1e6c25a72c0df212bb5e55e131ee3145a7 e028af692fd9dbc925c99cad05eb546777a18d41 Sim Zhen Quan <<EMAIL>> 1749435198 +0800	commit: Added sms notification
e028af692fd9dbc925c99cad05eb546777a18d41 40edf440dcadcd40b36aefd9620689a49d4ac480 Sim Zhen Quan <<EMAIL>> 1749435203 +0800	checkout: moving from enrollment-v2 to dev
40edf440dcadcd40b36aefd9620689a49d4ac480 a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 Sim Zhen Quan <<EMAIL>> 1749435208 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 4ab7f2c2937c563f5425f230b2033c55e8ba6b79 Sim Zhen Quan <<EMAIL>> 1749436675 +0800	checkout: moving from dev to api/duplicate-semester
4ab7f2c2937c563f5425f230b2033c55e8ba6b79 276e433b6af41f19567bf0dca6105dc29706c486 Sim Zhen Quan <<EMAIL>> 1749436680 +0800	pull: Fast-forward
276e433b6af41f19567bf0dca6105dc29706c486 cd33dad06336e66545ad211011780a8f33fe71be Sim Zhen Quan <<EMAIL>> 1749436681 +0800	merge origin/main: Merge made by the 'ort' strategy.
cd33dad06336e66545ad211011780a8f33fe71be a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 Sim Zhen Quan <<EMAIL>> 1749439593 +0800	checkout: moving from api/duplicate-semester to dev
a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 ce6182c9a78f71ad636114e600979165b6b75a2f Sim Zhen Quan <<EMAIL>> 1749439625 +0800	commit (merge): Merge branch 'api/duplicate-semester' into dev
ce6182c9a78f71ad636114e600979165b6b75a2f cd33dad06336e66545ad211011780a8f33fe71be Sim Zhen Quan <<EMAIL>> 1749447432 +0800	checkout: moving from dev to api/duplicate-semester
cd33dad06336e66545ad211011780a8f33fe71be 889e2b76262f52faf39b2059ef6b9805ef317d87 Sim Zhen Quan <<EMAIL>> 1749447437 +0800	pull: Fast-forward
889e2b76262f52faf39b2059ef6b9805ef317d87 4dcbb3b315391cb216d6d9c734fb7d6d65d7c09b Sim Zhen Quan <<EMAIL>> 1749451155 +0800	commit: Added semester setting to a batch
4dcbb3b315391cb216d6d9c734fb7d6d65d7c09b ab7f18147ea4431132acd5d091803192c96df022 Sim Zhen Quan <<EMAIL>> 1749451168 +0800	commit: Added semester setting to a batch
ab7f18147ea4431132acd5d091803192c96df022 b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 Sim Zhen Quan <<EMAIL>> 1749451243 +0800	commit: Fix resource name
b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 ce6182c9a78f71ad636114e600979165b6b75a2f Sim Zhen Quan <<EMAIL>> 1749451427 +0800	checkout: moving from api/duplicate-semester to dev
ce6182c9a78f71ad636114e600979165b6b75a2f 9f42083eae78fc49c1b7474ec34a04a1c784f7a4 Sim Zhen Quan <<EMAIL>> 1749451488 +0800	commit (merge): Merge branch 'api/duplicate-semester' into dev
9f42083eae78fc49c1b7474ec34a04a1c784f7a4 14fcd08402b780cf4a640fa35d54cee692b359aa Sim Zhen Quan <<EMAIL>> 1749454782 +0800	commit: Deployed to dev
14fcd08402b780cf4a640fa35d54cee692b359aa e028af692fd9dbc925c99cad05eb546777a18d41 Sim Zhen Quan <<EMAIL>> 1749454794 +0800	checkout: moving from dev to enrollment-v2
e028af692fd9dbc925c99cad05eb546777a18d41 82bef38e0ce58813182cd5a019c0ddbe2cc25445 Sim Zhen Quan <<EMAIL>> 1749455855 +0800	commit: Deployed to dev
82bef38e0ce58813182cd5a019c0ddbe2cc25445 14fcd08402b780cf4a640fa35d54cee692b359aa Sim Zhen Quan <<EMAIL>> 1749455864 +0800	checkout: moving from enrollment-v2 to dev
14fcd08402b780cf4a640fa35d54cee692b359aa 82bef38e0ce58813182cd5a019c0ddbe2cc25445 Sim Zhen Quan <<EMAIL>> 1749455896 +0800	checkout: moving from dev to enrollment-v2
82bef38e0ce58813182cd5a019c0ddbe2cc25445 14fcd08402b780cf4a640fa35d54cee692b359aa Sim Zhen Quan <<EMAIL>> 1749455907 +0800	checkout: moving from enrollment-v2 to dev
14fcd08402b780cf4a640fa35d54cee692b359aa 08a253143491acd98d05b76b68c610fbaf51c32e Sim Zhen Quan <<EMAIL>> 1749455910 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
08a253143491acd98d05b76b68c610fbaf51c32e 095b4c1cb2fe7e49a9ae742727d87d717af7f12a Sim Zhen Quan <<EMAIL>> 1749461896 +0800	commit: Deployed to dev
095b4c1cb2fe7e49a9ae742727d87d717af7f12a 82bef38e0ce58813182cd5a019c0ddbe2cc25445 Sim Zhen Quan <<EMAIL>> 1749461903 +0800	checkout: moving from dev to enrollment-v2
82bef38e0ce58813182cd5a019c0ddbe2cc25445 1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 Sim Zhen Quan <<EMAIL>> 1749462019 +0800	commit: Fix guardian phone number validation
1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 3891980d6276ab79276844e0ee0b70146379b29e Sim Zhen Quan <<EMAIL>> 1749462109 +0800	checkout: moving from enrollment-v2 to main
3891980d6276ab79276844e0ee0b70146379b29e 1a2e72a6b5c743da6749385536a3c82bd6dc8960 Sim Zhen Quan <<EMAIL>> 1749462122 +0800	commit: Fix active flag
1a2e72a6b5c743da6749385536a3c82bd6dc8960 05da7d6b6947e62e17bc4acf558183bbc5187182 Sim Zhen Quan <<EMAIL>> 1749462137 +0800	checkout: moving from main to fix/unique-validation-for-exam-slip
05da7d6b6947e62e17bc4acf558183bbc5187182 78f8562c070eeabbb18e160c96e798fbdd1bb5b9 Sim Zhen Quan <<EMAIL>> 1749462148 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
78f8562c070eeabbb18e160c96e798fbdd1bb5b9 1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 Sim Zhen Quan <<EMAIL>> 1749462371 +0800	checkout: moving from fix/unique-validation-for-exam-slip to enrollment-v2
1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 79756e607cef033594ad3c57d359433404a3e0c3 Sim Zhen Quan <<EMAIL>> 1749462377 +0800	pull: Fast-forward
79756e607cef033594ad3c57d359433404a3e0c3 095b4c1cb2fe7e49a9ae742727d87d717af7f12a Sim Zhen Quan <<EMAIL>> 1749462377 +0800	checkout: moving from enrollment-v2 to dev
095b4c1cb2fe7e49a9ae742727d87d717af7f12a c15c061e2ec9f185f57c806da44c95cc4184c8c9 Sim Zhen Quan <<EMAIL>> 1749462401 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
c15c061e2ec9f185f57c806da44c95cc4184c8c9 b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 Sim Zhen Quan <<EMAIL>> 1749462705 +0800	checkout: moving from dev to api/duplicate-semester
b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 be8bcf8f1e693cdab18d78399cdebae32706426f Sim Zhen Quan <<EMAIL>> 1749462711 +0800	pull: Fast-forward
be8bcf8f1e693cdab18d78399cdebae32706426f 447a0a3e522cb5cc90703836cda5b0312a8b73a5 Sim Zhen Quan <<EMAIL>> 1749462788 +0800	pull: Fast-forward
447a0a3e522cb5cc90703836cda5b0312a8b73a5 447a0a3e522cb5cc90703836cda5b0312a8b73a5 Sim Zhen Quan <<EMAIL>> 1749463792 +0800	reset: moving to HEAD
447a0a3e522cb5cc90703836cda5b0312a8b73a5 1a2e72a6b5c743da6749385536a3c82bd6dc8960 Sim Zhen Quan <<EMAIL>> 1749463793 +0800	checkout: moving from api/duplicate-semester to main
1a2e72a6b5c743da6749385536a3c82bd6dc8960 23bab7c6ed31b2ebfd7d3fe85311396eb0036b63 Sim Zhen Quan <<EMAIL>> 1749463799 +0800	pull: Fast-forward
23bab7c6ed31b2ebfd7d3fe85311396eb0036b63 358843091ccb57a9032bac1ef3d29dcf5bd60b46 Sim Zhen Quan <<EMAIL>> 1749463816 +0800	commit: Remove test code
358843091ccb57a9032bac1ef3d29dcf5bd60b46 79756e607cef033594ad3c57d359433404a3e0c3 Sim Zhen Quan <<EMAIL>> 1749463939 +0800	checkout: moving from main to enrollment-v2
79756e607cef033594ad3c57d359433404a3e0c3 538e8ede7fa84b81289a5a8330d919e565b19039 Sim Zhen Quan <<EMAIL>> 1749463974 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into enrollment-v2
538e8ede7fa84b81289a5a8330d919e565b19039 c95cd663ecca602ab5537aa625e10bab30bd259d Sim Zhen Quan <<EMAIL>> 1749464483 +0800	commit: Remove unused migration
c95cd663ecca602ab5537aa625e10bab30bd259d 987971fea1d642bb62a868ec3266ce2534529ec2 Sim Zhen Quan <<EMAIL>> 1749464751 +0800	commit: Added distinct to email and phone_number
987971fea1d642bb62a868ec3266ce2534529ec2 c15c061e2ec9f185f57c806da44c95cc4184c8c9 Sim Zhen Quan <<EMAIL>> 1749464764 +0800	checkout: moving from enrollment-v2 to dev
c15c061e2ec9f185f57c806da44c95cc4184c8c9 46846cfe4207adab919666d3cf2a67f674d52b71 Sim Zhen Quan <<EMAIL>> 1749464770 +0800	merge origin/main: Merge made by the 'ort' strategy.
46846cfe4207adab919666d3cf2a67f674d52b71 25ce66723142fd50be91f6a24450ba4233d1cca5 Sim Zhen Quan <<EMAIL>> 1749464779 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
25ce66723142fd50be91f6a24450ba4233d1cca5 358843091ccb57a9032bac1ef3d29dcf5bd60b46 Sim Zhen Quan <<EMAIL>> 1749465794 +0800	checkout: moving from dev to main
358843091ccb57a9032bac1ef3d29dcf5bd60b46 0f56c1589ee8a2056430fbbc0657e71e788beca3 Sim Zhen Quan <<EMAIL>> 1749465799 +0800	pull: Fast-forward
0f56c1589ee8a2056430fbbc0657e71e788beca3 7805ca3e1b691b98b44ac66ae71f9b4a84229349 Sim Zhen Quan <<EMAIL>> 1749523760 +0800	commit: Deployed to prd
7805ca3e1b691b98b44ac66ae71f9b4a84229349 a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749523972 +0800	commit: Move job trigger to the correct place
a95f0daa16ff8b0acce05c9082f63f13427aed5b 415557cf78c5aa30918aa6640ca6a7ad1c964cf2 Sim Zhen Quan <<EMAIL>> 1749543042 +0800	checkout: moving from main to jira-180-examination-result-by-semester-class
415557cf78c5aa30918aa6640ca6a7ad1c964cf2 5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d Sim Zhen Quan <<EMAIL>> 1749543065 +0800	checkout: moving from jira-180-examination-result-by-semester-class to exam-module-changes-v2
5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d 1a13da4be9c56f556d48d51236e5c4fceefced14 Sim Zhen Quan <<EMAIL>> 1749543681 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into exam-module-changes-v2
1a13da4be9c56f556d48d51236e5c4fceefced14 a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749543797 +0800	checkout: moving from exam-module-changes-v2 to main
a95f0daa16ff8b0acce05c9082f63f13427aed5b a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749543807 +0800	checkout: moving from main to staging/2025-06-10
a95f0daa16ff8b0acce05c9082f63f13427aed5b e6423294b65ad1444a8005eac9487add63276195 Sim Zhen Quan <<EMAIL>> 1749544899 +0800	checkout: moving from staging/2025-06-10 to enrollment-student-report-new-exam-bands
e6423294b65ad1444a8005eac9487add63276195 2502d13fe3e84c1cf4a84dac9379ba9ee4f81ff3 Sim Zhen Quan <<EMAIL>> 1749544905 +0800	merge origin/main: Merge made by the 'ort' strategy.
2502d13fe3e84c1cf4a84dac9379ba9ee4f81ff3 f4eeee6ad14483143f8a62afb365e78e54eb7f83 Sim Zhen Quan <<EMAIL>> 1749545387 +0800	commit: Fix test case
f4eeee6ad14483143f8a62afb365e78e54eb7f83 4d48ccca9ac383b33b8ba2c5e44cb83bb18054e4 Sim Zhen Quan <<EMAIL>> 1749545656 +0800	checkout: moving from enrollment-student-report-new-exam-bands to exam-report-card-format-changes
4d48ccca9ac383b33b8ba2c5e44cb83bb18054e4 ca99b5257ea435ab5eae12acacc96e220d29620d Sim Zhen Quan <<EMAIL>> 1749545662 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
ca99b5257ea435ab5eae12acacc96e220d29620d 25ce66723142fd50be91f6a24450ba4233d1cca5 Sim Zhen Quan <<EMAIL>> 1749546701 +0800	checkout: moving from exam-report-card-format-changes to dev
25ce66723142fd50be91f6a24450ba4233d1cca5 8351807d88619ad549669efee7f8c421d212f337 Sim Zhen Quan <<EMAIL>> 1749546707 +0800	merge origin/exam-module-changes-v2: Merge made by the 'ort' strategy.
8351807d88619ad549669efee7f8c421d212f337 64c383e7c7076e9aca307853cf3e0344d17d5213 Sim Zhen Quan <<EMAIL>> 1749547081 +0800	commit: Deployed to dev
64c383e7c7076e9aca307853cf3e0344d17d5213 415557cf78c5aa30918aa6640ca6a7ad1c964cf2 Sim Zhen Quan <<EMAIL>> 1749548684 +0800	checkout: moving from dev to jira-180-examination-result-by-semester-class
415557cf78c5aa30918aa6640ca6a7ad1c964cf2 cf8d0f413d94efb296afd23f1cf96c6e03bbdbdf Sim Zhen Quan <<EMAIL>> 1749549097 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into jira-180-examination-result-by-semester-class
cf8d0f413d94efb296afd23f1cf96c6e03bbdbdf 3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba Sim Zhen Quan <<EMAIL>> 1749550034 +0800	commit: Review WIP
3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba 64c383e7c7076e9aca307853cf3e0344d17d5213 Sim Zhen Quan <<EMAIL>> 1749550041 +0800	checkout: moving from jira-180-examination-result-by-semester-class to dev
64c383e7c7076e9aca307853cf3e0344d17d5213 9c3ecee9e3024b60d3a4480ef9f54e1589deced7 Sim Zhen Quan <<EMAIL>> 1749550095 +0800	commit (merge): Merge remote-tracking branch 'origin/jira-180-examination-result-by-semester-class' into dev
9c3ecee9e3024b60d3a4480ef9f54e1589deced7 1b916f39bac8f3597cb6761667e73baa029f3175 Sim Zhen Quan <<EMAIL>> 1749574635 +0800	commit: Deployed to dev
1b916f39bac8f3597cb6761667e73baa029f3175 a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749574640 +0800	checkout: moving from dev to main
a95f0daa16ff8b0acce05c9082f63f13427aed5b add5ae6c345bbfc033d19dc7f0833ecee0ec3548 Sim Zhen Quan <<EMAIL>> 1749574651 +0800	pull: Fast-forward
add5ae6c345bbfc033d19dc7f0833ecee0ec3548 49f07d1bc91d428d712fa9ac66206f1215791f72 Sim Zhen Quan <<EMAIL>> 1749619293 +0800	commit: Deployed to prd
49f07d1bc91d428d712fa9ac66206f1215791f72 8de7e3c8495c5d76d874ca5514233c14b0eff338 Sim Zhen Quan <<EMAIL>> 1749627114 +0800	commit: Added enrollment mail
8de7e3c8495c5d76d874ca5514233c14b0eff338 1b916f39bac8f3597cb6761667e73baa029f3175 Sim Zhen Quan <<EMAIL>> 1749627208 +0800	checkout: moving from main to dev
1b916f39bac8f3597cb6761667e73baa029f3175 75e840256e60a1c889974fdba957f8edc628bb35 Sim Zhen Quan <<EMAIL>> 1749627223 +0800	merge origin/main: Merge made by the 'ort' strategy.
75e840256e60a1c889974fdba957f8edc628bb35 3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba Sim Zhen Quan <<EMAIL>> 1749635252 +0800	checkout: moving from dev to jira-180-examination-result-by-semester-class
3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba eb6a3a35315782ff31577f8c28825be1a78f44ca Sim Zhen Quan <<EMAIL>> 1749635261 +0800	pull: Fast-forward
eb6a3a35315782ff31577f8c28825be1a78f44ca 609d68635b76aa851ae6a538891d0198bdf163fb Sim Zhen Quan <<EMAIL>> 1749635262 +0800	merge origin/main: Merge made by the 'ort' strategy.
609d68635b76aa851ae6a538891d0198bdf163fb 75e840256e60a1c889974fdba957f8edc628bb35 Sim Zhen Quan <<EMAIL>> 1749637122 +0800	checkout: moving from jira-180-examination-result-by-semester-class to dev
75e840256e60a1c889974fdba957f8edc628bb35 8972e0e67a9fa5b8066b99929cb7a93d56537e1d Sim Zhen Quan <<EMAIL>> 1749637129 +0800	merge origin/main: Merge made by the 'ort' strategy.
8972e0e67a9fa5b8066b99929cb7a93d56537e1d 178cf05b518b3b62a9daa49fe783cccc98688713 Sim Zhen Quan <<EMAIL>> 1749637142 +0800	merge jira-180-examination-result-by-semester-class: Merge made by the 'ort' strategy.
178cf05b518b3b62a9daa49fe783cccc98688713 609d68635b76aa851ae6a538891d0198bdf163fb Sim Zhen Quan <<EMAIL>> 1749665694 +0800	checkout: moving from dev to jira-180-examination-result-by-semester-class
609d68635b76aa851ae6a538891d0198bdf163fb fc7831aa263ab9cf39cbb1f10be13c5defcde951 Sim Zhen Quan <<EMAIL>> 1749666141 +0800	commit: Fix report formatting
fc7831aa263ab9cf39cbb1f10be13c5defcde951 8de7e3c8495c5d76d874ca5514233c14b0eff338 Sim Zhen Quan <<EMAIL>> 1749666227 +0800	checkout: moving from jira-180-examination-result-by-semester-class to main
8de7e3c8495c5d76d874ca5514233c14b0eff338 e68ead3f9f3ca54a70c021aa141286fcb9d38dcd Sim Zhen Quan <<EMAIL>> 1749666232 +0800	pull: Fast-forward
e68ead3f9f3ca54a70c021aa141286fcb9d38dcd a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 Sim Zhen Quan <<EMAIL>> 1749718576 +0800	checkout: moving from main to feature/get-summary-API
a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 e68ead3f9f3ca54a70c021aa141286fcb9d38dcd Sim Zhen Quan <<EMAIL>> 1749718581 +0800	checkout: moving from feature/get-summary-API to main
e68ead3f9f3ca54a70c021aa141286fcb9d38dcd 4f2e8b8d54f334bf21d2513339eb0335a68812ff Sim Zhen Quan <<EMAIL>> 1749718591 +0800	commit: Deployed to prd
4f2e8b8d54f334bf21d2513339eb0335a68812ff 5a10516379599ac547002541ee6f17f75f8e0ef0 Sim Zhen Quan <<EMAIL>> 1749718646 +0800	commit: Fix google form link
5a10516379599ac547002541ee6f17f75f8e0ef0 a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 Sim Zhen Quan <<EMAIL>> 1749718654 +0800	checkout: moving from main to feature/get-summary-API
a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 773306ac8280a371c2ab19ccfd76bc6011669dce Sim Zhen Quan <<EMAIL>> 1749718660 +0800	merge origin/main: Merge made by the 'ort' strategy.
773306ac8280a371c2ab19ccfd76bc6011669dce 19358a21b74e1d1e828b10de4f0b1ff758278066 Sim Zhen Quan <<EMAIL>> 1749721764 +0800	commit: Added summary into show api
19358a21b74e1d1e828b10de4f0b1ff758278066 178cf05b518b3b62a9daa49fe783cccc98688713 Sim Zhen Quan <<EMAIL>> 1749721829 +0800	checkout: moving from feature/get-summary-API to dev
178cf05b518b3b62a9daa49fe783cccc98688713 e5bd3e7ba856af267704a720d8acfdbdd01a1814 Sim Zhen Quan <<EMAIL>> 1749721839 +0800	merge feature/get-summary-API: Merge made by the 'ort' strategy.
e5bd3e7ba856af267704a720d8acfdbdd01a1814 227423700d400fb0c57d03bfd63c2e7fe8695770 Sim Zhen Quan <<EMAIL>> 1749745508 +0800	commit: Deployed to dev
227423700d400fb0c57d03bfd63c2e7fe8695770 19358a21b74e1d1e828b10de4f0b1ff758278066 Sim Zhen Quan <<EMAIL>> 1749745518 +0800	checkout: moving from dev to feature/get-summary-API
