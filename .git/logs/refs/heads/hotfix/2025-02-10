0000000000000000000000000000000000000000 08147cdf3bd0896b9cc2135699f8b143ce44a892 Sim <PERSON>hen <PERSON>uan <<EMAIL>> 1739150683 +0800	branch: Created from HEAD
08147cdf3bd0896b9cc2135699f8b143ce44a892 dfbc00e0192304792b59e328ed89993f43be3911 Sim Zhen Quan <<EMAIL>> 1739150801 +0800	commit: load additional relationships for user in UserController
dfbc00e0192304792b59e328ed89993f43be3911 117412964aa8fa7dfd73cf776b40cb110b851a0a Sim Zhen Quan <<EMAIL>> 1739152127 +0800	commit: array_unique() array before loading relationship
117412964aa8fa7dfd73cf776b40cb110b851a0a 18bd3f99a2d55ebef39c411d461a4ce54f840a45 Sim Zhen <PERSON>uan <<EMAIL>> 1739156063 +0800	pull: Fast-forward
18bd3f99a2d55ebef39c411d461a4ce54f840a45 d3dc08a7971e0d7b3f86503f05bdff3ceb7bce0c Sim Zhen Quan <<EMAIL>> 1739169298 +0800	merge origin/main: Merge made by the 'ort' strategy.
d3dc08a7971e0d7b3f86503f05bdff3ceb7bce0c d85d6730f831d8ff51cc3c45b4b8ecbd05f19e66 Sim Zhen Quan <<EMAIL>> 1739182975 +0800	commit: Revert to use UserableViewResource
d85d6730f831d8ff51cc3c45b4b8ecbd05f19e66 95454ff9ff9e1faf044a0531acd8064a3ca69457 Sim Zhen Quan <<EMAIL>> 1739183200 +0800	commit: Fix test case
95454ff9ff9e1faf044a0531acd8064a3ca69457 74bc7d5a4cd82756720ccbec89da3b73ae9369fb Sim Zhen Quan <<EMAIL>> 1739184068 +0800	commit: Added auditable to used models
74bc7d5a4cd82756720ccbec89da3b73ae9369fb 7e8f5a769c4f0be4d36039a7fd25d25110d13403 Sim Zhen Quan <<EMAIL>> 1739184259 +0800	commit: Added more models to Auditable
